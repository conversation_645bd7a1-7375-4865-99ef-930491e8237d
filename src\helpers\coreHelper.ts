import { create, all } from 'mathjs'
const config = {}
const math = create(all, config) as any
import { enumData } from '../constants/enumData'
import { UnauthorizedException } from '@nestjs/common'
import { nanoid } from 'nanoid'

class CoreHelper {
  /** Get array từ obj */
  public convertObjToArray(obj: any) {
    const arr = []
    // tslint:disable-next-line:forin
    for (const key in obj) {
      const value = obj[key]
      arr.push(value)
    }
    return arr
  }

  /** Get array từ obj */
  public convertObjToArrayCodeName(obj: any) {
    const arr = []
    // tslint:disable-next-line:forin
    for (const key in obj) {
      const value = obj[key]
      arr.push({ code: value.code, name: value.name })
    }
    return arr
  }

  public newDateTZ() {
    const d = new Date()
    const offset = 7
    // convert to msec
    // add local time zone offset
    // get UTC time in msec
    const utc = d.getTime() + d.getTimezoneOffset() * 60000

    // create new Date object for different city
    // using supplied offset
    const nd = new Date(utc + 3600000 * offset)
    return nd
  }

  async calFomular(fomular: string, lstField: any[], item: any) {
    let value = null
    const lstCol = lstField.filter((c) => c.type === enumData.QuestionType.Number.code)
    let tempFomular = fomular
    for (const col of lstCol) {
      tempFomular = tempFomular.replace(`[${col.code}]`, item[col.id])
    }

    try {
      value = math.evaluate(tempFomular)
      if (typeof value !== 'number') value = null
    } catch {
      value = null
      return value
    } finally {
      return value
    }
  }

  //#region tính điểm giá
  public async callScore(list: any, scoreDLC: number) {
    // Tính điểm
    let scorePrice = 0
    let minValue = 100000000
    let lstValue = []
    //Tìm minValue và lưu điểm tạm
    for (const item of list) {
      const bidPrice = await item.bidPrice
      if (
        bidPrice &&
        bidPrice.parentId === null &&
        bidPrice.type === enumData.QuestionType.Number.code &&
        item.value &&
        `${item.value}`.trim() != ''
      ) {
        const temp = await this.calScorePriceItem(bidPrice.percent, `${item.value}`)
        if (temp < minValue) {
          minValue = temp
        }
        lstValue.push(temp)
        item.score = temp
      }
    }

    const dlc = await this.calDLC(lstValue)
    for (const item of list) {
      const bidPrice = await item.bidPrice
      if (bidPrice && bidPrice.parentId === null && bidPrice.type === enumData.QuestionType.Number.code) {
        if (dlc > 0) {
          item.score = scoreDLC - (item.score - minValue) / dlc
          scorePrice += item.score
        } else {
          item.score = scoreDLC
          scorePrice += item.score
        }
      }
    }

    if (isNaN(scorePrice)) {
      return { scorePrice: 0, list }
    } else if (!isFinite(scorePrice)) {
      return { scorePrice: 0, list }
    } else return { scorePrice, list }
  }

  /** Hàm tính độ lệch chuẩn */
  public calDLC(lstValue: number[]) {
    const n = lstValue.length
    if (n < 2) {
      return 0
    }
    let sum = 0
    for (const i of lstValue) {
      sum += i
    }
    const avg = sum / n
    let sum2 = 0
    for (const i of lstValue) {
      sum2 += Math.pow(i - avg, 2)
    }
    const variance = sum2 / (n - 1)
    const std = Math.sqrt(variance)
    return std
  }

  /** Tính điểm đánh giá giá */
  async calScorePriceItem(percent: number, value: string) {
    let score = 0
    if (value && value.trim() != '') score = (+value * percent) / 100

    if (isNaN(score) || !isFinite(score)) return 0

    return score
  }

  /** Lấy distinct theo 1 trường */
  selectDistinct(arr: any[], field: string): string[] {
    return [...new Set(arr.map((c) => c[field]))]
  }

  /** Lấy sum theo 1 trường */
  selectSum(arr: any[], field: string): number {
    let sum = 0
    for (const item of arr) sum += +item[field] || 0
    return sum
  }

  public groupByArray(data: any, key: any) {
    const groupedObj = data.reduce((prev: any, cur: any) => {
      if (!prev[cur[key]]) {
        prev[cur[key]] = [cur]
      } else {
        prev[cur[key]].push(cur)
      }
      return prev
    }, {})
    return Object.keys(groupedObj).map((Heading) => ({
      heading: Heading,
      list: groupedObj[Heading],
    }))
  }

  rankABCD(score: number) {
    if (score > 90) return 'A'
    if (score > 70) return 'B'
    if (score > 50) return 'C'
    return 'D'
  }

  /** Hàm lấy đầu ngày */
  public getFirstDate(date?: Date): Date {
    if (!date) date = new Date()
    return new Date(new Date(date).setHours(0, 0, 0, 0))
  }

  /** Hàm lấy cuối ngày */
  public getLastDate(date?: Date): Date {
    if (!date) date = new Date()
    return new Date(new Date(date).setHours(23, 59, 59, 99))
  }

  public arrayToObject(arr: any[], key: string = 'id') {
    return arr.reduce((a, v) => (a[v[key]] ? { ...a, [v[key]]: { ...a[v[key]], ...v } } : { ...a, [v[key]]: v }), {})
  }

  public generatePOString(quantity?: number, character: string = "PO") {
    const now = new Date();
    const day = String(now.getDate()).padStart(2, '0');
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const year = String(now.getFullYear()).slice(-2);

    const formattedSequence = String(quantity).padStart(6, '0');
    const poCode = `${character}${day}${month}${year}${formattedSequence}`;
    return poCode;
  }

  public generatePOGroupString() {
    const uuid = nanoid()
    const poCode = `POG_${uuid}`;
    return poCode;
  }

  public generateDeliveryNoteCode(quantity: number, character: string = 'ASN') {
    const now = new Date();
    const day = String(now.getDate()).padStart(2, '0');
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const year = String(now.getFullYear()).slice(-2);

    const formattedSequence = String(quantity).padStart(6, '0');
    const deliveryNoteCode = `${character}${day}${month}${year}${formattedSequence}`;
    return deliveryNoteCode;
  }

  public generateDeliveryNoteChildCode(quantity) {
    const now = new Date();
    const day = String(now.getDate()).padStart(2, '0');
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const year = String(now.getFullYear()).slice(-2);

    const formattedSequence = String(quantity).padStart(6, '0');
    const deliveryNoteCode = `GRN${day}${month}${year}${formattedSequence}`;
    return deliveryNoteCode;
  }

  public mergeItemsToArray(input: any) {
    const mergedMap = new Map();

    for (const warehouse of input) {
      for (const item of warehouse.items) {
        const key = `${item.itemId}_${item.deliveryId}_${item.warehouseId}_${item.distributorId}`;

        if (!mergedMap.has(key)) {
          mergedMap.set(key, { ...item }); // Clone item
        } else {
          const existing = mergedMap.get(key)!;

          // Cộng dồn các trường cần thiết
          existing.quantityBasicUnit += item.quantityBasicUnit;
          existing.totalAmount += item.totalAmount;
          existing.totalAmountVat += item.totalAmountVat;
          existing.vat += item.vat;
        }
      }
    }

    return Array.from(mergedMap.values());
  }

}

export const coreHelper = new CoreHelper()
