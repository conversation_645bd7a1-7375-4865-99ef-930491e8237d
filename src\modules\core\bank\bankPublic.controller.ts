import { Controller, Get, Query, UseGuards } from "@nestjs/common";
import { BankService } from "./bank.service";
import { ApiBearerAuth, ApiTags } from "@nestjs/swagger";
import { PageRequest } from "../../../dto";
import { ApeAuthGuard } from "../../survey/common/guards";
import { BankBranchLstDto } from "./dto/bank.dto";

@ApiBearerAuth()
@UseGuards(ApeAuthGuard)
@ApiTags('Bank')
@Controller('bank_public')
export class BankPublicController {
  constructor(private readonly bankService: BankService) { }

  @Get("list")
  async getBankList() {
    return this.bankService.getBankList();
  }

  @Get("pagination")
  async getBankPagination(@Query() params: PageRequest) {
    return this.bankService.getBankPagination(params);
  }

  @Get("bank-branch")
  async getBrandOfBank(@Query() BankBranchLstDto: BankBranchLstDto) {
    return this.bankService.getBranchByBankCode(BankBranchLstDto);
  }

}