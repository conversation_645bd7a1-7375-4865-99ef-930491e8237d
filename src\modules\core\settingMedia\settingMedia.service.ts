import { Injectable } from '@nestjs/common'
import { PaginationDto } from '../../../dto/pagination.dto'
import { CurrentUser } from '../../common/decorators'
import { FilterOneDto, UserDto } from '../../../dto'
import { SettingMediaCreateDto } from './dto'
import { SettingMediaRepository } from '@/repositories/core/settingMedia.repository'

@Injectable()
export class SettingMediaService {
  constructor(private readonly repository: SettingMediaRepository) {}

  public async find(data: any) {
    let results = await this.repository.find(data)
    // results = results.filter((x) => x.itemInComboId == null && x.productId == null && x.isDeleted == false)
    results = results.filter((x) => x.isDeleted == false)
    if (results.length === 0) {
      return { banners: null, categories: [] }
    }
    const transformedData = {
      banners: results[0].banner,
      categories: results.map((item) => ({
        id: item.id,
        typeName: item.typeName,
        content: item.content,
        type: item.type,
        term: item.term,
        link: item.link,
        url: item.url,
        img: item.img && item.img.length > 0 ? item.img[0] : '',
        personalDeposit: item.personalDeposit,
        corporateDeposit: item.corporateDeposit,
        commission: item.commission,
      })),
    }
    for (let item of transformedData.categories) {
      if (item.type == 'TOURIST') item.type = 'TOURISM'
    }
    return transformedData
  }

  public async findOne(data: FilterOneDto) {
    const foundSettingMedia = await this.repository.findOne({
      where: {
        id: data.id,
      },
    })
    if (!foundSettingMedia) {
      throw new Error('Không tìm thấy')
    }

    return foundSettingMedia
  }

  public async createData(data: SettingMediaCreateDto, @CurrentUser() user: UserDto): Promise<any> {
    return await this.repository.createData(data, user)
  }

  public async updateData(data: SettingMediaCreateDto, @CurrentUser() user: UserDto): Promise<any> {
    return await this.repository.updateData(data, user)
  }

  public async pagination(data: PaginationDto) {
    return await this.repository.pagination(data)
  }

  public async updateIsDelete(id: string) {
    return await this.repository.updateIsDelete(id)
  }
}
