import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm'
import { InboundEntity } from './inbound.entity'
import { BaseEntity } from '../core/base.entity'

@Entity('inbound_history')
export class InboundHistoryEntity extends BaseEntity {
  @Column({ type: 'varchar', nullable: true })
  inboundId: string

  @Column({ type: 'varchar', length: '50', nullable: true })
  createdByName: string

  @Column({ type: 'text', nullable: true })
  description: string

  @ManyToOne(() => InboundEntity, (p) => p.histories)
  @JoinColumn({ name: 'inboundId', referencedColumnName: 'id' })
  inbound: Promise<InboundEntity>
}
