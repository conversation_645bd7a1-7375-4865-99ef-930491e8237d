import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import { AppService } from '../../app.service'
import { PublicSettingStringController } from './settingString.controller'
import { PublicSettingStringService } from './settingString.service'
import { SettingStringRepository, UserRepository } from '../../../repositories/core'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([SettingStringRepository])],
  controllers: [PublicSettingStringController],
  providers: [PublicSettingStringService],
  exports: [PublicSettingStringService],
})
export class PublicSettingStringModule {}
