import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import { UnitRepository } from '../../../repositories'
import { UnitService } from './unit.service'
import { UnitController } from './unit.controller'
import { UnitPublicController } from './unitPublic.controller'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([UnitRepository])],
  controllers: [UnitController, UnitPublicController],
  providers: [UnitService],
  exports: [UnitService],
})
export class UnitModule {}
