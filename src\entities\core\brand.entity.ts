import { <PERSON>ti<PERSON>, Column, <PERSON>To<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { WardEntity } from './ward.entity'
import { DistrictEntity } from './district.entity'
import { CityEntity } from './city.entity'
@Entity('brand')
export class BrandEntity extends BaseEntity {
  @Column({ type: 'varchar', length: 50, nullable: false, unique: false })
  code: string

  @Column({ type: 'varchar', length: 250, nullable: false })
  name: string

  @Column({ type: 'varchar', length: 50, nullable: true })
  phone: string

  /** Email */
  @Column({ type: 'varchar', length: 50, nullable: true })
  email: string

  @Column({ type: 'text', nullable: true })
  description: string

  @Column({ type: 'varchar', nullable: true })
  parentId: string

  /** Địa chỉ trụ sở */
  @Column({ type: 'varchar', length: 250, nullable: true })
  address: string

  @Column({ type: 'varchar', nullable: true })
  wardId: string

  @Column({ type: 'varchar', nullable: true })
  districtId: string

  @Column({ type: 'varchar', nullable: true })
  cityId: string

  @ManyToOne(() => WardEntity, (p) => p.id)
  @JoinColumn({ name: 'wardId', referencedColumnName: 'id' })
  ward: Promise<WardEntity>

  @ManyToOne(() => DistrictEntity, (p) => p.id)
  @JoinColumn({ name: 'districtId', referencedColumnName: 'id' })
  district: Promise<DistrictEntity>

  @ManyToOne(() => CityEntity, (p) => p.id)
  @JoinColumn({ name: 'cityId', referencedColumnName: 'id' })
  city: Promise<CityEntity>

  /** 1 Brand chỉ có 1 Brand cha */
  @ManyToOne(() => BrandEntity, (p) => p.childs)
  @JoinColumn({ name: 'parentId', referencedColumnName: 'id' })
  parent: BrandEntity

  /** 1 Brand có thể có nhiều Brand con */
  @OneToMany(() => BrandEntity, (p) => p.parent)
  childs: Promise<BrandEntity[]>
}
