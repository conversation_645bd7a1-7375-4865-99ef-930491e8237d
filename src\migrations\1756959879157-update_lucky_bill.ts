import { MigrationInterface, QueryRunner } from 'typeorm'

export class updateLuckyBill1756959879157 implements MigrationInterface {
  name = 'updateLuckyBill1756959879157'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "lucky_bill_config" ADD "lastDrawDate" TIMESTAMP WITH TIME ZONE`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "lucky_bill_config" DROP COLUMN "lastDrawDate"`)
  }
}
