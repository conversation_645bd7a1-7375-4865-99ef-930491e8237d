import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsBoolean, IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator'
import { PageRequest } from '../../../../dto'
import { isBoolean } from 'mathjs'
import { Transform } from 'class-transformer'
import { NSPo } from '../../../../constants'

export class CreateDeliveryNoteDto {
  @ApiProperty({ description: 'ID của PO' })
  @IsNotEmpty()
  @IsUUID()
  purchaseOrderId: string

  @ApiProperty({ description: 'Người tạo' })
  @IsOptional()
  createdBy?: string
}

export class UpdateDeliveryStatusDto {
  @ApiProperty({ description: 'ID của phiếu giao nhận' })
  @IsNotEmpty()
  @IsUUID()
  id: string
}
export class UpdateReceivingStatusDto {
  @ApiProperty({ description: 'ID của phiếu giao nhận' })
  @IsNotEmpty()
  @IsUUID()
  id: string
}

export class UpdateFileTrackingDto extends UpdateReceivingStatusDto {
  @ApiProperty({ description: 'Danh sách file' })
  @IsNotEmpty()
  files: string[]
}
export class FilterDeliverNoteDto extends PageRequest {
  @ApiProperty({ description: 'Code của phiếu giao nhận' })
  @IsOptional()
  code?: string

  @ApiProperty({ description: 'Trạng thái nhận' })
  @IsOptional()
  receivingStatus?: string

  @ApiProperty({ description: 'Trạng thái giao' })
  @IsOptional()
  deliveryStatus?: string

  @ApiProperty({ description: 'Ngày tạo' })
  @IsOptional()
  dateFrom?: string

  @ApiProperty({ description: 'Ngày tạo' })
  @IsOptional()
  dateTo?: string
}
export class UpdateDeliveryNoteFilesDto {
  @ApiProperty({
    description: 'Danh sách file đính kèm bên giao',
    example: { name: 'default', src: 'aws.com/dada' },
  })
  @IsOptional()
  filesDelivery?: string

  @ApiProperty({
    description: 'Danh sách file đính kèm bên nhận',
    example: { name: 'default', src: 'aws.com/dada' },
  })
  @IsOptional()
  filesReceiving?: string
}
export class UpdateDeliveryNoteDto extends UpdateDeliveryNoteFilesDto {
  @ApiProperty({ description: 'ID của Delivery Note' })
  @IsNotEmpty()
  @IsUUID()
  deliveryNoteId: string

  @ApiProperty({ description: 'Ghi chú bên giao' })
  @IsOptional()
  noteDelivery?: string

  @ApiProperty({ description: 'Ghi chú bên nhận' })
  @IsOptional()
  noteReceiving?: string
}

/** PORTAL */
export class TrackingList3PLDto extends PageRequest {
  @ApiProperty({ description: 'ID 3PL' })
  @IsUUID()
  deliveryId: string

  @ApiProperty({ description: 'Code Delivery Note' })
  @IsString()
  @IsOptional()
  deliveryCode?: string

  @ApiProperty({ description: 'PO Code' })
  @IsString()
  @IsOptional()
  poCode?: string

  @ApiPropertyOptional({ description: 'ID Nhà cung cấp' })
  @IsUUID()
  @IsOptional()
  fromId?: string

  @ApiPropertyOptional({ description: 'Là Phiếu đóng gói' })
  @IsOptional()
  @IsString()
  isPacking?: boolean

  @ApiProperty({ description: 'Trạng thái nhận' })
  @IsOptional()
  receivingStatus?: string

  @ApiProperty({ description: 'Ngày tạo' })
  @IsOptional()
  dateFrom?: string

  @ApiProperty({ description: 'Ngày tạo' })
  @IsOptional()
  dateTo?: string
}

export class TrackingListSupplierDto extends PageRequest {
  @ApiProperty({ description: 'ID Supplier' })
  @IsUUID()
  supplierId: string

  @ApiProperty({ description: 'Code Delivery Note' })
  @IsString()
  @IsOptional()
  deliveryCode?: string

  @ApiProperty({ description: 'PO Code' })
  @IsString()
  @IsOptional()
  poCode?: string

  @ApiPropertyOptional({ description: 'ID 3PL' })
  @IsUUID()
  @IsOptional()
  toId?: string

  @ApiProperty({ description: 'Trạng thái nhận' })
  @IsOptional()
  receivingStatus?: string

  @ApiProperty({ description: 'Ngày tạo' })
  @IsOptional()
  dateFrom?: string

  @ApiProperty({ description: 'Ngày tạo' })
  @IsOptional()
  dateTo?: string

  @ApiProperty({ description: 'Ngày giao hàng dự kiến đến' })
  @IsOptional()
  expectedDateFrom?: string

  @ApiProperty({ description: 'Ngày giao hàng dự kiến đến' })
  @IsOptional()
  expectedDateTo?: string
}

export class PackingListDto extends PageRequest {
  @ApiProperty({ description: 'Code Packing Note' })
  @IsString()
  @IsOptional()
  packingUnitId?: string

  @ApiProperty({ description: 'ID Delivery Note' })
  @IsUUID()
  @IsOptional()
  deliveryNoteId?: string

  @ApiProperty({ description: 'Trạng thái đóng gói' })
  @IsOptional()
  packingStatus?: NSPo.EDeliveryTracking

  @ApiProperty({ description: 'Ngày đóng gói' })
  @IsOptional()
  packingDate: string

  @ApiProperty({ description: 'Ngày tạo' })
  @IsOptional()
  dateFrom?: string

  @ApiProperty({ description: 'Ngày tạo' })
  @IsOptional()
  dateTo?: string
}

export class DetailTrackingDto {
  @ApiProperty({ description: 'ID Tracking' })
  @IsUUID()
  @IsNotEmpty()
  id: string

  @ApiProperty({ description: 'ID Distributor' })
  @IsOptional()
  distributorId?: string
}