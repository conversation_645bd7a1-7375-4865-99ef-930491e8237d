import { Column, Entity, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from './base.entity'

// Phiếu đóng gói tham chiếu PO
@Entity('delivery_note_packing_ref')
export class DeliveryNotPackingRefEntity extends BaseEntity {
  @ApiProperty({ description: "ID của PO" })
  @Column("uuid")
  @Index()
  poId: string;

  @ApiProperty({ description: "ID của phiếu đóng gói" })
  @Column("uuid")
  @Index()
  packingId: string;
}