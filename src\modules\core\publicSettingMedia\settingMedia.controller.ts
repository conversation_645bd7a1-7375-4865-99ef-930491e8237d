import { Body, Controller, Post, Req } from '@nestjs/common'
import { Request as IRequest } from 'express'
import { UserDto } from '../../../dto'
import { CurrentUser } from '../../common/decorators'
import { PublicSettingMediaService } from './settingMedia.service'

//@UseGuards(AuthGuard)
@Controller('public-media')
export class PublicSettingMediaController {
  constructor(private readonly service: PublicSettingMediaService) {}

  @Post('find-media-by-type')
  public async findMediaByType(@Body() body: { type: string }) {
    return await this.service.findMediaByType(body.type)
  }

  @Post('find')
  public async find(@Req() req: IRequest, @Body() body: any) {
    return await this.service.find(body)
  }

  @Post('find_all_business')
  public async findAllBusiness(@Req() req: IRequest) {
    return await this.service.findAllBusiness()
  }

  @Post('find_price')
  public async findPrice() {
    return await this.service.findPrice()
  }

  @Post('pagination')
  public async pagination(@Body() data: any) {
    return await this.service.pagination(data)
  }

  @Post('create_data')
  public async createData(@Body() data: any, @CurrentUser() user: UserDto) {
    return await this.service.createData(data, user)
  }

  // @UseGuards(JwtAuthGuard)
  // @Post('update_data')
  // public async updateData(@Body() data: any, @CurrentUser() user: UserDto) {
  //   return await this.service.updateData(data, user)
  // }

  // @UseGuards(JwtAuthGuard)
  // @Post('update_active')
  // public async updateActive(@Body() data: BannerUpdateIsActiveDto) {
  //   return await this.service.updateIsDelete(data.id)
  // }
}
