import { BaseEntity } from './base.entity'
import { Entity, Column, OneToMany, ManyToOne, JoinColumn, Index, BeforeInsert, BeforeUpdate } from 'typeorm'
import { UnitEntity } from './unit.entity'
import { ItemDetailEntity } from './itemDetail.entity'
import { ItemComboEntity } from './itemCombo.entity'
import { BrandEntity } from './brand.entity'
import { ItemTypeEntity } from './itemType.entity'
import { ItemPriceEntity } from './itemPrice.entity'
import { ItemGroupEntity } from './item-group.entity'
import { ItemCategoryEntity } from './itemCategory.entity'
import { transformer } from '../../constants'
import { SupplierEntity } from './supplier.entity'
import { TaxEntity } from './tax.entity'
import { ApiProperty } from '@nestjs/swagger'
import { NSItem } from '../../constants/NSItem'

/** <PERSON><PERSON>n phẩm */
@Entity('item')
export class ItemEntity extends BaseEntity {
  @Index({ fulltext: true })
  @Column({ type: 'varchar', length: 50, nullable: true })
  code: string

  @Index({ fulltext: true })
  @Column({ type: 'varchar', length: 250, nullable: true })
  name: string

  @Column({ type: 'text', nullable: true })
  description: string

  /** Giá mua */
  @Column({
    type: 'decimal',
    precision: 20,
    scale: 2,
    nullable: true,
    default: 0,
  })
  buyPrice: number

  /** Giá bán */
  @Column({
    type: 'decimal',
    precision: 20,
    scale: 2,
    nullable: true,
    default: 0,
  })
  sellPrice: number

  /** Giá bán */
  @Column({ nullable: true, default: 0 })
  vat: number

  /** Số lượng tồn ban đầu */
  @Column({ type: 'int', nullable: true, default: 0 })
  quantityBegin: number

  /** Thương hiệu */
  @Column({ type: 'varchar', nullable: true })
  brandId: string
  @ManyToOne(() => BrandEntity, (p) => p.id)
  @JoinColumn({ name: 'brandId', referencedColumnName: 'id' })
  brand: Promise<BrandEntity>

  @Column({ type: 'varchar', nullable: true })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.id)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  /** Số lượng */
  @Column({ type: 'int', nullable: true, default: 0 })
  quantity: number
  @BeforeInsert()
  @BeforeUpdate()
  private async checkQuantity() {
    if (this.isCombo === false) {
      const lstProductDetail = await this.details
      if (lstProductDetail && lstProductDetail.length > 0) {
        let totalQuantity: number = 0
        for (let pd of lstProductDetail) {
          totalQuantity += pd.quantity
        }
        // if (totalQuantity != this.quantity) {
        //   throw new Error(
        //     `Không thể cập nhật khối lượng của sản phẩm vì khối lượng của sản phẩm mẫu [ ${this.quantity} ] không bằng khối lượng sản phẩm thực [ ${totalQuantity} ] !`,
        //   )
        // }
      }
    }
  }

  /** Số lượng đã lên đơn tạm */
  @Column({ type: 'int', nullable: true, default: 0 })
  quantityLock: number

  @BeforeInsert()
  @BeforeUpdate()
  private async checkQuantityLock() {
    if (this.isCombo === false) {
      const lstProductDetail = await this.details
      if (lstProductDetail && lstProductDetail.length > 0) {
        let totalQuantity: number = 0
        for (let pd of lstProductDetail) {
          totalQuantity += pd.quantityLock
        }
        if (totalQuantity != this.quantityLock) {
          throw new Error(
            `Không thể cập nhật số lượng sản phẩm tạm khóa [ ${this.quantityLock} ] không bằng sản phẩm tạm khóa thực [ ${totalQuantity} ] !`,
          )
        }
      }
    }
  }

  /** Số lượng lock khi phân kho */
  @Column({ type: 'int', nullable: true, default: 0 })
  quantityLockEmp: number
  @BeforeInsert()
  @BeforeUpdate()
  private async checkQuantityLockEmp() {
    if (this.isCombo === false) {
      const lstProductDetail = await this.details
      if (lstProductDetail && lstProductDetail.length > 0) {
        let totalQuantity: number = 0
        for (let pd of lstProductDetail) {
          totalQuantity += pd.quantityLockEmp
        }
        if (totalQuantity != this.quantityLockEmp) {
          throw new Error(
            `Không thể cập nhật khối lượng của sản phẩm vì khối lượng của sản phẩm mẫu [ ${this.quantityLockEmp} ] không bằng khối lượng sản phẩm thực [ ${totalQuantity} ] !`,
          )
        }
      }
    }
  }

  /** Cập nhật sl tồn trước khi thêm hoặc cập nhật */
  // @BeforeUpdate()
  // @BeforeInsert()
  // updateQuantity() {
  //   this.quantity = (+this.quantityBegin || 0) + (+this.quantity || 0)
  // }

  /** Hạn sử dụng gần nhất */
  @Column({ type: 'timestamptz', nullable: true })
  expiryDate: Date

  /** Đã mở bán chưa */
  @Column({ default: false })
  isOpenSale: boolean

  /** Số công bố */
  @Column({ type: 'varchar', length: 250, nullable: true })
  osCode: string

  /** Quy cách */
  @Column({ type: 'varchar', length: 250, nullable: true })
  specifications: string

  /** Bar Code */
  @Column({ type: 'varchar', length: 250, nullable: true })
  barCode: string

  /** Tên công ty công bố */
  @Column({ type: 'varchar', length: 250, nullable: true })
  companyName: string

  /** Là comBo */
  @Column({ nullable: true, default: false })
  isCombo: boolean

  /** Đơn vị tính (Có số lượng đơn vị cơ sở) */
  @Column({ type: 'varchar', nullable: true })
  unitId: string
  @ManyToOne(() => UnitEntity, (p) => p.products)
  @JoinColumn({ name: 'unitId', referencedColumnName: 'id' })
  unit: Promise<UnitEntity>

  /** Đơn vị tính đặt hàng (Có số lượng đơn vị cơ sở) */
  @Column({ type: 'varchar', nullable: true })
  poUnitId: string
  @ManyToOne(() => UnitEntity, (p) => p.products)
  @JoinColumn({ name: 'poUnitId', referencedColumnName: 'id' })
  pOUnit: Promise<UnitEntity>

  /** Loại sp */
  @Column({ type: 'varchar', nullable: true })
  itemTypeId: string
  @ManyToOne(() => ItemTypeEntity, (p) => p.items)
  @JoinColumn({ name: 'itemTypeId', referencedColumnName: 'id' })
  itemType: Promise<ItemTypeEntity>

  /** Nhóm sp */
  @Column({ type: 'varchar', nullable: true })
  itemGroupId: string
  @ManyToOne(() => ItemGroupEntity, (p) => p.items)
  @JoinColumn({ name: 'itemGroupId', referencedColumnName: 'id' })
  itemGroup: Promise<ItemGroupEntity>

  /** Thể loại sp */
  @Column({ type: 'varchar', nullable: true })
  itemCategoryId: string
  @ManyToOne(() => ItemCategoryEntity, (p) => p.items)
  @JoinColumn({ name: 'itemCategoryId', referencedColumnName: 'id' })
  itemCategory: Promise<ItemCategoryEntity>

  // TODO: Dùng để quy đổi quy cách, Bỏ đi và dùng bảng quy cách riêng
  /** Số lượng đơn vị cơ sở (Có số lượng đơn vị cơ sở) */
  @Column({ type: 'int', nullable: true, default: 0 })
  quantityUnit: number

  /** Khối lượng */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 2, default: 0, transformer })
  kg: number

  /** Thể tích (m3) */
  @Column({ nullable: true, type: 'decimal', precision: 12, scale: 3, default: 0, transformer })
  cbm: number

  /** Dài */
  @Column({ nullable: true, type: 'decimal', precision: 12, scale: 3, default: 0, transformer })
  length: number

  /** Rộng */
  @Column({ nullable: true, type: 'decimal', precision: 12, scale: 3, default: 0, transformer })
  width: number

  /** Cao */
  @Column({ nullable: true, type: 'decimal', precision: 12, scale: 3, default: 0, transformer })
  height: number

  // TODO: Bỏ
  /** Quy cách đóng gói */
  @Column({ type: 'varchar', length: 36, nullable: true })
  packingId: string

  /** Thời gian áp dụng từ */
  @Column({ type: 'timestamptz', nullable: true })
  dateFrom: Date

  /** Thời gian áp dụng đến */
  @Column({ type: 'timestamptz', nullable: true })
  dateTo: Date

  @Column({ nullable: true, default: false })
  canPreOrder: boolean

  /** Là combo yêu thích */
  @Column({ nullable: true, default: false })
  isFavoriteCombo: boolean

  @Column({ type: 'text', nullable: true })
  orderPlatformType: string

  @OneToMany(() => ItemDetailEntity, (p) => p.item)
  details: Promise<ItemDetailEntity[]>

  @OneToMany(() => ItemComboEntity, (p) => p.item)
  itemCombo: Promise<ItemComboEntity[]>

  @OneToMany(() => ItemComboEntity, (p) => p.itemInCombo)
  itemCombination: Promise<ItemComboEntity[]>

  @OneToMany(() => ItemPriceEntity, (p) => p.item)
  prices: Promise<ItemPriceEntity[]>

  @Column({ type: 'varchar', nullable: true })
  buyTaxId: string
  @ManyToOne(() => TaxEntity, (p) => p.id)
  @JoinColumn({ name: 'buyTaxId', referencedColumnName: 'id' })
  buyTax: Promise<TaxEntity>

  @Column({ type: 'varchar', nullable: true })
  sellTaxId: string
  @ManyToOne(() => TaxEntity, (p) => p.id)
  @JoinColumn({ name: 'sellTaxId', referencedColumnName: 'id' })
  sellTax: Promise<TaxEntity>

  @ApiProperty({ description: 'Cờ sản phẩm có bán theo kỳ' })
  @Column({ nullable: true, default: false })
  isPeriodSale: boolean
}
