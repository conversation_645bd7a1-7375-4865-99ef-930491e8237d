import { IsNotEmpty, IsUUID } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { NewsCategoryCreateDto } from './newsCategoryCreate.dto'

/** Interface Cập nhật quốc gia. */
export class NewsCategoryUpdateDto extends NewsCategoryCreateDto {
  @ApiProperty({ description: 'Id quốc gia.', example: 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx' })
  @IsNotEmpty()
  @IsUUID()
  id: string
}
