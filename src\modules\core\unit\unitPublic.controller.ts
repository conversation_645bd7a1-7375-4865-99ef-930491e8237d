import { Controller, UseGuards, Post, Body, Req } from '@nestjs/common'
import { UnitService } from './unit.service'
import { UnitCreateDto, UnitImportDto, UnitUpdateDto } from './dto'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { CurrentUser } from '../../common/decorators'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { Request as IRequest } from 'express'
import { JwtAuthGuard } from '../../common/guards'
import { ApeAuthGuard } from '../../survey/common/guards'
// @ApiBearerAuth()
@ApiTags('Unit')
@UseGuards(ApeAuthGuard)
@Controller('unit_public')
export class UnitPublicController {
  constructor(private readonly service: UnitService) {}

  @Post('find')
  async find(@Body() data: any) {
    return await this.service.find(data)
  }

  @Post('load_data')
  async loadData() {
    return await this.service.loadData()
  }

  @Post('pagination')
  async pagination(@Body() data: PaginationDto) {
    return await this.service.pagination(data)
  }

  @Post('create_data')
  async createData(@CurrentUser() user: UserDto, @Body() data: UnitCreateDto, @Req() req: IRequest) {
    return await this.service.createData(user, data, req)
  }

  @Post('update_data')
  async updateData(@CurrentUser() user: UserDto, @Body() data: UnitUpdateDto, @Req() req: IRequest) {
    return await this.service.updateData(user, data, req)
  }

  @Post('update_active')
  async updateActive(@Body() data: FilterOneDto, @CurrentUser() user: UserDto, @Req() req: IRequest) {
    return await this.service.updateIsDelete(data, user, req)
  }

  @Post('find_one')
  async findOne(@Body() data: FilterOneDto) {
    return await this.service.findOne(data)
  }

  @Post('create_data_excel')
  public async createDataExcel(@Body() data: UnitImportDto[], @CurrentUser() user: UserDto, @Req() req: IRequest) {
    return await this.service.createDataExcel(data, user, req)
  }
}
