import { Injectable } from '@nestjs/common'
import { validate } from 'class-validator'
import { Like } from 'typeorm'
import { PermissionRepository } from '../../../repositories/core/permission.repository'
import { PermissionEntity } from '../../../entities/core/permission.entity'
import { CREATE_SUCCESS, ERROR_NOT_FOUND_DATA } from '../../../constants'
import { PaginationDto, UserDto } from '../../../dto'
import { PermissionCreateDto } from './dto'

@Injectable()
export class PermissionService {
  constructor(private readonly repo: PermissionRepository) { }

  async findByRoleCode(roleCode: string) {
    return this.repo.findOne({
      where: { roleCode },
    })
  }

  async createData(user: UserDto, data: any) {
    try {
      return this.repo.manager.transaction(async (transactionalManager) => {
        let result = []
        const permissionRepo = transactionalManager.getRepository(PermissionEntity);
        const checkRole = await permissionRepo.delete({
          userId: data.userId,
        })
        if (data.roleCode !== undefined || data.roleCode != null) {
          for (let item of data?.roleCode) {
            let property = new PermissionEntity()
            property.roleCode = item.roleCode
            property.userId = item.userId
            property.createdBy = user.id
            const errors = await validate(property)
            if (errors.length > 0) {
              throw new Error(CREATE_SUCCESS)
            }
            result.push(property)
          }
          await permissionRepo.insert(result)
        }
        return result
      })
    } catch (error) {
      throw error
    }
  }

  async findById(id: string) {
    const item = await this.repo.findOneBy({ id: id })
    if (!item) throw new Error(ERROR_NOT_FOUND_DATA)
    return item
  }

  async pagination(data: PaginationDto) {
    let where: any = {}
    where['isDeleted'] = false
    if (data.where.userId) {
      where.userId = data.where.userId;
    }
    return await this.repo.findAndCount({
      relations: data.relations,
      where: where,
      order: data.order,
      skip: data.skip,
      take: data.take,
    })
  }

  //#endregion
}
