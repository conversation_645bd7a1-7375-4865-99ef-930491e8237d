import { Body, Controller, Get, Post, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'

import { CurrentUser } from '../../common/decorators'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
// import { JwtAuthGuard } from '../../common/guards'
import { SettingStringCreateDto } from './dto/settingStringCreate.dto'
import { SettingStringUpdateDto } from './dto/settingStringUpdate.dto'
import { PublicSettingStringService } from './settingString.service'
import { SettingStringUpdateActiveStatusDto } from './dto/settingStringUpdateActiveStatus.dto'
import { JwtAuthGuard } from '../../common/guards'
import { ApeAuthGuard } from '../../survey/common/guards'
@ApiBearerAuth()
@ApiTags('PUBLIC_SETTING_STRING')
//@UseGuards(ApeAuthGuard)
@Controller('public_setting_string')
export class PublicSettingStringController {
  constructor(private readonly service: PublicSettingStringService) {}

  @ApiOperation({ summary: 'Hàm lấy danh sách Banner cho app Sale' })
  @Get('find_banner')
  public async findBanner() {
    return await this.service.findBanner()
  }

  @ApiOperation({ summary: 'Hàm lấy danh sách Banner cho Customer' })
  @Get('find_banner_customer')
  public async findBannerCustomer() {
    return await this.service.findBannerCustomer()
  }

  @ApiOperation({ summary: 'Hàm lấy danh sách Banner cho quảng cáo led' })
  @Get('find_banner_led')
  public async findBannerLed() {
    return await this.service.findBannerLed()
  }

  @ApiOperation({ summary: 'Hàm lấy danh sách Banner cho sme 360' })
  @Get('find_banner_sme_360')
  public async findBannerSME360() {
    return await this.service.findBannerSME360()
  }

  @ApiOperation({ summary: 'Hàm lấy danh sách Banner cho sức khỏe' })
  @Get('find_banner_health')
  public async findBannerHealth() {
    return await this.service.findBannerHealth()
  }

  @ApiOperation({ summary: 'Hàm lấy danh sách Banner cho du lịch' })
  @Get('find_banner_tourism')
  public async findBannerTourism() {
    return await this.service.findBannerTourism()
  }

  @ApiOperation({ summary: 'Hàm lấy danh sách Banner cho an sinh bình ổn' })
  @Get('find_banner_social')
  public async findBannerSocial() {
    return await this.service.findBannerSocial()
  }

  @ApiOperation({ summary: 'Hàm lấy danh sách Banner thực phẩm' })
  @Get('find_banner_food')
  public async findBannerFood() {
    return await this.service.findBannerFood()
  }

  @ApiOperation({ summary: 'Hàm lấy chính sách bảo mật của app Sale' })
  @Get('find_term_app_sale')
  public async findTermAppSale() {
    return await this.service.findTermAppSale()
  }

  @ApiOperation({ summary: 'Hàm lấy chính sách bảo mật của  Sale' })
  @Get('find_term_sale')
  public async findTermSale() {
    return await this.service.findTermSale()
  }

  @ApiOperation({ summary: 'Hàm tìm kiếm tất cả setting string' })
  // @UseGuards(JwtAuthGuard)
  @Get('find_all')
  public async findAll() {
    return await this.service.findAll()
  }

  @ApiOperation({ summary: 'Hàm load data setting string' })
  // @UseGuards(JwtAuthGuard)
  @Post('load_data')
  public async loadData() {
    return await this.service.loadData()
  }

  @ApiOperation({ summary: 'Hàm tìm kiếm tất cả setting string' })
  // @UseGuards(JwtAuthGuard)
  @Post('load_data_select')
  public async loadDataSelectBox(user: UserDto) {
    return await this.service.loadDataSelectBox(user)
  }

  @ApiOperation({ summary: 'Hàm tìm kiếm chi tiết setting string theo mã' })
  // @UseGuards(JwtAuthGuard)
  @Post('find_one_by_code')
  public async findOneByCode(@CurrentUser() user: UserDto, @Body() data: any) {
    return this.service.findOneByCode(data)
  }

  @ApiOperation({ summary: 'Hàm tìm kiếm chi tiết setting string theo danh sách mã' })
  // @UseGuards(JwtAuthGuard)
  @Post('find_list_by_code')
  public async findListByCode(@CurrentUser() user: UserDto, @Body() data: any) {
    return this.service.findListByCode(data)
  }

  @ApiOperation({ summary: 'Hàm cập nhật setting string' })
  @UseGuards(JwtAuthGuard)
  @Post('update')
  public async update(@CurrentUser() user: UserDto, @Body() data: SettingStringUpdateDto) {
    return this.service.update(user, data)
  }

  @ApiOperation({ summary: 'Hàm cập nhật trạng thái setting string' })
  // @UseGuards(JwtAuthGuard)
  @Post('update_active_status')
  public async updateActiveStatus(@CurrentUser() user: UserDto, @Body() data: SettingStringUpdateActiveStatusDto) {
    return this.service.updateActiveStatus(user, data)
  }

  @ApiOperation({ summary: 'Hàm phân trang' })
  // @UseGuards(JwtAuthGuard)
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return this.service.pagination(user, data)
  }
}
