import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import { OutboundService } from './outbound.service'
import { OutboundController } from './outbound.controller'
import {
  OutboundRepository,
  ItemDetailRepository,
  ItemPriceRepository,
  ItemRepository,
  WarehouseProductDetailRepository,
  WarehouseProductRepository,
  WarehouseRepository,
  UserRepository,
} from '../../../repositories'
import { InboundModule } from '../inbound/inbound.module'
import { OutboundPublicController } from './outboundPublic.controller'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      OutboundRepository,
      WarehouseRepository,
      ItemDetailRepository,
      ItemPriceRepository,
      WarehouseProductRepository,
      WarehouseProductDetailRepository,
      ItemRepository,
      UserRepository,
    ]),
    InboundModule,
  ],
  controllers: [OutboundController, OutboundPublicController],
  providers: [OutboundService],
  exports: [OutboundService],
})
export class OutboundModule {}
