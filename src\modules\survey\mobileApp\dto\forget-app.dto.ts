import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'

/** Interface quên mật khẩu app Survey */
export class ForgetAppDto {
  @ApiProperty({ description: 'Mã công ty' })
  @IsString()
  @IsNotEmpty()
  companyCode: string

  @ApiProperty({ description: '<PERSON>à<PERSON> khoản' })
  @IsString()
  @IsNotEmpty()
  username: string

  @ApiProperty({ description: 'Email đăng ký tài khoản' })
  @IsString()
  @IsNotEmpty()
  email: string
}
