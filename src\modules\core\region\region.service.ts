import { Injectable, ConflictException } from '@nestjs/common'
import { RegionRepository } from '../../../repositories'
import { CREATE_SUCCESS, ERROR_CODE_TAKEN, ERROR_NOT_FOUND_DATA, UPDATE_ACTIVE_SUCCESS, enumData } from '../../../constants'
import { PaginationDto, UserDto } from '../../../dto'
import { In, Like } from 'typeorm'
import { RegionCreateDto, RegionUpdateDto } from './dto'
import { CityEntity, DistrictEntity, RegionCityEntity, RegionDistrictEntity, RegionEntity, RegionWardEntity, WardEntity } from '../../../entities'

@Injectable()
export class RegionService {
  constructor(private readonly repo: RegionRepository) { }

  /** Lấy ds  */
  public async find(data: any) {
    return await this.repo.find(data)
  }

  public async findRegionCity() {
    let res = []
    await this.repo.manager.transaction(async (trans) => {
      const regionCityRepo = trans.getRepository(RegionCityEntity)
      res = await regionCityRepo.find({ select: { regionId: true, cityId: true, city: { code: true } }, relations: { city: true } })
      for (let item of res) {
        item.cityCode = item.__city__.code
        delete item.__city__
      }
    })
    return res
  }

  public async createData(user: UserDto, data: RegionCreateDto): Promise<any> {
    data.lstCityId = Array.from(new Set(data.lstCityId || []))
    data.lstDistrictId = Array.from(new Set(data.lstDistrictId || []))
    data.lstWardId = Array.from(new Set(data.lstWardId || []))
    // if (
    //   (!data.lstCityId && data.lstDistrictId && data.lstDistrictId?.length > 0) ||
    //   (data.lstCityId && data.lstCityId?.length > 1 && data.lstDistrictId && data.lstDistrictId?.length > 0)
    // )
    //   throw new Error('Danh sách quận huyện không hợp lệ')
    if (!data.lstCityId && data.lstDistrictId && data.lstDistrictId?.length > 0) throw new Error('Danh sách quận huyện không hợp lệ')

    // if (
    //   (!data.lstDistrictId && data.lstWardId && data.lstWardId?.length > 0) ||
    //   (data.lstDistrictId && data.lstDistrictId?.length > 1 && data.lstWardId && data.lstWardId?.length > 0)
    // )
    //   throw new Error('Danh sách phường xã không hợp lệ')
    if (!data.lstDistrictId && data.lstWardId && data.lstWardId?.length > 0) throw new Error('Danh sách phường xã không hợp lệ')

    const isTakenCode = await this.repo.findOne({ where: { code: data.code } })
    if (isTakenCode) throw new ConflictException(ERROR_CODE_TAKEN)

    const createdEntity = await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(RegionEntity)
      const cityRepo = trans.getRepository(CityEntity)
      const districtRepo = trans.getRepository(DistrictEntity)
      const wardRepo = trans.getRepository(WardEntity)
      const regionCityRepo = trans.getRepository(RegionCityEntity)
      const regionDistrictRepo = trans.getRepository(RegionDistrictEntity)
      const regionWardRepo = trans.getRepository(RegionWardEntity)

      const [foundCity, foundDistrict, foundWard] = await Promise.all([
        data.lstCityId && data.lstCityId.length > 0 ? cityRepo.find({ where: { id: In(data.lstCityId), isDeleted: false } }) : Promise.resolve([]),
        data.lstDistrictId && data.lstDistrictId.length > 0
          ? districtRepo.find({ where: { id: In(data.lstDistrictId), isDeleted: false } })
          : Promise.resolve([]),
        data.lstWardId && data.lstWardId.length > 0 ? wardRepo.find({ where: { id: In(data.lstWardId), isDeleted: false } }) : Promise.resolve([]),
      ])

      if (foundCity?.length !== data.lstCityId?.length) throw new Error(`${ERROR_NOT_FOUND_DATA} [Thành phố]`)
      if (foundDistrict?.length !== data.lstDistrictId?.length) throw new Error(`${ERROR_NOT_FOUND_DATA} [Quận huyện]`)
      if (foundWard?.length !== data.lstWardId?.length) throw new Error(`${ERROR_NOT_FOUND_DATA} [Phường xã]`)

      let newRegion = new RegionEntity()
      newRegion.code = data.code
      newRegion.name = data.name
      newRegion.createdAt = new Date()
      newRegion.createdBy = user.id
      newRegion = await repo.save(newRegion)

      const lstRegionCity = data.lstCityId.map((cityId: string) => {
        return regionCityRepo.create({
          regionId: newRegion.id,
          cityId: cityId,
          createdAt: new Date(),
          createdBy: user.id,
        })
      })
      const lstRegionDistrict = data.lstDistrictId.map((districtId: string) => {
        return regionDistrictRepo.create({
          regionId: newRegion.id,
          districtId: districtId,
          createdAt: new Date(),
          createdBy: user.id,
        })
      })
      const lstRegionWard = data.lstWardId.map((wardId: string) => {
        return regionWardRepo.create({
          regionId: newRegion.id,
          wardId: wardId,
          createdAt: new Date(),
          createdBy: user.id,
        })
      })

      await Promise.all([
        lstRegionCity && lstRegionCity.length > 0 ? regionCityRepo.save(lstRegionCity) : Promise.resolve([]),
        lstRegionDistrict && lstRegionDistrict.length > 0 ? regionDistrictRepo.save(lstRegionDistrict) : Promise.resolve([]),
        lstRegionWard && lstRegionWard.length > 0 ? regionWardRepo.save(lstRegionWard) : Promise.resolve([]),
      ])
      return newRegion
    })
    return { message: CREATE_SUCCESS }
  }

  public async updateData(user: UserDto, data: RegionUpdateDto) {
    data.lstCityId = Array.from(new Set(data.lstCityId || []))
    data.lstDistrictId = Array.from(new Set(data.lstDistrictId || []))
    data.lstWardId = Array.from(new Set(data.lstWardId || []))
    if (
      (!data.lstCityId && data.lstDistrictId && data.lstDistrictId?.length > 0) ||
      (data.lstCityId && data.lstCityId?.length > 1 && data.lstDistrictId && data.lstDistrictId?.length > 1)
    )
      throw new Error('Danh sách quận huyện không hợp lệ')

    if (
      (!data.lstDistrictId && data.lstWardId && data.lstWardId?.length > 0) ||
      (data.lstDistrictId && data.lstDistrictId?.length > 1 && data.lstWardId && data.lstWardId?.length > 1)
    )
      throw new Error('Danh sách phường xã không hợp lệ')

    const foundRegion = await this.repo.findOne({ where: { id: data.id, isDeleted: false } })
    if (!foundRegion) throw new Error(ERROR_NOT_FOUND_DATA)

    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(RegionEntity)
      const cityRepo = trans.getRepository(CityEntity)
      const districtRepo = trans.getRepository(DistrictEntity)
      const wardRepo = trans.getRepository(WardEntity)
      const regionCityRepo = trans.getRepository(RegionCityEntity)
      const regionDistrictRepo = trans.getRepository(RegionDistrictEntity)
      const regionWardRepo = trans.getRepository(RegionWardEntity)

      const [foundCity, foundDistrict, foundWard] = await Promise.all([
        data.lstCityId && data.lstCityId.length > 0 ? cityRepo.find({ where: { id: In(data.lstCityId), isDeleted: false } }) : Promise.resolve([]),
        data.lstDistrictId && data.lstDistrictId.length > 0
          ? districtRepo.find({ where: { id: In(data.lstDistrictId), isDeleted: false } })
          : Promise.resolve([]),
        data.lstWardId && data.lstWardId.length > 0 ? wardRepo.find({ where: { id: In(data.lstWardId), isDeleted: false } }) : Promise.resolve([]),
      ])

      if (foundCity?.length !== data.lstCityId?.length) throw new Error(`${ERROR_NOT_FOUND_DATA} [Thành phố]`)
      if (foundDistrict?.length !== data.lstDistrictId?.length) throw new Error(`${ERROR_NOT_FOUND_DATA} [Quận huyện]`)
      if (foundWard?.length !== data.lstWardId?.length) throw new Error(`${ERROR_NOT_FOUND_DATA} [Phường xã]`)

      foundRegion.name = data.name
      foundRegion.updatedAt = new Date()
      foundRegion.updatedBy = user.id
      await repo.save(foundRegion)
      await Promise.all([
        regionCityRepo.delete({ regionId: foundRegion.id }),
        regionDistrictRepo.delete({ regionId: foundRegion.id }),
        regionWardRepo.delete({ regionId: foundRegion.id }),
      ])

      const lstRegionCity = data.lstCityId.map((cityId: string) => {
        return regionCityRepo.create({
          regionId: foundRegion.id,
          cityId: cityId,
          createdAt: new Date(),
          createdBy: user.id,
        })
      })
      const lstRegionDistrict = data.lstDistrictId.map((districtId: string) => {
        return regionDistrictRepo.create({
          regionId: foundRegion.id,
          districtId: districtId,
          createdAt: new Date(),
          createdBy: user.id,
        })
      })
      const lstRegionWard = data.lstWardId.map((wardId: string) => {
        return regionWardRepo.create({
          regionId: foundRegion.id,
          wardId: wardId,
          createdAt: new Date(),
          createdBy: user.id,
        })
      })

      await Promise.all([
        lstRegionCity && lstRegionCity.length > 0 ? regionCityRepo.save(lstRegionCity) : Promise.resolve([]),
        lstRegionDistrict && lstRegionDistrict.length > 0 ? regionDistrictRepo.save(lstRegionDistrict) : Promise.resolve([]),
        lstRegionWard && lstRegionWard.length > 0 ? regionWardRepo.save(lstRegionWard) : Promise.resolve([]),
      ])
    })
    return { message: CREATE_SUCCESS }
  }

  public async pagination(data: PaginationDto) {
    let whereCon: any = {}
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    const res: any = await this.repo.findAndCount({
      where: whereCon,
      order: { createdAt: 'DESC' },
      skip: data.skip,
      take: data.take,
      relations: {
        cities: true,
        districts: true,
        wards: true,
      },
    })

    for (let item of res[0]) {
      item.lstCityId = item?.__cities__?.map((detail: any) => detail.cityId)
      item.lstDistrictId = item?.__districts__?.map((detail: any) => detail.districtId)
      item.lstWardId = item?.__wards__?.map((detail: any) => detail.wardId)

      delete item.__cities__
      delete item.__districts__
      delete item.__lstWardId__
    }
    return res
  }

  public async updateActive(id: string, user: UserDto) {
    const entity = await this.repo.findOne({ where: { id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    entity.isDeleted = !entity.isDeleted
    await this.repo.save(entity)
    return { message: UPDATE_ACTIVE_SUCCESS, updatedEntity: entity }
  }

  async findDetail(id: string) {
    const foundRegion = await this.repo.findOne({ where: { id } })
    if (!foundRegion) throw new Error(ERROR_NOT_FOUND_DATA)

    const [lstRegionCity, foundRegionDistrict, lstRegionWard] = await Promise.all([foundRegion.cities, foundRegion.districts, foundRegion.wards])
    const lstCity = await Promise.all(lstRegionCity?.map((item) => item.city))
    const lstDistrict = await Promise.all(foundRegionDistrict?.map((item) => item.district))
    const lstWard = await Promise.all(lstRegionWard?.map((item) => item.ward))

    const newDataRegion: any = { ...foundRegion }

    newDataRegion.lstCity = lstCity
    newDataRegion.lstDistrict = lstDistrict
    newDataRegion.lstWard = lstWard
    newDataRegion.lstCityName = lstCity?.map((item) => item.name)
    newDataRegion.lstDistrictName = lstDistrict?.map((item) => item.name)
    newDataRegion.lstWardName = lstWard?.map((item) => item.name)
    return newDataRegion
  }
}
