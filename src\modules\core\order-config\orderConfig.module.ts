import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { TypeOrmExModule } from "../../../typeorm";
import { OrderConfigController } from "./orderConfig.controller";
import { OrderConfigService } from "./orderConfig.service";
import { OrderConfigRepository } from "../../../repositories/core/orderConfig.repository";
import { SupplierRepository } from "../../../repositories";

@Module({
  imports: [TypeOrmExModule.forCustomRepository([
    OrderConfigRepository,
    SupplierRepository
  ])],
  controllers: [OrderConfigController],
  providers: [OrderConfigService],
  exports: [],
})
export class OrderConfigModule { }