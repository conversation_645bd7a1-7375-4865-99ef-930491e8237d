import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'

export class SendNotifyDto {
  @ApiProperty({ description: 'Id nhân viên' })
  @IsNotEmpty({ message: 'Id nhân viên không được trống' })
  userId: string

  @ApiProperty({ description: 'Id phiếu khảo sát' })
  @IsNotEmpty({ message: 'Id phiếu khảo sát không được trống' })
  surveyId: string
}

export class SendAllNotifyDto {
  @ApiProperty({ description: 'Id phiếu khảo sát' })
  @IsNotEmpty({ message: 'Id phiếu khảo sát không được trống' })
  surveyId: string
}
