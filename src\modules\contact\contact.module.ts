import { Module } from '@nestjs/common'
import { ContactController } from './contact.controller'
import { ContactService } from './contact.service'
import { TypeOrmExModule } from '../../typeorm'
import { ContactRepository } from '../../repositories/core/contact.repository'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([ContactRepository])],
  controllers: [ContactController],
  providers: [ContactService],
})
export class ContactModule {}
