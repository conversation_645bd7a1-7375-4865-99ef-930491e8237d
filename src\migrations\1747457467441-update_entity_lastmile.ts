import { MigrationInterface, QueryRunner } from "typeorm";

export class updateEntityLastmile1747457467441 implements MigrationInterface {
    name = 'updateEntityLastmile1747457467441'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "delivery_note_child_detail" ADD "expectedDate" TIMESTAMP WITH TIME ZONE`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "delivery_note_child_detail" DROP COLUMN "expectedDate"`);
    }

}
