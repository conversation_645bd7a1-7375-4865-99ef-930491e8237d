import { WarehouseTransferEntity } from '.'
import { BaseEntity } from '../core/base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'

@Entity('warehouse_transfer_history')
export class WarehouseTransferHistoryEntity extends BaseEntity {
  /** <PERSON><PERSON>u chuyển kho */
  @Column({ type: 'varchar', nullable: true })
  warehouseTransferId: string
  @ManyToOne(() => WarehouseTransferEntity, (p) => p.histories)
  @JoinColumn({ name: 'warehouseTransferId', referencedColumnName: 'id' })
  warehouseTransfer: Promise<WarehouseTransferEntity>

  /** <PERSON><PERSON> chú */
  @Column({ type: 'text', nullable: true })
  description: string
}
