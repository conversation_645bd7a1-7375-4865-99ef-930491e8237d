import { ApiPropertyOptional, ApiProperty } from '@nestjs/swagger'
import { IsDate, IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator'

export class WarehouseTransferCreateDto {
  /** Kho đi */
  @ApiProperty({ description: "ID của kho đi" })
  @IsNotEmpty()
  @IsString()
  fromWarehouseId: string

  /** Kho đến */
  @ApiProperty({ description: "ID của kho đến" })
  @IsNotEmpty()
  @IsString()
  toWarehouseId: string

  /** Ghi chú */
  @ApiProperty({ description: "Ghi chú" })
  @IsOptional()
  description: string

  @ApiProperty({ description: "ID người tạo phiếu" })
  @IsUUID()
  createBy: string

  lstTransferWarehouseDetail: WarehouseTransferDetailCreateDto[]
}

export class WarehouseTransferDetailCreateDto {
  /** Sản phẩm trong kho đi */
  @IsNotEmpty()
  @IsString()
  productId: string

  /** <PERSON>ản phẩm thực trong kho đi */
  @IsNotEmpty()
  @IsString()
  productDetailId: string

  /** Đơn vị cơ sở */
  @IsNotEmpty()
  @IsString()
  unitId: string

  /** Hạn sử dụng */
  @IsNotEmpty()
  expiryDate: Date

  /** Hạn sử dụng */
  @IsOptional()
  manufactureDate?: Date

  /** Số lô */
  @IsNotEmpty()
  @IsString()
  lotNumber: string

  /** Tồn kho vật lý */
  @IsNotEmpty()
  inventory: number

  @IsNotEmpty()
  quantity: number
}
