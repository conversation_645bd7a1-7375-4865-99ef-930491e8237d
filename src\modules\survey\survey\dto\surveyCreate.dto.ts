import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'

export class SurveyCreateDto {
  @ApiProperty({ description: 'Tiêu đề bài khảo sát' })
  @IsNotEmpty({ message: 'Tiêu đề không được trống' })
  @IsString()
  name: string

  @ApiProperty({ description: 'Mã bài khảo sát' })
  code?: string

  @ApiProperty({ description: 'Tổng điểm bài khảo sát' })
  totalPoint?: number

  @ApiProperty({ description: 'Mô tả bài khảo sát' })
  description: string

  @ApiProperty({ description: 'Thời gian bắt đầu' })
  timeStart?: Date

  @ApiProperty({ description: 'Thời gian kết thúc' })
  timeEnd?: Date

  @ApiProperty({ description: 'Danh sách người khả<PERSON> sát' })
  lstMember?: string[]

  @ApiProperty({ description: 'ID chủ đề' })
  topicId?: string
}
