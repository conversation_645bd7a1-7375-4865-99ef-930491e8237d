import { WarehouseProductSafetyEntity } from './../../entities/wms/warehouseProductSafety.entity'
import { Repository } from 'typeorm'
import {
  WarehouseEntity,
  WarehouseProductDetailEntity,
  WarehouseProductEntity,
  WarehouseTransferDetailEntity,
  WarehouseTransferEntity,
  WarehouseTransferHistoryEntity,
} from '../../entities'
import { CustomRepository } from '../../typeorm'

@CustomRepository(WarehouseEntity)
export class WarehouseRepository extends Repository<WarehouseEntity> {}

@CustomRepository(WarehouseProductEntity)
export class WarehouseProductRepository extends Repository<WarehouseProductEntity> {}

@CustomRepository(WarehouseProductDetailEntity)
export class WarehouseProductDetailRepository extends Repository<WarehouseProductDetailEntity> {}

@CustomRepository(WarehouseTransferEntity)
export class WarehouseTransferRepository extends Repository<WarehouseTransferEntity> {}

@CustomRepository(WarehouseTransferHistoryEntity)
export class WarehouseTransferHistoryRepository extends Repository<WarehouseTransferHistoryEntity> {}

@CustomRepository(WarehouseTransferDetailEntity)
export class WarehouseTransferDetailRepository extends Repository<WarehouseTransferDetailEntity> {}

@CustomRepository(WarehouseProductSafetyEntity)
export class WarehouseProductSafetyEntityRepository extends Repository<WarehouseProductSafetyEntity> {}
