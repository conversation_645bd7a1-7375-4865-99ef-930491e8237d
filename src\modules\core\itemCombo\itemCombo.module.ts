import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import { ItemComboRepository, ItemPriceRepository, ItemRepository, OutboundDetailRepository, OutboundRepository } from '../../../repositories'
import { ItemComboController } from './itemCombo.controller'
import { ItemComboService } from './itemCombo.service'
import { ActionLogModule } from '../actionLog/actionLog.module'

@Module({
  imports: [
    ActionLogModule,
    TypeOrmExModule.forCustomRepository([ItemComboRepository, ItemPriceRepository, ItemRepository, OutboundDetailRepository, OutboundRepository]),
  ],
  controllers: [ItemComboController],
  providers: [ItemComboService],
  exports: [ItemComboService],
})
export class ItemComboModule {}
