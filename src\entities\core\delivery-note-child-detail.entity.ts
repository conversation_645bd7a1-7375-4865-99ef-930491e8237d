import { Column, Entity, Index } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { BaseEntity } from './base.entity'
import { NSPo } from '../../constants';

/** Chi tiết phiếu giao nhận con theo MBC */
@Entity('delivery_note_child_detail')
export class DeliveryNoteChildDetailEntity extends BaseEntity {
  @ApiProperty({ description: 'Mã phiếu giao nhận tham chiếu' })
  @Column("uuid")
  @Index()
  deliveryNoteChildId: string;

  @ApiProperty({ description: "Code" })
  @Column("varchar", { nullable: true })
  code: string;

  //Ngày giao dự kiến
  @ApiProperty({ description: "Ngày giao dự kiến" })
  @Column("timestamptz", { nullable: true })
  expectedDate: Date;

  @ApiProperty({ description: "ID người nhận trên phiếu lm" })
  @Column("uuid", { nullable: true })
  partnerId: string;

  @ApiProperty({ description: "Tên người nhận trên phiếu lm" })
  @Column({ nullable: true })
  partnerName: string;

  @ApiProperty({ description: "Số điện thoại người nhận trên phiếu lm" })
  @Column({ nullable: true })
  partnerPhone: string;

  @ApiProperty({ description: "Địa người nhận trên phiếu lm" })
  @Column({ nullable: true })
  partnerAddress: string;

  @ApiProperty({ description: "Tổng giá trị" })
  @Column('numeric', { precision: 20, scale: 0, default: 0, nullable: true })
  totalAmount: number;

  @ApiProperty({ description: "Tổng giá bán chung" })
  @Column('numeric', { precision: 20, scale: 0, default: 0, nullable: true })
  totalSellPrice: number;

  @ApiPropertyOptional({
    description: `Trạng thái nhập kho ${Object.values(NSPo.EDeliveryNoteInboundStatus).join(' | ')}`,
    enum: NSPo.EDeliveryNoteInboundStatus,
    default: NSPo.EDeliveryNoteInboundStatus.NOTINSTOCK,
  })
  @Column({ default: NSPo.EDeliveryNoteInboundStatus.NOTINSTOCK })
  status: NSPo.EDeliveryNoteInboundStatus;

  @ApiPropertyOptional({
    description: `Trạng thái gửi hàng ${Object.values(NSPo.EDeliveryNoteSendInboundStatus).join(' | ')}`,
    enum: NSPo.EDeliveryNoteSendInboundStatus,
    default: NSPo.EDeliveryNoteSendInboundStatus.SEND_PENDING,
  })
  @Column({ default: NSPo.EDeliveryNoteSendInboundStatus.SEND_PENDING })
  sendStatus: NSPo.EDeliveryNoteSendInboundStatus;

  @ApiPropertyOptional({
    description: `Loại phiếu last mile ${Object.values(NSPo.DeliveryLastMileType).join(' | ')}`,
    enum: NSPo.DeliveryLastMileType,
    default: NSPo.DeliveryLastMileType.MBC,
  })
  @Column({ default: NSPo.DeliveryLastMileType.MBC })
  type: NSPo.DeliveryLastMileType

  @ApiProperty({ description: "Mã SO tham chiếu để cập nhật đơn hàng khi giao về khách" })
  @Column({ nullable: true })
  soId: string
}