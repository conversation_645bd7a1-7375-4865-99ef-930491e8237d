import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { PaginationDto, UserDto } from '../../../dto'
import { ActionLogService } from './actionLog.service'
import { JwtAuthGuard } from '../../common/guards'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { ActionLogCreateDto } from './dto'
import { CurrentUser } from '../../common/decorators'

@ApiBearerAuth()
@ApiTags('ActionLog')
@UseGuards(JwtAuthGuard)
@Controller('action_log')
export class ActionLogController {
  constructor(private readonly service: ActionLogService) { }

  @Post('pagination')
  public async pagination(@Body() data: PaginationDto) {
    return await this.service.pagination(data)
  }

  @Post('create_data')
  public async createData(@Body() data: ActionLogCreateDto, @CurrentUser() user: UserDto) {
    return await this.service.createData(user, data)
  }

  // L<PERSON>u lịch sử kích hoạt tay giao dịch pending
  @Post('create_pending_transaction')
  public async createPendingTransaction(@Body() data: ActionLogCreateDto, @CurrentUser() user: UserDto) {
    return await this.service.createPendingTransaction(user, data)
  }

  // Lấy danh sách lịch sử kích hoạt tay giao dịch
  @Post('pagination_pending_transaction')
  public async paginationPendingTransaction(@Body() data: PaginationDto) {
    return await this.service.paginationPendingTransaction(data)
  }

}
