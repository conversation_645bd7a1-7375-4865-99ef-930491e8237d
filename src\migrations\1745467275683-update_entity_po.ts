import { MigrationInterface, QueryRunner } from "typeorm";

export class updateEntityPo1745467275683 implements MigrationInterface {
    name = 'updateEntityPo1745467275683'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "purchase_order" ADD "distributorId" uuid`);
        await queryRunner.query(`ALTER TABLE "purchase_order_item" ADD "distributorId" uuid`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "purchase_order_item" DROP COLUMN "distributorId"`);
        await queryRunner.query(`ALTER TABLE "purchase_order" DROP COLUMN "distributorId"`);
    }

}
