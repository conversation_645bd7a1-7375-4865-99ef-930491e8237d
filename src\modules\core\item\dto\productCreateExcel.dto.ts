import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString } from 'class-validator'
import { NSItem } from '../../../../constants/NSItem'

export class ProductCreateExcelDto {
  @IsNotEmpty()
  @IsString()
  name: string

  @IsNotEmpty()
  @IsString()
  code: string

  @IsOptional()
  brandCode: string

  @IsOptional()
  supplierCode: string

  @IsOptional()
  description: string

  barCode: string

  isOpenSale: boolean

  @IsOptional()
  itemTypeCode: string

  @IsOptional()
  itemGroupCode: string

  @IsOptional()
  itemCategoryCode: string

  osCode: string

  companyName: string

  unitCode: string

  poUnitCode: string

  kg: number

  packingCode: string

  quantityUnit: number

  cbm?: number

  canPreOrder: boolean

  @ApiProperty({ description: 'dài' })
  @IsOptional()
  length: number

  @ApiProperty({ description: 'rộng' })
  @IsOptional()
  width: number

  @ApiProperty({ description: 'cao' })
  @IsOptional()
  height: number

  @ApiProperty({ description: '%VAT' })
  @IsOptional()
  vat: number

  @ApiProperty({ description: 'url media' })
  @IsOptional()
  url: number

  @IsOptional()
  buyTaxCode: string

  @IsOptional()
  sellTaxCode: string

  @IsOptional()
  isDeleted: boolean

  @ApiProperty({ description: 'Các nền tảng được phép đặt hàng' })
  @IsOptional()
  orderPlatformType?: string
}
