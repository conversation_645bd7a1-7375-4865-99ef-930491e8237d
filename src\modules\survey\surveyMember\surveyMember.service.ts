import { Injectable } from '@nestjs/common'
import { In, Like } from 'typeorm'
import { EmployeeRepository, QuestionRepository, SurveyMemberRepository } from '../../../repositories'
import { PaginationDto, UserDto } from '../../../dto'
import { enumData } from '../../../constants'

@Injectable()
export class SurveyMemberService {
  constructor(private repo: SurveyMemberRepository, private employeeRepo: EmployeeRepository) {}

  /** Phân trang nhân viên thuộc phiếu khảo sát */
  public async pagination(user: UserDto, data: PaginationDto) {
    const condition: any = { surveyId: data.where.surveyId }
    let conditionUser: any = {}
    if (data.where.departmentId) {
      conditionUser.departmentId = data.where.departmentId
    }
    if (data.where.name) {
      conditionUser.name = Like(`%${data.where.name}%`)
    }
    if (data.where.code) {
      conditionUser.code = Like(`%${data.where.code}%`)
    }
    if (data.where.statusId === enumData.StatusFilter.Active.value) {
      condition.isDeleted = false
    } else if (data.where.statusId === enumData.StatusFilter.InActive.value) {
      condition.isDeleted = true
    }
    if (data.where.surveyId) {
      condition.surveyId = data.where.surveyId
    }

    /** Tìm kiếm */
    if (conditionUser.departmentId || conditionUser.name || conditionUser.code) {
      const lstUser = await this.employeeRepo.find({
        where: conditionUser,
        select: {
          userId: true,
        },
      })
      const lstUserId = lstUser.map((user) => user.userId)
      condition.userId = In(lstUserId)
    }

    let res: any = await this.repo.findAndCount({
      where: condition,
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'ASC' },
      select: {
        id: true,
        surveyId: true,
        userId: true,
        createdAt: true,
        updatedAt: true,
        score: true,
        status: true,
      },
    })
    for (let item of res[0]) {
      if (item.status === enumData.SurveyMemberStatus.NotStart.code) {
        item.statusName = enumData.SurveyMemberStatus.NotStart.name
      } else if (item.status === enumData.SurveyMemberStatus.InProgress.code) {
        item.statusName = enumData.SurveyMemberStatus.InProgress.name
      } else if (item.status === enumData.SurveyMemberStatus.Done.code) {
        item.statusName = enumData.SurveyMemberStatus.Done.name
      }
      const itemUser = await this.employeeRepo.findOne({
        where: {
          userId: item.userId,
        },
        relations: { department: true },
      })
      if (itemUser) {
        item.employeeId = itemUser.id
        item.userName = itemUser.name
        item.userCode = itemUser.code
        item.userEmail = itemUser.email
        item.userPhone = itemUser.phone
        item.isDeletedUser = itemUser.isDeleted
        item.department = { departmentId: itemUser.departmentId, departmentName: (await itemUser.department).name }
      }
    }
    return res
  }

  public async findBySurvey(user: UserDto, data: { surveyId: string }) {
    return await this.repo.find({
      where: { isDeleted: false, surveyId: data.surveyId },
    })
  }

  findMostFrequent(arr: number[]) {
    if (arr.length === 0) {
      return null
    }
    if (arr.length < 3) {
      return arr[0] || arr[1]
    }
    const modeMap: Record<number, number> = {}
    let maxEl = arr[0]
    let maxCount: number = 1

    for (var i = 0; i < arr.length; i++) {
      const el = arr[i]
      if (modeMap[el] === null) {
        modeMap[el] = 1
      } else {
        modeMap[el]++
      }
      if (modeMap[el] >= maxCount) {
        maxEl = el
        maxCount = modeMap[el]
      }
    }
    return maxEl
  }

  public async getSurveyMembersDone(user: UserDto, data: { surveyId: string }) {
    return await this.repo.find({
      where: {
        isDeleted: false,
        surveyId: data.surveyId,
        status: enumData.SurveyMemberStatus.Done.code,
      },
    })
  }
}
