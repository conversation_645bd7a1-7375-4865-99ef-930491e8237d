import { Repository } from 'typeorm'
import { CustomRepository } from '../../typeorm'
import {
  BrandEntity,
  ItemCategoryEntity,
  ItemComboEntity,
  ItemDetailEntity,
  ItemEntity,
  ItemPriceEntity,
  ItemTypeEntity,
  PackingEntity,
  UnitEntity,
} from '../../entities'
import { ItemGroupEntity } from '../../entities/core/item-group.entity'
import { BaseRepository } from '../base.repo'

@CustomRepository(ItemEntity)
export class ItemRepository extends Repository<ItemEntity> { }

@CustomRepository(ItemDetailEntity)
export class ItemDetailRepository extends Repository<ItemDetailEntity> { }

@CustomRepository(ItemGroupEntity)
export class ItemGroupRepository extends BaseRepository<ItemGroupEntity> { }

@CustomRepository(ItemCategoryEntity)
export class ItemCategoryRepository extends Repository<ItemCategoryEntity> { }

@CustomRepository(ItemPriceEntity)
export class ItemPriceRepository extends Repository<ItemPriceEntity> { }

@CustomRepository(ItemComboEntity)
export class ItemComboRepository extends Repository<ItemComboEntity> { }

@CustomRepository(UnitEntity)
export class UnitRepository extends Repository<UnitEntity> { }

@CustomRepository(BrandEntity)
export class BrandRepository extends Repository<BrandEntity> { }

@CustomRepository(PackingEntity)
export class PackingRepository extends Repository<PackingEntity> { }

@CustomRepository(ItemTypeEntity)
export class ItemTypeRepository extends Repository<ItemTypeEntity> { }

@CustomRepository(ItemEntity)
export class ItemBaseRepository extends BaseRepository<ItemEntity> { }
