import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsOptional, IsString, IsUUID } from 'class-validator'

/** Interface Id đối tượng */
export class FilterOneDto {
  @ApiProperty({ description: 'Id của đối tượng', example: 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx' })
  @IsOptional()
  id?: string

  @ApiProperty({ description: 'Mã của đối tượng', example: 'xxxxxxx' })
  @IsOptional()
  code?: string

  @ApiProperty({ description: 'Mã của đối tượng', example: 'xxxxxxx' })
  @IsOptional()
  type?: string

  @ApiProperty({ description: 'ID người duyệt phiếu xuất kho' })
  @IsOptional()
  approveBy?: string

  @ApiProperty({ description: 'Số record bỏ qua', example: 0 })
  skip?: number

  @ApiProperty({ description: 'Số record lấy', example: 10 })
  take?: number

  @ApiPropertyOptional({ description: 'ID của kho', example: 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx' })
  @IsOptional()
  warehouseId?: string
}

export class ListProvinceCodeDto {
  @ApiProperty({ description: 'Danh saách mã của tỉnh', example: '01' })
  provinceCodes: string[]
}