import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import { TaxRepository } from '../../../repositories'
import { TaxService } from './tax.service'
import { TaxController } from './tax.controller'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([TaxRepository])],
  controllers: [TaxController],
  providers: [TaxService],
  exports: [TaxService],
})
export class TaxModule {}
