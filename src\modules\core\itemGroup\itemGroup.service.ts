import { Injectable, ConflictException, NotFoundException } from '@nestjs/common'
import { ItemCategoryRepository, ItemGroupRepository, MediaRepository } from '../../../repositories'
import {
  ERROR_NOT_FOUND_DATA,
  ERROR_CODE_TAKEN,
  UPDATE_ACTIVE_SUCCESS,
  CREATE_SUCCESS,
  UPDATE_SUCCESS,
  IMPORT_SUCCESS,
  enumData,
} from '../../../constants'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { ILike, In, Like } from 'typeorm'
import { ItemGroupCreateDto, ItemGroupUpdateDto, MediaDto } from './dto'
import { ItemGroupEntity } from '../../../entities'
import { ItemGroupCreateExcelDto } from './dto/itemGroupCreateExcel.dto'

@Injectable()
export class ItemGroupService {
  constructor(private readonly repo: ItemGroupRepository, private mediaRepo: MediaRepository, private itemCategoryRepo: ItemCategoryRepository) { }

  public async find(data: any) {
    const whereCon: any = { isDeleted: false }
    if (data.name) whereCon.name = Like(`%${data.name}%`)
    if (data.code) whereCon.code = Like(`%${data.code}%`)
    if (data.lstId?.length > 0) whereCon.id = In(data.lstId)
    if (data.lstCode?.length > 0) whereCon.code = In(data.lstCode)
    if (data.lstItemCategoryId?.length > 0) whereCon.itemCategoryId = In(data.lstItemCategoryId)
    if (data.itemCategoryId) whereCon.itemCategoryId = data.itemCategoryId
    let res: any = await this.repo.find({ where: whereCon, order: { name: 'ASC' } })
    const dictMedia: any = {}
    {
      const lstProductId = res.mapAndDistinct((e) => e.id)
      const lstMedia: any = await this.mediaRepo.find({ where: { productId: In(lstProductId), table: enumData.MediaTable.ItemGroup.code } })
      lstMedia.forEach((c) => {
        if (Array.isArray(dictMedia[c.productId])) {
          dictMedia[c.productId] = [...dictMedia[c.productId], c]
        } else {
          dictMedia[c.productId] = [c]
        }
      })
    }
    for (let item of res) {
      item.images = dictMedia[item.id] || []
    }
    return res
  }

  public async createData(user: UserDto, data: ItemGroupCreateDto) {
    const checkCodeExist = await this.repo.findOne({ where: { code: data.code } })
    if (checkCodeExist) throw new ConflictException(ERROR_CODE_TAKEN)
    if (data.itemCategoryId) {
      const checkItemCategory = await this.itemCategoryRepo.findOne({ where: { id: data.itemCategoryId, isDeleted: false } })
      if (!checkItemCategory) throw new Error(ERROR_NOT_FOUND_DATA + 'Danh mục sản phẩm')
    }
    const newEntity = this.repo.create({ ...data })
    newEntity.createdBy = user.id
    newEntity.createdAt = new Date()
    await this.repo.insert(newEntity)
    if (data.lstMediaProduct && data.lstMediaProduct.length > 0) {
      let lstImages: any = []
      for (let item of data.lstMediaProduct) {
        item.productId = newEntity.id
        item.url = item.url
        item.table = enumData.MediaTable.ItemGroup.code
        lstImages.push(item)
      }
      await this.mediaRepo.insert(lstImages)
    }
    return { message: CREATE_SUCCESS }
  }

  public async updateData(user: UserDto, data: ItemGroupUpdateDto) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    if (entity.code != data.code) {
      const checkCodeExist = await this.repo.findOne({ where: { code: data.code } })
      if (checkCodeExist) throw new ConflictException(ERROR_CODE_TAKEN)
    }
    entity.itemCategoryId = data.itemCategoryId
    entity.updatedAt = new Date()
    entity.updatedBy = user.id

    if (data.lstMediaProduct && data.lstMediaProduct.length > 0) {
      await this.mediaRepo.delete({ productId: entity.id })
      let lstImages: any = []
      for (let item of data.lstMediaProduct) {
        item.productId = entity.id
        item.url = item.url
        item.table = enumData.MediaTable.ItemGroup.code
        lstImages.push(item)
      }
      await this.mediaRepo.insert(lstImages)
      // await authApiHelper.createMedia(req, lstImages)
    }
    delete data.lstMediaProduct
    await this.repo.update(entity.id, {
      ...entity,
      ...data,
    })
    return { message: UPDATE_SUCCESS }
  }

  public async pagination(data: PaginationDto) {
    let whereCon: any = {}
    if (data.where.name) whereCon.name = ILike(`%${data.where.name}%`)
    if (data.where.code) whereCon.code = ILike(`%${data.where.code}%`)
    if (data.where.itemCategoryId) whereCon.itemCategoryId = data.where.itemCategoryId
    if (data.where.groupType) whereCon.groupType = data.where.groupType
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    const result: any = await this.repo.findAndCount({
      where: whereCon,
      order: { createdAt: 'DESC', code: 'DESC' },
      skip: data.skip,
      take: data.take,
    })

    const dictMedia: any = {}
    {
      const lstProductId = result[0].mapAndDistinct((e) => e.id)
      const lstMedia: any = await this.mediaRepo.find({ where: { productId: In(lstProductId), table: enumData.MediaTable.ItemGroup.code } })
      lstMedia.forEach((c) => {
        if (Array.isArray(dictMedia[c.productId])) {
          dictMedia[c.productId] = [...dictMedia[c.productId], c]
        } else {
          dictMedia[c.productId] = [c]
        }
      })
    }
    for (let item of result[0]) {
      item.images = dictMedia[item.id] || []
    }
    return result
  }

  public async updateIsDelete(id: string, user: UserDto) {
    const entity = await this.repo.findOne({ where: { id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    await this.repo.update({ id: entity.id }, { isDeleted: !entity.isDeleted })
    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  async findOne(data: FilterOneDto) {
    const whereCon: any = { id: data.id, isDeleted: false }
    return await this.repo.findOneBy(whereCon)
  }

  public async createDataExcel(data: ItemGroupCreateExcelDto[], user: UserDto) {
    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(ItemGroupEntity);

      const dicCode: Record<string, ItemGroupEntity> = {};
      {
        const lstItemGroup = await repo.find({
          where: { isDeleted: false },
          select: { id: true, code: true },
        });
        lstItemGroup.forEach((c) => (dicCode[c.code] = c));
      }

      const dictItemCategory: Record<string, string> = {};
      {
        const lstItemCategory = await this.itemCategoryRepo.find({
          where: { isDeleted: false },
          select: { id: true, code: true },
        });
        lstItemCategory.forEach((x) => (dictItemCategory[x.code] = x.id));
      }

      const dicCodeFile: Record<string, number> = {};
      const today = new Date();

      for (const [idx, item] of data.entries()) {
        if (dicCodeFile[item.code])
          throw new Error(`[ Dòng ${idx + 1} - Mã Loại item [${item.code}] trùng với dòng ${dicCodeFile[item.code]} ]`);

        if (!dictItemCategory[item.itemCategoryCode])
          throw new Error(`[ Dòng ${idx + 1} - Mã danh mục item [${item.itemCategoryCode}] không tìm thấy ]`);

        dicCodeFile[item.code] = idx + 1;

        const saveData = {
          code: item.code,
          name: item.name,
          itemCategoryId: dictItemCategory[item.itemCategoryCode],
          groupType: item.groupType,
          description: item.description,
          updatedAt: today,
          updatedBy: user.id,
        };

        let itemGroupEntity: ItemGroupEntity;

        if (dicCode[item.code]) {
          await repo.update(dicCode[item.code].id, saveData);
          itemGroupEntity = await repo.findOneByOrFail({ id: dicCode[item.code].id });
        } else {
          const newItem = this.repo.create({
            ...saveData,
            createdAt: today,
            createdBy: user.id,
          });
          itemGroupEntity = await this.repo.save(newItem);
        }
        if (item.lstMediaProduct) {
          const dataImage: MediaDto = {
            productId: itemGroupEntity.id,
            url: String(item.lstMediaProduct),
            content: "",
            name: '',
            table: enumData.MediaTable.ItemGroup.code,
          };
          await this.mediaRepo.insert(dataImage);
        }
      }
    });

    return { message: IMPORT_SUCCESS };
  }

}
