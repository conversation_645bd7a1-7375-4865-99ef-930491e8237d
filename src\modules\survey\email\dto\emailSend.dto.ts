import { ApiProperty } from '@nestjs/swagger'
import { IsString } from 'class-validator'

export class EmailSendDto {
  @ApiProperty({ description: 'Email nhận' })
  toAddresses?: string

  @ApiProperty({ description: 'Ti<PERSON>u đề' })
  subject?: string

  ccAddresses?: string

  bccAddresses?: string

  @ApiProperty({ description: 'Nội dung' })
  body_text?: string

  @ApiProperty({ description: 'Nội dung html' })
  body_html?: string

  @ApiProperty({ description: 'Loại email' })
  type?: string

  @ApiProperty({ description: 'Gửi lại' })
  isResend?: boolean

  @ApiProperty({ description: 'Id lịch sử gửi email' })
  historyId?: string

  @ApiProperty({ description: 'Id công ty' })
  companyId?: string

  @ApiProperty({ description: 'Id user' })
  userId?: string
}
