import { CanActivate, ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';

@Injectable()
export class VerifyTokenGuard implements CanActivate {
  constructor(private readonly configService: ConfigService) { }

  canActivate(context: ExecutionContext): boolean {
    const request: Request = context.switchToHttp().getRequest();

    const tokenAuth = this.configService.get<string>('KEY_INTEGRATION');
    if (!tokenAuth) {
      throw new UnauthorizedException('Authorization failed');
    }

    const headers = request.headers;
    const token = headers['authorization'] || headers['Authorization'];
    if (!token) {
      throw new UnauthorizedException('Authorization failed');
    }

    if (token !== tokenAuth) {
      throw new UnauthorizedException('Invalid token');
    }

    return true;
  }
}