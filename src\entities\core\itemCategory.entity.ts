import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { ItemEntity } from './item.entity'
import { ItemTypeEntity } from './itemType.entity'
import { ItemGroupEntity } from './item-group.entity'

/** Thể loại sản phẩm */
@Entity('item_category')
export class ItemCategoryEntity extends BaseEntity {
  /** Mã */
  @Column({ type: 'varchar', length: 50, nullable: false })
  code: string

  /** Tên */
  @Column({ type: 'varchar', length: 250, nullable: false })
  name: string

  @Column({ type: 'varchar', nullable: true })
  itemTypeId: string
  @ManyToOne(() => ItemTypeEntity, (p) => p.itemCategories)
  @JoinColumn({ name: 'itemTypeId', referencedColumnName: 'id' })
  itemType: Promise<ItemEntity>

  /** <PERSON><PERSON> chú */
  @Column({ type: 'text', nullable: true })
  description: string

  /** Danh sách các sản phẩm thuộc loại này */
  @OneToMany(() => ItemGroupEntity, (p) => p.itemCategory)
  itemGroups: Promise<ItemGroupEntity[]>

  /** Danh sách các sản phẩm thuộc thể loại này */
  @OneToMany(() => ItemEntity, (p) => p.itemCategory)
  items: Promise<ItemEntity[]>
}
