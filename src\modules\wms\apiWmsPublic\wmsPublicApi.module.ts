import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import {
  CheckInventoryRepository,
  InboundRepository,
  OutboundRepository,
  ItemDetailRepository,
  ItemRepository,
  WarehouseProductDetailRepository,
  WarehouseProductRepository,
  WarehouseRepository,
  ItemCategoryRepository,
  ItemPriceRepository,
  PackingRepository,
  ItemTypeRepository,
  MediaRepository,
  ItemComboRepository,
  ItemGroupRepository,
} from '../../../repositories'
import { InboundModule } from '../inbound/inbound.module'
import { OutboundModule } from '../outbound/outbound.module'
import { wmsPublicApiController } from './wmsPublicApi.controller'
import { wmsPublicApiService } from './wmsPulicApi.service'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      CheckInventoryRepository,
      WarehouseRepository,
      ItemDetailRepository,
      ItemRepository,
      ItemPriceRepository,
      PackingRepository,
      WarehouseProductRepository,
      WarehouseProductDetailRepository,
      InboundRepository,
      ItemCategoryRepository,
      ItemTypeRepository,
      ItemComboRepository,
      MediaRepository,
      OutboundRepository,
      ItemGroupRepository,
    ]),
    InboundModule,
    OutboundModule,
  ],
  controllers: [wmsPublicApiController],
  providers: [wmsPublicApiService],
  exports: [wmsPublicApiService],
})
export class wmsPublicApiModule {}
