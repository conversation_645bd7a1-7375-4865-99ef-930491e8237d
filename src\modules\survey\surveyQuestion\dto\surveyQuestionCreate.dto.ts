import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString, IsDate } from 'class-validator'

export class SurveyCreateDto {
  @ApiProperty({ description: 'Phiếu khảo sát' })
  @IsNotEmpty({ message: 'Phiếu khảo sát không được trống' })
  @IsString()
  surveyId: string

  @ApiProperty({ description: 'Chủ đề' })
  @IsNotEmpty({ message: 'Chủ đề không được trống' })
  @IsString()
  topicId: string
}
