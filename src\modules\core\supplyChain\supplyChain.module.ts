import { Module } from "@nestjs/common";
import { SupplyChainService } from "./supplyChain.service";
import { TypeOrmExModule } from "../../../typeorm";
import { SupplyChainConfigRepository, SupplyChainConfigDetailRepository, SupplyChainConfigApproveRepository, SupplyChainConfigTimeRepository } from "../../../repositories";
import { SupplyChainController } from "./supplyChain.controller";


@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      SupplyChainConfigRepository,
      SupplyChainConfigDetailRepository,
      SupplyChainConfigApproveRepository,
      SupplyChainConfigTimeRepository
    ])
  ],
  controllers: [SupplyChainController],
  providers: [SupplyChainService],
  exports: []
})
export class SupplyChainModule { }