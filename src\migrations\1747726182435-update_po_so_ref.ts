import { MigrationInterface, QueryRunner } from "typeorm";

export class updatePoSoRef1747726182435 implements MigrationInterface {
    name = 'updatePoSoRef1747726182435'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_c0118074257e9d3aba08c314f7"`);
        await queryRunner.query(`ALTER TABLE "purchase_order_so" ADD "warehouseId" uuid`);
        await queryRunner.query(`CREATE INDEX "IDX_79f1832eeb8b0377af891262cb" ON "purchase_order_so" ("poId", "soId", "warehouseId") `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_79f1832eeb8b0377af891262cb"`);
        await queryRunner.query(`ALTER TABLE "purchase_order_so" DROP COLUMN "warehouseId"`);
        await queryRunner.query(`CREATE INDEX "IDX_c0118074257e9d3aba08c314f7" ON "purchase_order_so" ("poId", "soId") `);
    }

}
