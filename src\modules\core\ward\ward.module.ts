import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import { CityRepository, DistrictRepository, WardRepository } from '../../../repositories'
import { WardService } from './ward.service'
import { WardController } from './ward.controller'
import { WardPublicController } from './wardPublic.controller'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([WardRepository, DistrictRepository, CityRepository])],
  controllers: [WardController, WardPublicController],
  providers: [WardService],
  exports: [WardService],
})
export class WardModule {}
