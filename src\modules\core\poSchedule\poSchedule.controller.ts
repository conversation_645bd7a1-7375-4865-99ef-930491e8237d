import { Controller, UseGuards, Post, Body, Req } from '@nestjs/common';
import { CurrentUser } from '../../common/decorators';
import { FilterIdDto, PaginationDto, UserDto } from '../../../dto';
import { Request as IRequest } from 'express';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../common/guards';
import { PoScheduleService } from './poSchedule.service';
import { ApprovePoScheduleDto, CreatePoScheduleDto, UpdatePoScheduleDto } from './dto';




@ApiBearerAuth()
@ApiTags('Po Schedule')
@UseGuards(JwtAuthGuard)
@Controller('po-schedule')
export class PoScheduleController {
  constructor(private readonly service: PoScheduleService) { }

  @ApiOperation({ summary: 'Danh sách lịch hàng về có phân trang' })
  @Post('pagination')
  async pagination(@Req() req: IRequest, @Body() data: PaginationDto) {
    return await this.service.pagination(req, data);
  }

  @ApiOperation({ summary: 'Tạo lịch hàng về' })
  @Post('create_data')
  async createData(@Req() req: IRequest, @Body() data: CreatePoScheduleDto, @CurrentUser() user: UserDto) {
    return await this.service.createData(data, req, user);
  }

  @ApiOperation({ summary: 'Cập nhật lịch hàng về' })
  @Post('update_data')
  async updateData(@Req() req: IRequest, @Body() data: UpdatePoScheduleDto, @CurrentUser() user: UserDto) {
    return await this.service.updateData(data, req, user);
  }

  @ApiOperation({ summary: 'Duyệt lịch hàng về' })
  @Post('approve_data')
  async approveData(@Req() req: IRequest, @Body() data: ApprovePoScheduleDto, @CurrentUser() user: UserDto) {
    return await this.service.approveData(data, req, user);
  }

  @ApiOperation({ summary: 'Hủy lịch hàng về' })
  @Post('cancel_data')
  async cancelData(@Req() req: IRequest, @Body() data: ApprovePoScheduleDto, @CurrentUser() user: UserDto) {
    return await this.service.cancelData(data, req, user);
  }

  @ApiOperation({ summary: 'Chi tiết lịch hàng về' })
  @Post('detail')
  async findDetail(@Req() req: IRequest, @Body() data: { id: string }) {
    return await this.service.findDetail(req, data.id);
  }

  @ApiOperation({ summary: 'Tìm kiếm lịch hàng về' })
  @Post('find')
  async find(@Body() data: FilterIdDto, @Req() req: IRequest) {
    // return await this.service.find(data, req);
  }

  @ApiOperation({ summary: 'Lịch sử thay đổi lịch hàng về' })
  @Post('pagination_history')
  async paginationHistory(@Req() req: IRequest, @Body() data: PaginationDto) {
    return await this.service.paginationHistory(req, data);
  }

  @ApiOperation({ summary: 'Xuất file Excel danh sách lịch hàng về' })
  @Post('export_excel')
  async exportExcel(@Req() req: IRequest, @Body() data: PaginationDto, @CurrentUser() user: UserDto) {
    return await this.service.exportExcel(req, data, user);
  }

  @ApiOperation({ summary: 'In lịch hàng về' })
  @Post('print')
  public async print(@Body() data: string[], @Req() req: IRequest) {
    return await this.service.print(data, req);
  }
}
