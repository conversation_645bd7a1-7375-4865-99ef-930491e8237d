import { Module } from '@nestjs/common'
import { QuestionController } from './question.controller'
import { QuestionService } from './question.service'
import { TypeOrmExModule } from '../../../typeorm'
import { QuestionListDetailRepository, QuestionRepository, SurveyQuestionRepository, TopicRepository } from '../../../repositories/survey'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([QuestionRepository, TopicRepository, QuestionListDetailRepository, SurveyQuestionRepository])],
  controllers: [QuestionController],
  providers: [QuestionService],
  exports: [QuestionService],
})
export class QuestionModule {}
