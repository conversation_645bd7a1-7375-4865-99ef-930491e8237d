import { Column, Entity, Index } from 'typeorm'
import { BaseEntity } from './base.entity'

/** <PERSON>uản lý các công ty là khách hàng của APE */
@Entity('partner')
export class PartnerEntity extends BaseEntity {
  /** Mã partner */
  @Index({ unique: true })
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  code: string

  /** Tên partner */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  /** <PERSON><PERSON> số thuế */
  @Index({ unique: true })
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  taxCode: string

  /** Số điện thoại */
  @Index({ unique: false })
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  phone: string

  /** Email */
  @Index({ unique: false })
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  email: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  address: string

  /** <PERSON><PERSON> điện thoại liên hệ */
  @Index({ unique: false })
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  contactPhone: string
}
