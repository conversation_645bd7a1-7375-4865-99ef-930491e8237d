import { InboundEntity, OutboundEntity, WarehouseEntity, WarehouseTransferDetailEntity, WarehouseTransferHistoryEntity } from '.'
import { BaseEntity } from '../core/base.entity'
import { Entity, Column, ManyToOne, JoinColumn, OneToMany } from 'typeorm'

/** Phiếu chuyển kho */
@Entity('warehouse_transfer')
export class WarehouseTransferEntity extends BaseEntity {
  /** Mã */
  @Column({ type: 'varchar', nullable: false })
  code: string

  /** Kho đi */
  @Column({ type: 'varchar', nullable: true })
  fromWarehouseId: string
  @ManyToOne(() => WarehouseEntity, (p) => p.fromWarehouseTransfers)
  @JoinColumn({ name: 'fromWarehouseId', referencedColumnName: 'id' })
  fromWarehouse: Promise<WarehouseEntity>

  /** <PERSON>ho đến */
  @Column({ type: 'varchar', nullable: true })
  toWarehouseId: string
  @ManyToOne(() => WarehouseEntity, (p) => p.toWarehouseTransfers)
  @JoinColumn({ name: 'toWarehouseId', referencedColumnName: 'id' })
  toWarehouse: Promise<WarehouseEntity>

  /** Trạng thái phiếu xuất - enumData.WarehouseTransferStatus */
  @Column({ type: 'varchar', length: 36, nullable: true, default: 'NEW' })
  status: string

  /**
   * Ngày nhận hàng
   * Lấy thời gian user thao tác Xác nhận nhận đơn hàng
   *  */
  @Column({ type: 'varchar', length: 36, nullable: true })
  receivedBy: string

  /**
   * Ngày nhận hàng
   * Lấy thời gian user thao tác Xác nhận nhận đơn hàng
   *  */
  @Column({ type: 'timestamptz', nullable: true })
  receivedAt: Date

  /** Ngày duyệt phiếu xuất kho */
  @Column({ type: 'timestamptz', nullable: true })
  approvedDate: Date

  /**
   * Người duyệt
   * Lấy id người nhấn nút duyệt
   *  */
  @Column({ type: 'varchar', length: 36, nullable: true })
  approvedBy: string

  /** Ghi chú */
  @Column({ type: 'text', nullable: true })
  description: string

  /** Danh sách sản phẩm trong phiếu chuyển kho */
  @OneToMany(() => WarehouseTransferDetailEntity, (p) => p.warehouseTransfer)
  warehouseTransferDetails: Promise<WarehouseTransferDetailEntity[]>

  /** Lịch sử chuyển kho */
  @OneToMany(() => WarehouseTransferHistoryEntity, (p) => p.warehouseTransfer)
  histories: Promise<WarehouseTransferHistoryEntity[]>

  /** Danh sách phiếu xuất kho */
  @OneToMany(() => OutboundEntity, (p) => p.warehouseTransfer)
  outbounds: Promise<OutboundEntity[]>

  /** Danh sách phiếu nhập kho */
  @OneToMany(() => InboundEntity, (p) => p.warehouseTransfer)
  inbounds: Promise<InboundEntity[]>
}
