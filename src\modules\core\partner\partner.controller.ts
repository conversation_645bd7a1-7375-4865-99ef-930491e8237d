import { Controller, UseGuards, Post, Body, Req, Get } from '@nestjs/common'
import { JwtAuthGuard } from '../../common/guards'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { CurrentUser } from '../../common/decorators'
import { PartnerService } from './partner.service'
import { PartnerCreateDto, PartnerUpdateDto } from './dto'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { Request as IRequest } from 'express'
import { omsApiHelper } from '../../../helpers/omsApiHelper'

@ApiBearerAuth()
@ApiTags('Partner')
@UseGuards(JwtAuthGuard)
@Controller('partner')
export class PartnerController {
  constructor(private readonly service: PartnerService) {}

  @Post('load_data')
  async loadData() {
    return await this.service.loadData()
  }

  @Get('list-partner-lv1')
  async listPartnerLv1(@Req() req: IRequest) {
    return await omsApiHelper.lstPartnerLv1(req)
  }
  // @Post('pagination')
  // public async pagination(@Body() data: PaginationDto) {
  //   return await this.service.pagination(data)
  // }

  // @Post('create_data')
  // public async createData(@CurrentUser() user: UserDto, @Body() data: PartnerCreateDto, @Req() req: IRequest) {
  //   return await this.service.createData(user, data, req)
  // }

  // @Post('update_data')
  // public async updateData(@CurrentUser() user: UserDto, @Body() data: PartnerUpdateDto) {
  //   return await this.service.updateData(user, data)
  // }

  // @Post('update_active')
  // public async updateActive(@Body() data: any, @CurrentUser() user: UserDto) {
  //   return await this.service.updateIsDelete(data.id, user)
  // }

  // @Post('find_one')
  // public async findOne(@Body() data: FilterOneDto) {
  //   return await this.service.findOne(data)
  // }

  // @Post('create_data_excel')
  // public async createDataExcel(@Body() data: PartnerCreateDto[], @CurrentUser() user: UserDto) {
  //   return await this.service.createDataExcel(data, user)
  // }
}
