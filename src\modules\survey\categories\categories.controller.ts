import { Body, Controller, Post, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { CurrentUser } from '../../common/decorators'
import { CategoriesService } from './categories.service'
import { CategoriesCreateDto, CategoriesUpdateDto } from './dto'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { JwtAuthGuard } from '../../common/guards'
import { ApeAuthGuard } from '../common/guards'

@ApiBearerAuth()
@ApiTags('Categories')
@Controller('categories')
export class CategoriesController {
  constructor(private readonly service: CategoriesService) {}

  @ApiOperation({ summary: 'Hàm tìm kiếm' })
  @Post('find')
  public async find(@Body() data: { isDeleted?: boolean; type?: string }) {
    return await this.service.find(data)
  }

  @ApiOperation({ summary: 'Hàm phân trang' })
  @Post('pagination')
  public async pagination(@Body() data: PaginationDto) {
    return await this.service.pagination(data)
  }

  @ApiOperation({ summary: 'Hàm tạo mới' })
  @UseGuards(JwtAuthGuard)
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: CategoriesCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Hàm cập nhật' })
  @UseGuards(JwtAuthGuard)
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: CategoriesUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Hàm cập nhật trạng thái hoạt động' })
  @UseGuards(JwtAuthGuard)
  @Post('update_active')
  public async updateActive(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateActive(user, data.id)
  }

  @ApiOperation({ summary: 'Hàm tạo mới bằng excel' })
  @UseGuards(JwtAuthGuard)
  @Post('create_data_by_excel')
  public async createDataByExcel(@CurrentUser() user: UserDto, @Body() data: CategoriesCreateDto[]) {
    return await this.service.createDataByExcel(user, data)
  }

  @ApiOperation({ summary: 'Lấy 1 danh mục' })
  @Post('find_one')
  public async findOne(@CurrentUser() user: UserDto, @Body() data: { id?: string; code?: string }) {
    return await this.service.findOne(user, data)
  }
  @ApiOperation({ summary: 'chi tiết' })
  @Post('find_detail')
  public async findDetail(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.findDetail(user, data)
  }
}
