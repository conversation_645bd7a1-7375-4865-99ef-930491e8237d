import { MigrationInterface, QueryRunner } from 'typeorm'

export class update1754564304169 implements MigrationInterface {
  name = 'update1754564304169'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "item_price_discount" RENAME COLUMN "qualifiedQuantity" TO "targetQuantity"`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "item_price_discount" RENAME COLUMN "targetQuantity" TO "qualifiedQuantity"`)
  }
}
