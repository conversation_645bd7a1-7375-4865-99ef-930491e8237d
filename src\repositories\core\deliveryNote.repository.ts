import { DeliveryNoteEntity, DeliveryNoteTrackingEntity } from '../../entities'
import { DeliveryNotPackingRefEntity } from '../../entities/core/delivery-note-packing-ref.entity'
import { DeliveryNotPackingEntity } from '../../entities/core/delivery-note-packing.entity'
import { CustomRepository } from '../../typeorm'
import { BaseRepository } from '../base.repo'

@CustomRepository(DeliveryNoteEntity)
export class DeliveryNoteRepository extends BaseRepository<DeliveryNoteEntity> { }

@CustomRepository(DeliveryNoteTrackingEntity)
export class DeliveryNoteTrackingRepository extends BaseRepository<DeliveryNoteTrackingEntity> { }

@CustomRepository(DeliveryNotPackingEntity)
export class DeliveryNotePackingRepository extends BaseRepository<DeliveryNotPackingEntity> { }

@CustomRepository(DeliveryNotPackingRefEntity)
export class DeliveryNotePackingRefRepository extends BaseRepository<DeliveryNotPackingRefEntity> { }


