import { Controller, UseGuards, Request, Post, Body, Param, Get } from '@nestjs/common'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { Request as IRequest } from 'express'
import { FilterOneDto, UserDto } from '../../../dto'
import { JwtAuthGuard, CurrentUser } from '../../common'
import { WardCreateDto, WardCreateExcelDto, WardUpdateDto } from './dto'
import { WardService } from './ward.service'
import { ApeAuthGuard } from '../../survey/common/guards'

@ApiBearerAuth()
@ApiTags('Geo Ward')
@UseGuards(ApeAuthGuard)
@Controller('ward_public')
export class WardPublicController {
  constructor(private readonly service: WardService) {}

  @Post('find')
  public async find(@Body() data: any) {
    return await this.service.find(data)
  }

  @Post('load_data')
  public async loadData(@Body() data: { id?: string }) {
    return await this.service.loadData(data)
  }

  @Get('load_ward_by_districtId/:districtId')
  public async loadWardByDistrictId(@Param('districtId') districtId: string) {
    return await this.service.loadWardByDistrictId(districtId)
  }

  @Post('pagination')
  public async pagination(@Body() data: any) {
    return await this.service.pagination(data)
  }
}
