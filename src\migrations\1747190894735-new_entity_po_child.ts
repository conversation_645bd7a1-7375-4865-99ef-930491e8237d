import { MigrationInterface, QueryRunner } from "typeorm";

export class newEntityPoChild1747190894735 implements MigrationInterface {
    name = 'newEntityPoChild1747190894735'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "purchase_order_child" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "createdBy" character varying(36), "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT now(), "updatedBy" character varying(36), "isDeleted" boolean NOT NULL DEFAULT false, "purchaseOrderId" character varying NOT NULL, "purchaseOrderCode" character varying NOT NULL, "purchaseOrderType" character varying NOT NULL DEFAULT 'WITHCOMBO', "supplierId" uuid NOT NULL, "distributorId" uuid, "vat" numeric NOT NULL DEFAULT '0', "totalAmount" numeric(20,0) NOT NULL, "totalAmountVat" numeric(20,0) NOT NULL, "files" jsonb, "deposit" numeric(20,0), "deliveryTerms" character varying, "supplyChainId" uuid, CONSTRAINT "UQ_73ab6fa1fc03dc8dbcd5a4fa9d8" UNIQUE ("purchaseOrderCode"), CONSTRAINT "PK_8447c588d42c4a6d19db884c42a" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_f2ee99ba35e910c6554e63b373" ON "purchase_order_child" ("purchaseOrderId") `);
        await queryRunner.query(`CREATE INDEX "IDX_73ab6fa1fc03dc8dbcd5a4fa9d" ON "purchase_order_child" ("purchaseOrderCode") `);
        await queryRunner.query(`CREATE INDEX "IDX_038fb03cb0b40d4397e830c367" ON "purchase_order_child" ("supplierId") `);
        await queryRunner.query(`CREATE INDEX "IDX_062dfb0072a230720fe0a80feb" ON "purchase_order_child" ("distributorId") `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_062dfb0072a230720fe0a80feb"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_038fb03cb0b40d4397e830c367"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_73ab6fa1fc03dc8dbcd5a4fa9d"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_f2ee99ba35e910c6554e63b373"`);
        await queryRunner.query(`DROP TABLE "purchase_order_child"`);
    }

}
