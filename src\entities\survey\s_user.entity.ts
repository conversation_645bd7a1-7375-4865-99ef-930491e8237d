import { Column, <PERSON>tity, BeforeInsert, BeforeUpdate, OneToOne, Join<PERSON><PERSON>um<PERSON> } from 'typeorm'
import { compare, hash } from 'bcrypt'
import { PWD_SALT_ROUNDS } from '../../constants'
import { BaseEntity } from '../core'

@Entity({ name: 's_user' })
export class UserSurveyEntity extends BaseEntity {
  @Column({ type: 'varchar', length: 50, nullable: false, unique: true })
  username: string

  @Column({ name: 'password', type: 'text', nullable: false })
  password: string

  @Column({ type: 'varchar', length: 50, nullable: false })
  type: string

  @Column({ type: 'varchar', length: 36, nullable: true })
  companyId: string

  @Column({ type: 'varchar', nullable: true })
  employeeId: string

  @Column({ type: 'varchar', length: 36, nullable: true })
  partnerId: string

  @Column({ type: 'text', nullable: true })
  fcmToken: string

  @Column({ type: 'varchar', length: 250, nullable: true })
  email: string

  @Column({ type: 'varchar', length: 250, nullable: true })
  phone: string

  /** Chứng minh nhân dân */
  @Column({ type: 'varchar', length: 50, nullable: true })
  cardId: string

  /** ngày sinh */
  @Column({ type: 'varchar', length: 100, nullable: true })
  birthdate: string

  /** Họ và Tên */
  @Column({ type: 'varchar', length: 250, nullable: true })
  fullName: string

  @Column({ type: 'varchar', length: 250, nullable: true })
  address: string

  /** Nhân thông báo hay không ?*/
  @Column({ nullable: true, default: true })
  isAllowMessage: boolean

  @BeforeInsert()
  @BeforeUpdate()
  async hashPassword() {
    if (this.password) {
      const hashedPassword = await hash(this.password, PWD_SALT_ROUNDS)
      this.password = hashedPassword
    }
  }

  comparePassword(candidate: string) {
    return compare(candidate, this.password)
  }
}
