import { BaseEntity } from '../core/base.entity'
import { Entity, Column, OneToMany, JoinColumn, ManyToOne } from 'typeorm'
import {
  CheckInventoryEntity,
  InboundDetailEntity,
  InboundDetailPriceEntity,
  InboundHistoryEntity,
  WarehouseEntity,
  WarehouseTransferEntity,
} from '.'
import { InboundCostAllocationEntity } from './inboundCostAllocation.entity'
import { InboundDetailCostPriceEntity } from './inboundDetailCostPrice.entity'

/** Phiếu nhập kho (PNK) */
@Entity('inbound')
export class InboundEntity extends BaseEntity {
  /** Id PO */
  @Column({ type: 'varchar', length: 36, nullable: true })
  poId: string

  /** Mã PO */
  @Column({ type: 'varchar', length: 36, nullable: true })
  poCode: string

  /** Mã phiếu giao nhận con chi tiết */
  @Column({ type: 'varchar', nullable: true })
  deliveryNoteLmCode: string

  /** Id Kho */
  @Column({ type: 'varchar', nullable: true })
  warehouseId: string
  @ManyToOne(() => WarehouseEntity, (p) => p.inbounds)
  @JoinColumn({ name: 'warehouseId', referencedColumnName: 'id' })
  warehouse: Promise<WarehouseEntity>

  /** Id phiếu chuyển kho */
  @Column({ type: 'varchar', nullable: true })
  warehouseTransferId: string
  @ManyToOne(() => WarehouseTransferEntity, (p) => p.inbounds)
  @JoinColumn({ name: 'warehouseTransferId', referencedColumnName: 'id' })
  warehouseTransfer: Promise<WarehouseTransferEntity>

  /** Id phiếu kiểm kho (Dành cho module kiểm kho) */
  @Column({ type: 'varchar', nullable: true })
  checkInventoryId: string
  @ManyToOne(() => CheckInventoryEntity, (p) => p.inbounds)
  @JoinColumn({ name: 'checkInventoryId', referencedColumnName: 'id' })
  checkInventory: Promise<CheckInventoryEntity>

  /** Mã PNK tự sinh */
  @Column({ type: 'varchar', length: 250, nullable: true })
  code: string

  /** Loại phiếu nhập kho (enumData.InboundType) */
  @Column({ type: 'varchar', length: 36, nullable: true, default: 'PO' })
  type: string

  /** Tỉ giá nhập kho */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 2, default: 0 })
  exchangeRate: number

  /** Đơn vị tiền tệ enum Currency */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  currencyCode: string

  /** Cập nhật chứng từ nhập kho */
  @Column("jsonb", { nullable: true })
  files: string[];

  /** Tổng trị giá nhập (theo đvtt PO) = tổng thành tiền nhập (theo đvtt PO) các Item trong PNK */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 2, default: 0 })
  totalPrice: number

  /** Tổng trị giá nhập (theo VNĐ) = tổng thành tiền nhập (theo VNĐ) các Item trong PNK  */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 2, default: 0 })
  totalPriceVND: number

  /** Chênh lệch tổng tiền = Tổng tiền - tổng tiền lưu */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 0, default: 0 })
  totalPriceOld: number

  /** Chênh lệch tổng tiền = Tổng tiền - tổng tiền lưu */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 0, default: 0 })
  totalPriceVNDOld: number

  /** Chênh lệch tổng tiền = Tổng tiền - tổng tiền lưu */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 0, default: 0 })
  totalPriceDiff: number

  /** Chênh lệch tổng tiền = Tổng tiền - tổng tiền lưu */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 0, default: 0 })
  totalPriceVNDDiff: number

  /** Ngày duyệt phiếu nhập kho */
  @Column({ type: 'timestamptz', nullable: true })
  approvedDate: Date

  /**
   * Người duyệt
   * Lấy id người nhấn nút duyệt
   *  */
  @Column({ type: 'varchar', length: 36, nullable: true })
  approvedBy: string

  /** Ghi chú PNK */
  @Column({ type: 'text', nullable: true })
  description: string

  /** Trạng thái (enumData.InboundStatus) */
  @Column({ type: 'varchar', length: 50, nullable: true })
  status: string

  /** Chi phí phân bổ */
  @Column({ nullable: true, default: 0, type: 'float' })
  costAllocation: number

  /** Trạng thái (enumData.TransportType) */
  @Column({ type: 'varchar', length: 50, nullable: true })
  transportType: string

  /** Các Item trong PNK */
  @OneToMany(() => InboundDetailEntity, (p) => p.inbound)
  details: Promise<InboundDetailEntity[]>

  /** Lịch sử PNK */
  @OneToMany(() => InboundHistoryEntity, (p) => p.inbound)
  histories: Promise<InboundHistoryEntity[]>

  /** Danh sách chi phí phân bổ */
  @OneToMany(() => InboundCostAllocationEntity, (p) => p.inbound)
  costAllocations: Promise<InboundCostAllocationEntity[]>

  /** Danh sách lịch sử thay đổi giá vốn */
  @OneToMany(() => InboundDetailCostPriceEntity, (p) => p.inbound)
  costPrices: Promise<InboundDetailCostPriceEntity[]>

  /** Danh sách chi phí phân bổ */
  @OneToMany(() => InboundDetailPriceEntity, (p) => p.inbound)
  prices: Promise<InboundDetailPriceEntity[]>
}
