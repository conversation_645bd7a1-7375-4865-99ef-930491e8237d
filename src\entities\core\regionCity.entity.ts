import { <PERSON><PERSON><PERSON>, Column, ManyTo<PERSON>ne, <PERSON><PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm'
import { BaseEntity } from './base.entity'
import { RegionEntity } from './region.entity'
import { CityEntity } from './city.entity'

@Entity('region_city')
export class RegionCityEntity extends BaseEntity {
  /** Vùng */
  @Column({ type: 'varchar', nullable: false })
  regionId: string
  @ManyToOne(() => RegionEntity, (p) => p.cities)
  @JoinColumn({ name: 'regionId', referencedColumnName: 'id' })
  region: Promise<RegionEntity>

  /** Thành phố */
  @Column({ type: 'varchar', nullable: false })
  cityId: string
  @ManyToOne(() => CityEntity, (p) => p.id)
  @JoinColumn({ name: 'cityId', referencedColumnName: 'id' })
  city: Promise<CityEntity>
}
