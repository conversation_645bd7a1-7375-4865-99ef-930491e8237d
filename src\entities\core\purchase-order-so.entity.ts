import { Column, Entity, Index } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { BaseEntity } from './base.entity'
import { NSPo } from '../../constants';

/** <PERSON><PERSON>ng tham chiếu PO với SO */
@Entity('purchase_order_so')
@Index(['poId', 'soId', 'warehouseId'])
export class PurchaseOrderSalesOrderEntity extends BaseEntity {
    @ApiProperty({ description: 'Id purchase order' })
    @Column("uuid")
    @Index()
    poId: string;

    @ApiProperty({ description: 'ID sales order' })
    @Column("uuid")
    soId: string

    @ApiProperty({ description: 'Kho trung tâm nhận hàng' })
    @Column("uuid", { nullable: true })
    warehouseId: string

    // Loại đơn hàng so
    @Column("varchar", { nullable: true })
    orderAddressType: string;

    @ApiPropertyOptional({
        description: `Trạng thái ${Object.values(NSPo.EPoStatus).join(' | ')}`,
        enum: NSPo.EPoStatus,
        default: NSPo.EPoStatus.NEWLYCREATED,
    })
    @Column({ default: NSPo.EPoStatus.NEWLYCREATED, nullable: true })
    status: NSPo.EPoStatus;
}

