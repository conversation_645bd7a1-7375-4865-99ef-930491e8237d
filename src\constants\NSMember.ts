export namespace NSMember {
  export enum EAddressType {
    HOME = 'HOME', // Nhà riêng
    COMPANY = 'COMPANY', // Công ty
  }
  /**
   * Loại hình thành viên.
   */
  export enum EMembershipType {
    // FARMER = 'FARMER', // Nông hộ
    PERSONAL = 'PERSONAL', // Cá nhân
    ENTERPRISE = 'ENTERPRISE', // Doanh nghiệp
  }
  export enum EMemberType {
    MEMBER = 'MEMBER',
    COLLABORATOR = 'COLLABORATOR', // Cộng tác viên,
    POST_OFFICE = 'POST_OFFICE', // Bưu cục
    STORE_TELLER = 'STORE_TELLER', // Giao dịch viên tại cửa hàng
  }

  export enum EStatus {
    INACTIVE = 'INACTIVE',
    ACTIVE = 'ACTIVE',
    WAITING_FOR_VERIFY = 'WAITING_FOR_VERIFY',
    DELETED = 'DELETED',
    WAITING_FOR_APPROVE = 'WAITING_FOR_APPROVE',
  }

  export enum EBusinessType {
    LED_ADVERTISEMENT = 'LED_ADVERTISEMENT', // 'Quảng cáo LED',
    SOCIAL_SECURITY_CARD = 'SOCIAL_SECURITY_CARD', // 'Thẻ An Sinh Bình Ổn',
    TOURISM = 'TOURISM', // 'Du lịch',
    HEALTH = 'HEALTH', // 'Sức khoẻ',
    SME360 = 'SME360', // 'SME 360',
    FOOD = 'FOOD', // 'Thực phẩm nhân đạo',
    RETAIL = 'RETAIL', // Bán lẻ
    EDUCATION = 'EDUCATION', // Giáo dục
  }

  export enum EReferenceCodeBala {
    BALACOM_BENEFIT = 'BALACOM_BENEFIT'
  }
}
