import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, OneToMany } from 'typeorm'
import { BaseEntity } from '../core/base.entity'
import { SurveyEntity } from './s_survey.entity'
import { QuestionEntity } from './s_question.entity'

/** Chi tiết câu hỏi loại danh sách */
@Entity('s_survey_question_list_detail')
export class SurveyQuestionListDetailEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  /**  JSON 
   * {
   *    name: string
   *    value: number
   *    sort: number
      }
  */
  @Column({
    nullable: true,
  })
  questionListDetail: string

  @Column({
    nullable: true,
  })
  type: string

  /** Cấp độ */
  @Column({
    nullable: false,
    default: 1,
  })
  level: number

  /** C<PERSON> bắt buộc nhập hay không */
  @Column({
    nullable: false,
    default: false,
  })
  isRequired: boolean

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  code: string

  @Column({
    nullable: true,
  })
  topicId: string

  @Column({
    nullable: true,
    default: 0,
  })
  sort: number

  /** Id của công thức cha */
  @Column({
    nullable: true,
  })
  parentId: string

  /** Cha */
  @ManyToOne(() => SurveyQuestionListDetailEntity, (p) => p.childs)
  @JoinColumn({ name: 'parentId', referencedColumnName: 'id' })
  parent: SurveyQuestionListDetailEntity

  /** Con - 1 công thức sẽ có thể có nhiều con */
  @OneToMany(() => SurveyQuestionListDetailEntity, (p) => p.parent)
  childs: Promise<SurveyQuestionListDetailEntity[]>

  /** Phiếu khảo sát */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  surveyId: string
  @ManyToOne(() => SurveyEntity, (p) => p.questionInfos)
  @JoinColumn({ name: 'surveyId', referencedColumnName: 'id' })
  survey: Promise<SurveyEntity>
}
