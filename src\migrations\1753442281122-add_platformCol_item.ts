import { MigrationInterface, QueryRunner } from 'typeorm'

export class addPlatformColItem1753442281122 implements MigrationInterface {
  name = 'addPlatformColItem1753442281122'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "item" DROP COLUMN "orderPlatformType"`)
    await queryRunner.query(`ALTER TABLE "item" ADD "orderPlatformType" text`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "item" DROP COLUMN "orderPlatformType"`)
    await queryRunner.query(`ALTER TABLE "item" ADD "orderPlatformType" text array`)
  }
}
