import { Body, Controller, Post, Req, UseGuards, Get, Query } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { Request as IRequest } from 'express'
import { SurveyQuestionService } from './surveyQuestion.service'
import { SurveyCreateDto } from './dto/surveyQuestionCreate.dto'
import { PaginationDto, UserDto } from '../../../dto'
import { QuestionEntity, SurveyQuestionEntity } from '../../../entities/survey'
import { CurrentUser } from '../../common/decorators'
import { JwtAuthGuard } from '../../common'

// @UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@ApiTags('SurveyQuestion')
@Controller('survey_question')
export class SurveyQuestionController {
  constructor(private service: SurveyQuestionService) {}

  @ApiOperation({ summary: 'Danh sách phân trang' })
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto, @Req() req: IRequest) {
    return await this.service.pagination(user, data, req)
  }

  @ApiOperation({ summary: 'Danh sách phân trang phiếu trả lời thuộc khảo sát' })
  @Post('answer_overview')
  public async answerOverviewPagination(@Body() data: PaginationDto) {
    return await this.service.answerOverviewPagination(data)
  }

  @ApiOperation({ summary: 'Tìm toàn bộ survey question' })
  @Post('find_all')
  public async findALl(@Body() data: any) {
    return await this.service.findAll(data)
  }

  @ApiOperation({ summary: 'Thống kê số lượt làm khảo sát từ ngày đến ngày' })
  @Post('get_survey_stats')
  public async ggetSurveyStats(@Body() data: { from: any; to: any }) {
    return await this.service.getSurveyStatsByMonth(data.from, data.to)
  }

  @ApiOperation({ summary: 'Tìm câu hỏi survey question trong survey' })
  @Post('find_question')
  public async findQuestion(@Body() data: any) {
    return await this.service.findQuestion(data)
  }

  @ApiOperation({ summary: 'Tìm chủ đề trong phiếu khảo sát' })
  @Post('find_topic')
  public async findTopic(@CurrentUser() user: UserDto, @Body('surveyId') surveyId: string) {
    return await this.service.findTopicBySurvey(user, surveyId)
  }
  @ApiOperation({ summary: 'Số lượng phiếu trả lời theo chủ đề' })
  @Get('find_survey_answer_with_topic')
  public async findSurveyAnswerWithTopic(@CurrentUser() user: UserDto, @Query('year') year?: string, @Query('month') month?: string) {
    const filter = {
      year: year ? parseInt(year, 10) : undefined,
      month: month ? parseInt(month, 10) : undefined,
    }
    return await this.service.findSurveyAnswerWithTopic(user, filter)
  }

  @ApiOperation({ summary: 'Tìm bộ câu hỏi của nhân viên trong phiếu khảo sát' })
  @Post('find_survey_question')
  public async findSurveyQuestion(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.findSurveyQuestionUser(user, data)
  }

  @ApiOperation({ summary: 'Tạo câu hỏi bằng chủ đề' })
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: SurveyCreateDto) {
    return await this.service.createQuestionByTopic(user, data)
  }

  @ApiOperation({ summary: 'Xóa chủ đề ra khỏi phiếu khảo sát' })
  @Post('update_active')
  public async updateActive(@CurrentUser() user: UserDto, @Body() data: SurveyCreateDto) {
    return await this.service.deleteQuestionByTopic(user, data)
  }

  @ApiOperation({ summary: 'Tạo ds câu hỏi khảo sát mới cho phiếu khảo sát' })
  @Post('create_list_survey_question')
  public async createListSurveyQuestion(
    @CurrentUser() user: UserDto,
    @Body() data: { lstQuestion: QuestionEntity[]; surveyId: string; surveyMemberId: string },
  ) {
    return await this.service.createListSurveyQuestion(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật ds câu hỏi khảo sát mới cho phiếu khảo sát' })
  @Post('edit_list_survey_question')
  public async editListSurveyQuestion(
    @CurrentUser() user: UserDto,
    @Body() data: { lstQuestionRemove: SurveyQuestionEntity[]; lstQuestionAdd: QuestionEntity[]; surveyId: string; surveyMemberId: string },
  ) {
    return await this.service.editListSurveyQuestion(user, data)
  }
}
