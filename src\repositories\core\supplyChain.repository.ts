import { Repository } from 'typeorm'
import { CustomRepository } from '../../typeorm'

import {
  SupplyChainConfigEntity,
  SupplyChainConfigDetailEntity,
  SupplyChainConfigApproveEntity,
  SupplyChainConfigTimeEntity
} from '../../entities/core/'
import { BaseRepository } from '../base.repo'

@CustomRepository(SupplyChainConfigEntity)
export class SupplyChainConfigRepository extends BaseRepository<SupplyChainConfigEntity> { }

@CustomRepository(SupplyChainConfigDetailEntity)
export class SupplyChainConfigDetailRepository extends BaseRepository<SupplyChainConfigDetailEntity> { }

@CustomRepository(SupplyChainConfigApproveEntity)
export class SupplyChainConfigApproveRepository extends BaseRepository<SupplyChainConfigApproveEntity> { }

@CustomRepository(SupplyChainConfigTimeEntity)
export class SupplyChainConfigTimeRepository extends BaseRepository<SupplyChainConfigTimeEntity> { }