// src/modules/operational-areas/operational-area.entity.ts
import {
  Entity,
  Column,
  Index,
} from 'typeorm';
import { BaseEntity } from './base.entity';
import { NSOperational } from '../../constants';
import { ApiProperty } from '@nestjs/swagger';

@Entity('operational_areas')
export class OperationalAreaEntity extends BaseEntity {
  @ApiProperty({ description: 'ID của khu vực' })
  @Column()
  areaId: string;

  @ApiProperty({ description: 'Mã của khu vực' })
  @Column()
  areaCode: string;

  @ApiProperty({ description: 'Tên của khu vực' })
  @Column()
  areaName: string

  @ApiProperty({ description: 'Loại khu vực: province, district hoặc ward' })
  @Column({
    comment: 'Loại khu vực: province, district hoặc ward',
  })
  type: string;

  @ApiProperty({ description: 'Loại đối tượng áp dụng: warehouse hoặc supplier' })
  @Index()
  @Column({
    comment: 'Loại đối tượng áp dụng: warehouse hoặc supplier',
  })
  mappingRefType: string;


  @ApiProperty({ description: 'ID của đối tượng áp dụng (kho hoặc nhà cung cấp)' })
  @Index()
  @Column({
    comment: 'ID của đối tượng áp dụng (kho hoặc nhà cung cấp)',
  })
  mappingRefId: string;

}