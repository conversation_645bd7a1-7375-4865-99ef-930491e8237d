import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import { AppService } from '../../app.service'
import { SettingStringController } from './settingString.controller'
import { SettingStringService } from './settingString.service'
import { SettingStringRepository, UserRepository } from '../../../repositories/core'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([SettingStringRepository, UserRepository])],
  controllers: [SettingStringController],
  providers: [SettingStringService, AppService],
  exports: [SettingStringService],
})
export class SettingStringModule {}
