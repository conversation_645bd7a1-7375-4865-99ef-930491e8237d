import { MigrationInterface, QueryRunner } from "typeorm";

export class init190320251742369465078 implements MigrationInterface {
    name = 'init190320251742369465078'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "tax" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "createdBy" character varying(36), "updatedAt" TIMESTAMP DEFAULT now(), "updatedBy" character varying(36), "isDeleted" boolean NOT NULL DEFAULT false, "code" character varying(250) NOT NULL, "type" character varying, "name" character varying, "percent" character varying, CONSTRAINT "PK_2c1e62c595571139e2fb0e9c319" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_b8301e6f0f2aad1cb459ddcf87" ON "tax" ("code") `);
        await queryRunner.query(`CREATE TABLE "purchase_order_approve" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "createdBy" character varying(36), "updatedAt" TIMESTAMP DEFAULT now(), "updatedBy" character varying(36), "isDeleted" boolean NOT NULL DEFAULT false, "purchaseOrderId" uuid NOT NULL, "supplierId" uuid NOT NULL, "isApproved" boolean NOT NULL DEFAULT false, CONSTRAINT "PK_4ea8e5f2ab7a834e20ca694fa69" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_77827135799beecb02330990eb" ON "purchase_order_approve" ("purchaseOrderId") `);
        await queryRunner.query(`CREATE TABLE "supply_chain_config_approve" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "createdBy" character varying(36), "updatedAt" TIMESTAMP DEFAULT now(), "updatedBy" character varying(36), "isDeleted" boolean NOT NULL DEFAULT false, "supplyChainId" uuid NOT NULL, "supplyChainDetailId" uuid, "regionId" uuid, "approverId" uuid NOT NULL, "approverCode" character varying NOT NULL, "approverName" character varying NOT NULL, "approvalLevel" integer NOT NULL, CONSTRAINT "PK_6bf075ab87f30fc01bc6c5949d5" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "supply_chain_config_time" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "createdBy" character varying(36), "updatedAt" TIMESTAMP DEFAULT now(), "updatedBy" character varying(36), "isDeleted" boolean NOT NULL DEFAULT false, "supplyChainDetailId" uuid, "approvalTime" character varying, "supplierDeliveryTime" character varying, "thirdPartyDeliveryTime" character varying, CONSTRAINT "PK_04057868f81624ba28efbb9d84d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "po_schedule_history" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "createdBy" character varying(36), "updatedAt" TIMESTAMP DEFAULT now(), "updatedBy" character varying(36), "isDeleted" boolean NOT NULL DEFAULT false, "poScheduleId" uuid, "actionType" character varying(50) NOT NULL, "description" text, "performedBy" character varying(255) NOT NULL, "performedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_d551806507d034462899c9ace66" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "po_schedule" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "createdBy" character varying(36), "updatedAt" TIMESTAMP DEFAULT now(), "updatedBy" character varying(36), "isDeleted" boolean NOT NULL DEFAULT false, "code" character varying(255) NOT NULL, "supplier" character varying(255) NOT NULL, "supplierId" character varying(36), "productName" text NOT NULL, "productId" character varying(36), "estimatedQuantity" integer NOT NULL, "distributedQuantity" integer NOT NULL, "distributionTime" character varying(255), "status" character varying(255) NOT NULL, CONSTRAINT "PK_caef84ea530e7fa67045cdf02b3" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "delivery_schedule" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "createdBy" character varying(36), "updatedAt" TIMESTAMP DEFAULT now(), "updatedBy" character varying(36), "isDeleted" boolean NOT NULL DEFAULT false, "code" character varying(50) NOT NULL, "deliveryDate" date NOT NULL, "mbcId" character varying(50) NOT NULL, "thirdPartyLogisticsId" character varying(50) NOT NULL, "productId" character varying(50) NOT NULL, "unit" character varying(50) NOT NULL, "orderDemand" integer NOT NULL, "supplyTimeType" character varying(100) NOT NULL, "supplyTime" date NOT NULL, CONSTRAINT "UQ_90d0f3940b1a049a013072d5811" UNIQUE ("code"), CONSTRAINT "PK_97a65f0c2ee99c7e28984c45352" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "order_config" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "createdBy" character varying(36), "updatedAt" TIMESTAMP DEFAULT now(), "updatedBy" character varying(36), "isDeleted" boolean NOT NULL DEFAULT false, "supplierId" uuid, "approvalTime" character varying, "supplierDeliveryTime" character varying, "thirdPartyDeliveryTime" character varying, "minReceivingTime" character varying DEFAULT '7', "maxReceivingTime" character varying DEFAULT '90', CONSTRAINT "PK_fe4ea9dde3873c641199a0a62f4" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_27a9d8236dc01163efb8d5e418" ON "order_config" ("supplierId") `);
        await queryRunner.query(`CREATE TABLE "contact" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "createdBy" character varying(36), "updatedAt" TIMESTAMP DEFAULT now(), "updatedBy" character varying(36), "isDeleted" boolean NOT NULL DEFAULT false, "fullName" character varying(50), "interest" character varying(250), "email" character varying(50), "topic" character varying(250), "message" character varying(10000), CONSTRAINT "PK_2cbbe00f59ab6b3bb5b8d19f989" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "employee" ADD "address" character varying(250)`);
        await queryRunner.query(`ALTER TABLE "employee" ADD "cityId" character varying`);
        await queryRunner.query(`ALTER TABLE "employee" ADD "wardId" character varying`);
        await queryRunner.query(`ALTER TABLE "employee" ADD "districtId" character varying`);
        await queryRunner.query(`ALTER TABLE "user" ADD "supplierId" uuid`);
        await queryRunner.query(`ALTER TABLE "user" ADD CONSTRAINT "UQ_031cdc2c9c5eb56d48b5bdb4e54" UNIQUE ("supplierId")`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "userId" uuid`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD CONSTRAINT "UQ_e8902c50550ff82dd0143913c0a" UNIQUE ("userId")`);
        await queryRunner.query(`ALTER TABLE "item_price" ADD "priceOriginal" numeric(20,2) DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "item" ADD "canPreOrder" boolean DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "item" ADD "buyTaxId" uuid`);
        await queryRunner.query(`ALTER TABLE "item" ADD "sellTaxId" uuid`);
        await queryRunner.query(`ALTER TABLE "purchase_order" ADD "supplyChainId" uuid`);
        await queryRunner.query(`ALTER TABLE "purchase_order" ADD "approverCurrentId" uuid`);
        await queryRunner.query(`ALTER TABLE "purchase_order" ADD "approvalLevelCurrent" numeric`);
        await queryRunner.query(`ALTER TABLE "delivery_note_tracking" ADD "files" jsonb`);
        await queryRunner.query(`CREATE TYPE "public"."warehouse_type_enum" AS ENUM('3PL', 'MBC', 'SUPPLIER')`);
        await queryRunner.query(`ALTER TABLE "warehouse" ADD "type" "public"."warehouse_type_enum" DEFAULT 'MBC'`);
        await queryRunner.query(`ALTER TABLE "inbound" ADD "files" jsonb`);
        await queryRunner.query(`ALTER TABLE "action_log" ADD CONSTRAINT "PK_63cffa5d8af90621882f0388359" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "contracts" ADD CONSTRAINT "PK_2c7b8f3a7b1acdd49497d83d0fb" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "supplier_history" ADD CONSTRAINT "PK_e99a304491bc41175120b95e8e3" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "department" ADD CONSTRAINT "PK_9a2213262c1593bffb581e382f5" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "employee" ADD CONSTRAINT "PK_3c2bc72f03fd5abbbc5ac169498" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "employee" ADD CONSTRAINT "UQ_348a4a9894eef0760bfe0a26328" UNIQUE ("code")`);
        await queryRunner.query(`ALTER TABLE "employee" ADD CONSTRAINT "UQ_f4b0d329c4a3cf79ffe9d565047" UNIQUE ("userId")`);
        await queryRunner.query(`ALTER TABLE "permission" ADD CONSTRAINT "PK_3b8b97af9d9d8807e41e6f48362" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "user" ADD CONSTRAINT "PK_cace4a159ff9f2512dd42373760" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "user" ADD CONSTRAINT "UQ_78a916df40e02a9deb1c4b75edb" UNIQUE ("username")`);
        await queryRunner.query(`ALTER TABLE "user" ADD CONSTRAINT "UQ_ab4a80281f1e8d524714e00f38f" UNIQUE ("employeeId")`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD CONSTRAINT "PK_2bc0d2cab6276144d2ff98a2828" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "ward" ADD CONSTRAINT "PK_e6725fa4a50e449c4352d2230e1" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "district" ADD CONSTRAINT "PK_ee5cb6fd5223164bb87ea693f1e" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "city" ADD CONSTRAINT "PK_b222f51ce26f7e5ca86944a6739" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "unit" ADD CONSTRAINT "PK_4252c4be609041e559f0c80f58a" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "item_detail" ADD CONSTRAINT "PK_864d3cb5dd8bc67512e2a533d9b" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "item_combo" ADD CONSTRAINT "PK_a1837070559e47576aafb827e81" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "brand" ADD CONSTRAINT "PK_a5d20765ddd942eb5de4eee2d7f" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "item_group" ADD CONSTRAINT "PK_6b0100c5cb7c67d99ae46197727" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "item_category" ADD CONSTRAINT "PK_91ba90f150e8804bdaad7b17ff8" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "item_type" ADD CONSTRAINT "PK_64cde7db02a99c28d4b67efb367" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "item_price" ADD CONSTRAINT "PK_bf831a4a3a9eca20d4ef2322d7d" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "item" ADD CONSTRAINT "PK_d3c0c71f23e7adcf952a1d13423" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "item" DROP COLUMN "buyPrice"`);
        await queryRunner.query(`ALTER TABLE "item" ADD "buyPrice" numeric(20,2) DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "item" DROP COLUMN "sellPrice"`);
        await queryRunner.query(`ALTER TABLE "item" ADD "sellPrice" numeric(20,2) DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "packing" ADD CONSTRAINT "PK_f2700177c619b75fb91ee9b9bcd" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "media" ADD CONSTRAINT "PK_f4e0fcac36e050de337b670d8bd" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "region_city" ADD CONSTRAINT "PK_2be03f2f8bfe3bf5d8240c940bf" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "region_district" ADD CONSTRAINT "PK_cb2462c0e11db0b57153bf8a028" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "region_ward" ADD CONSTRAINT "PK_6212798eefff8e19604cb0d89fc" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "region" ADD CONSTRAINT "PK_5f48ffc3af96bc486f5f3f3a6da" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "setting_string" ADD CONSTRAINT "PK_cd993a95b69adc4fcb1c4e779c3" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "setting_media" ADD CONSTRAINT "PK_c630b4a975de7f9967483bc4e7c" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "partner" ADD CONSTRAINT "PK_8f34ff11ddd5459eacbfacd48ca" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "partner_map" ADD CONSTRAINT "PK_219fd2f1848e94dcbc62ed6072f" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "news_category" ADD CONSTRAINT "PK_aac53a9364896452e463139e4a0" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "news" ADD CONSTRAINT "PK_39a43dfcb6007180f04aff2357e" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "purchase_order" ADD CONSTRAINT "PK_ad3e1c7b862f4043b103a6c8c60" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "purchase_order" ADD CONSTRAINT "UQ_0a0352b213c03e6aca1b7e33881" UNIQUE ("purchaseOrderCode")`);
        await queryRunner.query(`ALTER TABLE "purchase_order_item" ADD CONSTRAINT "PK_f3eaf81afb216ae78a59cc19503" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "purchase_order_history" ADD CONSTRAINT "PK_734bfddbb92e6dadfeef12377db" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "purchase_order_so" ADD CONSTRAINT "PK_c09084a8c8a241650bc53e63a15" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "supply_chain_config" ADD CONSTRAINT "PK_f64c27636fe55fa13e8af5a2ef0" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "supply_chain_config" ADD CONSTRAINT "UQ_57ddb35b1c1326b9a847d51272a" UNIQUE ("supplyChainCode")`);
        await queryRunner.query(`ALTER TABLE "supply_chain_config" ALTER COLUMN "partnerId" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "supply_chain_config_detail" ADD CONSTRAINT "PK_b0ff4a30cc410958da917619b4c" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "bank" ADD CONSTRAINT "PK_7651eaf705126155142947926e8" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "bank" ADD CONSTRAINT "UQ_efdd3f589f04cd21d79136de1aa" UNIQUE ("code")`);
        await queryRunner.query(`ALTER TABLE "bank" ADD CONSTRAINT "UQ_46b9a736b36e121ddaac9e4e1fe" UNIQUE ("bankCode")`);
        await queryRunner.query(`ALTER TABLE "bank" ADD CONSTRAINT "UQ_11f196da2e68cef1c7e84b4fe94" UNIQUE ("name")`);
        await queryRunner.query(`ALTER TABLE "bank_branch" ADD CONSTRAINT "PK_758e63cf6e1278222f4c6538561" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "bank_branch" ADD CONSTRAINT "UQ_161b6bcd432d8838b1468701223" UNIQUE ("code")`);
        await queryRunner.query(`ALTER TABLE "delivery_note" ADD CONSTRAINT "PK_b2e8966e12465b34021b9a2c6eb" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "delivery_note" ADD CONSTRAINT "UQ_1146366945a250fbf7279e39498" UNIQUE ("code")`);
        await queryRunner.query(`ALTER TABLE "delivery_note_child" ADD CONSTRAINT "PK_f03212060575e6f3005895c8050" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "delivery_note_child" ADD CONSTRAINT "UQ_f0e1f6adf6955495740e7c6c9ec" UNIQUE ("code")`);
        await queryRunner.query(`ALTER TABLE "delivery_note_child_detail" ADD CONSTRAINT "PK_0d902ad7014106e593ac24ac192" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "delivery_note_tracking" ADD CONSTRAINT "PK_0722547b4e7f8897a61a627a5c0" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "delivery_note_lastmile_product" ADD CONSTRAINT "PK_47ea90713bfcc1bbef520c3a7b7" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "s_company" ADD CONSTRAINT "PK_6e76eaf206210ce77bc3e6b6908" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "s_company_categories" ADD CONSTRAINT "PK_bebf8a975194b857b6d4890eee9" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "s_email_history" ADD CONSTRAINT "PK_9a1705346c0e5a9200255c35b77" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "s_survey_member" ADD CONSTRAINT "PK_3acaae0cc4742a8f08ba1b4dad2" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "s_survey_question" ADD CONSTRAINT "PK_38e088585762534a4fe530583ef" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "s_question_list_detail" ADD CONSTRAINT "PK_73a987f3844ffbf669b3c6a7799" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "s_question" ADD CONSTRAINT "PK_0b44a192f88b22643d70bc12011" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "s_categories" ADD CONSTRAINT "PK_d8c3e66d9df7fff07882c48efc2" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "s_topic" ADD CONSTRAINT "PK_469453fff959c16d846a80d7eac" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "s_survey_history" ADD CONSTRAINT "PK_b024fce6c6101eb2d8dd953871d" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "s_survey_question_list_detail" ADD CONSTRAINT "PK_81c5564f07fcda90b2a3954a97e" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "s_survey" ADD CONSTRAINT "PK_52c1b3d137f6fe70da993f680bb" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "s_notify" ADD CONSTRAINT "PK_b6c710ecb0b325e0e5a07220b89" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "s_user" ADD CONSTRAINT "PK_0b8252ae567a2ff566537044a6b" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "s_user" ADD CONSTRAINT "UQ_8850058e9092c644c998ad39809" UNIQUE ("username")`);
        await queryRunner.query(`ALTER TABLE "warehouse_product_safety" ADD CONSTRAINT "PK_449b886af4a4aa004a90bf8fa6e" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "warehouse" ADD CONSTRAINT "PK_965abf9f99ae8c5983ae74ebde8" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "warehouse_transfer_history" ADD CONSTRAINT "PK_2250c759863e6bf4020584cf697" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "warehouse_product_detail" ADD CONSTRAINT "PK_9000f6073b7cd21638583ab3f3e" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "warehouse_product" ADD CONSTRAINT "PK_327c519be4aeb4ddabc14e595ca" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "warehouse_transfer" ADD CONSTRAINT "PK_941ddf7509ae1af0786a29efa98" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "warehouse_transfer_detail" ADD CONSTRAINT "PK_b84761f5a1ddbe72918b064e7fd" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "inbound_detail_cost_price" ADD CONSTRAINT "PK_d4826803c3ac259e62d0ec31f46" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "inbound_detail_price" ADD CONSTRAINT "PK_baa807b0f40819ef0fb5ccf0c8d" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "inbound_detail" ADD CONSTRAINT "PK_c4248a70881db4d2441366e54e4" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "inbound_cost_allocation" ADD CONSTRAINT "PK_92cd62022686117c80b1bab8d1f" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "inbound" ADD CONSTRAINT "PK_837651a56a588fd82392d68a5fd" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "inbound_history" ADD CONSTRAINT "PK_97ead0ba575cd39d1d259af8f48" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "outbound" ADD CONSTRAINT "PK_46deb2c727cdef44d1e19c428da" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "outbound_detail" ADD CONSTRAINT "PK_0e0bb73f6ea09b9c006e1713eb3" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "outbound_history" ADD CONSTRAINT "PK_1730b9127c8aa799ace8f844aec" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "check_inventory_detail_by_employee" ADD CONSTRAINT "PK_47844a22b7efb42908bcfa56742" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "check_inventory_history" ADD CONSTRAINT "PK_c0bfde91552f0f1d7eba0ae8ce3" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "product_inventory_history" ADD CONSTRAINT "PK_a20935c24daadabbb2cb2eced36" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "check_inventory_detail" ADD CONSTRAINT "PK_4d404cfc20651a40ea061362df7" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "check_inventory" ADD CONSTRAINT "PK_e1ebce52293976a744a7cdbc576" PRIMARY KEY ("id")`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_2a04c94f9fb9f958bb39a2a2bf" ON "supplier" ("username") `);
        await queryRunner.query(`CREATE INDEX "IDX_ee4429a8fe2b5758456617e1ef" ON "item" ("code") `);
        await queryRunner.query(`CREATE INDEX "IDX_c6ae12601fed4e2ee5019544dd" ON "item" ("name") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_a032a2037d6fa09db7538e5438" ON "partner" ("code") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_bd6cf97e174dd9be6c8901cb79" ON "partner" ("taxCode") `);
        await queryRunner.query(`CREATE INDEX "IDX_4b648f730b4a67501ddb007075" ON "partner" ("phone") `);
        await queryRunner.query(`CREATE INDEX "IDX_39ba44d32530f1c7076c182ebc" ON "partner" ("email") `);
        await queryRunner.query(`CREATE INDEX "IDX_ae4e670c706a66da7cee6a52a3" ON "partner" ("contactPhone") `);
        await queryRunner.query(`CREATE INDEX "IDX_0a0352b213c03e6aca1b7e3388" ON "purchase_order" ("purchaseOrderCode") `);
        await queryRunner.query(`CREATE INDEX "IDX_32d8f4c996cc712a7248737c96" ON "purchase_order_history" ("purchaseOrderId") `);
        await queryRunner.query(`CREATE INDEX "IDX_464a6e7e41c5c27b2455403073" ON "purchase_order_so" ("poId") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_c0118074257e9d3aba08c314f7" ON "purchase_order_so" ("poId", "soId") `);
        await queryRunner.query(`CREATE INDEX "IDX_57ddb35b1c1326b9a847d51272" ON "supply_chain_config" ("supplyChainCode") `);
        await queryRunner.query(`CREATE INDEX "IDX_efdd3f589f04cd21d79136de1a" ON "bank" ("code") `);
        await queryRunner.query(`CREATE INDEX "IDX_46b9a736b36e121ddaac9e4e1f" ON "bank" ("bankCode") `);
        await queryRunner.query(`CREATE INDEX "IDX_161b6bcd432d8838b146870122" ON "bank_branch" ("code") `);
        await queryRunner.query(`CREATE INDEX "IDX_1146366945a250fbf7279e3949" ON "delivery_note" ("code") `);
        await queryRunner.query(`CREATE INDEX "IDX_4238e9d92717c3845dc73d1b96" ON "delivery_note" ("poId") `);
        await queryRunner.query(`CREATE INDEX "IDX_6b5327a7a4b9d389df3a7fd4d2" ON "delivery_note" ("poGroupId") `);
        await queryRunner.query(`CREATE INDEX "IDX_f0e1f6adf6955495740e7c6c9e" ON "delivery_note_child" ("code") `);
        await queryRunner.query(`CREATE INDEX "IDX_29aeaa8b802100506c22a2088a" ON "delivery_note_child" ("deliveryNoteId") `);
        await queryRunner.query(`CREATE INDEX "IDX_e6bd7b2577879d4eb6e352297f" ON "delivery_note_child_detail" ("deliveryNoteChildId") `);
        await queryRunner.query(`CREATE INDEX "IDX_ece7e907a6f2e6f18d4802f70a" ON "delivery_note_tracking" ("deliveryNoteId") `);
        await queryRunner.query(`CREATE INDEX "IDX_d52e44eb58b29ee0f2433ea04d" ON "delivery_note_tracking" ("poId") `);
        await queryRunner.query(`CREATE INDEX "IDX_2ca4380177071afe16243c8a86" ON "delivery_note_lastmile_product" ("code") `);
        await queryRunner.query(`CREATE INDEX "IDX_273a6d9401def7a02df45d69cb" ON "delivery_note_lastmile_product" ("deliveryNoteChildDetailId") `);
        await queryRunner.query(`ALTER TABLE "supplier_history" ADD CONSTRAINT "FK_0a04398f3acc707810153fdc5c6" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "employee" ADD CONSTRAINT "FK_9ad20e4029f9458b6eed0b0c454" FOREIGN KEY ("departmentId") REFERENCES "department"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "employee" ADD CONSTRAINT "FK_f4b0d329c4a3cf79ffe9d565047" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "permission" ADD CONSTRAINT "FK_c60570051d297d8269fcdd9bc47" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user" ADD CONSTRAINT "FK_ab4a80281f1e8d524714e00f38f" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user" ADD CONSTRAINT "FK_031cdc2c9c5eb56d48b5bdb4e54" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD CONSTRAINT "FK_46dc0c8922a4421b47cf47f8c91" FOREIGN KEY ("wardId") REFERENCES "ward"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD CONSTRAINT "FK_fab6bea78157b6f21e26aa85087" FOREIGN KEY ("districtId") REFERENCES "district"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD CONSTRAINT "FK_82cb75e968b854b8cc5ecff014e" FOREIGN KEY ("cityId") REFERENCES "city"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD CONSTRAINT "FK_e8902c50550ff82dd0143913c0a" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ward" ADD CONSTRAINT "FK_19a3bc9b3be291e8b9bc2bb623b" FOREIGN KEY ("districtId") REFERENCES "district"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "district" ADD CONSTRAINT "FK_148f1c944d0fec4114a54984da1" FOREIGN KEY ("cityId") REFERENCES "city"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "item_detail" ADD CONSTRAINT "FK_177b0e41ff7682b965ed8b87a4a" FOREIGN KEY ("itemId") REFERENCES "item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "item_combo" ADD CONSTRAINT "FK_54656e43714e6a8a3313c70e1fe" FOREIGN KEY ("itemId") REFERENCES "item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "item_combo" ADD CONSTRAINT "FK_2b8fea84ce0ad620ebbedec92b2" FOREIGN KEY ("itemInComboId") REFERENCES "item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "brand" ADD CONSTRAINT "FK_ca163361870d39b2a62dfcb4b57" FOREIGN KEY ("wardId") REFERENCES "ward"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "brand" ADD CONSTRAINT "FK_ccaa9f24e0e3b80957802464fcc" FOREIGN KEY ("districtId") REFERENCES "district"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "brand" ADD CONSTRAINT "FK_eb6cb8908ddda111620a0d4c24a" FOREIGN KEY ("cityId") REFERENCES "city"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "brand" ADD CONSTRAINT "FK_71f9615d50eac7d6e8f400a6885" FOREIGN KEY ("parentId") REFERENCES "brand"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "item_group" ADD CONSTRAINT "FK_fe01e2482b285214279aee5147e" FOREIGN KEY ("itemCategoryId") REFERENCES "item_category"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "item_category" ADD CONSTRAINT "FK_5dcad956164b4afe8b0ffddb97b" FOREIGN KEY ("itemTypeId") REFERENCES "item_type"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "item_price" ADD CONSTRAINT "FK_f8046453707754686bb1775ef71" FOREIGN KEY ("itemId") REFERENCES "item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "item" ADD CONSTRAINT "FK_9e2a16fa67338b5d7ba015b4e98" FOREIGN KEY ("brandId") REFERENCES "brand"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "item" ADD CONSTRAINT "FK_e7c44eae41483213a380f4c57c5" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "item" ADD CONSTRAINT "FK_f2a17f5a011c6a65d2e9e918a81" FOREIGN KEY ("unitId") REFERENCES "unit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "item" ADD CONSTRAINT "FK_6b5e685d1766edcbbeb397fb38a" FOREIGN KEY ("poUnitId") REFERENCES "unit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "item" ADD CONSTRAINT "FK_449c3593a1c196b51ae40c72b73" FOREIGN KEY ("itemTypeId") REFERENCES "item_type"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "item" ADD CONSTRAINT "FK_b0e27715ec6c59f16241121a2c5" FOREIGN KEY ("itemGroupId") REFERENCES "item_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "item" ADD CONSTRAINT "FK_5bd02a93f5b64c5f1d0d2c77781" FOREIGN KEY ("itemCategoryId") REFERENCES "item_category"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "item" ADD CONSTRAINT "FK_c682dec0b548cf37cfbb99daceb" FOREIGN KEY ("buyTaxId") REFERENCES "tax"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "item" ADD CONSTRAINT "FK_1a51047ba4f9903e60e6b482dce" FOREIGN KEY ("sellTaxId") REFERENCES "tax"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "region_city" ADD CONSTRAINT "FK_cf91b2bd6828ddddaf530e4b939" FOREIGN KEY ("regionId") REFERENCES "region"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "region_city" ADD CONSTRAINT "FK_77974209f148107bff56572b0c8" FOREIGN KEY ("cityId") REFERENCES "city"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "region_district" ADD CONSTRAINT "FK_96c0fc5bce7180e5bd1e825c40c" FOREIGN KEY ("regionId") REFERENCES "region"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "region_district" ADD CONSTRAINT "FK_da4f1592d933086353bb1f79ad6" FOREIGN KEY ("districtId") REFERENCES "district"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "region_ward" ADD CONSTRAINT "FK_9509de282f311db116ad995f242" FOREIGN KEY ("regionId") REFERENCES "region"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "region_ward" ADD CONSTRAINT "FK_73c484f250cb18815d0ff2ec2c9" FOREIGN KEY ("ward") REFERENCES "ward"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "news" ADD CONSTRAINT "FK_6573fe000551c966d07f27513c0" FOREIGN KEY ("newsCategoryId") REFERENCES "news_category"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_schedule_history" ADD CONSTRAINT "FK_28b8c4dc8bc7d2931bdb8c6a49a" FOREIGN KEY ("poScheduleId") REFERENCES "po_schedule"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "s_company_categories" ADD CONSTRAINT "FK_1126cfa15b21961c7a0f7f67de1" FOREIGN KEY ("companyId") REFERENCES "s_company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "s_survey_member" ADD CONSTRAINT "FK_24fe8c632976b2c9412dc37a33d" FOREIGN KEY ("surveyId") REFERENCES "s_survey"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "s_survey_question" ADD CONSTRAINT "FK_3191916b20c6d93330feca6afb9" FOREIGN KEY ("surveyId") REFERENCES "s_survey"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "s_question_list_detail" ADD CONSTRAINT "FK_50e92215501e3e43ffd075fabf8" FOREIGN KEY ("questionId") REFERENCES "s_question"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "s_question" ADD CONSTRAINT "FK_10473848b53976e270611b4d41b" FOREIGN KEY ("parentId") REFERENCES "s_question"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "s_question" ADD CONSTRAINT "FK_4503c596d47202c7b48a28e7f16" FOREIGN KEY ("topicId") REFERENCES "s_topic"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "s_topic" ADD CONSTRAINT "FK_0f82a6005035b5d87d6b79b16d9" FOREIGN KEY ("categoryId") REFERENCES "s_categories"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "s_survey_history" ADD CONSTRAINT "FK_57a4f59cf7916c253cf62745041" FOREIGN KEY ("surveyId") REFERENCES "s_survey"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "s_survey_history" ADD CONSTRAINT "FK_3ec4fe70819a62b3f57ef2bfdf0" FOREIGN KEY ("topicId") REFERENCES "s_topic"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "s_survey_history" ADD CONSTRAINT "FK_efefab0786c091fbba50180cb95" FOREIGN KEY ("categoryId") REFERENCES "s_categories"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "s_survey_question_list_detail" ADD CONSTRAINT "FK_faa5f787ebb9d4e1c846c50a818" FOREIGN KEY ("parentId") REFERENCES "s_survey_question_list_detail"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "s_survey_question_list_detail" ADD CONSTRAINT "FK_9a25693b259f45b2c97d63f9b8d" FOREIGN KEY ("surveyId") REFERENCES "s_survey"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "s_notify" ADD CONSTRAINT "FK_bf53f431bbe9ee5154f15458871" FOREIGN KEY ("surveyId") REFERENCES "s_survey"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "warehouse_product_safety" ADD CONSTRAINT "FK_593dbb5bed876090bdb4797faf5" FOREIGN KEY ("warehouseId") REFERENCES "warehouse"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "warehouse_transfer_history" ADD CONSTRAINT "FK_32a5b10115db9e48bda52f87c28" FOREIGN KEY ("warehouseTransferId") REFERENCES "warehouse_transfer"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "warehouse_product_detail" ADD CONSTRAINT "FK_3cf17315f381c71ec8e946f19ef" FOREIGN KEY ("warehouseProductId") REFERENCES "warehouse_product"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "warehouse_product" ADD CONSTRAINT "FK_a8c9aee14d47ec7b3f2ac429ebc" FOREIGN KEY ("warehouseId") REFERENCES "warehouse"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "warehouse_transfer" ADD CONSTRAINT "FK_372ee757dff8e9bbe2efba8fffd" FOREIGN KEY ("fromWarehouseId") REFERENCES "warehouse"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "warehouse_transfer" ADD CONSTRAINT "FK_3dfc66515ba0c3d469d52f294d2" FOREIGN KEY ("toWarehouseId") REFERENCES "warehouse"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "warehouse_transfer_detail" ADD CONSTRAINT "FK_f821bb90a5daeb7f9168d5d32d2" FOREIGN KEY ("warehouseTransferId") REFERENCES "warehouse_transfer"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "inbound_detail_cost_price" ADD CONSTRAINT "FK_a81f1fe09fbef58a0f469171774" FOREIGN KEY ("inboundId") REFERENCES "inbound"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "inbound_detail_cost_price" ADD CONSTRAINT "FK_91ec8cccb505ba9b6326a1c7d98" FOREIGN KEY ("inboundDetailId") REFERENCES "inbound_detail"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "inbound_detail_price" ADD CONSTRAINT "FK_8315feba6b8cebc18b3133806e9" FOREIGN KEY ("inboundId") REFERENCES "inbound"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "inbound_detail_price" ADD CONSTRAINT "FK_55b31e85ce24d32f9aa86e53f69" FOREIGN KEY ("inboundDetailId") REFERENCES "inbound_detail"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "inbound_detail" ADD CONSTRAINT "FK_9d011b26b0e596234529a18bb89" FOREIGN KEY ("inboundId") REFERENCES "inbound"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "inbound_cost_allocation" ADD CONSTRAINT "FK_9ad135bf03895b3f6efa4937801" FOREIGN KEY ("inboundId") REFERENCES "inbound"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "inbound_cost_allocation" ADD CONSTRAINT "FK_90182c3df32688deb5a171c901a" FOREIGN KEY ("inboundDetailId") REFERENCES "inbound_detail"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "inbound" ADD CONSTRAINT "FK_34fe1002c4ce18e646133af1423" FOREIGN KEY ("warehouseId") REFERENCES "warehouse"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "inbound" ADD CONSTRAINT "FK_d6f6189c53e3068276952a77e6d" FOREIGN KEY ("warehouseTransferId") REFERENCES "warehouse_transfer"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "inbound" ADD CONSTRAINT "FK_b3175bd1eea50f4f95535db1dd0" FOREIGN KEY ("checkInventoryId") REFERENCES "check_inventory"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "inbound_history" ADD CONSTRAINT "FK_a7443e90d941238599f0e66744e" FOREIGN KEY ("inboundId") REFERENCES "inbound"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "outbound" ADD CONSTRAINT "FK_db95126b3d7d36ac29559c24874" FOREIGN KEY ("warehouseTransferId") REFERENCES "warehouse_transfer"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "outbound" ADD CONSTRAINT "FK_acf726edc48644cdf0df4dc182a" FOREIGN KEY ("checkInventoryId") REFERENCES "check_inventory"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "outbound" ADD CONSTRAINT "FK_eebf9fa02a0b1177db5bbaca016" FOREIGN KEY ("warehouseId") REFERENCES "warehouse"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "outbound_detail" ADD CONSTRAINT "FK_0991ac48e89442b5ca9580c8518" FOREIGN KEY ("outboundId") REFERENCES "outbound"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "outbound_history" ADD CONSTRAINT "FK_b9edfe83dbb57eff242434473fd" FOREIGN KEY ("outboundId") REFERENCES "outbound"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "check_inventory_detail_by_employee" ADD CONSTRAINT "FK_bfe197c694a88d8babcc8be6811" FOREIGN KEY ("checkInventoryDetailId") REFERENCES "check_inventory_detail"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "check_inventory_history" ADD CONSTRAINT "FK_a08425acf82b876567a54e82a78" FOREIGN KEY ("checkInventoryId") REFERENCES "check_inventory"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "check_inventory_detail" ADD CONSTRAINT "FK_3e62bb42bfc7f4cc7c490c6d3a9" FOREIGN KEY ("checkInventoryId") REFERENCES "check_inventory"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "check_inventory" ADD CONSTRAINT "FK_77791d642f3d0caa1e79ff2b37d" FOREIGN KEY ("warehouseId") REFERENCES "warehouse"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "check_inventory" DROP CONSTRAINT "FK_77791d642f3d0caa1e79ff2b37d"`);
        await queryRunner.query(`ALTER TABLE "check_inventory_detail" DROP CONSTRAINT "FK_3e62bb42bfc7f4cc7c490c6d3a9"`);
        await queryRunner.query(`ALTER TABLE "check_inventory_history" DROP CONSTRAINT "FK_a08425acf82b876567a54e82a78"`);
        await queryRunner.query(`ALTER TABLE "check_inventory_detail_by_employee" DROP CONSTRAINT "FK_bfe197c694a88d8babcc8be6811"`);
        await queryRunner.query(`ALTER TABLE "outbound_history" DROP CONSTRAINT "FK_b9edfe83dbb57eff242434473fd"`);
        await queryRunner.query(`ALTER TABLE "outbound_detail" DROP CONSTRAINT "FK_0991ac48e89442b5ca9580c8518"`);
        await queryRunner.query(`ALTER TABLE "outbound" DROP CONSTRAINT "FK_eebf9fa02a0b1177db5bbaca016"`);
        await queryRunner.query(`ALTER TABLE "outbound" DROP CONSTRAINT "FK_acf726edc48644cdf0df4dc182a"`);
        await queryRunner.query(`ALTER TABLE "outbound" DROP CONSTRAINT "FK_db95126b3d7d36ac29559c24874"`);
        await queryRunner.query(`ALTER TABLE "inbound_history" DROP CONSTRAINT "FK_a7443e90d941238599f0e66744e"`);
        await queryRunner.query(`ALTER TABLE "inbound" DROP CONSTRAINT "FK_b3175bd1eea50f4f95535db1dd0"`);
        await queryRunner.query(`ALTER TABLE "inbound" DROP CONSTRAINT "FK_d6f6189c53e3068276952a77e6d"`);
        await queryRunner.query(`ALTER TABLE "inbound" DROP CONSTRAINT "FK_34fe1002c4ce18e646133af1423"`);
        await queryRunner.query(`ALTER TABLE "inbound_cost_allocation" DROP CONSTRAINT "FK_90182c3df32688deb5a171c901a"`);
        await queryRunner.query(`ALTER TABLE "inbound_cost_allocation" DROP CONSTRAINT "FK_9ad135bf03895b3f6efa4937801"`);
        await queryRunner.query(`ALTER TABLE "inbound_detail" DROP CONSTRAINT "FK_9d011b26b0e596234529a18bb89"`);
        await queryRunner.query(`ALTER TABLE "inbound_detail_price" DROP CONSTRAINT "FK_55b31e85ce24d32f9aa86e53f69"`);
        await queryRunner.query(`ALTER TABLE "inbound_detail_price" DROP CONSTRAINT "FK_8315feba6b8cebc18b3133806e9"`);
        await queryRunner.query(`ALTER TABLE "inbound_detail_cost_price" DROP CONSTRAINT "FK_91ec8cccb505ba9b6326a1c7d98"`);
        await queryRunner.query(`ALTER TABLE "inbound_detail_cost_price" DROP CONSTRAINT "FK_a81f1fe09fbef58a0f469171774"`);
        await queryRunner.query(`ALTER TABLE "warehouse_transfer_detail" DROP CONSTRAINT "FK_f821bb90a5daeb7f9168d5d32d2"`);
        await queryRunner.query(`ALTER TABLE "warehouse_transfer" DROP CONSTRAINT "FK_3dfc66515ba0c3d469d52f294d2"`);
        await queryRunner.query(`ALTER TABLE "warehouse_transfer" DROP CONSTRAINT "FK_372ee757dff8e9bbe2efba8fffd"`);
        await queryRunner.query(`ALTER TABLE "warehouse_product" DROP CONSTRAINT "FK_a8c9aee14d47ec7b3f2ac429ebc"`);
        await queryRunner.query(`ALTER TABLE "warehouse_product_detail" DROP CONSTRAINT "FK_3cf17315f381c71ec8e946f19ef"`);
        await queryRunner.query(`ALTER TABLE "warehouse_transfer_history" DROP CONSTRAINT "FK_32a5b10115db9e48bda52f87c28"`);
        await queryRunner.query(`ALTER TABLE "warehouse_product_safety" DROP CONSTRAINT "FK_593dbb5bed876090bdb4797faf5"`);
        await queryRunner.query(`ALTER TABLE "s_notify" DROP CONSTRAINT "FK_bf53f431bbe9ee5154f15458871"`);
        await queryRunner.query(`ALTER TABLE "s_survey_question_list_detail" DROP CONSTRAINT "FK_9a25693b259f45b2c97d63f9b8d"`);
        await queryRunner.query(`ALTER TABLE "s_survey_question_list_detail" DROP CONSTRAINT "FK_faa5f787ebb9d4e1c846c50a818"`);
        await queryRunner.query(`ALTER TABLE "s_survey_history" DROP CONSTRAINT "FK_efefab0786c091fbba50180cb95"`);
        await queryRunner.query(`ALTER TABLE "s_survey_history" DROP CONSTRAINT "FK_3ec4fe70819a62b3f57ef2bfdf0"`);
        await queryRunner.query(`ALTER TABLE "s_survey_history" DROP CONSTRAINT "FK_57a4f59cf7916c253cf62745041"`);
        await queryRunner.query(`ALTER TABLE "s_topic" DROP CONSTRAINT "FK_0f82a6005035b5d87d6b79b16d9"`);
        await queryRunner.query(`ALTER TABLE "s_question" DROP CONSTRAINT "FK_4503c596d47202c7b48a28e7f16"`);
        await queryRunner.query(`ALTER TABLE "s_question" DROP CONSTRAINT "FK_10473848b53976e270611b4d41b"`);
        await queryRunner.query(`ALTER TABLE "s_question_list_detail" DROP CONSTRAINT "FK_50e92215501e3e43ffd075fabf8"`);
        await queryRunner.query(`ALTER TABLE "s_survey_question" DROP CONSTRAINT "FK_3191916b20c6d93330feca6afb9"`);
        await queryRunner.query(`ALTER TABLE "s_survey_member" DROP CONSTRAINT "FK_24fe8c632976b2c9412dc37a33d"`);
        await queryRunner.query(`ALTER TABLE "s_company_categories" DROP CONSTRAINT "FK_1126cfa15b21961c7a0f7f67de1"`);
        await queryRunner.query(`ALTER TABLE "po_schedule_history" DROP CONSTRAINT "FK_28b8c4dc8bc7d2931bdb8c6a49a"`);
        await queryRunner.query(`ALTER TABLE "news" DROP CONSTRAINT "FK_6573fe000551c966d07f27513c0"`);
        await queryRunner.query(`ALTER TABLE "region_ward" DROP CONSTRAINT "FK_73c484f250cb18815d0ff2ec2c9"`);
        await queryRunner.query(`ALTER TABLE "region_ward" DROP CONSTRAINT "FK_9509de282f311db116ad995f242"`);
        await queryRunner.query(`ALTER TABLE "region_district" DROP CONSTRAINT "FK_da4f1592d933086353bb1f79ad6"`);
        await queryRunner.query(`ALTER TABLE "region_district" DROP CONSTRAINT "FK_96c0fc5bce7180e5bd1e825c40c"`);
        await queryRunner.query(`ALTER TABLE "region_city" DROP CONSTRAINT "FK_77974209f148107bff56572b0c8"`);
        await queryRunner.query(`ALTER TABLE "region_city" DROP CONSTRAINT "FK_cf91b2bd6828ddddaf530e4b939"`);
        await queryRunner.query(`ALTER TABLE "item" DROP CONSTRAINT "FK_1a51047ba4f9903e60e6b482dce"`);
        await queryRunner.query(`ALTER TABLE "item" DROP CONSTRAINT "FK_c682dec0b548cf37cfbb99daceb"`);
        await queryRunner.query(`ALTER TABLE "item" DROP CONSTRAINT "FK_5bd02a93f5b64c5f1d0d2c77781"`);
        await queryRunner.query(`ALTER TABLE "item" DROP CONSTRAINT "FK_b0e27715ec6c59f16241121a2c5"`);
        await queryRunner.query(`ALTER TABLE "item" DROP CONSTRAINT "FK_449c3593a1c196b51ae40c72b73"`);
        await queryRunner.query(`ALTER TABLE "item" DROP CONSTRAINT "FK_6b5e685d1766edcbbeb397fb38a"`);
        await queryRunner.query(`ALTER TABLE "item" DROP CONSTRAINT "FK_f2a17f5a011c6a65d2e9e918a81"`);
        await queryRunner.query(`ALTER TABLE "item" DROP CONSTRAINT "FK_e7c44eae41483213a380f4c57c5"`);
        await queryRunner.query(`ALTER TABLE "item" DROP CONSTRAINT "FK_9e2a16fa67338b5d7ba015b4e98"`);
        await queryRunner.query(`ALTER TABLE "item_price" DROP CONSTRAINT "FK_f8046453707754686bb1775ef71"`);
        await queryRunner.query(`ALTER TABLE "item_category" DROP CONSTRAINT "FK_5dcad956164b4afe8b0ffddb97b"`);
        await queryRunner.query(`ALTER TABLE "item_group" DROP CONSTRAINT "FK_fe01e2482b285214279aee5147e"`);
        await queryRunner.query(`ALTER TABLE "brand" DROP CONSTRAINT "FK_71f9615d50eac7d6e8f400a6885"`);
        await queryRunner.query(`ALTER TABLE "brand" DROP CONSTRAINT "FK_eb6cb8908ddda111620a0d4c24a"`);
        await queryRunner.query(`ALTER TABLE "brand" DROP CONSTRAINT "FK_ccaa9f24e0e3b80957802464fcc"`);
        await queryRunner.query(`ALTER TABLE "brand" DROP CONSTRAINT "FK_ca163361870d39b2a62dfcb4b57"`);
        await queryRunner.query(`ALTER TABLE "item_combo" DROP CONSTRAINT "FK_2b8fea84ce0ad620ebbedec92b2"`);
        await queryRunner.query(`ALTER TABLE "item_combo" DROP CONSTRAINT "FK_54656e43714e6a8a3313c70e1fe"`);
        await queryRunner.query(`ALTER TABLE "item_detail" DROP CONSTRAINT "FK_177b0e41ff7682b965ed8b87a4a"`);
        await queryRunner.query(`ALTER TABLE "district" DROP CONSTRAINT "FK_148f1c944d0fec4114a54984da1"`);
        await queryRunner.query(`ALTER TABLE "ward" DROP CONSTRAINT "FK_19a3bc9b3be291e8b9bc2bb623b"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT "FK_e8902c50550ff82dd0143913c0a"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT "FK_82cb75e968b854b8cc5ecff014e"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT "FK_fab6bea78157b6f21e26aa85087"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT "FK_46dc0c8922a4421b47cf47f8c91"`);
        await queryRunner.query(`ALTER TABLE "user" DROP CONSTRAINT "FK_031cdc2c9c5eb56d48b5bdb4e54"`);
        await queryRunner.query(`ALTER TABLE "user" DROP CONSTRAINT "FK_ab4a80281f1e8d524714e00f38f"`);
        await queryRunner.query(`ALTER TABLE "permission" DROP CONSTRAINT "FK_c60570051d297d8269fcdd9bc47"`);
        await queryRunner.query(`ALTER TABLE "employee" DROP CONSTRAINT "FK_f4b0d329c4a3cf79ffe9d565047"`);
        await queryRunner.query(`ALTER TABLE "employee" DROP CONSTRAINT "FK_9ad20e4029f9458b6eed0b0c454"`);
        await queryRunner.query(`ALTER TABLE "supplier_history" DROP CONSTRAINT "FK_0a04398f3acc707810153fdc5c6"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_273a6d9401def7a02df45d69cb"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_2ca4380177071afe16243c8a86"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_d52e44eb58b29ee0f2433ea04d"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_ece7e907a6f2e6f18d4802f70a"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_e6bd7b2577879d4eb6e352297f"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_29aeaa8b802100506c22a2088a"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_f0e1f6adf6955495740e7c6c9e"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_6b5327a7a4b9d389df3a7fd4d2"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_4238e9d92717c3845dc73d1b96"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_1146366945a250fbf7279e3949"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_161b6bcd432d8838b146870122"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_46b9a736b36e121ddaac9e4e1f"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_efdd3f589f04cd21d79136de1a"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_57ddb35b1c1326b9a847d51272"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_c0118074257e9d3aba08c314f7"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_464a6e7e41c5c27b2455403073"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_32d8f4c996cc712a7248737c96"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_0a0352b213c03e6aca1b7e3388"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_ae4e670c706a66da7cee6a52a3"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_39ba44d32530f1c7076c182ebc"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_4b648f730b4a67501ddb007075"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_bd6cf97e174dd9be6c8901cb79"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_a032a2037d6fa09db7538e5438"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_c6ae12601fed4e2ee5019544dd"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_ee4429a8fe2b5758456617e1ef"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_2a04c94f9fb9f958bb39a2a2bf"`);
        await queryRunner.query(`ALTER TABLE "check_inventory" DROP CONSTRAINT "PK_e1ebce52293976a744a7cdbc576"`);
        await queryRunner.query(`ALTER TABLE "check_inventory_detail" DROP CONSTRAINT "PK_4d404cfc20651a40ea061362df7"`);
        await queryRunner.query(`ALTER TABLE "product_inventory_history" DROP CONSTRAINT "PK_a20935c24daadabbb2cb2eced36"`);
        await queryRunner.query(`ALTER TABLE "check_inventory_history" DROP CONSTRAINT "PK_c0bfde91552f0f1d7eba0ae8ce3"`);
        await queryRunner.query(`ALTER TABLE "check_inventory_detail_by_employee" DROP CONSTRAINT "PK_47844a22b7efb42908bcfa56742"`);
        await queryRunner.query(`ALTER TABLE "outbound_history" DROP CONSTRAINT "PK_1730b9127c8aa799ace8f844aec"`);
        await queryRunner.query(`ALTER TABLE "outbound_detail" DROP CONSTRAINT "PK_0e0bb73f6ea09b9c006e1713eb3"`);
        await queryRunner.query(`ALTER TABLE "outbound" DROP CONSTRAINT "PK_46deb2c727cdef44d1e19c428da"`);
        await queryRunner.query(`ALTER TABLE "inbound_history" DROP CONSTRAINT "PK_97ead0ba575cd39d1d259af8f48"`);
        await queryRunner.query(`ALTER TABLE "inbound" DROP CONSTRAINT "PK_837651a56a588fd82392d68a5fd"`);
        await queryRunner.query(`ALTER TABLE "inbound_cost_allocation" DROP CONSTRAINT "PK_92cd62022686117c80b1bab8d1f"`);
        await queryRunner.query(`ALTER TABLE "inbound_detail" DROP CONSTRAINT "PK_c4248a70881db4d2441366e54e4"`);
        await queryRunner.query(`ALTER TABLE "inbound_detail_price" DROP CONSTRAINT "PK_baa807b0f40819ef0fb5ccf0c8d"`);
        await queryRunner.query(`ALTER TABLE "inbound_detail_cost_price" DROP CONSTRAINT "PK_d4826803c3ac259e62d0ec31f46"`);
        await queryRunner.query(`ALTER TABLE "warehouse_transfer_detail" DROP CONSTRAINT "PK_b84761f5a1ddbe72918b064e7fd"`);
        await queryRunner.query(`ALTER TABLE "warehouse_transfer" DROP CONSTRAINT "PK_941ddf7509ae1af0786a29efa98"`);
        await queryRunner.query(`ALTER TABLE "warehouse_product" DROP CONSTRAINT "PK_327c519be4aeb4ddabc14e595ca"`);
        await queryRunner.query(`ALTER TABLE "warehouse_product_detail" DROP CONSTRAINT "PK_9000f6073b7cd21638583ab3f3e"`);
        await queryRunner.query(`ALTER TABLE "warehouse_transfer_history" DROP CONSTRAINT "PK_2250c759863e6bf4020584cf697"`);
        await queryRunner.query(`ALTER TABLE "warehouse" DROP CONSTRAINT "PK_965abf9f99ae8c5983ae74ebde8"`);
        await queryRunner.query(`ALTER TABLE "warehouse_product_safety" DROP CONSTRAINT "PK_449b886af4a4aa004a90bf8fa6e"`);
        await queryRunner.query(`ALTER TABLE "s_user" DROP CONSTRAINT "UQ_8850058e9092c644c998ad39809"`);
        await queryRunner.query(`ALTER TABLE "s_user" DROP CONSTRAINT "PK_0b8252ae567a2ff566537044a6b"`);
        await queryRunner.query(`ALTER TABLE "s_notify" DROP CONSTRAINT "PK_b6c710ecb0b325e0e5a07220b89"`);
        await queryRunner.query(`ALTER TABLE "s_survey" DROP CONSTRAINT "PK_52c1b3d137f6fe70da993f680bb"`);
        await queryRunner.query(`ALTER TABLE "s_survey_question_list_detail" DROP CONSTRAINT "PK_81c5564f07fcda90b2a3954a97e"`);
        await queryRunner.query(`ALTER TABLE "s_survey_history" DROP CONSTRAINT "PK_b024fce6c6101eb2d8dd953871d"`);
        await queryRunner.query(`ALTER TABLE "s_topic" DROP CONSTRAINT "PK_469453fff959c16d846a80d7eac"`);
        await queryRunner.query(`ALTER TABLE "s_categories" DROP CONSTRAINT "PK_d8c3e66d9df7fff07882c48efc2"`);
        await queryRunner.query(`ALTER TABLE "s_question" DROP CONSTRAINT "PK_0b44a192f88b22643d70bc12011"`);
        await queryRunner.query(`ALTER TABLE "s_question_list_detail" DROP CONSTRAINT "PK_73a987f3844ffbf669b3c6a7799"`);
        await queryRunner.query(`ALTER TABLE "s_survey_question" DROP CONSTRAINT "PK_38e088585762534a4fe530583ef"`);
        await queryRunner.query(`ALTER TABLE "s_survey_member" DROP CONSTRAINT "PK_3acaae0cc4742a8f08ba1b4dad2"`);
        await queryRunner.query(`ALTER TABLE "s_email_history" DROP CONSTRAINT "PK_9a1705346c0e5a9200255c35b77"`);
        await queryRunner.query(`ALTER TABLE "s_company_categories" DROP CONSTRAINT "PK_bebf8a975194b857b6d4890eee9"`);
        await queryRunner.query(`ALTER TABLE "s_company" DROP CONSTRAINT "PK_6e76eaf206210ce77bc3e6b6908"`);
        await queryRunner.query(`ALTER TABLE "delivery_note_lastmile_product" DROP CONSTRAINT "PK_47ea90713bfcc1bbef520c3a7b7"`);
        await queryRunner.query(`ALTER TABLE "delivery_note_tracking" DROP CONSTRAINT "PK_0722547b4e7f8897a61a627a5c0"`);
        await queryRunner.query(`ALTER TABLE "delivery_note_child_detail" DROP CONSTRAINT "PK_0d902ad7014106e593ac24ac192"`);
        await queryRunner.query(`ALTER TABLE "delivery_note_child" DROP CONSTRAINT "UQ_f0e1f6adf6955495740e7c6c9ec"`);
        await queryRunner.query(`ALTER TABLE "delivery_note_child" DROP CONSTRAINT "PK_f03212060575e6f3005895c8050"`);
        await queryRunner.query(`ALTER TABLE "delivery_note" DROP CONSTRAINT "UQ_1146366945a250fbf7279e39498"`);
        await queryRunner.query(`ALTER TABLE "delivery_note" DROP CONSTRAINT "PK_b2e8966e12465b34021b9a2c6eb"`);
        await queryRunner.query(`ALTER TABLE "bank_branch" DROP CONSTRAINT "UQ_161b6bcd432d8838b1468701223"`);
        await queryRunner.query(`ALTER TABLE "bank_branch" DROP CONSTRAINT "PK_758e63cf6e1278222f4c6538561"`);
        await queryRunner.query(`ALTER TABLE "bank" DROP CONSTRAINT "UQ_11f196da2e68cef1c7e84b4fe94"`);
        await queryRunner.query(`ALTER TABLE "bank" DROP CONSTRAINT "UQ_46b9a736b36e121ddaac9e4e1fe"`);
        await queryRunner.query(`ALTER TABLE "bank" DROP CONSTRAINT "UQ_efdd3f589f04cd21d79136de1aa"`);
        await queryRunner.query(`ALTER TABLE "bank" DROP CONSTRAINT "PK_7651eaf705126155142947926e8"`);
        await queryRunner.query(`ALTER TABLE "supply_chain_config_detail" DROP CONSTRAINT "PK_b0ff4a30cc410958da917619b4c"`);
        await queryRunner.query(`ALTER TABLE "supply_chain_config" ALTER COLUMN "partnerId" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "supply_chain_config" DROP CONSTRAINT "UQ_57ddb35b1c1326b9a847d51272a"`);
        await queryRunner.query(`ALTER TABLE "supply_chain_config" DROP CONSTRAINT "PK_f64c27636fe55fa13e8af5a2ef0"`);
        await queryRunner.query(`ALTER TABLE "purchase_order_so" DROP CONSTRAINT "PK_c09084a8c8a241650bc53e63a15"`);
        await queryRunner.query(`ALTER TABLE "purchase_order_history" DROP CONSTRAINT "PK_734bfddbb92e6dadfeef12377db"`);
        await queryRunner.query(`ALTER TABLE "purchase_order_item" DROP CONSTRAINT "PK_f3eaf81afb216ae78a59cc19503"`);
        await queryRunner.query(`ALTER TABLE "purchase_order" DROP CONSTRAINT "UQ_0a0352b213c03e6aca1b7e33881"`);
        await queryRunner.query(`ALTER TABLE "purchase_order" DROP CONSTRAINT "PK_ad3e1c7b862f4043b103a6c8c60"`);
        await queryRunner.query(`ALTER TABLE "news" DROP CONSTRAINT "PK_39a43dfcb6007180f04aff2357e"`);
        await queryRunner.query(`ALTER TABLE "news_category" DROP CONSTRAINT "PK_aac53a9364896452e463139e4a0"`);
        await queryRunner.query(`ALTER TABLE "partner_map" DROP CONSTRAINT "PK_219fd2f1848e94dcbc62ed6072f"`);
        await queryRunner.query(`ALTER TABLE "partner" DROP CONSTRAINT "PK_8f34ff11ddd5459eacbfacd48ca"`);
        await queryRunner.query(`ALTER TABLE "setting_media" DROP CONSTRAINT "PK_c630b4a975de7f9967483bc4e7c"`);
        await queryRunner.query(`ALTER TABLE "setting_string" DROP CONSTRAINT "PK_cd993a95b69adc4fcb1c4e779c3"`);
        await queryRunner.query(`ALTER TABLE "region" DROP CONSTRAINT "PK_5f48ffc3af96bc486f5f3f3a6da"`);
        await queryRunner.query(`ALTER TABLE "region_ward" DROP CONSTRAINT "PK_6212798eefff8e19604cb0d89fc"`);
        await queryRunner.query(`ALTER TABLE "region_district" DROP CONSTRAINT "PK_cb2462c0e11db0b57153bf8a028"`);
        await queryRunner.query(`ALTER TABLE "region_city" DROP CONSTRAINT "PK_2be03f2f8bfe3bf5d8240c940bf"`);
        await queryRunner.query(`ALTER TABLE "media" DROP CONSTRAINT "PK_f4e0fcac36e050de337b670d8bd"`);
        await queryRunner.query(`ALTER TABLE "packing" DROP CONSTRAINT "PK_f2700177c619b75fb91ee9b9bcd"`);
        await queryRunner.query(`ALTER TABLE "item" DROP COLUMN "sellPrice"`);
        await queryRunner.query(`ALTER TABLE "item" ADD "sellPrice" integer DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "item" DROP COLUMN "buyPrice"`);
        await queryRunner.query(`ALTER TABLE "item" ADD "buyPrice" integer DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "item" DROP CONSTRAINT "PK_d3c0c71f23e7adcf952a1d13423"`);
        await queryRunner.query(`ALTER TABLE "item_price" DROP CONSTRAINT "PK_bf831a4a3a9eca20d4ef2322d7d"`);
        await queryRunner.query(`ALTER TABLE "item_type" DROP CONSTRAINT "PK_64cde7db02a99c28d4b67efb367"`);
        await queryRunner.query(`ALTER TABLE "item_category" DROP CONSTRAINT "PK_91ba90f150e8804bdaad7b17ff8"`);
        await queryRunner.query(`ALTER TABLE "item_group" DROP CONSTRAINT "PK_6b0100c5cb7c67d99ae46197727"`);
        await queryRunner.query(`ALTER TABLE "brand" DROP CONSTRAINT "PK_a5d20765ddd942eb5de4eee2d7f"`);
        await queryRunner.query(`ALTER TABLE "item_combo" DROP CONSTRAINT "PK_a1837070559e47576aafb827e81"`);
        await queryRunner.query(`ALTER TABLE "item_detail" DROP CONSTRAINT "PK_864d3cb5dd8bc67512e2a533d9b"`);
        await queryRunner.query(`ALTER TABLE "unit" DROP CONSTRAINT "PK_4252c4be609041e559f0c80f58a"`);
        await queryRunner.query(`ALTER TABLE "city" DROP CONSTRAINT "PK_b222f51ce26f7e5ca86944a6739"`);
        await queryRunner.query(`ALTER TABLE "district" DROP CONSTRAINT "PK_ee5cb6fd5223164bb87ea693f1e"`);
        await queryRunner.query(`ALTER TABLE "ward" DROP CONSTRAINT "PK_e6725fa4a50e449c4352d2230e1"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT "PK_2bc0d2cab6276144d2ff98a2828"`);
        await queryRunner.query(`ALTER TABLE "user" DROP CONSTRAINT "UQ_ab4a80281f1e8d524714e00f38f"`);
        await queryRunner.query(`ALTER TABLE "user" DROP CONSTRAINT "UQ_78a916df40e02a9deb1c4b75edb"`);
        await queryRunner.query(`ALTER TABLE "user" DROP CONSTRAINT "PK_cace4a159ff9f2512dd42373760"`);
        await queryRunner.query(`ALTER TABLE "permission" DROP CONSTRAINT "PK_3b8b97af9d9d8807e41e6f48362"`);
        await queryRunner.query(`ALTER TABLE "employee" DROP CONSTRAINT "UQ_f4b0d329c4a3cf79ffe9d565047"`);
        await queryRunner.query(`ALTER TABLE "employee" DROP CONSTRAINT "UQ_348a4a9894eef0760bfe0a26328"`);
        await queryRunner.query(`ALTER TABLE "employee" DROP CONSTRAINT "PK_3c2bc72f03fd5abbbc5ac169498"`);
        await queryRunner.query(`ALTER TABLE "department" DROP CONSTRAINT "PK_9a2213262c1593bffb581e382f5"`);
        await queryRunner.query(`ALTER TABLE "supplier_history" DROP CONSTRAINT "PK_e99a304491bc41175120b95e8e3"`);
        await queryRunner.query(`ALTER TABLE "contracts" DROP CONSTRAINT "PK_2c7b8f3a7b1acdd49497d83d0fb"`);
        await queryRunner.query(`ALTER TABLE "action_log" DROP CONSTRAINT "PK_63cffa5d8af90621882f0388359"`);
        await queryRunner.query(`ALTER TABLE "inbound" DROP COLUMN "files"`);
        await queryRunner.query(`ALTER TABLE "warehouse" DROP COLUMN "type"`);
        await queryRunner.query(`DROP TYPE "public"."warehouse_type_enum"`);
        await queryRunner.query(`ALTER TABLE "delivery_note_tracking" DROP COLUMN "files"`);
        await queryRunner.query(`ALTER TABLE "purchase_order" DROP COLUMN "approvalLevelCurrent"`);
        await queryRunner.query(`ALTER TABLE "purchase_order" DROP COLUMN "approverCurrentId"`);
        await queryRunner.query(`ALTER TABLE "purchase_order" DROP COLUMN "supplyChainId"`);
        await queryRunner.query(`ALTER TABLE "item" DROP COLUMN "sellTaxId"`);
        await queryRunner.query(`ALTER TABLE "item" DROP COLUMN "buyTaxId"`);
        await queryRunner.query(`ALTER TABLE "item" DROP COLUMN "canPreOrder"`);
        await queryRunner.query(`ALTER TABLE "item_price" DROP COLUMN "priceOriginal"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT "UQ_e8902c50550ff82dd0143913c0a"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "userId"`);
        await queryRunner.query(`ALTER TABLE "user" DROP CONSTRAINT "UQ_031cdc2c9c5eb56d48b5bdb4e54"`);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "supplierId"`);
        await queryRunner.query(`ALTER TABLE "employee" DROP COLUMN "districtId"`);
        await queryRunner.query(`ALTER TABLE "employee" DROP COLUMN "wardId"`);
        await queryRunner.query(`ALTER TABLE "employee" DROP COLUMN "cityId"`);
        await queryRunner.query(`ALTER TABLE "employee" DROP COLUMN "address"`);
        await queryRunner.query(`DROP TABLE "contact"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_27a9d8236dc01163efb8d5e418"`);
        await queryRunner.query(`DROP TABLE "order_config"`);
        await queryRunner.query(`DROP TABLE "delivery_schedule"`);
        await queryRunner.query(`DROP TABLE "po_schedule"`);
        await queryRunner.query(`DROP TABLE "po_schedule_history"`);
        await queryRunner.query(`DROP TABLE "supply_chain_config_time"`);
        await queryRunner.query(`DROP TABLE "supply_chain_config_approve"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_77827135799beecb02330990eb"`);
        await queryRunner.query(`DROP TABLE "purchase_order_approve"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_b8301e6f0f2aad1cb459ddcf87"`);
        await queryRunner.query(`DROP TABLE "tax"`);
    }

}
