import { Injectable, ConflictException, NotFoundException } from '@nestjs/common'
import { UnitCreateDto } from './dto/unitCreate.dto'
import {
  ERROR_NOT_FOUND_DATA,
  ERROR_CODE_TAKEN,
  UPDATE_ACTIVE_SUCCESS,
  enumData,
  CREATE_SUCCESS,
  UPDATE_SUCCESS,
  IMPORT_SUCCESS,
} from '../../../constants'
import { UnitImportDto, UnitUpdateDto } from './dto'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { ILike, In, Like } from 'typeorm'
import { UnitRepository } from '../../../repositories'
import { UnitEntity } from '../../../entities'
// import { PaginationInterface, paginationFunc } from '../../../helpers/typeormHelper'
import { Request as IRequest } from 'express'

@Injectable()
export class UnitService {
  constructor(private readonly repo: UnitRepository) { }

  async find(data: any) {
    const whereCon: any = { isDeleted: false }
    if (data.name) whereCon.name = Like(`%${data.name}%`)
    if (data.code) whereCon.code = Like(`%${data.code}%`)
    if (data.lstId?.length > 0) whereCon.id = In(data.lstId)
    if (data.lstCode?.length > 0) whereCon.code = In(data.lstCode)
    return await this.repo.find({ where: whereCon })
  }

  async loadData() {
    return await this.repo.find({
      where: { isDeleted: false },
      select: { id: true, name: true, code: true },
    })
  }

  async pagination(data: PaginationDto) {
    const whereCon: any = {}
    if (data.where.name) whereCon.name = ILike(`%${data.where.name}%`)
    if (data.where.code) whereCon.code = ILike(`%${data.where.code}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    return await this.repo.findAndCount({
      where: whereCon,
      order: { createdAt: 'DESC' },
      skip: data.skip,
      take: data.take,
    })
  }

  async createData(user: UserDto, data: UnitCreateDto, req: IRequest) {
    const checkCodeExist = await this.repo.findOne({ where: { code: data.code } })
    if (checkCodeExist) throw new ConflictException(ERROR_CODE_TAKEN)

    const unit = new UnitEntity()
    unit.code = data.code
    unit.name = data.name
    unit.baseUnit = data.baseUnit
    unit.description = data?.description ?? null
    unit.createdBy = user.id
    await this.repo.insert(unit)

    return { message: CREATE_SUCCESS }
  }

  async updateData(user: UserDto, data: UnitUpdateDto, req: IRequest) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    if (entity.code != data.code) {
      const checkCodeExist = await this.repo.findOne({ where: { code: data.code } })
      if (checkCodeExist) throw new ConflictException(ERROR_CODE_TAKEN)
    }

    entity.code = data.code
    entity.name = data.name
    entity.description = data?.description ?? null
    entity.updatedBy = user.id
    entity.baseUnit = data.baseUnit

    await this.repo.update(entity.id, entity)

    return { message: UPDATE_SUCCESS }
  }

  async updateIsDelete(data: FilterOneDto, user: UserDto, req: IRequest) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    entity.isDeleted = !entity.isDeleted
    entity.updatedBy = user.id
    await this.repo.update(entity.id, entity)

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  async findOne(data: FilterOneDto) {
    return await this.repo.findOneBy({ id: data.id })
  }

  public async createDataExcel(data: UnitImportDto[], user: UserDto, req: IRequest) {
    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(UnitEntity)
      const lsCategory: UnitEntity[] = []
      const dicCode: any = {}
      {
        const listBrand: any[] = await repo.find({
          where: { isDeleted: false },
          select: { id: true, code: true },
        })
        listBrand.forEach((c) => (dicCode[c.code] = c))
      }
      const dicCodeFile: any = {}
      for (const [idx, item] of data.entries()) {
        if (dicCodeFile[item.code]) throw new Error(`[ Dòng ${idx + 1} - Mã Đơn vị tính [${item.code}] trùng với dòng ${dicCodeFile[item.code]} ]`)
        if (dicCode[item.code]) throw new Error(`[ Dòng ${idx + 1} - Mã Đơn vị tính [${item.code}] đã được sử dụng ]`)

        const newReason = repo.create({
          ...item,
          createdAt: new Date(),
          createdBy: user.id,
        })
        lsCategory.push(newReason)
        dicCodeFile[item.code] = idx + 1
      }
      await repo.insert(lsCategory)
    })
    return { message: IMPORT_SUCCESS }
  }
}
