import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import { PoScheduleController } from './poSchedule.controller'
import { PoScheduleService } from './poSchedule.service'
import { PoScheduleRepository } from '../../../repositories/core/poSchedule.repository'
import { PoScheduleHistoryRepository } from '../../../repositories/core/poScheduleHistory.repository'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([PoScheduleRepository, PoScheduleHistoryRepository])],
  controllers: [PoScheduleController],
  providers: [PoScheduleService],
  exports: [PoScheduleService],
})
export class PoScheduleModule { }
