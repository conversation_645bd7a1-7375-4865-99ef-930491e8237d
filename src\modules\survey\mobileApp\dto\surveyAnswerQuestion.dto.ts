import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'

export class SurveyAnswerQuestionDto {
  @ApiProperty({ description: 'Id phiếu khảo sát' })
  @IsNotEmpty({ message: 'Id phiếu khảo sát không được trống' })
  surveyId: string

  @ApiProperty({ description: 'Id người làm khảo sát' })
  @IsNotEmpty({ message: 'Id người làm khảo sát không được trống' })
  surveyMemberId: string

  // @ApiProperty({ description: 'Họ và tên người khảo sát' })
  // fullName: string

  // @ApiProperty({ description: 'Số điện thoại người khảo sát' })
  // phone: string

  // @ApiProperty({ description: 'Công ty người khảo sát' })
  // companyId: string

  // @ApiProperty({ description: '<PERSON><PERSON> chú người khảo sát' })
  // note: string

  @ApiProperty({ description: 'thông tin câu trả lời' })
  @IsNotEmpty({ message: 'thông tin câu trả lời không được trống' })
  questionInfo: UserCreateQuestionDto[]
}

export class UserCreateQuestionDto {
  @ApiPropertyOptional({ description: 'Id phiếu khảo sát - câu hỏi' })
  id?: string

  @ApiProperty({ description: 'question Id' })
  @IsString()
  questionId: string

  @ApiProperty({ description: 'Giá trị' })
  @IsString()
  value: string[]
}
