import { CustomRepository } from '../../typeorm'
import { BaseRepository } from '../base.repo'
import { BusinessOrderEntity } from '../../entities/core/business-order.entity'
import { BusinessOrderItemEntity } from '../../entities/core/business-order-item.entity'

@CustomRepository(BusinessOrderEntity)
export class BusinessOrderRepository extends BaseRepository<BusinessOrderEntity> {}

@CustomRepository(BusinessOrderItemEntity)
export class BusinessOrderItemRepository extends BaseRepository<BusinessOrderItemEntity> {}
