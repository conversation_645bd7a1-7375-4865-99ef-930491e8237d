import { Injectable } from '@nestjs/common'
import {
  InboundDetailRepository,
  InboundHistoryRepository,
  InboundRepository,
  OutboundDetailRepository,
  ItemPriceRepository,
  ItemRepository,
  UserRepository,
  WarehouseRepository,
  PurchaseOrderRepository,
  PurchaseOrderItemRepository,
  DeliveryNoteLastMileProductRepository,
  DeliveryNoteChildDetailRepository,
  PurchaseOrderSaleOrderRepository,
  SupplierRepository,
  DeliveryNoteRepository,
  DeliveryNoteChildRepository,
  UnitRepository,
} from '../../../repositories'
import { InboundCreateDto, InboundtApproveDto, InboundtUpdateDescriptionDto, InboundUpdateDto, ReportInboundDto } from './dto'
import { FilterIdDto, PaginationDto, UserDto } from '../../../dto'
import {
  CheckInventoryDetailEntity,
  InboundCostAllocationEntity,
  InboundDetailCostPriceEntity,
  InboundDetailEntity,
  InboundDetailPriceEntity,
  InboundEntity,
  InboundHistoryEntity,
  OutboundDetailEntity,
  ItemDetailEntity,
  ItemEntity,
  ProductInventoryHistoryEntity,
  ItemPriceEntity,
  UnitEntity,
  WarehouseProductDetailEntity,
  WarehouseProductEntity,
} from '../../../entities'
import { Between, EntityManager, Equal, FindOptionsWhere, ILike, In, Like, Not, Raw } from 'typeorm'
import {
  CREATE_SUCCESS,
  ERROR_CODE_GEN_TAKEN,
  ERROR_NOT_FOUND_DATA,
  NSPo,
  NSRecurringOrder,
  NSWarehouse,
  UPDATE_SUCCESS,
  enumData,
} from '../../../constants'
import { coreHelper } from '../../../helpers'
import { Request as IRequest } from 'express'
import * as moment from 'moment'
import { v4 as uuidv4 } from 'uuid'
import { omsApiHelper } from '../../../helpers/omsApiHelper'
// import { InboundCreateFromOutsideDto } from '../warehouseTransfer/dto'

@Injectable()
export class InboundService {
  constructor(
    private repo: InboundRepository,
    private warehouseRepository: WarehouseRepository,
    private detailRepo: InboundDetailRepository,
    private historyRepo: InboundHistoryRepository,
    private productRepository: ItemRepository,
    private userRepo: UserRepository,
    private supplierRepo: SupplierRepository,
    private itemPriceRepo: ItemPriceRepository,
    private unitRepo: UnitRepository,
    private poRepo: PurchaseOrderRepository,
    private poDetailRepo: PurchaseOrderItemRepository,
    private poOrderRepo: PurchaseOrderSaleOrderRepository,
    private readonly deliveryNoteRepo: DeliveryNoteRepository,
    private readonly deliveryNoteChildRepo: DeliveryNoteChildRepository,
    private readonly deliveryNoteLastMileProductRepo: DeliveryNoteLastMileProductRepository,
    private readonly deliveryNoteChildDetailRepo: DeliveryNoteChildDetailRepository,
  ) {}

  /** DS PNK phân trang */
  async pagination(req: IRequest, data: PaginationDto) {
    try {
      const { skip, take, where } = data
      const whereCon: any = { isDeleted: false }
      if (where.poCode) whereCon.poCode = where.poCode
      if (where.code) whereCon.code = where.code
      if (where.poId) whereCon.poId = where.poId
      if (where.warehouseId) whereCon.warehouseId = where.warehouseId
      if (where.createdBy) whereCon.createdBy = where.createdBy

      if (where.isNew) {
        whereCon.status = In([enumData.InboundStatus.New.code, enumData.InboundStatus.Cancel.code])
      } else {
        whereCon.status = enumData.InboundStatus.Approved.code
      }
      if (where.status) whereCon.status = where.status
      if (where.warehouseCode) {
        const warehouse = await this.warehouseRepository.findOne({ where: { code: where.warehouseCode } })
        if (warehouse) {
          whereCon.warehouseId = warehouse.id
        }
      }
      if (where.storeId) {
        const warehouse = await this.warehouseRepository.findOne({ where: { storeId: where.storeId } })
        if (warehouse) {
          whereCon.warehouseId = warehouse.id
        }
      }
      // Cho màn hình lấy lịch sử
      if (where.history === true) {
        if (typeof where.type == 'string') {
          whereCon.type = where.type
        } else {
          whereCon.type = In([
            enumData.InboundType.CHECK_INVENTORY.code,
            enumData.InboundType.COMBO.code,
            enumData.InboundType.ENTER_INTERNAL_WAREHOUSE.code,
            enumData.InboundType.WAREHOUSE_TRANSFER.code,
          ])
        }
      }

      if (where.createdAt && where.createdAt.length > 0) {
        whereCon.createdAt = Raw(
          (alias) =>
            `DATE(${alias}) BETWEEN DATE("${moment(where.createdAt[0]).format('YYYY-MM-DD')}") AND DATE("${moment(where.createdAt[1]).format(
              'YYYY-MM-DD',
            )}")`,
        )
      }

      // Tìm PNX bằng id sản phẩm
      if (where.productId) {
        const lstProduct = await this.detailRepo.find({
          where: { productId: where.productId, isDeleted: false },
        })
        if (lstProduct.length === 0) return { data: [], total: 0 }
        const listId = lstProduct.map((x) => x.inboundId)
        whereCon.id = In(listId)
      }

      // Tìm bằng thương hiệu
      if (where.brandId) {
        whereCon.details = {}
        whereCon.details.product = {}
        whereCon.details.product.brandId = Like(`%${where.brandId}%`)
      }

      const [lst, total]: any = await this.repo.findAndCount({
        where: whereCon,
        order: { createdAt: 'DESC' },
        relations: { warehouse: true },
        skip,
        take,
      })

      if (lst.length == 0) {
        return { data: [], total: 0 }
      }

      for (let item of lst) {
        item.warehouseName = item.__warehouse__?.name
        item.typeName = enumData.InboundType[item.type]?.name
        item.statusName = enumData.InboundStatus[item.status]?.name
        item.statusColor = enumData.InboundStatus[item.status]?.color
        item.colorText = enumData.InboundStatus[item.status]?.colorText
        item.transportTypeName = enumData.TransportType[item.transportType]?.name

        delete item.__warehouse__
      }

      const listMemCreateIds = lst.map((val) => val.createdBy)
      const memberCreate: any[] = (await omsApiHelper.getMemberByListId(req, listMemCreateIds)) || []

      const listMemApproveIds = lst.map((val) => val.approvedBy)
      const memberApprove: any[] = (await omsApiHelper.getMemberByListId(req, listMemApproveIds)) || []

      const warehouses = await this.warehouseRepository.find({ where: { type: NSWarehouse.EWarehouseType['3PL'] } })

      const supplier = await this.supplierRepo.find({ where: { id: In(warehouses.map((m) => m.storeId)) } })

      const mappingResult = lst.map((val) => {
        let memCreate: any
        let memApproved: any
        const wh = warehouses.find((w) => w.id === val.warehouseId) // Là kho 3PL

        if (wh) {
          memCreate = supplier.find((s) => s.id === val.createdBy)?.name
          memApproved = supplier.find((s) => s.id === val.approvedBy)?.name
        } else {
          memCreate = memberCreate.find((m: any) => m.id === val.createdBy)?.fullName
          memApproved = memberApprove.find((m: any) => m.id === val.approvedBy)?.fullName
        }

        return {
          ...val,
          createdByName: memCreate,
          approvedByName: memApproved,
        }
      })

      return { data: mappingResult, total: total }
    } catch (error) {
      throw new Error('Lỗi lấy danh sách PNX')
    }
  }

  async findProductLotNumberByExpiryDate(data: { productId: string; expiryDate: Date }) {
    // Lấy đầu ngày
    const firstDay = new Date(new Date(data.expiryDate).setHours(0, 0, 0, 0))
    // Lấy cuối ngày
    const lastDay = new Date(new Date(data.expiryDate).setHours(23, 59, 59, 99))

    const findInboundDetail = await this.detailRepo.find({
      where: {
        productId: data.productId,
        expiryDate: Raw(
          (alias) =>
            `(${alias}) BETWEEN ("${moment(firstDay).format('YYYY-MM-DD HH:mm:ss')}") AND ("${moment(lastDay).format('YYYY-MM-DD HH:mm:ss')}")`,
        ),
      },
    })

    return findInboundDetail
  }

  /** Tạo PNK */
  async createData(data: InboundCreateDto, req: IRequest) {
    let poDetailLst = []
    if (data.poId) {
      const checkPo = await this.poRepo.findOne({ where: { id: data.poId } })
      if (!checkPo) throw new Error(`PO không còn tồn tại !`)
      poDetailLst = await this.poDetailRepo.find({ where: { purchaseOrderId: data.poId } })
      if (poDetailLst.length === 0) throw new Error(`PO không có sản phẩm !`)
    }

    if (!data.isFromDeliveryNote) {
      for (let item of data.lstDetail) {
        if (!item.isCombo) {
          if (!item.expiryDate) throw new Error(`Hạn sử dụng không được để trống !`)
          if (!item.manufactureDate) throw new Error(`Ngày sản xuất không được để trống !`)
        }
        // if (!item.lotNumber) throw new Error(`Số lô không được để trống !`)
        // if (!item.expiryDate) throw new Error(`Hạn sử dụng không được để trống !`)
        // if (!item.manufactureDate) throw new Error(`Ngày sản xuất không được để trống !`)
      }
    }

    return this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(InboundEntity)
      const detailRepo = trans.getRepository(InboundDetailEntity)
      const historyRepo = trans.getRepository(InboundHistoryEntity)

      const unitRepo = trans.getRepository(UnitEntity)

      // Thêm mới InBound
      const code = await this.codeDefault()
      // Kiểm tra code đã tồn tại chưa
      const checkCodeExist = await repo.findOne({ where: { code }, select: { id: true } })
      if (checkCodeExist) throw new Error(ERROR_CODE_GEN_TAKEN)

      const inboundNew = new InboundEntity()
      inboundNew.poId = data.poId
      inboundNew.files = data.files || null
      // inboundNew.poCode = po.code
      inboundNew.warehouseId = data.warehouseId
      inboundNew.code = code
      inboundNew.poCode = data.poCode
      inboundNew.currencyCode = data.currencyCode
      inboundNew.exchangeRate = +data.exchangeRate || 1
      inboundNew.totalPrice = 0 // tính sau
      inboundNew.totalPriceVND = 0 // tính sau
      inboundNew.status = enumData.InboundStatus.New.code
      inboundNew.transportType = data.transportType
      inboundNew.description = data.description
      inboundNew.createdBy = data.createBy
      inboundNew.createdAt = new Date()
      inboundNew.updatedAt = new Date()
      inboundNew.id = uuidv4()
      inboundNew.deliveryNoteLmCode = data.deliveryNoteLmCode
      await repo.insert(inboundNew)

      // Lưu lại lịch sử inbound
      let arr = new Array()
      // Lọc qua danh sách detail và tạo detail
      for (let item of data.lstDetail) {
        const unitObj: any = await unitRepo.findOne({ where: { id: item.unitId, isDeleted: false } })
        if (!unitObj) throw new Error(`Đơn vị tính không còn tồn tại!`)

        // const checkItemPrice = await this.itemPriceRepo.findOne({ where: { isDeleted: false, itemId: item.productId } })
        // const inboundItemPrice = await this.detailRepo.find({ where: { productId: item.productId }, select: { price: true, quantity: true } })
        // const totalPriceQuantity =
        //  inboundItemPrice.reduce((sum, current) => sum + (current.price * current.quantity || 0), 0) + item.price * item.quantity

        // const totalQuantity = inboundItemPrice.reduce((sum, current) => sum + (current.quantity || 0), 0) + item.quantity

        // Tính trung bình giá
        // const averagePrice = totalQuantity > 0 ? totalPriceQuantity / totalQuantity : 0
        // if (checkItemPrice) {
        //   checkItemPrice.priceInput = +item.price || 0
        //   checkItemPrice.priceCapital = averagePrice
        //   checkItemPrice.updatedBy = data.createBy
        //   checkItemPrice.updatedAt = new Date()
        //   this.itemPriceRepo.save(checkItemPrice)
        // } else {
        //   const itemPriceNew = new ItemPriceEntity()
        //   itemPriceNew.itemId = item.productId
        //   itemPriceNew.priceInput = item.price
        //   itemPriceNew.priceCapital = item.price
        //   this.itemPriceRepo.save(itemPriceNew)
        // }
        const inboundDetailNew = new InboundDetailEntity()
        inboundDetailNew.inboundId = inboundNew.id
        inboundDetailNew.poDetailId = item.poDetailId
        inboundDetailNew.productId = item.productId
        inboundDetailNew.productCode = item.productCode
        inboundDetailNew.productName = item.productName
        inboundDetailNew.unitId = item.unitId
        inboundDetailNew.baseUnit = unitObj.baseUnit || 0
        inboundDetailNew.unitName = unitObj.name
        inboundDetailNew.unitCode = unitObj.code
        inboundDetailNew.code = code
        inboundDetailNew.quantity = +item.quantity || 0
        inboundDetailNew.totalQuantity = +item.quantity || 0
        inboundDetailNew.price = +item.price || 0
        inboundDetailNew.priceVND = +item.priceVND
        inboundDetailNew.manufactureDate = item.manufactureDate
        inboundDetailNew.expiryDate = item.expiryDate
        inboundDetailNew.description = item.description
        //inboundDetailNew.totalPrice = +inboundDetailNew.price * +inboundDetailNew.quantity
        //inboundDetailNew.totalPrice = +item.buyPrice * +item.quantity
        inboundDetailNew.totalPrice = +item.buyPrice * +item.quantity // Thành tiền giá mua
        inboundDetailNew.totalPriceVND = +item.price * +item.quantity // Thành tiền giá bán chung
        inboundDetailNew.lotNumber = item.lotNumber
        inboundDetailNew.transportType = data.transportType

        if (data.poId) {
          //const poDetail = poDetailLst.find((c) => c.id == item.poDetailId)
          //if (!poDetail) throw new Error(`Có sản phẩm không còn tồn tại trong PO ${item.poDetailId}!`)
          inboundDetailNew.buyPrice = +item.buyPrice || 0
          inboundDetailNew.buyPriceVND = +item.buyPriceVND || 0
        }

        inboundDetailNew.createdBy = data.createBy
        await detailRepo.insert(inboundDetailNew)

        arr.push({
          'Mã sản phẩm': inboundDetailNew.productCode,
          'Tên sản phẩm': inboundDetailNew.productName,
          'Giá mua': inboundDetailNew.buyPriceVND.toLocaleString('vi', { style: 'currency', currency: 'VND' }),
          'Giá nhập': inboundDetailNew.priceVND.toLocaleString('vi', { style: 'currency', currency: 'VND' }),
          'Số lượng nhập': inboundDetailNew.quantity,
          'Số lượng nhập theo đvcs': inboundDetailNew.totalQuantity,
          'Hạn sử dụng': inboundDetailNew.expiryDate ? moment(inboundDetailNew.expiryDate).format('YYYY-MM-DD') : '',
          'Ngày sản xuất': inboundDetailNew.manufactureDate ? moment(inboundDetailNew.manufactureDate).format('YYYY-MM-DD') : '',
        })

        inboundNew.totalPrice = +inboundNew.totalPrice + Number(inboundDetailNew.totalPrice)
        inboundNew.totalPriceVND = +inboundNew.totalPriceVND + Number(inboundDetailNew.totalPriceVND)
      }

      // Cập nhật lại tổng giá trị nhập cho PNK
      await repo.update(inboundNew.id, { totalPrice: inboundNew.totalPrice, totalPriceVND: inboundNew.totalPriceVND })

      const member: any = await omsApiHelper.getMemberByListId(req, [data.createBy])

      const history = new InboundHistoryEntity()
      history.inboundId = inboundNew.id
      history.createdBy = data.createBy
      history.createdAt = inboundNew.createdAt
      history.createdByName = member[0]?.fullName

      let description = {
        'Thêm mới phiếu nhập kho. Thao tác chi tiết': {
          'Mã phiếu nhập kho': `${inboundNew.code}`,
          'Mã PO': `${inboundNew.poCode}`,
          'Thời gian tạo': `${moment(inboundNew.createdAt).format('YYYY-MM-DD HH:mm:ss')}`,
          'Người tạo': `${member[0]?.fullName}`,
          'Thông tin sản phẩm': arr,
          'IP Request': req.headers['x-forwarded-for'] || req.socket.remoteAddress || null,
        },
      }

      history.description = JSON.stringify(description)
      await historyRepo.insert(history)

      return { message: CREATE_SUCCESS, data: inboundNew }
    })
  }

  /** Tạo PNK từ phiếu chuyển kho */
  // async createDataApproved(user: UserDto, data: InboundCreateFromOutsideDto, req: IRequest, manager: EntityManager) {
  async createDataApproved(data: any, req: IRequest, manager: EntityManager) {
    return manager.transaction(async (trans) => {
      const repo = trans.getRepository(InboundEntity)
      const detailRepo = trans.getRepository(InboundDetailEntity)
      const historyRepo = trans.getRepository(InboundHistoryEntity)
      const unitRepo = trans.getRepository(UnitEntity)

      // Thêm mới InBound
      const code = await this.codeDefault()
      // Kiểm tra code đã tồn tại chưa
      const checkCodeExist = await repo.findOne({ where: { code }, select: { id: true } })
      if (checkCodeExist) throw new Error(ERROR_CODE_GEN_TAKEN)

      const inboundNew = new InboundEntity()
      inboundNew.id = uuidv4()
      inboundNew.warehouseId = data.warehouseId
      inboundNew.warehouseTransferId = data?.warehouseTransferId ?? null
      inboundNew.checkInventoryId = data?.checkInventoryId ?? null
      inboundNew.type = data.type
      inboundNew.code = code
      inboundNew.totalPrice = 0
      inboundNew.totalPriceVND = 0
      inboundNew.status = enumData.InboundStatus.Approved.code
      inboundNew.createdBy = data.createBy
      inboundNew.createdAt = new Date()
      inboundNew.approvedBy = data.createBy
      inboundNew.approvedDate = new Date()
      await repo.insert(inboundNew)

      // Lưu lại lịch sử inbound
      let arr = new Array()

      // Lọc qua danh sách detail và tạo detail
      for (let item of data.lstDetail) {
        const unitObj: any = await unitRepo.findOne({ where: { id: item.unitId, isDeleted: false } })
        if (!unitObj) throw new Error(`Đơn vị tính không còn tồn tại!`)

        const inboundDetailNew = new InboundDetailEntity()
        inboundDetailNew.inboundId = inboundNew.id
        inboundDetailNew.productId = item.productId
        inboundDetailNew.productCode = item.productCode
        inboundDetailNew.productName = item.productName
        inboundDetailNew.unitId = item.unitId
        inboundDetailNew.baseUnit = unitObj.baseUnit || 0
        inboundDetailNew.code = code
        inboundDetailNew.costPrice = +item.costPrice || 0
        inboundDetailNew.quantity = +item.quantity || 0
        inboundDetailNew.totalQuantity = 0
        inboundDetailNew.price = 0
        inboundDetailNew.priceVND = 0
        inboundDetailNew.expiryDate = item?.expiryDate ? new Date(item.expiryDate) : null
        inboundDetailNew.manufactureDate = item?.manufactureDate ? new Date(item.manufactureDate) : null
        inboundDetailNew.totalPrice = +inboundDetailNew.price * +inboundDetailNew.quantity
        inboundDetailNew.totalPriceVND = 0
        inboundDetailNew.lotNumber = item.lotNumber
        inboundDetailNew.buyPrice = 0
        inboundDetailNew.buyPriceVND = 0
        inboundDetailNew.createdBy = data.createBy
        await detailRepo.insert(inboundDetailNew)

        arr.push({
          'Mã sản phẩm': inboundDetailNew.productCode,
          'Tên sản phẩm': inboundDetailNew.productName,
          'Giá mua': inboundDetailNew.buyPriceVND.toLocaleString('vi', { style: 'currency', currency: 'VND' }),
          'Giá nhập': inboundDetailNew.priceVND.toLocaleString('vi', { style: 'currency', currency: 'VND' }),
          'Số lượng nhập': inboundDetailNew.quantity,
          'Số lượng nhập theo đvcs': inboundDetailNew.totalQuantity,
          'Hạn sử dụng': inboundDetailNew.expiryDate ? moment(inboundDetailNew.expiryDate).format('YYYY-MM-DD') : '',
          'Ngày sản xuất': inboundDetailNew.manufactureDate ? moment(inboundDetailNew.manufactureDate).format('YYYY-MM-DD') : '',
        })
      }

      const member: any = await omsApiHelper.getMemberByListId(req, [data.createBy])

      const history = new InboundHistoryEntity()
      history.inboundId = inboundNew.id
      history.createdBy = data.createBy
      history.createdByName = member[0]?.fullName

      let description = {
        'Thêm mới phiếu nhập kho đã duyệt. Thao tác chi tiết': {
          'Mã phiếu nhập kho': `${inboundNew.code}`,
          'Thời gian tạo': `${moment(inboundNew.createdAt).format('YYYY-MM-DD HH:mm:ss')}`,
          'Người tạo': `${member[0]?.fullName}`,
          'Thông tin sản phẩm': arr,
          'IP Request': req.headers['x-forwarded-for'] || req.socket.remoteAddress || null,
        },
      }

      history.description = JSON.stringify(description)
      await historyRepo.insert(history)

      return { message: CREATE_SUCCESS }
    })
  }

  public async codeDefault() {
    const code = 'NK' + '_' + moment(new Date()).format('DDMMYYYY') + '_'
    const objData = await this.repo.findOne({
      where: { code: Like(`%${code}%`) },
      order: { code: 'DESC' },
    })
    let sortString = '0'
    if (objData) {
      sortString = objData.code.substring(code.length, code.length + 4)
    }
    const lastSort = parseInt(sortString)
    sortString = ('000' + (lastSort + 1)).slice(-4)

    return code + sortString
  }

  /** Sửa PNK */
  async updateData(data: InboundUpdateDto, req: IRequest) {
    const inbound: any = await this.repo.findOne({ where: { id: data.id } })
    if (!inbound) throw new Error(`Phiếu nhập kho không còn tồn tại!`)

    if (inbound.status != enumData.InboundStatus.New.code) {
      throw new Error(`Phiếu nhập kho ở trạng thái ${enumData.InboundStatus[inbound.status]?.name}, không được chỉnh sửa!`)
    }

    // const po: any = await authApiHelper.findOnePo(req, { poId: data.poId })
    // if (!po) throw new Error(`PO không còn tồn tại!`)
    for (let item of data.lstDetail) {
      if (!item.isCombo) {
        if (!item.expiryDate) throw new Error(`Hạn sử dụng không được để trống !`)
        if (!item.manufactureDate) throw new Error(`Ngày sản xuất không được để trống !`)
      }
      // if (!item.lotNumber) throw new Error(`Số lô không được để trống !`)
      // if (!item.expiryDate) throw new Error(`Hạn sử dụng không được để trống !`)
      // if (!item.manufactureDate) throw new Error(`Ngày sản xuất không được để trống !`)
    }
    return this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(InboundEntity)
      const detailRepo = trans.getRepository(InboundDetailEntity)
      const historyRepo = trans.getRepository(InboundHistoryEntity)
      const unitRepo = trans.getRepository(UnitEntity)
      inbound.poId = data.poId
      // inbound.poCode = po.code
      inbound.warehouseId = data.warehouseId
      inbound.currencyCode = data.currencyCode
      inbound.exchangeRate = +data.exchangeRate || 0
      inbound.totalPrice = 0 // tính sau
      inbound.totalPriceVND = 0 // tính sau
      inbound.status = enumData.InboundStatus.New.code
      inbound.transportType = data.transportType
      inbound.description = data.description
      inbound.files = data.files
      inbound.updatedAt = new Date()
      inbound.updatedBy = data.updateBy

      await repo.save(inbound)

      // xóa data cũ để tạo lại
      await detailRepo.delete({ inboundId: inbound.id })

      // lưu lại lịch sử inbound
      let arr = new Array()
      //Lọc qua danh sách detail và tạo detail
      for (let item of data.lstDetail) {
        const checkItemPrice = await this.itemPriceRepo.findOne({ where: { isDeleted: false, itemId: item.productId } })
        const inboundItemPrice = await this.detailRepo.find({ where: { productId: item.productId }, select: { price: true, quantity: true } })
        const totalPriceQuantity =
          inboundItemPrice.reduce((sum, current) => sum + (current.price * current.quantity || 0), 0) + item.price * item.quantity

        const totalQuantity = inboundItemPrice.reduce((sum, current) => sum + (current.quantity || 0), 0) + item.quantity

        // Tính trung bình giá
        const averagePrice = totalQuantity > 0 ? totalPriceQuantity / totalQuantity : 0
        if (checkItemPrice) {
          checkItemPrice.priceInput = +item.price || 0
          checkItemPrice.priceCapital = averagePrice
          checkItemPrice.updatedBy = data.updateBy
          checkItemPrice.updatedAt = new Date()
          this.itemPriceRepo.save(checkItemPrice)
        } else {
          const itemPriceNew = new ItemPriceEntity()
          itemPriceNew.itemId = item.productId
          itemPriceNew.priceInput = item.price
          itemPriceNew.priceCapital = item.price
          this.itemPriceRepo.save(itemPriceNew)
        }

        const unitObj: any = await unitRepo.findOne({ where: { id: item.unitId, isDeleted: false } })
        if (!unitObj) throw new Error(`Đơn vị tính không còn tồn tại!`)
        const inboundDetailNew = new InboundDetailEntity()
        inboundDetailNew.inboundId = inbound.id
        inboundDetailNew.poDetailId = item.poDetailId
        inboundDetailNew.productId = item.productId
        inboundDetailNew.productCode = item.productCode
        inboundDetailNew.productName = item.productName
        inboundDetailNew.unitId = item.unitId
        inboundDetailNew.baseUnit = unitObj.baseUnit || 0
        inboundDetailNew.unitCode = unitObj.unitCode
        inboundDetailNew.unitName = unitObj.unitName
        inboundDetailNew.code = inbound.code
        inboundDetailNew.quantity = +item.quantity || 0
        // inboundDetailNew.totalQuantity = inboundDetailNew.quantity * inboundDetailNew.baseUnit // Code Busan
        inboundDetailNew.totalQuantity = inboundDetailNew.quantity // Bala
        inboundDetailNew.price = +item.price || 0
        inboundDetailNew.priceVND = inboundDetailNew.price * inbound.exchangeRate
        inboundDetailNew.manufactureDate = item.manufactureDate
        inboundDetailNew.expiryDate = item.expiryDate
        inboundDetailNew.description = item.description
        inboundDetailNew.totalPrice = inboundDetailNew.price * inboundDetailNew.quantity
        inboundDetailNew.totalPriceVND = inboundDetailNew.priceVND * inboundDetailNew.quantity
        inboundDetailNew.lotNumber = item.lotNumber
        inboundDetailNew.transportType = data.transportType
        // const poDetail = po.lstDetail.find((c) => c.id == item.poDetailId)
        // if (!poDetail) throw new Error(`Sản phẩm không còn tồn tại trong PO!`)

        inboundDetailNew.buyPrice = +item.buyPrice || 0
        inboundDetailNew.buyPriceVND = +item.buyPriceVND || 0
        inboundDetailNew.createdAt = new Date()
        inboundDetailNew.createdBy = data.updateBy

        await detailRepo.insert(inboundDetailNew)

        arr.push({
          'Mã sản phẩm': inboundDetailNew.productCode,
          'Tên sản phẩm': inboundDetailNew.productName,
          'Giá mua': inboundDetailNew.buyPriceVND.toLocaleString('vi', { style: 'currency', currency: 'VND' }),
          'Giá nhập': inboundDetailNew.priceVND.toLocaleString('vi', { style: 'currency', currency: 'VND' }),
          'Số lượng nhập': inboundDetailNew.quantity,
          'Số lượng nhập theo đvcs': inboundDetailNew.totalQuantity,
          'Hạn sử dụng': inboundDetailNew.expiryDate ? moment(inboundDetailNew.expiryDate).format('YYYY-MM-DD') : '',
          'Ngày sản xuất': inboundDetailNew.manufactureDate ? moment(inboundDetailNew.manufactureDate).format('YYYY-MM-DD') : '',
        })

        inbound.totalPrice = +inbound.totalPrice + Number(inboundDetailNew.totalPrice)
        inbound.totalPriceVND = +inbound.totalPriceVND + Number(inboundDetailNew.totalPriceVND)
      }

      // Cập nhật lại tổng giá trị nhập cho PNK
      await repo.update(inbound.id, { totalPrice: inbound.totalPrice, totalPriceVND: inbound.totalPriceVND })

      const member: any = await omsApiHelper.getMemberByListId(req, [data.updateBy])

      const history = new InboundHistoryEntity()
      history.inboundId = inbound.id
      history.createdBy = data.updateBy
      history.createdAt = inbound.updatedAt
      history.createdByName = member[0]?.fullName

      let description = {
        'Cập nhật phiếu nhập kho. Thao tác chi tiết': {
          'Mã phiếu nhập kho': `${inbound.code}`,
          'Mã PO': `${inbound.poCode}`,
          'Thời gian cập nhật': `${moment(inbound.updatedAt).format('YYYY-MM-DD HH:mm:ss')}`,
          'Người cập nhật': `${member[0]?.fullName}`,
          'Thông tin sản phẩm': arr,
          'IP Request': req.headers['x-forwarded-for'] || req.socket.remoteAddress || null,
        },
      }

      history.description = JSON.stringify(description)
      await historyRepo.insert(history)

      return { message: UPDATE_SUCCESS }
    })
  }

  /** Duyệt PNK */
  async approveData(data: InboundtApproveDto, req: IRequest) {
    const inbound = await this.repo.findOne({ where: { id: data.id } })
    if (!inbound) throw new Error(`Phiếu nhập kho không còn tồn tại!`)

    const warehouseId = inbound.warehouseId
    if (inbound.status != enumData.InboundStatus.New.code) {
      throw new Error(`Phiếu nhập kho ở trạng thái ${enumData.InboundStatus[inbound.status]?.name}, không được thao tác!`)
    }

    const lstDetail = await this.detailRepo.find({ where: { inboundId: data.id, isDeleted: false }, order: { expiryDate: 'ASC' } })
    if (lstDetail.length == 0) throw new Error(`Phiếu nhập kho không có sản phẩm, không được thao tác!`)
    const lstDetailIds = lstDetail.map((x) => x.productId)
    // for (let item of lstDetail) {
    //   // if (!item.expiryDate) throw new Error(`Hạn sử dụng không được để trống !`)
    //   //if (!item.manufactureDate) throw new Error(`Ngày sản xuất không được để trống !`)
    // }

    const warehouse = await this.warehouseRepository.findOne({ where: { id: inbound.warehouseId } }) // Lấy ra kho của Inbound
    const storeId = warehouse.storeId // MBC ID
    const poId = inbound.poId
    // Lấy thông tin PO
    const po = await this.poRepo.findOne({ where: { id: poId } })
    // Gửi thông báo đến member với thông tin từ SO hàng đã nhập kho
    if (poId) {
      if (!inbound.files) {
        throw new Error(`Vui lòng cập nhật chứng từ đặt hàng`)
      } else {
        // Lấy ra danh sách sản phẩm trong phiếu giao nhận
        const lmDeliveryNotes = await this.deliveryNoteLastMileProductRepo.find({ where: { productId: In(lstDetailIds), poId } })

        for (let i = 0; i < lstDetail.length; i++) {
          const productDetail = lstDetail[i]
          // Lấy ra danh sách phiếu giao nhận của sản phẩm trong phiếu giao nhận
          const lmDeliveryNote = lmDeliveryNotes.filter((x) => x.productId == productDetail.productId)
          const lstLmDeliveryNoteIds = lmDeliveryNote.map((x) => x.deliveryNoteChildDetailId)
          const quantity = productDetail.quantity

          // Lấy ra danh sách phiếu giao nhận của sản phẩm
          const lmdDetails = await this.deliveryNoteChildDetailRepo.find({ where: { id: In(lstLmDeliveryNoteIds), partnerId: storeId } })

          for (let y = 0; y < lmDeliveryNote.length; y++) {
            const lmd = lmDeliveryNote[y]
            const lmdDetail = lmdDetails.find((x) => x.id == lmd.deliveryNoteChildDetailId)
            if (lmdDetail) {
              // Check quantity lmdDetail
              const dlnLastmile = await this.deliveryNoteLastMileProductRepo.findOne({
                where: {
                  id: lmd.id,
                  productId: productDetail.productId,
                  poId,
                },
              })
              if (+dlnLastmile.quantityBegin < +quantity + +lmd.quantityExport) {
                throw new Error(`Số lượng sản phẩm [${productDetail.productName}] trong phiếu nhập kho vượt quá số lượng phiếu giao nhận!`)
              }

              await this.deliveryNoteLastMileProductRepo.update(
                {
                  id: lmd.id,
                  productId: productDetail.productId,
                  poId,
                },
                {
                  quantityExport: Number(quantity) + Number(lmd.quantityExport),
                },
              )

              const countLM = await this.deliveryNoteLastMileProductRepo.count({
                where: {
                  deliveryNoteChildDetailId: dlnLastmile.deliveryNoteChildDetailId,
                },
              })

              const checkFull = await this.deliveryNoteLastMileProductRepo.count({
                where: {
                  deliveryNoteChildDetailId: dlnLastmile.deliveryNoteChildDetailId,
                  quantityBegin: Raw((alias) => `${alias} = "quantityExport"`),
                },
              })

              if (checkFull == countLM) {
                await this.deliveryNoteChildDetailRepo.update(
                  { id: dlnLastmile.deliveryNoteChildDetailId },
                  { status: NSPo.EDeliveryNoteInboundStatus.INSTOCK },
                )

                await this.deliveryNoteChildRepo.update({ id: lmdDetail.deliveryNoteChildId }, { status: NSPo.EDeliveryNoteInboundStatus.INSTOCK })

                if (warehouse.type === NSWarehouse.EWarehouseType.MBC) {
                  // Nhập đủ số lượng thì gửi thông báo đến member hàng đã nhập kho
                  const orders = await this.poOrderRepo.find({ where: { poId } })
                  // Kiểm tra order còn tham chiếu cho PO nào khác không
                  const checkOrder = await this.poOrderRepo.find({ where: { soId: In(orders.map((o) => o.soId)), poId: Not(poId) } })

                  if (checkOrder.length > 0) {
                    // Lấy ra danh sách PO trừ poId của orders
                    const poIds = checkOrder.map((o) => o.poId)
                    // Lấy ra danh sách phiếu giao nhận con
                    const deliveryNotes = await this.deliveryNoteRepo.find({
                      where: {
                        poId: Raw((alias) => `${alias} ?| array[${poIds.map((id) => `'${id}'`).join(',')}]`),
                      },
                    })
                    // Lấy ra danh sách phiếu giao nhận con
                    const deliveryNotesChild = await this.deliveryNoteChildRepo.find({
                      where: { deliveryNoteId: In(deliveryNotes.map((d) => d.id)) },
                    })
                    const deliveryNotesChild_InStock = await this.deliveryNoteChildRepo.find({
                      where: {
                        deliveryNoteId: In(deliveryNotes.map((d) => d.id)),
                        status: NSPo.EDeliveryNoteInboundStatus.INSTOCK,
                      },
                    })

                    if (deliveryNotesChild.length == deliveryNotesChild_InStock.length) {
                      // Tổng hợp orderIds của orders và checkOrder
                      const orderInPo = (await this.poOrderRepo.find({ where: { poId: In(poIds) } })).map((o) => o.soId)
                      const orderIds = [...orders.map((o) => o.soId), ...orderInPo]
                      // Loại bỏ trùng lặp
                      const orderIdsUnique = Array.from(new Set(orderIds))

                      // Gửi thông báo đến member hàng đã nhập kho
                      // Nhập kho MBC thì mới bắn Noti
                      if (orderIdsUnique.length > 0) {
                        await Promise.all([
                          omsApiHelper.updateSOStatus(req, { ids: orderIdsUnique, status: NSRecurringOrder.EStatus.DELIVERING }),
                          omsApiHelper.sendNotificationOrder(req, { ids: orderIdsUnique }),
                        ])
                      }
                    }
                  } else {
                    // Gửi thông báo đến member hàng đã nhập kho
                    // Nhập kho MBC thì mới bắn Noti
                    if (orders.length > 0) {
                      await Promise.all([
                        omsApiHelper.updateSOStatus(req, { ids: orders.map((o) => o.soId), status: NSRecurringOrder.EStatus.DELIVERING }),
                        omsApiHelper.sendNotificationOrder(req, { ids: orders.map((o) => o.soId) }),
                      ])
                    }
                  }
                }
              } else {
                await this.deliveryNoteChildDetailRepo.update(
                  { id: dlnLastmile.deliveryNoteChildDetailId },
                  { status: NSPo.EDeliveryNoteInboundStatus.PARTIALLY_INSTOCK },
                )
              }
            }
          }
        }
      }
    }

    const currentTime = new Date()
    return this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(InboundEntity)
      const historyRepo = trans.getRepository(InboundHistoryEntity)
      const detailRepo = trans.getRepository(InboundDetailEntity)
      const warehouseProductRepo = trans.getRepository(WarehouseProductEntity)
      const warehouseProductDetailRepo = trans.getRepository(WarehouseProductDetailEntity)
      const productInventoryHistoryRepo = trans.getRepository(ProductInventoryHistoryEntity)
      const productRepo = trans.getRepository(ItemEntity)
      const detailProductRepo = trans.getRepository(ItemDetailEntity)
      const productPriceRepo = trans.getRepository(ItemPriceEntity)
      const inboundDetailCostPriceRepo = trans.getRepository(InboundDetailCostPriceEntity)

      const checkInvetory = trans.getRepository(CheckInventoryDetailEntity)

      for (let item of lstDetail) {
        let whereCon: any = {}
        whereCon.checkInventory = {}
        whereCon.checkInventory.status = enumData.CheckInventoryStatus.NEW.code
        whereCon.productId = item.productId
        whereCon.isDeleted = false
        const checkPKK = await checkInvetory.findOne({ where: whereCon })
        if (checkPKK) throw new Error(`Không thể duyệt phiếu nhập kho vì có phiếu kiểm kho đang chờ duyệ!`)
      }

      // Cập nhật trạng thái PNK
      await repo.update(data.id, {
        status: enumData.InboundStatus.Approved.code,
        updatedAt: currentTime,
        updatedBy: data.approveBy,
        approvedBy: data.approveBy,
        approvedDate: currentTime,
      })

      const member: any = await omsApiHelper.getMemberByListId(req, [data.approveBy])

      const history = new InboundHistoryEntity()
      history.inboundId = inbound.id
      history.createdBy = data.approveBy
      history.createdAt = currentTime
      history.createdByName = member[0]?.fullName

      let description = {
        'Duyệt phiếu nhập kho. Thao tác chi tiết': {
          'Mã phiếu nhập kho': `${inbound.code}`,
          'Mã PO': `${inbound.poCode}`,
          'Thời gian duyệt': `${moment(currentTime).format('YYYY-MM-DD HH:mm:ss')}`,
          'Người duyệt': `${member[0]?.fullName}`,
          'IP Request': req.headers['x-forwarded-for'] || req.socket.remoteAddress || null,
        },
      }

      history.description = JSON.stringify(description)
      await historyRepo.insert(history)

      const quantityNew = coreHelper.selectSum(lstDetail, 'quantity')
      const lstProductWH = lstDetail.mapAndDistinct((c) => c.productId)
      let quantityOld: any = 0
      let lstOldWh = await warehouseProductRepo.find({ where: { warehouseId: warehouseId, productId: In(lstProductWH) } })
      if (lstOldWh.length > 0) {
        quantityOld = coreHelper.selectSum(lstOldWh, 'quantity')
      }

      /** Cập nhật sản lượng tồn sp trong kho */
      for (const detail of lstDetail) {
        let warehouseProduct: any = await warehouseProductRepo.findOne({
          where: { warehouseId: warehouseId, productId: detail.productId },
          select: ['id', 'quantity', 'quantityImport'],
        })
        if (!warehouseProduct) {
          const warehouseProductNew = new WarehouseProductEntity()
          warehouseProductNew.warehouseId = warehouseId
          warehouseProductNew.productId = detail.productId
          warehouseProductNew.productCode = detail.productCode
          warehouseProductNew.productName = detail.productName
          warehouseProductNew.quantity = detail.quantity
          warehouseProductNew.quantityImport = detail.quantity
          warehouseProductNew.createdAt = new Date()
          warehouseProductNew.createdBy = data.approveBy
          warehouseProduct = await warehouseProductRepo.save(warehouseProductNew)
        } else {
          const quantityOld = +warehouseProduct.quantity
          const quantityNew = quantityOld + +detail.totalQuantity
          warehouseProduct.quantity = quantityNew
          warehouseProduct.quantityImport += detail.totalQuantity
          warehouseProduct = await warehouseProductRepo.save(warehouseProduct)
        }

        const dateStr = moment(detail.expiryDate).format('YYYY-MM-DD')
        const ds = new Date(new Date(detail.expiryDate).setHours(0, 0, 0, 0))
        const de = new Date(new Date(detail.expiryDate).setHours(23, 59, 59, 59))

        const dsManu = new Date(new Date(detail.manufactureDate).setHours(0, 0, 0, 0))
        const deManu = new Date(new Date(detail.manufactureDate).setHours(23, 59, 59, 59))

        let warehouseProductDetail: any = await warehouseProductDetailRepo.findOne({
          where: {
            warehouseId: warehouseId,
            productId: detail.productId,
            expiryDate: Between(ds, de),
            lotNumber: detail.lotNumber,
            manufactureDate: Between(dsManu, deManu),
          },
          select: ['id', 'quantity', 'quantityImport'],
        })
        if (!warehouseProductDetail) {
          const warehouseProductDetailNew = new WarehouseProductDetailEntity()
          warehouseProductDetailNew.warehouseId = warehouseId
          warehouseProductDetailNew.productId = detail.productId
          warehouseProductDetailNew.productCode = detail.productCode
          warehouseProductDetailNew.productName = detail.productName
          warehouseProductDetailNew.warehouseProductId = warehouseProduct.id
          warehouseProductDetailNew.quantity = detail.quantity
          warehouseProductDetailNew.quantityImport = detail.quantity
          warehouseProductDetailNew.manufactureDate = detail.manufactureDate
          warehouseProductDetailNew.expiryDate = detail.expiryDate
          warehouseProductDetailNew.createdAt = new Date()
          warehouseProductDetailNew.createdBy = data.approveBy
          warehouseProductDetailNew.lotNumber = detail.lotNumber
          warehouseProductDetail = await warehouseProductDetailRepo.save(warehouseProductDetailNew)
        } else {
          const quantityDetailNew = +warehouseProductDetail.quantity + +detail.totalQuantity
          warehouseProductDetail.quantity = quantityDetailNew
          warehouseProductDetail.quantityImport += detail.totalQuantity
          warehouseProductDetail = await warehouseProductDetailRepo.save(warehouseProductDetail)
        }

        // lưu lịch sử
        const his = new ProductInventoryHistoryEntity()
        his.warehouseId = warehouseId
        his.warehouseProductId = warehouseProduct.id
        his.warehouseProductDetailId = warehouseProductDetail.id
        his.productId = detail.productId
        his.inboundId = detail.inboundId
        his.inboundDetailId = detail.id
        his.quantity = quantityOld
        his.quantityNew = quantityNew
        his.description = `Duyệt PNK [${detail.code}]<br>Nhập thêm: ${detail.totalQuantity}<br>HSD ${dateStr}`
        his.createdAt = new Date()
        his.createdBy = data.approveBy
        await productInventoryHistoryRepo.insert(his)

        // Nếu PO là MANUAL thì chỉ nhập kho hàng đang hoạt động
        const isTypePOCheck: any = {}
        if (po.purchaseOrderType == NSPo.EPoType.MANUAL) {
          isTypePOCheck.isDeleted = false
        }
        let product = await productRepo.findOne({ where: { id: detail.productId, ...isTypePOCheck } })
        if (!product) throw new Error(`Sản phẩm ${detail.productId} ${{ ...isTypePOCheck }} không còn tồn tại!`)

        const quantityPdOld = +product.quantity
        const quantityPdNew = quantityPdOld + +detail.totalQuantity
        const quantityPdNewLockEmp = product.quantityLockEmp + +detail.totalQuantity
        await productRepo.update(product.id, {
          quantity: quantityPdNew,
          quantityLockEmp: quantityPdNewLockEmp,
          expiryDate: detail.expiryDate,
          createdAt: new Date(),
          createdBy: data.approveBy,
        })

        let productDetail = await detailProductRepo.findOne({
          where: {
            itemId: detail.productId,
            expiryDate: Between(ds, de),
            lotNumber: detail.lotNumber,
            manufactureDate: Between(dsManu, deManu),
          },
        })

        if (!productDetail) {
          const productDetailNew = new ItemDetailEntity()
          productDetailNew.itemId = detail.productId
          productDetailNew.quantity = detail.quantity
          productDetailNew.manufactureDate = detail.manufactureDate
          productDetailNew.expiryDate = detail.expiryDate
          productDetailNew.createdAt = new Date()
          productDetailNew.createdBy = data.approveBy
          productDetailNew.lotNumber = detail.lotNumber
          productDetailNew.quantityLockEmp = detail.quantity
          productDetail = await detailProductRepo.save(productDetailNew)
        } else {
          const quantityPdDetailNew = +productDetail.quantity + +detail.totalQuantity
          // cập nhật cache
          productDetail.quantity = quantityPdDetailNew
          productDetail.lotNumber = detail.lotNumber
          productDetail.quantityLockEmp = +productDetail.quantityLockEmp + +detail.totalQuantity
          await detailProductRepo.update(productDetail.id, { quantity: quantityPdDetailNew, quantityLockEmp: quantityPdDetailNew })
        }

        const checkItemPrice = await this.itemPriceRepo.findOne({
          where: { isDeleted: false, itemId: detail.productId },
          select: ['id', 'priceInput', 'priceCapital', 'updatedBy', 'updatedAt'],
        })

        const inboundItemPrice = await this.detailRepo.find({
          where: {
            inboundId: data.id,
            productId: detail.productId,
          },
          select: {
            price: true,
            buyPrice: true, // Giá từ PO
            quantity: true,
          },
        })

        const totalPriceQuantity =
          inboundItemPrice.reduce((sum, current) => sum + (current.buyPrice * current.quantity || 0), 0) + detail.buyPrice * detail.quantity
        const totalQuantity = inboundItemPrice.reduce((sum, current) => sum + (current.quantity || 0), 0) + detail.quantity

        const averagePrice = totalQuantity > 0 ? totalPriceQuantity / totalQuantity : 0
        if (checkItemPrice) {
          checkItemPrice.priceInput = +detail.price || 0
          checkItemPrice.priceCapital = averagePrice
          checkItemPrice.updatedBy = data.approveBy
          checkItemPrice.updatedAt = new Date()
          this.itemPriceRepo.save(checkItemPrice)
        } else {
          const itemPriceNew = new ItemPriceEntity()
          itemPriceNew.itemId = detail.productId
          itemPriceNew.priceInput = detail.price
          itemPriceNew.priceCapital = detail.buyPrice // Giá từ PO
          this.itemPriceRepo.save(itemPriceNew)
        }

        await warehouseProductDetailRepo.update(warehouseProductDetail.id, { productDetailId: productDetail.id })
      }
      // tính giá vốn nhập kho
      let lstGroupProduct = coreHelper.groupByArray(lstDetail, 'productId')
      for (let itemGroup of lstGroupProduct) {
        const product: any = await productRepo.findOne({ where: { id: itemGroup.heading } })
        if (product) {
          let lstInboundDetail = await detailRepo.find({
            where: { productId: product.id },
            relations: { inbound: true },
            order: { createdAt: 'DESC' },
          })
          if (lstInboundDetail.length === 1) {
            let totalPrice = itemGroup.list.reduce((sum: number, current: any) => sum + Number(+current.quantity * +current.buyPriceVND), 0)
            const inbondObj = await repo.findOne({
              where: { id: lstInboundDetail[0].inboundId, type: enumData.InboundType.PO.code },
              relations: { details: true },
              order: { createdAt: 'DESC' },
            })
            let giavon = (+totalPrice + (+inbondObj.costAllocation || 0)) / +product.quantity
            // lưu lại lịch sử giá vốn
            const inboundDetailCostPrice = new InboundDetailCostPriceEntity()
            inboundDetailCostPrice.inboundId = data.id
            inboundDetailCostPrice.inboundDetailId = lstInboundDetail[0].id
            inboundDetailCostPrice.costPriceNew = giavon
            inboundDetailCostPrice.costPriceOld = lstInboundDetail[0].costPrice || 0
            inboundDetailCostPrice.description = `Giá vốn của phiếu nhập kho [${inbound.code}]. Chi tiết : `
            inboundDetailCostPrice.description += `Giá vốn trước [${lstInboundDetail[0]?.costPrice || 0}]`
            inboundDetailCostPrice.description += `Giá vốn mới [${giavon}]`
            inboundDetailCostPrice.createdAt = new Date()
            inboundDetailCostPrice.createdBy = data.approveBy
            await inboundDetailCostPriceRepo.insert(inboundDetailCostPrice)
            await detailRepo.update(
              {
                productId: product.id,
                inboundId: data.id,
              },
              {
                costPrice: giavon,
              },
            )

            await detailProductRepo.update(
              {
                itemId: product.id,
                expiryDate: Between(lstInboundDetail[0].expiryDate, lstInboundDetail[0].expiryDate),
                lotNumber: lstInboundDetail[0].lotNumber,
                manufactureDate: Between(lstInboundDetail[0].manufactureDate, lstInboundDetail[0].manufactureDate),
              },
              {
                costPrice: giavon,
              },
            )

            const productPriceObj = await productPriceRepo.findOne({
              where: { itemId: itemGroup.heading, isFinal: true },
              order: { createdAt: 'DESC' },
            })
            if (productPriceObj) {
              await productPriceRepo.update(
                {
                  itemId: product.id,
                  id: productPriceObj.id,
                },
                {
                  priceCapital: giavon,
                },
              )
            }
          } else {
            // const lstInboundId = lstInboundDetail.map((x) => x.inboundId)
            // const lstInbound: any = await repo.find({
            //   where: { id: In(lstInboundId), type: enumData.InboundType.PO.code },
            //   relations: { details: true },
            //   order: { createdAt: 'DESC' },
            // })
            // let lstInboundDetail = lstInboundDetail.filter(x => x.__inbound__.status === enumData.InboundStatus.Approved.code)
            let itemInbound: any = lstInboundDetail && lstInboundDetail.length > 1 ? lstInboundDetail[1] : lstInboundDetail[0]

            let giavon = 0
            let total = itemGroup.list.reduce((sum: number, current: { quantity: any }) => sum + Number(current.quantity), 0)
            let SumToTal = product.quantity
            let quantityOld = product.quantity - total
            let totalPrice = itemGroup.list.reduce((sum: number, current: any) => sum + Number(+current.quantity * +current.buyPriceVND), 0)
            if (quantityOld === null) {
              quantityOld = product.quantity - total
            } else {
              quantityOld = quantityOld
            }
            let giaVonCu = quantityOld * Number(+itemInbound.costPrice || 0)
            giavon = (Number(+totalPrice) + Number(+giaVonCu)) / Number(+SumToTal)
            // lưu lại lịch sử giá vốn
            const inboundDetailCostPrice = new InboundDetailCostPriceEntity()
            inboundDetailCostPrice.inboundId = data.id
            inboundDetailCostPrice.inboundDetailId = lstInboundDetail[0].id
            inboundDetailCostPrice.costPriceNew = giavon
            inboundDetailCostPrice.costPriceOld = itemInbound.costPrice || 0
            inboundDetailCostPrice.description = `Giá vốn của phiếu nhập kho [${inbound.code}]. Chi tiết : `
            inboundDetailCostPrice.description += `Giá vốn trước [${itemInbound.costPrice || 0}]`
            inboundDetailCostPrice.description += `Giá vốn mới [${giavon}]`
            inboundDetailCostPrice.createdAt = new Date()
            inboundDetailCostPrice.createdBy = data.approveBy
            await inboundDetailCostPriceRepo.insert(inboundDetailCostPrice)
            await detailRepo.update(
              {
                productId: product.id,
                inboundId: data.id,
              },
              {
                costPrice: giavon,
              },
            )

            await detailProductRepo.update(
              {
                itemId: product.id,
                expiryDate: Between(lstInboundDetail[0].expiryDate, lstInboundDetail[0].expiryDate),
                lotNumber: lstInboundDetail[0].lotNumber,
                manufactureDate: Between(lstInboundDetail[0].manufactureDate, lstInboundDetail[0].manufactureDate),
              },
              {
                costPrice: giavon,
              },
            )

            const productPriceObj = await productPriceRepo.findOne({
              where: { itemId: product.id, isFinal: true },
              order: { createdAt: 'DESC' },
            })
            if (productPriceObj) {
              await productPriceRepo.update(
                {
                  itemId: product.id,
                  id: productPriceObj.id,
                },
                {
                  priceCapital: giavon,
                },
              )
            }
          }
        }
      }

      // Cập nhật PO khi duyệt PNK
      // await authApiHelper.approveInboundPo(req, { poId: inbound.poId, lstDetail })

      return { message: UPDATE_SUCCESS }
    })
  }

  /** Hủy PNK */
  async cancelData(data: InboundtApproveDto, req: IRequest) {
    const inbound = await this.repo.findOne({ where: { id: data.id }, select: { id: true, code: true, status: true, poCode: true } })
    if (!inbound) throw new Error(`Phiếu nhập kho không còn tồn tại!`)
    if (inbound.status != enumData.InboundStatus.New.code) {
      throw new Error(`Phiếu nhập kho ở trạng thái ${enumData.InboundStatus[inbound.status]?.name}, không được thao tác!`)
    }
    const currentTime = new Date()
    await this.repo.update(data.id, {
      status: enumData.InboundStatus.Cancel.code,
      updatedAt: currentTime,
      updatedBy: data.approveBy,
      description: data.note,
    })
    const member: any = await omsApiHelper.getMemberByListId(req, [data.approveBy])

    const history = new InboundHistoryEntity()
    history.inboundId = inbound.id
    history.createdBy = data.approveBy
    history.updatedAt = currentTime
    history.createdAt = currentTime
    history.createdByName = member[0]?.fullName
    let description = {
      'Hủy phiếu nhập kho. Thao tác chi tiết': {
        'Mã phiếu nhập kho': `${inbound.code}`,
        'Mã PO': `${inbound.poCode}`,
        'Thời gian hủy': `${moment(currentTime).format('YYYY-MM-DD HH:mm:ss')}`,
        'Người hủy': `${member[0]?.fullName}`,
        'IP Request': req.headers['x-forwarded-for'] || req.socket.remoteAddress || null,
      },
    }

    history.description = JSON.stringify(description)
    await this.historyRepo.insert(history)

    return { message: UPDATE_SUCCESS }
  }

  async findDetail(req: IRequest, id: string) {
    // Tim inBound
    const res: any = await this.repo.findOne({
      where: { id: id, isDeleted: false },
      relations: {
        details: true,
        histories: true,
        warehouse: true,
        prices: true,
        costPrices: { inboundDetail: true },
        costAllocations: true,
      },
      order: {
        histories: { createdAt: 'DESC' },
        costPrices: { createdAt: 'DESC' },
        costAllocations: { createdAt: 'DESC' },
        prices: { createdAt: 'DESC' },
      },
    })
    if (!res) throw new Error(ERROR_NOT_FOUND_DATA)

    res.warehouseCode = await res.__warehouse__.code
    res.warehouseName = await res.__warehouse__.name
    // const po: any = await authApiHelper.findOnePo(req, { poId: res.poId })
    // if (!po) throw new Error(`PO không còn tồn tại!`)

    let lstDetail = await res.__details__
    let lstHistory = await res.__histories__

    const productIds = lstDetail.mapAndDistinct((c) => c.productId)
    const lstProduct = await this.productRepository.find({ where: { id: In(productIds) } })
    const unit = await this.unitRepo.find({ where: { isDeleted: false } })

    for (let item of lstDetail) {
      const product = lstProduct.find((c) => c.id === item.productId)
      const u = unit.find((c) => c.id === product?.unitId)
      item.unitName = u?.name || 'Bộ'
      item.unitCode = u?.code || 'Bộ'
      delete item.__product__
    }

    res.lstHistory = lstHistory
    res.lstDetail = lstDetail

    res.typeName = enumData.InboundType[res.type]?.name
    res.statusName = enumData.InboundStatus[res.status]?.name
    res.statusColor = enumData.InboundStatus[res.status]?.color
    res.colorText = enumData.InboundStatus[res.status]?.colorText

    res.lstPrice = await res.__prices__
    res.lstCostPrice = await res.__costPrices__
    for (let item of res.lstCostPrice) {
      item.productCode = item?.__inboundDetail__?.productCode
      item.productName = item?.__inboundDetail__?.productName
      // item.unitName = item?.__inboundDetail__?.__unit__?.name
      // item.unitCode = item?.__inboundDetail__?.__unit__?.code
      delete item.__inboundDetail__
    }

    res.lstCostAllocation = await res.__costAllocations__
    // const createdByName = await authApiHelper.getCreatedByName(req, res.createdBy)
    delete res.__details__
    delete res.__histories__
    delete res.__warehouse__
    delete res.__prices__
    delete res.__costPrices__
    delete res.__costAllocations__

    const memberCreate: any[] = (await omsApiHelper.getMemberByListId(req, [res.createdBy])) || []
    const memberApprove: any[] = (await omsApiHelper.getMemberByListId(req, [res.approvedBy])) || []

    const mappingResult = {
      ...res,
      createdByName: memberCreate[0]?.fullName,
      approvedByName: memberApprove[0]?.fullName,
    }

    return { ...mappingResult }
  }

  async updateInboundDescription(data: InboundtUpdateDescriptionDto, req: IRequest, user?: UserDto): Promise<any> {
    const entity = await this.repo.findOne({
      where: {
        id: data.id,
        isDeleted: false,
      },
    })
    if (!entity) {
      throw new Error('Không tìm thấy dử liệu')
    }
    entity.description = data.description
    entity.updatedBy = data.updateBy
    await this.repo.save(entity)

    return { message: UPDATE_SUCCESS }
  }

  async findProduct(productId: string) {
    const res = await this.detailRepo.findOne({
      where: { productId, isDeleted: false },
      order: { createdAt: 'DESC' },
    })
    return res
  }

  async exportExcel(req: IRequest, data: PaginationDto, user?: UserDto) {
    let where: any = {}
    if (data.where.productId) {
      where.productId = data.where.productId
    }
    if (data.where.createdAt) {
      where.createdAt = Equal(new Date(data.where.createdAt))
    }
    if (data.where.createdBy) {
      where.createdBy = data.where.createdBy
    }
    let result: any = await this.detailRepo.find({
      where: where,
      skip: data.skip,
      take: data.take,
      relations: { inbound: true },
      order: { expiryDate: 'DESC' },
    })
    if (result.length === 0) return []
    // for (let item of result) {
    //   item.createdByName = await authApiHelper.getCreatedByName(req, item.createdBy)
    // }
    return result
  }

  async find(data: any, req: IRequest) {
    let whereCon: any = { isDeleted: false }
    if (data?.name) whereCon.name = Like(`%${data.name}%`)
    if (data?.code) whereCon.code = Like(`%${data.code}%`)
    if (data?.lstId?.length > 0) whereCon.id = In(data.lstId)
    if (data?.lstCode?.length > 0) whereCon.code = In(data.lstCode)
    if (data?.id) whereCon.id = Like(`%${data.id}%`)
    if (data?.poId) whereCon.poId = Like(`%${data.poId}%`)
    if (data?.lstPoId?.length > 0) whereCon.poId = In(data.lstPoId)
    let res: any = await this.repo.find({ where: whereCon, relations: { details: true } })
    if (res.length === 0) return []
    const dicNameOfUser: any = {}
    {
      const lstUserId = res[0].mapAndDistinct((c) => c.createdBy)
      const lstUser = await this.userRepo.find({ where: { id: In(lstUserId) } })
      lstUser.forEach((c) => (dicNameOfUser[c.id] = c.fullName))
    }

    const dictProudct: any = {}
    {
      const lstProudct: any = await this.productRepository.find()
      lstProudct.forEach((c) => (dictProudct[c.id] = c))
    }

    for (let item of res) {
      item.createdByName = dicNameOfUser[item.createdBy]
      item.warehouseName = item.__warehouse__?.name
      item.statusName = enumData.InboundStatus[item.status]?.name
      item.statusColor = enumData.InboundStatus[item.status]?.color
      item.colorText = enumData.InboundStatus[item.status]?.colorText

      for (let x of item.__details__) {
        x.kg = x.quantity * dictProudct[x.productId]?.kg
      }
      item.listDetail = await item.__details__
      delete item.__details__
      delete item.__warehouse__
    }

    return res
  }

  async findDataSelectBox(data: FilterIdDto) {
    let whereCon: any = { isDeleted: false }
    if (data?.poId) whereCon.poId = data.poId
    whereCon.status = enumData.InboundStatus.Approved.code
    let res: any = await this.repo.find({ where: whereCon, relations: { details: true } })
    if (res.length === 0) return []
    const dictProudct: any = {}
    {
      const lstProudct: any = await this.productRepository.find({ where: { isDeleted: false } })
      lstProudct.forEach((c) => (dictProudct[c.id] = c))
    }
    for (let item of res) {
      let lstDetail = await item.__details__
      item.quantity = lstDetail.reduce((sum, current) => sum + Number(current.quantity), 0)
      for (let x of lstDetail) {
        x.kg = x.quantity * dictProudct[x.productId]?.kg
      }
      item.totalKg = lstDetail.reduce((sum, current) => sum + Number(current.kg), 0)
      delete item.__details__
    }
    return res
  }

  async updateCostAllocation(data: InboundtApproveDto, req: IRequest, user?: UserDto): Promise<any> {
    const inboundObj = await this.repo.findOne({ where: { id: data.id, isDeleted: false } })
    if (!inboundObj) throw new Error(`Không tìm thấy phiếu nhập kho. Vui lòng kiểm tra lại.`)
    const currentTime = new Date()

    const lstDetail: any = await this.detailRepo.find({ where: { inboundId: data.id, isDeleted: false }, order: { expiryDate: 'ASC' } })
    if (lstDetail.length == 0) throw new Error(`Phiếu nhập kho không có sản phẩm, không được thao tác!`)

    const member: any = await omsApiHelper.getMemberByListId(req, [data.approveBy])

    await this.repo.manager.transaction('SERIALIZABLE', async (trans) => {
      const repo = trans.getRepository(InboundEntity)
      const inboundCostRepo = trans.getRepository(InboundCostAllocationEntity)
      const historyRepo = trans.getRepository(InboundHistoryEntity)
      const history = new InboundHistoryEntity()
      const detailRepo = trans.getRepository(InboundDetailEntity)
      const productRepo = trans.getRepository(ItemEntity)
      const productPriceRepo = trans.getRepository(ItemPriceEntity)
      const inboundDetailCostPriceRepo = trans.getRepository(InboundDetailCostPriceEntity)
      const inboundDetailPriceRepo = trans.getRepository(InboundDetailPriceEntity)
      const detailProductRepo = trans.getRepository(ItemDetailEntity)
      history.inboundId = inboundObj.id
      history.createdBy = data.approveBy
      history.createdByName = member[0]?.fullName

      let description = {
        'Cập nhật phân bổ chi phí cho phiếu nhập kho. Thao tác chi tiết': {
          'Mã phiếu nhập kho': `${inboundObj.code}`,
          'Thời gian cập nhật phân bổ chi phí': `${moment(currentTime).format('YYYY-MM-DD HH:mm:ss')}`,
          'Người cập nhật phân bổ chi phí': `${member[0]?.fullName}`,
          'IP Request': req.headers['x-forwarded-for'] || req.socket.remoteAddress || null,
          'Chi phí trước khi phân bổ': `${+inboundObj.costAllocation}`,
          'Chi phí sau phân bổ': `${+inboundObj.costAllocation + data.price}`,
        },
      }

      history.description = JSON.stringify(description)
      await historyRepo.insert(history)

      // lưu lịch sử phân bổ chi phí
      const his = new InboundCostAllocationEntity()
      his.inboundId = inboundObj.id
      his.costAllocationNew = data.price
      his.costAllocationOld = inboundObj.costAllocation
      his.description = `Chi phí phân bổ của phiếu nhập kho [${inboundObj.code}]. Chi tiết : `
      his.description += `<br/> Chi phí trước khi phân bổ [${inboundObj.costAllocation}]`
      his.description += `<br/> Chi phí sau khi phân bổ [${inboundObj.costAllocation + data.price}]`
      his.createdAt = new Date()
      his.createdBy = data.approveBy
      await inboundCostRepo.insert(his)
      let quantity = lstDetail.reduce((sum, current) => sum + Number(current.quantity), 0)
      // tính lại giá nhập kho
      let costAllocation = 0
      for (let item of lstDetail) {
        costAllocation = (data.price * item.quantity) / quantity

        // lưu lịch sử phân bổ chi phí
        const hisCost = new InboundCostAllocationEntity()
        hisCost.inboundDetailId = item.id
        hisCost.costAllocationDetailNew = +item.costAllocation + +costAllocation
        hisCost.costAllocationDetailOld = +item.costAllocation
        hisCost.description = `Chi phí phân bổ của phiếu nhập kho [${inboundObj.code}]. Chi tiết : `
        hisCost.description += `<br/> Chi phí trước khi phân bổ [${+item.costAllocation}]`
        hisCost.description += `<br/> Chi phí sau khi phân bổ [${+item.costAllocation + costAllocation}]`
        hisCost.createdAt = new Date()
        hisCost.createdBy = data.approveBy
        await inboundCostRepo.insert(hisCost)

        let priceNew = costAllocation / item.quantity + +item.priceVND
        const his = new InboundDetailPriceEntity()
        his.inboundId = inboundObj.id
        his.priceNew = +priceNew
        his.priceOld = +item.priceAfterCost
        his.description = `Cập nhật giá nhập của phiếu nhập kho [${inboundObj.code}] khi phân bổ chi phí. Chi tiết : `
        his.description += `<br/> Giá nhập trước khi phân bổ [${+item.priceAfterCost || 0}]`
        his.description += `<br/> Giá nhập sau khi phân bổ [${+priceNew}]`
        his.createdAt = new Date()
        his.createdBy = data.approveBy
        await inboundDetailPriceRepo.insert(his)
        await detailRepo.update({ id: item.id }, { priceAfterCost: +priceNew, costAllocation: +costAllocation })
      }

      // tính giá vốn nhập kho
      let lstGroupProduct = coreHelper.groupByArray(lstDetail, 'productId')
      for (let itemGroup of lstGroupProduct) {
        const product: any = await productRepo.findOne({ where: { id: itemGroup.heading } })
        if (product) {
          let lstInboundDetail = await detailRepo.find({
            where: { productId: product.id },
            relations: { inbound: true },
            order: { createdAt: 'DESC' },
          })
          if (lstInboundDetail.length === 1) {
            let giavon =
              (Number(lstInboundDetail[0]?.costAllocation || costAllocation) +
                Number(lstInboundDetail[0]?.costPrice) * lstInboundDetail[0]?.quantity) /
              Number(lstInboundDetail[0]?.quantity)
            // lưu lại lịch sử giá vốn
            const inboundDetailCostPrice = new InboundDetailCostPriceEntity()
            inboundDetailCostPrice.inboundId = data.id
            inboundDetailCostPrice.inboundDetailId = lstInboundDetail[0]?.id
            inboundDetailCostPrice.costPriceNew = giavon
            inboundDetailCostPrice.costPriceOld = lstInboundDetail[0]?.costPrice || 0
            inboundDetailCostPrice.description = `Giá vốn của phiếu nhập kho [${inboundObj.code}]. Chi tiết : `
            inboundDetailCostPrice.description += `<br/> Giá vốn trước khi phân bổ chi phí [${lstInboundDetail[0]?.costPrice || 0}]`
            inboundDetailCostPrice.description += ` <br/> Giá vốn mới sau khi phân bổ chi phí [${giavon}]`
            inboundDetailCostPrice.createdAt = new Date()
            inboundDetailCostPrice.createdBy = data.approveBy
            await inboundDetailCostPriceRepo.insert(inboundDetailCostPrice)
            await detailRepo.update(
              {
                productId: product.id,
                inboundId: data.id,
              },
              {
                costPrice: giavon,
              },
            )

            await detailProductRepo.update(
              {
                itemId: product.id,
                expiryDate: Raw(
                  (alias) =>
                    `(${alias}) BETWEEN ("${moment(lstInboundDetail[0].expiryDate).format('YYYY-MM-DD HH:mm:ss')}") AND ("${moment(
                      lstInboundDetail[0].expiryDate,
                    ).format('YYYY-MM-DD HH:mm:ss')}")`,
                ),
                lotNumber: lstInboundDetail[0].lotNumber,
                manufactureDate: Raw(
                  (alias) =>
                    `(${alias}) BETWEEN ("${moment(lstInboundDetail[0].manufactureDate).format('YYYY-MM-DD HH:mm:ss')}") AND ("${moment(
                      lstInboundDetail[0].manufactureDate,
                    ).format('YYYY-MM-DD HH:mm:ss')}")`,
                ),
              },
              {
                costPrice: giavon,
              },
            )

            const productPriceObj = await productPriceRepo.findOne({
              where: { itemId: itemGroup.heading, isFinal: true },
              order: { createdAt: 'DESC' },
            })
            // cập nhật lại giá vốn
            if (productPriceObj) {
              await productPriceRepo.update(
                {
                  itemId: product.id,
                  id: productPriceObj.id,
                },
                {
                  priceCapital: giavon,
                },
              )
            }
          } else {
            const lstInboundId = lstInboundDetail.map((x) => x.inboundId)
            const lstInbound: any = await repo.find({
              where: { id: In(lstInboundId), type: enumData.InboundType.PO.code, status: enumData.InboundStatus.Approved.code },
              relations: { details: true },
              order: { details: { createdAt: 'DESC' } },
            })
            let itemInbound: any
            if (lstInbound[0].__details__.length === 1) {
              itemInbound = lstInbound[0].__details__.find((x) => x.productId === itemGroup.heading)
            } else {
              itemInbound = lstInbound[0].__details__?.find((x) => x.productId === itemGroup.heading)
            }
            let total = 0
            for (let item of lstInbound) {
              total += item.__details__
                .filter((x) => x.productId === itemGroup.heading)
                .reduce((sum: number, current: any) => sum + Number(+current.quantity), 0)
            }
            let giavon = 0
            costAllocation = lstDetail.find((x) => x.productId === itemGroup.heading)?.costAllocation
            giavon = (Number(itemInbound?.costAllocation || costAllocation) + Number(itemInbound?.costPrice) * total) / Number(total)
            // lưu lại lịch sử giá vốn
            const inboundDetailCostPrice = new InboundDetailCostPriceEntity()
            inboundDetailCostPrice.inboundId = data.id
            inboundDetailCostPrice.inboundDetailId = itemInbound.id
            inboundDetailCostPrice.costPriceNew = giavon
            inboundDetailCostPrice.costPriceOld = itemInbound.costPrice || 0
            inboundDetailCostPrice.description = `Giá vốn của phiếu nhập kho [${inboundObj.code}]. Chi tiết : `
            inboundDetailCostPrice.description += `<br/> Giá vốn trước khi phân bổ chi phí [${itemInbound.costPrice || 0}]`
            inboundDetailCostPrice.description += ` <br/> Giá vốn sau khi phân bổ chi phí [${giavon}]`
            inboundDetailCostPrice.createdAt = new Date()
            inboundDetailCostPrice.createdBy = data.approveBy
            await inboundDetailCostPriceRepo.insert(inboundDetailCostPrice)
            await detailRepo.update({ id: itemInbound.id }, { costPrice: +giavon })
            await detailProductRepo.update(
              {
                itemId: product.id,
                expiryDate: Raw(
                  (alias) =>
                    `(${alias}) BETWEEN ("${moment(itemInbound.expiryDate).format('YYYY-MM-DD HH:mm:ss')}") AND ("${moment(
                      itemInbound.expiryDate,
                    ).format('YYYY-MM-DD HH:mm:ss')}")`,
                ),
                lotNumber: itemInbound.lotNumber,
                manufactureDate: Raw(
                  (alias) =>
                    `(${alias}) BETWEEN ("${moment(itemInbound.manufactureDate).format('YYYY-MM-DD HH:mm:ss')}") AND ("${moment(
                      itemInbound.manufactureDate,
                    ).format('YYYY-MM-DD HH:mm:ss')}")`,
                ),
              },
              {
                costPrice: giavon,
              },
            )
            const productPriceObj = await productPriceRepo.findOne({
              where: { itemId: product.id, isFinal: true },
              order: { createdAt: 'DESC' },
            })
            if (productPriceObj) {
              await productPriceRepo.update(
                {
                  itemId: product.id,
                  id: productPriceObj.id,
                },
                {
                  priceCapital: giavon,
                },
              )
            }
          }
        }
      }

      inboundObj.updatedBy = data.approveBy
      inboundObj.costAllocation = inboundObj.costAllocation + data.price
      inboundObj.updatedAt = currentTime
      await repo.save(inboundObj)
    })
    return { message: UPDATE_SUCCESS }
  }

  async findInbondDetail(id: string) {
    return await this.detailRepo.find({ where: { inboundId: id, isDeleted: false } })
  }

  /** DS PNK phân trang */
  async paginationHistory(req: IRequest, data: PaginationDto) {
    const whereCon: any = { isDeleted: false }
    if (data.where.poId) whereCon.poId = data.where.poId
    if (data.where.poCode) whereCon.poCode = data.where.poCode
    if (data.where.warehouseId) whereCon.warehouseId = [data.where.warehouseId]
    if (data.where?.storeId) {
      const warehouse = await this.warehouseRepository.find({
        where: { storeId: data.where.storeId, ...(whereCon?.warehouseId != null && { id: In(whereCon.warehouseId) }) },
      })
      if (warehouse) {
        whereCon.warehouseId = warehouse.map((x) => x.id)
      }
    }
    if (data.where.productId) {
      const lstProduct = await this.detailRepo.find({
        where: { productId: data.where.productId, isDeleted: false },
      })
      if (lstProduct.length === 0) return [[], 0]
      const lstdId = lstProduct.map((x) => x.inboundId)
      whereCon.id = In(lstdId)
    }
    whereCon.type = In([enumData.InboundType.PO.code, enumData.InboundType.CHECK_INVENTORY.code])
    whereCon.status = enumData.InboundStatus.Approved.code

    if (data.where.createdAt && data.where.createdAt.length > 0) {
      const startDate = new Date(data.where.createdAt[0])
      const endDate = new Date(data.where.createdAt[1])

      startDate.setHours(0, 0, 0, 0)
      endDate.setHours(23, 59, 59, 999)

      whereCon.createdAt = Between(startDate, endDate)
    }

    whereCon.details = {}
    // whereCon.details.product = {}

    if (data.where.brandId) {
      whereCon.details.product.brandId = Like(`%${data.where.brandId}%`)
    }

    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.createdBy) whereCon.createdBy = data.where.createdBy

    let relations: any = {}

    if (data.where.downloadExcel === true) {
      relations = { warehouse: true, details: true }
    } else {
      relations = { warehouse: true }
    }

    let [lst, total]: any[] = []
    if (whereCon.warehouseId) {
      ;[lst, total] = await this.repo.findAndCount({
        where: { ...whereCon, warehouseId: In(whereCon.warehouseId) },
        order: { createdAt: 'DESC' },
        relations: relations,
        skip: data.skip,
        take: data.take,
      })
    } else {
      ;[lst, total] = await this.repo.findAndCount({
        where: whereCon,
        order: { createdAt: 'DESC' },
        relations: relations,
        skip: data.skip,
        take: data.take,
      })
    }
    if (lst.length == 0) return { data: lst, total: total }

    const listMemCreateIds = lst.map((val) => val.createdBy)
    const memberCreate: any[] = (await omsApiHelper.getMemberByListId(req, listMemCreateIds)) || []

    const listMemApproveIds = lst.map((val) => val.approvedBy)
    const memberApprove: any[] = (await omsApiHelper.getMemberByListId(req, listMemApproveIds)) || []
    const warehouses = await this.warehouseRepository.find({ where: { type: NSWarehouse.EWarehouseType['3PL'] } })
    const list3PL = await this.supplierRepo.find({ where: { id: In(warehouses.map((m) => m.storeId)) } })
    const listSup = await this.supplierRepo.find({ where: { id: In(listMemApproveIds) } })
    for (let item of lst) {
      item.warehouseName = item.__warehouse__?.name
      item.typeName = enumData.InboundType[item.type]?.name
      item.statusName = enumData.InboundStatus[item.status]?.name
      item.statusColor = enumData.InboundStatus[item.status]?.color
      item.colorText = enumData.InboundStatus[item.status]?.colorText
      if (data.where.downloadExcel) {
        item.lstDetail = item.__details__
      }
      delete item.__details__
      delete item.__warehouse__
    }
    const dictAdminCreate: any = {}
    {
      const adminCreate: any = await this.userRepo.find({ where: { id: In(listMemCreateIds) } })
      adminCreate.forEach((c) => (dictAdminCreate[c.id] = c))
    }

    const dictAdminApprove: any = {}
    {
      const adminApprove: any = await this.userRepo.find({ where: { id: In(listMemApproveIds) } })
      adminApprove.forEach((c) => (dictAdminApprove[c.id] = c))
    }

    const mappingResult = lst.map((val) => {
      let memCreated: any
      let memApproved: any
      const wh = warehouses.find((w) => w.id === val.warehouseId) // Là kho 3PL

      if (wh) {
        memCreated = list3PL.find((s) => s.id === val.createdBy)?.name
        memApproved = list3PL.find((s) => s.id === val.approvedBy)?.name
      } else {
        memCreated = listSup.find((m: any) => m.id === val.createdBy)?.name
        memApproved = listSup.find((m: any) => m.id === val.approvedBy)?.name
      }

      if (dictAdminCreate[val.createdBy]) {
        memCreated = dictAdminCreate[val.createdBy]?.fullName
      }

      if (dictAdminApprove[val.approveBy]) {
        memApproved = dictAdminApprove[val.approveBy]?.fullName
      }

      return {
        ...val,
        createdByName: memCreated,
        approvedByName: memApproved,
      }
    })

    return { data: mappingResult, total: total }
  }

  /** DS PNK phân trang */
  async paginationHistoryMBC(req: IRequest, data: PaginationDto) {
    const whereCon: any = { isDeleted: false }
    if (data.where.poId) whereCon.poId = data.where.poId
    if (data.where.poCode) whereCon.poCode = data.where.poCode
    if (data.where.warehouseId) whereCon.warehouseId = [data.where.warehouseId]
    if (data.where?.storeId) {
      const warehouse = await this.warehouseRepository.find({
        where: { storeId: data.where.storeId, ...(whereCon?.warehouseId != null && { id: In(whereCon.warehouseId) }) },
      })
      if (warehouse) {
        whereCon.warehouseId = warehouse.map((x) => x.id)
      }
    }
    if (data.where.productId) {
      const lstProduct = await this.detailRepo.find({
        where: { productId: data.where.productId, isDeleted: false },
      })
      if (lstProduct.length === 0) return [[], 0]
      const lstdId = lstProduct.map((x) => x.inboundId)
      whereCon.id = In(lstdId)
    }
    whereCon.type = In([enumData.InboundType.PO.code, enumData.InboundType.CHECK_INVENTORY.code])
    whereCon.status = enumData.InboundStatus.Approved.code

    if (data.where.createdAt && data.where.createdAt.length > 0) {
      const startDate = new Date(data.where.createdAt[0])
      const endDate = new Date(data.where.createdAt[1])

      startDate.setHours(0, 0, 0, 0)
      endDate.setHours(23, 59, 59, 999)

      whereCon.createdAt = Between(startDate, endDate)
    }

    whereCon.details = {}
    // whereCon.details.product = {}

    if (data.where.brandId) {
      whereCon.details.product.brandId = Like(`%${data.where.brandId}%`)
    }

    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.createdBy) whereCon.createdBy = data.where.createdBy

    let relations: any = { warehouse: true }

    if (data.where.downloadExcel === true) {
      relations = { warehouse: true, details: true }
    } else {
      relations = { warehouse: true }
    }

    let [lst, total]: any[] = []
    if (whereCon.warehouseId) {
      ;[lst, total] = await this.repo.findAndCount({
        where: { ...whereCon, warehouseId: In(whereCon.warehouseId), warehouse: { type: NSWarehouse.EWarehouseType.MBC } },
        order: { createdAt: 'DESC' },
        relations: relations,
        skip: data.skip,
        take: data.take,
      })
    } else {
      ;[lst, total] = await this.repo.findAndCount({
        where: { ...whereCon, warehouse: { type: NSWarehouse.EWarehouseType.MBC } },
        order: { createdAt: 'DESC' },
        relations: relations,
        skip: data.skip,
        take: data.take,
      })
    }
    if (lst.length == 0) return { data: lst, total: total }

    const listMemCreateIds = lst.map((val) => val.createdBy)
    const memberCreate: any[] = (await omsApiHelper.getMemberByListId(req, listMemCreateIds)) || []

    const listMemApproveIds = lst.map((val) => val.approvedBy)
    const memberApprove: any[] = (await omsApiHelper.getMemberByListId(req, listMemApproveIds)) || []
    const warehouses = await this.warehouseRepository.find({ where: { type: NSWarehouse.EWarehouseType['3PL'] } })
    const list3PL = await this.supplierRepo.find({ where: { id: In(warehouses.map((m) => m.storeId)) } })
    const listSup = await this.supplierRepo.find({ where: { id: In(listMemApproveIds) } })
    for (let item of lst) {
      item.warehouseName = item.__warehouse__?.name
      item.typeName = enumData.InboundType[item.type]?.name
      item.statusName = enumData.InboundStatus[item.status]?.name
      item.statusColor = enumData.InboundStatus[item.status]?.color
      item.colorText = enumData.InboundStatus[item.status]?.colorText
      if (data.where.downloadExcel) {
        item.lstDetail = item.__details__
      }
      delete item.__details__
      delete item.__warehouse__
    }
    const dictAdminCreate: any = {}
    {
      const adminCreate: any = await this.userRepo.find({ where: { id: In(listMemCreateIds) } })
      adminCreate.forEach((c) => (dictAdminCreate[c.id] = c))
    }

    const dictAdminApprove: any = {}
    {
      const adminApprove: any = await this.userRepo.find({ where: { id: In(listMemApproveIds) } })
      adminApprove.forEach((c) => (dictAdminApprove[c.id] = c))
    }

    const mappingResult = lst.map((val) => {
      let memCreated: any
      let memApproved: any
      const wh = warehouses.find((w) => w.id === val.warehouseId) // Là kho 3PL

      if (wh) {
        memCreated = list3PL.find((s) => s.id === val.createdBy)?.name
        memApproved = list3PL.find((s) => s.id === val.approvedBy)?.name
      } else {
        memCreated = listSup.find((m: any) => m.id === val.createdBy)?.name
        memApproved = listSup.find((m: any) => m.id === val.approvedBy)?.name
      }

      if (dictAdminCreate[val.createdBy]) {
        memCreated = dictAdminCreate[val.createdBy]?.fullName
      }

      if (dictAdminApprove[val.approveBy]) {
        memApproved = dictAdminApprove[val.approveBy]?.fullName
      }

      return {
        ...val,
        createdByName: memCreated,
        approvedByName: memApproved,
      }
    })

    return { data: mappingResult, total: total }
  }

  async updateDataApproved(data: InboundUpdateDto, req: IRequest, user?: UserDto) {
    const inbound: any = await this.repo.findOne({ where: { id: data.id } })
    if (!inbound) throw new Error(`Phiếu nhập kho không còn tồn tại!`)

    if (inbound.status != enumData.InboundStatus.Approved.code) {
      throw new Error(`Phiếu nhập kho ở trạng thái ${enumData.InboundStatus[inbound.status]?.name}, không được chỉnh sửa !`)
    }

    // const po: any = await authApiHelper.findOnePo(req, { poId: data.poId })
    // if (!po) throw new Error(`PO không còn tồn tại!`)
    for (let item of data.lstDetail) {
      // if (!item.lotNumber) throw new Error(`Số lô không được để trống !`)
      if (!item.isCombo) {
        if (!item.expiryDate) throw new Error(`Hạn sử dụng không được để trống !`)
        if (!item.manufactureDate) throw new Error(`Ngày sản xuất không được để trống !`)
      }
      // if (!item.expiryDate) throw new Error(`Hạn sử dụng không được để trống !`)
      // if (!item.manufactureDate) throw new Error(`Ngày sản xuất không được để trống !`)
    }
    return this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(InboundEntity)
      const detailRepo = trans.getRepository(InboundDetailEntity)
      const historyRepo = trans.getRepository(InboundHistoryEntity)
      const unitRepo = trans.getRepository(UnitEntity)
      inbound.updatedAt = new Date()
      inbound.updatedBy = data.updateBy
      await repo.save(inbound)

      // lưu lại lịch sử inbound
      let arr = new Array()
      //Lọc qua danh sách detail và tạo detail
      for (let item of data.lstDetail) {
        const detailObj = await detailRepo.findOne({ where: { id: item.id, isDeleted: false } })
        if (!detailObj) throw new Error(`Chi tiết phiếu nhập kho không còn tồn tại!`)

        const unitObj: any = await unitRepo.findOne({ where: { id: item.unitId, isDeleted: false } })
        if (!unitObj) throw new Error(`Đơn vị tính không còn tồn tại!`)
        detailObj.quantityOld = detailObj.quantity
        detailObj.quantityDiff = item.quantity - detailObj.quantity
        detailObj.inboundId = inbound.id
        detailObj.poDetailId = item.poDetailId
        detailObj.productId = item.productId
        detailObj.productCode = item.productCode
        detailObj.productName = item.productName
        detailObj.unitId = item.unitId
        detailObj.baseUnit = unitObj.baseUnit || 0
        detailObj.code = inbound.code
        detailObj.quantity = +item.quantity || 0
        //detailObj.totalQuantity = detailObj.quantity * detailObj.baseUnit
        detailObj.totalQuantity = detailObj.quantity // Bala
        detailObj.price = +item.price || 0
        detailObj.priceVND = detailObj.price * inbound.exchangeRate
        detailObj.manufactureDate = item.manufactureDate
        detailObj.expiryDate = item.expiryDate
        detailObj.description = item.description
        detailObj.totalPrice = detailObj.price * detailObj.quantity
        detailObj.totalPriceVND = detailObj.priceVND * detailObj.quantity
        detailObj.lotNumber = item.lotNumber
        // const poDetail = po.lstDetail.find((c) => c.id == item.poDetailId)
        // if (!poDetail) throw new Error(`Sản phẩm không còn tồn tại trong PO!`)

        // detailObj.buyPrice = +poDetail.buyPrice || 0
        // detailObj.buyPriceVND = +poDetail.buyPriceVND || 0
        detailObj.createdAt = new Date()
        detailObj.createdBy = data.createBy

        await detailRepo.insert(detailObj)

        arr.push({
          'Mã sản phẩm': detailObj.productCode,
          'Tên sản phẩm': detailObj.productName,
          'Giá mua': detailObj.buyPriceVND.toLocaleString('vi', { style: 'currency', currency: 'VND' }),
          'Giá nhập': detailObj.priceVND.toLocaleString('vi', { style: 'currency', currency: 'VND' }),
          'Số lượng nhập': detailObj.quantity,
          'Số lượng nhập theo đvcs': detailObj.totalQuantity,
          'Hạn sử dụng': detailObj.expiryDate ? moment(detailObj.expiryDate).format('YYYY-MM-DD') : '',
          'Ngày sản xuất': detailObj.manufactureDate ? moment(detailObj.manufactureDate).format('YYYY-MM-DD') : '',
        })

        inbound.totalPrice = +inbound.totalPrice + Number(detailObj.totalPrice)
        inbound.totalPriceVND = +inbound.totalPriceVND + Number(detailObj.totalPriceVND)
      }

      // Cập nhật lại tổng giá trị nhập cho PNK
      await repo.update(inbound.id, { totalPrice: inbound.totalPrice, totalPriceVND: inbound.totalPriceVND })
      const member: any = await omsApiHelper.getMemberByListId(req, [data.updateBy])

      const history = new InboundHistoryEntity()
      history.inboundId = inbound.id
      history.createdBy = data.updateBy
      history.createdByName = member[0]?.fullName
      history.createdAt = new Date()
      let description = {
        'Cập nhật phiếu nhập kho. Thao tác chi tiết': {
          'Mã phiếu nhập kho': `${inbound.code}`,
          'Mã PO': `${inbound.poCode}`,
          'Thời gian cập nhật': `${moment(inbound.updatedAt).format('YYYY-MM-DD HH:mm:ss')}`,
          'Người cập nhật': `${member[0]?.fullName}`,
          'Thông tin sản phẩm': arr,
          'IP Request': req.headers['x-forwarded-for'] || req.socket.remoteAddress || null,
        },
      }

      history.description = JSON.stringify(description)
      await historyRepo.insert(history)

      return { message: UPDATE_SUCCESS }
    })
  }

  public async print(data: string[], req: IRequest) {
    if (data.length === 0) throw new Error(`Vui lòng chọn ít nhất một PO !`)
    const result = []
    for (let item of data) {
      const res: any = await this.repo.findOne({
        where: { id: item, isDeleted: false },
        relations: { warehouse: true, details: true },
        order: { createdAt: 'DESC' },
      })
      if (!res) throw new Error(ERROR_NOT_FOUND_DATA)

      res.warehouseCode = await res.__warehouse__.code
      res.warehouseName = await res.__warehouse__.name
      // const po: any = await authApiHelper.findOnePo(req, { poId: res.poId })
      let lstDetail = await res.__details__
      for (let item of lstDetail) {
        // if (po) {
        //   const poDetail = po.lstDetail.find((c) => c.id == item.poDetailId)
        //   item.quantityPO = poDetail?.quantity
        //   item.quantityNotInbound = poDetail?.quantityNotInbound || 0
        // }
        // item.productCode = item?.__product__?.code
        // item.productName = item?.__product__?.name
        // item.unitName = item?.__product__?.__unit__?.name
        // item.unitCode = item?.__product__?.__unit__?.code
        delete item.__product__
      }
      res.lstDetail = lstDetail
      res.typeName = enumData.InboundType[res.type]?.name
      res.statusName = enumData.InboundStatus[res.status]?.name
      res.statusColor = enumData.InboundStatus[res.status]?.color
      res.colorText = enumData.InboundStatus[res.status]?.colorText
      delete res.__details__
      delete res.__histories__
      delete res.__warehouse__
      result.push(res)
    }
    return result
  }

  async findProductByTransportType(data: { productId: string; transportType: string }) {
    let whereCon: any = {}
    whereCon.isDeleted = false
    whereCon.inbound = {}
    whereCon.inbound.status = Not(Equal(enumData.InboundStatus.Cancel.code))
    whereCon.inbound.type = enumData.InboundType.PO.code
    whereCon.productId = data.productId
    whereCon.transportType = data.transportType
    const findInboundDetail = await this.detailRepo.findOne({
      where: whereCon,
      order: { createdAt: 'DESC' },
    })
    return findInboundDetail
  }
}
