import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString } from 'class-validator'

export class WarehouseCreateExcelDto {
  @IsNotEmpty()
  @IsString()
  name: string

  @IsNotEmpty()
  @IsString()
  code: string

  description: string

  cityCode?: string

  districtCode?: string

  wardCode?: string

  address?: string

  isDefault?: boolean

  cbm?: number

  @ApiProperty({ description: 'dài' })
  @IsOptional()
  length: number

  @ApiProperty({ description: 'rộng' })
  @IsOptional()
  width: number

  @ApiProperty({ description: 'cao' })
  @IsOptional()
  height: number
}
