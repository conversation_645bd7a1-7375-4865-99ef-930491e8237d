import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON> } from 'typeorm'
import { CheckInventoryEntity } from './checkInventory.entity'
import { BaseEntity } from '../core/base.entity'
import { CheckInventoryDetailByEmployeeEntity } from '.'

/** Item trong kiểm kho */
@Entity('check_inventory_detail')
export class CheckInventoryDetailEntity extends BaseEntity {
  @Column({ type: 'varchar', nullable: false })
  checkInventoryId: string
  @ManyToOne(() => CheckInventoryEntity, (p) => p.details)
  @JoinColumn({ name: 'checkInventoryId', referencedColumnName: 'id' })
  checkInventory: Promise<CheckInventoryEntity>

  /** Sản phẩm kiểm kho */
  @Column({ type: 'varchar', length: 36, nullable: false })
  productId: string

  /** <PERSON>ã sp */
  @Column({ type: 'varchar', length: 50, nullable: true })
  productCode: string

  /** Tên sp */
  @Column({ type: 'varchar', length: 250, nullable: true })
  productName: string

  /** Sản phẩm thực kiểm kho */
  @Column({ type: 'varchar', length: 36, nullable: true })
  productDetailId: string

  /** Hạn sử dụng */
  @Column({ type: 'timestamptz', nullable: true })
  expiryDate: Date

  /** Ngày sản xuất (fill theo hạn sử dụng) */
  @Column({ type: 'timestamptz', nullable: true })
  manufactureDate: Date

  /**
   * Autofill theo hạn sử dụng
   * Nếu cùng 1 HSD nhưng có nhiều số lô → cho phép chọn số lô thuộc hạn sử dụng đó
   */
  @Column({ type: 'varchar', length: 36, nullable: true })
  lotNumber: string

  /** Số lượng tồn thực tế */
  @Column({ nullable: true, default: 0 })
  quantity: number

  /** Số lượng tồn trên hệ thống */
  @Column({ nullable: true, default: 0 })
  inventory: number

  /** Chênh lệch */
  @Column({ nullable: true, default: 0 })
  quantityDiff: number

  /** Phần trăm hạn sử dụng */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 2, default: 0 })
  expiryPercent: number

  /** Danh sách kho nhân viên bị trừ tồn kho */
  @OneToMany(() => CheckInventoryDetailByEmployeeEntity, (p) => p.checkInventoryDetail)
  checkInventoryDetailByEmployees: Promise<CheckInventoryDetailByEmployeeEntity[]>
}
