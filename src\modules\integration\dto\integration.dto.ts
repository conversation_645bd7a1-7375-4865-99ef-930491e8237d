import { ApiPropertyOptional } from "@nestjs/swagger";
import { PageRequest } from "../../../dto";
import { IsArray, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsUUID } from "class-validator";
import { NSMember } from "../../../constants/NSMember";
import { NSCard } from "../../../constants/NSCard";
import { NSPartner } from "../../../constants/NSPartner";
import { Transform } from "class-transformer";
import { NSRecurringOrder } from "../../../constants";

export class ListCardReq extends PageRequest {
  @ApiPropertyOptional({ description: 'ID card Type' })
  @IsOptional()
  @IsUUID('4')
  cardTypeId?: string;

  @ApiPropertyOptional({ description: 'Từ khóa tìm kiếm theo name' })
  keyword?: string;

  @ApiPropertyOptional({
    enum: NSMember.EBusinessType,
    enumName: 'EBusinessType',
    isArray: true,
  })
  @IsOptional()
  businessTypes?: NSMember.EBusinessType[];

  @ApiPropertyOptional({
    description: 'Trạng thái hoạt động của card',
    enumName: 'ECardTypeStatus',
    enum: NSCard.ECardTypeStatus,
  })
  @IsOptional()
  status?: NSCard.ECardTypeStatus;

  @ApiPropertyOptional({ description: 'Tìm danh sách thẻ bằng comboId' })
  @IsOptional()
  comboId?: string
}
export class ListPartnerReq extends PageRequest {
  @ApiPropertyOptional({
    description: `${Object.values(NSPartner.EType).join(' / ')} (Store: Bưu cục)`,
    enum: NSPartner.EType,
  })
  @IsOptional()
  @IsEnum(NSPartner.EType)
  type?: NSPartner.EType;
  @ApiPropertyOptional()
  @ApiPropertyOptional()
  @IsOptional()
  partnerCode?: string;
  @ApiPropertyOptional()
  @IsOptional()
  partnerName?: string;
  @ApiPropertyOptional({
    description: `${Object.values(NSPartner.Status).join(' / ')} (Store: Bưu cục)`,
    enum: NSPartner.Status,
  })
  @IsOptional()
  @IsEnum(NSPartner.Status)
  partnerStatus?: NSPartner.Status;
  @ApiPropertyOptional({ description: 'level', required: false })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => (value ? parseInt(value) : undefined))
  level?: number;

  @ApiPropertyOptional()
  @IsOptional()
  parentId?: string

  @ApiPropertyOptional()
  @IsOptional()
  cardTypeId?: string

  @ApiPropertyOptional({ description: "Lấy cả cấp 1" })
  @IsOptional()
  includesLevel?: string
}
export class CardSalesHistoryPartnerSearchReq extends PageRequest {
  @ApiPropertyOptional()
  personalId?: string;
  @ApiPropertyOptional()
  cardId?: string;
  @ApiPropertyOptional()
  referrerCode?: string;
}

export class ListIdReq {
  @ApiPropertyOptional({ description: 'Danh sách ID đơn hàng cần cập nhật' })
  @IsArray()
  ids: string[];

  @ApiPropertyOptional({ description: 'Trạng thái đơn hàng' })
  @IsNotEmpty()
  status: NSRecurringOrder.EStatus;
}
export class UUIDReq {
  @ApiPropertyOptional({ description: 'ID' })
  @IsNotEmpty()
  @IsUUID()
  id: string;
}