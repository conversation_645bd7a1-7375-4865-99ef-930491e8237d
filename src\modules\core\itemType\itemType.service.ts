import { Injectable, ConflictException, NotFoundException } from '@nestjs/common'
import { ItemTypeRepository, MediaRepository } from '../../../repositories'
import {
  ERROR_NOT_FOUND_DATA,
  ERROR_CODE_TAKEN,
  UPDATE_ACTIVE_SUCCESS,
  CREATE_SUCCESS,
  UPDATE_SUCCESS,
  IMPORT_SUCCESS,
  enumData,
} from '../../../constants'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { In, Like } from 'typeorm'
import { ItemTypeCreateDto, ItemTypeUpdateDto } from './dto'
import { ItemTypeEntity, MediaEntity } from '../../../entities'

@Injectable()
export class ItemTypeService {
  constructor(
    private readonly repo: ItemTypeRepository,
    private mediaRepo: MediaRepository) { }

  public async find(data: any) {
    const whereCon: any = { isDeleted: false }
    if (data.name) whereCon.name = Like(`%${data.name}%`)
    if (data.code) whereCon.code = Like(`%${data.code}%`)
    if (data.lstId?.length > 0) whereCon.id = In(data.lstId)
    if (data.lstCode?.length > 0) whereCon.code = In(data.lstCode)
    const rs = await this.repo.find({ where: whereCon, order: { name: 'ASC' } })
    const ids = rs.map((val) => val.id)
    const imgs = await this.mediaRepo.find({ where: { productId: In(ids) } });

    const lstItemType = rs.map((item) => {
      const imgLst = imgs.filter(i => i.productId == item.id);
      return {
        ...item,
        images: imgLst
      }
    })

    return lstItemType
  }

  public async createData(user: UserDto, data: ItemTypeCreateDto) {
    const checkCodeExist = await this.repo.findOne({ where: { code: data.code } })
    if (checkCodeExist) throw new ConflictException(ERROR_CODE_TAKEN)
    const newEntity = this.repo.create({ ...data })
    newEntity.createdBy = user.id
    newEntity.createdAt = new Date()
    await this.repo.insert(newEntity)
    if (data.lstMediaItemType && data.lstMediaItemType.length > 0) {
      let lstImages: any = []
      for (let item of data.lstMediaItemType) {
        item.productId = newEntity.id
        item.url = item.url
        item.table = enumData.MediaTable.ItemType.code
        lstImages.push(item)
      }
      await this.mediaRepo.insert(lstImages)
    }
    return { message: CREATE_SUCCESS }
  }

  public async updateData(user: UserDto, data: ItemTypeUpdateDto) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    if (entity.code != data.code) {
      const checkCodeExist = await this.repo.findOne({ where: { code: data.code } })
      if (checkCodeExist) throw new ConflictException(ERROR_CODE_TAKEN)
    }
    entity.name = data.name
    entity.code = data.code
    entity.description = data.description

    entity.updatedAt = new Date()
    entity.updatedBy = user.id
    await this.repo.update(entity.id, entity)

    if (data.lstMediaItemType && data.lstMediaItemType.length > 0) {
      await this.mediaRepo.delete({ productId: entity.id, table: enumData.MediaTable.ItemType.code })
      let lstImages: any = []
      for (let item of data.lstMediaItemType) {
        item.productId = entity.id
        item.url = item.url
        item.table = enumData.MediaTable.ItemType.code
        lstImages.push(item)
      }
      await this.mediaRepo.insert(lstImages)
    }
    return { message: UPDATE_SUCCESS }
  }

  public async pagination(data: PaginationDto) {
    let whereCon: any = {}
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    const result: any = await this.repo.findAndCount({
      where: whereCon,
      order: { createdAt: 'DESC', code: 'DESC' },
      skip: data.skip,
      take: data.take,
    })
    const dicMedia: any = {}
    {
      const lstMedia = await this.mediaRepo.find({ where: { table: enumData.MediaTable.ItemType.code } })
      lstMedia.forEach((x) => (dicMedia[x.productId] = x))
    }
    for (let item of result[0]) {
      item.media = dicMedia[item.id]
    }
    return result
  }

  public async updateIsDelete(id: string, user: UserDto) {
    const entity = await this.repo.findOne({ where: { id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    await this.repo.update({ id: entity.id }, { isDeleted: !entity.isDeleted })
    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  async findOne(data: FilterOneDto) {
    const whereCon: any = { id: data.id, isDeleted: false }
    return await this.repo.findOneBy(whereCon)
  }

  public async createDataExcel(data: ItemTypeCreateDto[], user: UserDto) {
    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(ItemTypeEntity)
      const lstInsert: ItemTypeEntity[] = []
      const dicUrl: any = {}
      const dicCode: any = {}
      {
        const lstItemType: any[] = await repo.find({
          where: { isDeleted: false },
          select: { id: true, code: true },
        })
        lstItemType.forEach((c) => (dicCode[c.code] = c))
      }
      const dicCodeFile: any = {}
      for (const [idx, item] of data.entries()) {
        if (dicCodeFile[item.code]) throw new Error(`[ Dòng ${idx + 1} - Mã Loại item [${item.code}] trùng với dòng ${dicCodeFile[item.code]} ]`)
        if (dicCode[item.code]) throw new Error(`[ Dòng ${idx + 1} - Mã Loại item [${item.code}] đã được sử dụng ]`)
        const newItemType = repo.create({
          ...item,
          createdAt: new Date(),
          createdBy: user.id,
        })
        lstInsert.push(newItemType)
        dicCodeFile[item.code] = idx + 1
      }
      await repo.insert(lstInsert)
      for (let item of lstInsert) {
        if (dicUrl[item.code]) {
          const newMedia = new MediaEntity()
          newMedia.url = dicUrl[item.code]
          newMedia.productId = item.id
          newMedia.table = enumData.MediaTable.ItemType.code
          newMedia.createdAt = new Date()
          newMedia.createdBy = user?.id
          await this.mediaRepo.insert(newMedia)
        }
      }
    })
    return { message: IMPORT_SUCCESS }
  }
}
