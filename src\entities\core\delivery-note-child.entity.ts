import { Column, Entity, Index } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { BaseEntity } from './base.entity'
import { NSPo } from '../../constants';

@Entity('delivery_note_child')
export class DeliveryNoteChildEntity extends BaseEntity {
  @ApiProperty({ description: 'Mã Phiếu giao nhận con' })
  @Column({ unique: true })
  @Index()
  code: string;

  @ApiProperty({ description: 'Mã phiếu giao nhận tham chiếu' })
  @Column("uuid")
  @Index()
  deliveryNoteId: string;

  @ApiProperty({ description: "3PL" })
  @Column("uuid", { nullable: true })
  thirdPartyId: string;

  @ApiProperty({ description: "Balance Life Id" })
  @Column("uuid", { nullable: true })
  balanceLifeId: string;

  @ApiPropertyOptional({
    description: `Trạng thái nhận ${Object.values(NSPo.EDeliveryNoteInboundStatus).join(' | ')}`,
    enum: NSPo.EDeliveryNoteInboundStatus,
    default: NSPo.EDeliveryNoteInboundStatus.NOTINSTOCK,
  })
  @Column({ default: NSPo.EDeliveryNoteInboundStatus.NOTINSTOCK })
  status: NSPo.EDeliveryNoteInboundStatus;
}