import { ApiProperty } from '@nestjs/swagger'

export class MediaFilterDto {
  @ApiProperty({ description: 'Id sản phẩm' })
  productId?: string

  @ApiProperty({ description: '<PERSON>h sách Id sản phẩm' })
  lstProductId?: string[]

  @ApiProperty({ description: 'Danh sách Id đơn hàng' })
  lstOrderId?: string[]

  @ApiProperty({ description: 'Id đơn hàng' })
  orderId?: string

  @ApiProperty({ description: 'Loại' })
  type?: string
}
