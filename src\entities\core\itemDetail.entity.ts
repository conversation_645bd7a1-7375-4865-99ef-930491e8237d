import { BaseEntity } from './base.entity'
import { Enti<PERSON>, Column, JoinColumn, ManyToOne, OneToMany, BeforeInsert, BeforeUpdate } from 'typeorm'
import { ItemEntity } from './item.entity'

@Entity('item_detail')
export class ItemDetailEntity extends BaseEntity {
  /** Sản phẩm */
  @Column({ type: 'varchar', nullable: true })
  itemId: string
  @ManyToOne(() => ItemEntity, (p) => p.details)
  @JoinColumn({ name: 'itemId', referencedColumnName: 'id' })
  item: Promise<ItemEntity>

  /** Giá mua */
  @Column({ nullable: true })
  buyPrice: number

  /** Giá vốn */
  @Column({ type: 'decimal', precision: 20, scale: 2, nullable: true, default: 0 })
  costPrice: number

  /** Gi<PERSON> bán */
  @Column({ type: 'decimal', precision: 20, scale: 2, nullable: true, default: 0 })
  sellPrice: number

  /** <PERSON>ố lượng */
  @Column({ nullable: true })
  quantity: number

  /** Số lượng tồn ban đầu */
  @Column({ nullable: true, default: 0 })
  quantityBegin: number

  /** Số lượng đã lên đơn tạm */
  @Column({ nullable: true, default: 0 })
  quantityLock: number

  /** Số lượng lock khi phân kho */
  @Column({ nullable: true, default: 0 })
  quantityLockEmp: number

  /** Ngày sản xuất */
  @Column({ type: 'timestamptz', nullable: true })
  manufactureDate: Date

  /** Hạn sử dụng */
  @Column({ type: 'timestamptz', nullable: true })
  expiryDate: Date

  @Column({ type: 'varchar', length: 100, nullable: true })
  lotNumber: string

  /** Ghi chú */
  @Column({
    type: 'text',
    nullable: true,
  })
  description: string

  /** Cập nhật sl tồn trước khi thêm hoặc cập nhật */
  // @BeforeUpdate()
  // @BeforeInsert()
  // updateQuantity() {
  //   this.quantity = (+this.quantityBegin || 0) + (+this.quantity || 0)
  // }
}
