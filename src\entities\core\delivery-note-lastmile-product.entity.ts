import { Column, Entity, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from './base.entity'

/** Chi tiết hàng hóa, số lượng trong phiếu lastmile */
@Entity('delivery_note_lastmile_product')
export class DeliveryNoteLastMileProductEntity extends BaseEntity {
  @ApiProperty({ description: 'Mã phiếu giao nhận tham chiếu' })
  @Column({ nullable: true })
  @Index()
  code: string;

  @ApiProperty({ description: 'Mã phiếu giao nhận tham chiếu' })
  @Column("uuid")
  @Index()
  deliveryNoteChildDetailId: string;

  @ApiProperty({ description: "ID sản phẩm" })
  @Column("uuid", { nullable: true })
  productId: string;

  @ApiProperty({ description: "ID PO" })
  @Column("uuid", { nullable: true })
  poId: string;

  @ApiProperty({ description: "Số lượng khởi tạo" })
  @Column({ nullable: true })
  quantityBegin: number;

  @ApiProperty({ description: "Số lượng đã mang đi nhập kho" })
  @Column({ nullable: true })
  quantityExport: number;
}