import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm'
import { ItemEntity } from './item.entity'
import { BaseEntity } from './base.entity'
@Entity('item_price_discount')
export class ItemPriceDiscountEntity extends BaseEntity {
  @Column({ type: 'uuid', nullable: false })
  itemId: string

  @ManyToOne(() => ItemEntity, (p) => p.id, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'itemId', referencedColumnName: 'id' })
  item: Promise<ItemEntity>

  @Column({ type: 'int', nullable: false })
  targetQuantity: number

  @Column({ type: 'decimal', precision: 20, scale: 2, nullable: false, default: 0 })
  priceOriginal: number
}
