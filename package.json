{"name": "ntvv_hr_api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "prestart": "yarn build", "start": "nest start", "dev": "yarn start --watch", "start:debug": "nest start --watch --debug 3300", "start:prod": "yarn build && node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "ts-node ./node_modules/typeorm/cli -d ./src/typeorm/typeorm.config.ts", "typeorm:generate-migration": "yarn typeorm migration:generate ./src/migrations/migration_name", "typeorm:run-migrations": "yarn typeorm migration:run", "typeorm:revert-migration": "yarn typeorm migration:revert"}, "dependencies": {"@nestjs/axios": "^2.0.0", "@nestjs/common": "^9.0.0", "@nestjs/config": "^2.2.0", "@nestjs/core": "^9.0.0", "@nestjs/jwt": "^9.0.0", "@nestjs/passport": "^9.0.0", "@nestjs/platform-express": "^9.0.0", "@nestjs/schedule": "^2.1.0", "@nestjs/swagger": "^6.3.0", "@nestjs/typeorm": "^9.0.0", "@types/bcrypt": "^5.0.0", "@types/cron": "^2.0.0", "@types/multer": "^1.4.7", "@types/passport-jwt": "^3.0.7", "@types/passport-local": "^1.0.34", "@types/validator": "^13.7.10", "@types/passport-oauth2": "^1.8.0", "aws-sdk": "^2.1363.0", "axios": "^1.4.0", "bcrypt": "^5.1.0", "class-transformer": "^0.5.1", "class-validator": "^0.13.2", "express-rate-limit": "^6.5.1", "firebase-admin": "10.2.0", "html-pdf-node": "^1.0.8", "html-to-pdf": "^0.1.11", "ip": "^1.1.8", "mathjs": "^11.8.0", "moment": "^2.29.4", "mysql2": "^2.3.3", "nanoid": "^3.1.20", "nodemailer": "^6.9.7", "passport": "^0.7.0", "passport-jwt": "^4.0.0", "passport-local": "^1.0.0", "passport-oauth2": "^1.8.0", "pdf-creator-node": "^2.3.5", "pdfkit": "^0.14.0", "pg": "^8.13.1", "puppeteer": "^23.11.1", "reflect-metadata": "^0.1.13", "request-ip": "^3.3.0", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "sharp": "^0.33.5", "sqs-consumer": "^7.0.3", "typeorm": "^0.3.10", "xlsx": "^0.18.5"}, "devDependencies": {"@nestjs/cli": "^9.0.0", "@nestjs/schematics": "^9.0.0", "@nestjs/testing": "^9.0.0", "@types/cron": "^2.0.0", "@types/express": "^4.17.13", "@types/jest": "28.1.4", "@types/node": "^16.0.0", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "28.1.2", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "28.0.5", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.0.0", "typescript": "^4.3.5"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^@/(.*)$": "<rootDir>/../src/$1", "^@dto/(.*)$": "<rootDir>/../src/dto/$1"}}}