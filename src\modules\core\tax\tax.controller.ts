import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { TaxCreateDto, TaxUpdateDto } from './dto'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { CurrentUser } from '../../common/decorators'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { Request as IRequest } from 'express'
import { JwtAuthGuard } from '../../common/guards'
import { TaxService } from './tax.service'
@ApiBearerAuth()
@ApiTags('Tax')
@UseGuards(JwtAuthGuard)
@Controller('tax')
export class TaxController {
  constructor(private readonly service: TaxService) {}

  @Post('find')
  async find(@Body() data: any) {
    return await this.service.find(data)
  }

  @Post('load_data')
  async loadData() {
    return await this.service.loadData()
  }

  @Post('pagination')
  async pagination(@Body() data: PaginationDto) {
    return await this.service.pagination(data)
  }

  @Post('create_data')
  async createData(@CurrentUser() user: UserDto, @Body() data: TaxCreateDto) {
    return await this.service.createData(user, data)
  }

  @Post('update_data')
  async updateData(@CurrentUser() user: UserDto, @Body() data: TaxUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @Post('update_active')
  async updateActive(@Body() data: FilterOneDto, @CurrentUser() user: UserDto) {
    return await this.service.updateIsDelete(data, user)
  }

  @Post('find_one')
  async findOne(@Body() data: FilterOneDto) {
    return await this.service.findOne(data)
  }

  @Post('create_data_excel')
  public async createDataExcel(@Body() data: TaxCreateDto[], @CurrentUser() user: UserDto) {
    return await this.service.createDataExcel(data, user)
  }
}
