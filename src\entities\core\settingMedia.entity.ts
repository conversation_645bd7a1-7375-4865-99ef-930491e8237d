import { BaseEntity } from './base.entity'
import { Entity, Column, OneToMany } from 'typeorm'

/** C<PERSON><PERSON> h<PERSON>nh <PERSON>  */
@Entity('setting_media')
export class SettingMediaEntity extends BaseEntity {
  /** Link*/
  @Column({ type: 'varchar', length: 100, nullable: true })
  link: string

  /** URL Video */
  @Column({ type: 'varchar', length: 250, nullable: false })
  url: string

  @Column({ type: 'varchar', array: true, nullable: true })
  banner: string[]

  /** content */
  @Column({ type: 'varchar', nullable: false })
  content: string

  /** Type */
  @Column({ type: 'varchar', length: 50, nullable: true })
  type: string

  @Column({ type: 'varchar', length: 50, nullable: true })
  typeName: string

  /** điều khoản */
  @Column({ type: 'varchar', nullable: true })
  term: string

  /**<PERSON>i<PERSON><PERSON> cọc cá nhân */
  @Column({ nullable: true, default: 0 })
  personalDeposit: number

  /**Tiền cọc doanh nghiệp */
  @Column({ nullable: true, default: 0 })
  corporateDeposit: number

  @Column({
    type: 'varchar',
    array: true,
    nullable: true,
  })
  img: string[]

  /**Hoa hồng */
  @Column({ nullable: true, default: 0 })
  commission: number

  /** Bank code*/
  @Column({ type: 'varchar', length: 250, nullable: true })
  bankCode: string
}
