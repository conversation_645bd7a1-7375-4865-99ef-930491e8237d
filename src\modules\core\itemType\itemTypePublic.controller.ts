import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { JwtAuthGuard } from '../../common/guards'
import { FilterOneDto, PaginationDto } from '../../../dto'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { ItemTypeService } from './itemType.service'
import { AuthGuard } from '../../common/guards/auth.guard'


@ApiTags('ItemType')
@Controller('item_type_public')
export class ItemTypePublicController {
  constructor(private readonly service: ItemTypeService) { }

  @Post('find')
  public async find(@Body() data: any) {
    return await this.service.find(data)
  }

  @Post('pagination')
  public async pagination(@Body() data: PaginationDto) {
    return await this.service.pagination(data)
  }

  @Post('find_one')
  public async findOne(@Body() data: FilterOneDto) {
    return await this.service.findOne(data)
  }

}
