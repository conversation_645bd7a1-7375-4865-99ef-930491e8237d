'use strict'
import * as nodemailer from 'nodemailer'
import { Injectable } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { SQSService } from '../common/sqs/sqs.service'
import { EmailSendPasswordDto } from './dto'
import { EmailHistoryRepository } from '../../../repositories/survey'
import { enumData } from '../../../constants'
import { EmailHistoryEntity } from '../../../entities/survey'
import { EmployeeRepository, PurchaseOrderRepository, SupplierRepository } from '../../../repositories'
import { EmailSendDto } from './dto/emailSend.dto'
import { UserDto } from '../../../dto'

@Injectable()
export class EmailService {
  //#region config

  // If you're using Amazon Pinpoint in a region other than US West (Oregon),
  // replace email-smtp.us-west-2.amazonaws.com with the Amazon Pinpoint SMTP
  // endpoint in the appropriate AWS Region.
  private smtpEndpoint: string

  // The port to use when connecting to the SMTP server.
  private port: number

  // Replace <EMAIL> with your "From" address.
  // This address must be verified with Amazon Pinpoint.
  private senderAddress: string
  // 'Mary Major <<EMAIL>>'

  // Replace smtp_username with your Amazon Pinpoint SMTP user name.
  private smtpUsername: string

  // Replace smtp_password with your Amazon Pinpoint SMTP password.
  private smtpPassword: string
  private transporter: any
  constructor(
    private readonly configService: ConfigService,
    private readonly sqsService: SQSService,
    private readonly emailHistoryRepo: EmailHistoryRepository,
    private readonly purchaseOrderRepo: PurchaseOrderRepository,
    private readonly supplierRepo: SupplierRepository,
    private readonly employeeRepo: EmployeeRepository,
  ) {
    this.smtpEndpoint = this.configService.get<string>('AWS_SMTP_END_POINT') || 'email-smtp.ap-southeast-2.amazonaws.com'

    this.port = this.configService.get<number>('AWS_SMTP_PORT') || 587

    this.senderAddress = this.configService.get<string>('AWS_SMTP_SENDER_ADDRESS') || process.env.BL_EMAIL

    this.smtpUsername = this.configService.get<string>('AWS_SMTP_USERNAME') || process.env.BL_EMAIL

    this.smtpPassword = this.configService.get<string>('AWS_SMTP_PASSWORD') || process.env.BL_EMAIL_PASSWORD

    this.transporter = nodemailer.createTransport({
      host: 'smtp.gmail.com',
      port: this.port,
      secure: false, // true for 465, false for other ports
      auth: {
        user: this.smtpUsername,
        pass: this.smtpPassword,
      },
    })
  }

  public async sendEmail(params: EmailSendDto) {
    const {
      toAddresses,
      subject,
      ccAddresses,
      bccAddresses,
      body_text,
      body_html,
      type,
      isResend = false,
      historyId = '',
      companyId = '',
      userId = '',
    } = params
    // Create the SMTP transport.

    // Specify the fields in the email.
    const mailOptions = {
      from: this.senderAddress,
      to: toAddresses,
      subject: subject,
      cc: ccAddresses,
      bcc: bccAddresses,
      text: body_text,
      html: body_html,
    }

    let info: any
    let status = enumData.EmailStatus.Success.code
    // Send the email.
    try {
      info = await this.transporter.sendMail(mailOptions)
    } catch (error) {
      info = error
      status = enumData.EmailStatus.Fail.code
    }
    if (!isResend) {
      const emailHistory = new EmailHistoryEntity()
      emailHistory.toAddresses = toAddresses
      emailHistory.subject = subject
      emailHistory.ccAddresses = ccAddresses
      emailHistory.bccAddresses = bccAddresses
      emailHistory.body_text = body_text
      emailHistory.body_html = body_html
      emailHistory.status = status
      emailHistory.result = info
      emailHistory.type = type
      emailHistory.companyId = companyId
      emailHistory.createdBy = userId
      await emailHistory.save()
    } else {
      const emailHistory = await this.emailHistoryRepo.findOne({ where: { id: historyId } })
      if (!emailHistory) return

      emailHistory.toAddresses = toAddresses
      emailHistory.subject = subject
      emailHistory.ccAddresses = ccAddresses
      emailHistory.bccAddresses = bccAddresses
      emailHistory.body_text = body_text
      emailHistory.body_html = body_html
      emailHistory.status = status
      emailHistory.result = info
      emailHistory.count = emailHistory.count + 1
      emailHistory.updatedBy = userId

      await this.emailHistoryRepo.update(historyId, emailHistory)
    }
    return info
  }

  //#endregion

  //#region Danh sách các hàm email

  /** Gửi mật khẩu qua email đăng ký tài khoản khi user quên mật khẩu */
  public async sendPassword(data: EmailSendPasswordDto) {
    // Người nhận mail
    const toAddresses = data.email
    // Tiêu đề mail
    const subject = '[SURVEY AUTH] Mật khẩu mới'
    // nội dung mail
    const text_html = `<html>
<head></head>
<body>
  <h1>New Password</h1>
  <p>Mật khẩu mới cho tài khoản của bạn là: ${data.password}</p>
</body>
</html>`

    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject,
        ccAddresses: '',
        bccAddresses: '',
        body_text: text_html,
        body_html: text_html,
        type: 'SendPassword',
        companyId: data.companyId,
      },
    })
  }

  //#endregion
}
