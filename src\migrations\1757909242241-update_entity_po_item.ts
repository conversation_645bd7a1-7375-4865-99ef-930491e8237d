import { MigrationInterface, QueryRunner } from "typeorm";

export class updateEntityPoItem1757909242241 implements MigrationInterface {
    name = 'updateEntityPoItem1757909242241'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "purchase_order_item" ADD "itemCode" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "purchase_order_item" DROP COLUMN "itemCode"`);
    }

}
