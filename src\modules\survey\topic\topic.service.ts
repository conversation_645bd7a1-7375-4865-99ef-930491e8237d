import { ConflictException, Injectable, NotFoundException } from '@nestjs/common'
import { In, IsNull, Like } from 'typeorm'
import { CREATE_SUCCESS, enumData, ERROR_CODE_TAKEN, ERROR_NOT_FOUND_DATA, UPDATE_ACTIVE_SUCCESS, UPDATE_SUCCESS } from '../../../constants'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { SurveyHistoryEntity, TopicEntity } from '../../../entities/survey'
import {
  CategoriesRepository,
  QuestionRepository,
  SurveyHistoryRepository,
  SurveyQuestionListDetailRepository,
  SurveyQuestionRepository,
  SurveyRepository,
  TopicRepository,
  UserSurveyRepository,
} from '../../../repositories/survey'
import { TopicCreateDto, TopicCreateExcelDto, TopicCreateMasterDataDto, TopicUpdateDto } from './dto'
@Injectable()
export class TopicService {
  constructor(
    private readonly repo: TopicRepository,
    private readonly categoryRepo: CategoriesRepository,
    private readonly surveyRepo: SurveyRepository,
    private readonly questionRepo: QuestionRepository,
    private readonly surveyQuestionListDetailRepo: SurveyQuestionListDetailRepository,
    private readonly surveyQuesRepo: SurveyQuestionRepository,
    private surveyHistoryRepo: SurveyHistoryRepository,
    private readonly userRepo: UserSurveyRepository,
  ) {}

  /** Hàm tìm kiếm */
  public async find(data: { isDeleted?: boolean; categoryCode?: string; categoryId?: string; categoryName?: string }) {
    let whereCon: any = { isDeleted: false }
    if (data.isDeleted !== undefined) whereCon.isDeleted = data.isDeleted
    if (data.categoryCode !== undefined) whereCon.category = { code: data.categoryCode }
    if (data.categoryName !== undefined) whereCon.category = { name: Like(data.categoryName) }
    if (data.categoryId !== undefined) whereCon.categoryId = data.categoryId
    const res = await this.repo.find({ where: whereCon, order: { createdAt: 'DESC' } })
    return res
  }

  /**
   * Hàm thêm mới chủ đề
   * @param user User Token
   * @param id Id của chủ đề
   * @param code Mã chủ đề
   * @param name Tên chủ đề
   * @param description Ghi chú
   * @returns message: Tạo mới thành công
   */
  public async createData(user: UserDto, data: TopicCreateDto) {
    const category = await this.categoryRepo.findOne({ where: { id: data.categoryId, isDeleted: false } })
    const lstHitory = []
    if (!category) throw new Error(ERROR_NOT_FOUND_DATA + 'danh mục!')
    const topic: any = data
    topic.code = await this.codeDefault()
    if (data.isTail === true) {
      await this.repo.update({ categoryId: category.id }, { isTail: false })
    }
    const newEntity = this.repo.create(data)
    newEntity.createdAt = new Date()
    await newEntity.save()
    const history = new SurveyHistoryEntity()
    history.topicId = newEntity.id
    history.createdAt = new Date()
    history.createdBy = user.id
    history.description = `Nhân viên [${user.username}] vừa tạo chủ đề khảo sát có mã là [${newEntity.code}] `
    history.status = enumData.TopicStatus.New.code
    await this.surveyHistoryRepo.save(history)

    return { message: CREATE_SUCCESS }
  }

  private async codeDefault() {
    const objData = await this.repo.findOne({
      where: { code: Like(`TOPIC_%`) },
      order: { code: 'DESC' },
    })
    let sortString = '0'
    if (objData) {
      sortString = objData.code.substring(6, 10)
    }
    const lastSort = parseInt(sortString)
    sortString = ('000' + (lastSort + 1)).slice(-4)

    return 'TOPIC_' + sortString
  }

  /**
   * Hàm cập nhật chủ đề
   * @param user User Token
   * @param id Id của chủ đề
   * @param code Mã chủ đề
   * @param name Tên chủ đề
   * @param description Ghi chú
   * @returns message: Cập nhật thành công
   */
  public async updateData(user: UserDto, data: TopicUpdateDto) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new NotFoundException(ERROR_NOT_FOUND_DATA + 'chủ đề')
    const category = await this.categoryRepo.findOne({ where: { id: data.categoryId, isDeleted: false } })
    if (!category) throw new NotFoundException(ERROR_NOT_FOUND_DATA + 'danh mục')
    //entity.updatedBy = user.id
    if (data.isTail) {
      let lstTopic = await this.repo.find({ where: { categoryId: data.categoryId, isTail: true } })
      for (let item of lstTopic) {
        if (item.id != data.id) {
          item.isTail = false
          await this.repo.update(item.id, { isTail: false, updatedAt: new Date() })
        }
      }
    }
    entity.isTail = data.isTail
    entity.categoryId = data.categoryId
    entity.updatedAt = new Date()
    entity.name = data.name
    entity.description = data.description
    // entity.companyId = user.companyId
    await entity.save()
    const history = new SurveyHistoryEntity()
    history.topicId = entity.id
    history.createdAt = new Date()
    history.createdBy = user.id
    history.description = `Nhân viên [${user.username}] vừa update chủ đề khảo sát có mã là [${entity.code}] `
    history.status = enumData.TopicStatus.Update.code
    await this.surveyHistoryRepo.save(history)

    return { message: UPDATE_SUCCESS }
  }

  public async findWithSurvey(data: { isDeleted?: boolean; type?: string }) {
    let whereCon: any = {}
    if (data.isDeleted != undefined) whereCon.isDeleted = data.isDeleted
    if (data.type != undefined) whereCon.type = data.type
    let res: any = await this.repo.find({ select: { name: true, id: true }, order: { createdAt: 'DESC' } })
    let list = []
    for (let r of res) {
      let survey = await this.surveyRepo.findOne({
        where: { questions: { topicId: r.id } },
        relations: { questions: true },
        order: { createdAt: 'DESC' },
      })
      if (survey) r.surveyId = survey.id
    }
    return res
  }

  /**
   * Hàm phân trang
   * @param relations mối quan hệ tới những bảng khác trong db
   * @param where các điều kiện tìm kiếm
   * @param order thứ tự sắp xếp kết quả trả về theo 1 cột nào đó
   * @param skip dùng để phẩn trang, chỉ định vị trí bắt đầu của kết quả trả về
   * @param take dùng để phẩn trang, giới hạn kết quả trả về
   * @returns
   */
  public async pagination(data: PaginationDto) {
    const whereCon: any = {}
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.categoryId) whereCon.categoryId = data.where.categoryId
    const res: any = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
      relations: { questions: true, category: true },
    })
    for (const item of res[0]) {
      item.numQuestion = item.__questions__.length || 0
      if (item.__category__ !== null) {
        item.cateName = item.__category__?.name
      }

      delete item.__questions__
      delete item.__category__
    }
    return res
  }

  /**
   * Hàm cập nhật trạng thái hoạt động của chủ đề
   * @param user User Token
   * @param idStr id của chủ đề
   * @returns message: Cập nhật trạng thái thành công
   */
  public async updateActive(user: UserDto, idStr: string) {
    const entity = await this.repo.findOne({ where: { id: idStr } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    await this.repo.update(idStr, { isDeleted: !entity.isDeleted, updatedBy: user.id, updatedAt: new Date() })
    const history = new SurveyHistoryEntity()
    history.topicId = entity.id
    history.createdAt = new Date()
    history.createdBy = user.id
    history.description = `Nhân viên [${user.username}] vừa cập nhật trạng thái có mã là [${entity.code}] `
    history.status = enumData.TopicStatus.Update.code
    await this.surveyHistoryRepo.save(history)

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  /**
   * Hàm cập nhật chủ đề bẳng excel
   */
  public async createDataByExcel(user: UserDto, data: TopicCreateExcelDto[]) {
    await this.repo.manager.transaction('READ UNCOMMITTED', async (trans) => {
      const lstTask = []
      const repo = trans.getRepository(TopicEntity)
      // danh sách chủ đề
      const dictTopic: any = {}
      {
        const lstTopic = await repo.find({ where: { isDeleted: false } })
        lstTopic.forEach((c) => (dictTopic[c.code] = c.id))
      }

      for (const item of data) {
        if (dictTopic[item.code]) throw new Error(`Mã chủ đề [${item.code}] đã có trong hệ thống`)
      }

      const dictCategory: any = {}
      {
        const lstCategory = await this.categoryRepo.find({ where: { isDeleted: false } })
        lstCategory.forEach((x) => (dictCategory[x.code] = x.id))
      }

      const setExcelTopic = new Set()
      data.forEach((topic) => {
        if (setExcelTopic.has(topic.code) === true) {
          throw new Error(`Mã chủ đề [${topic.code}] lặp lại trong file excel`)
        }
        setExcelTopic.add(topic.code)
      })

      for (let item of data) {
        if (!dictCategory[item.cateCode]) throw new Error(ERROR_NOT_FOUND_DATA + ` Danh mục [${item.cateCode}]`)
        const topic = new TopicEntity()
        topic.createdBy = user.id
        topic.createdAt = new Date()
        topic.code = await this.codeDefault()
        topic.name = item.name
        topic.categoryId = dictCategory[item.cateCode]
        topic.description = item.description
        await repo.save(topic)
      }
      return { message: CREATE_SUCCESS }
    })
  }

  public async createMasterData(data: TopicCreateMasterDataDto[]) {
    let lstTask = []
    await this.repo.manager.transaction('READ UNCOMMITTED', async (trans) => {
      const repo = trans.getRepository(TopicEntity)
      for (let item of data) {
        const topic = new TopicEntity()
        topic.createdBy = item.createdBy
        topic.createdAt = new Date()
        topic.code = item.code
        topic.name = item.name
        topic.description = item.description
        const task = repo.save(topic)
        lstTask.push(task)
      }
      await Promise.all(lstTask)
    })

    return { message: CREATE_SUCCESS }
  }

  /** Lấy bộ câu hỏi đầu tiên của danh mục câu hỏi */
  public async findQuestionOfFirstTopic(topicId: string) {
    let topic = await this.repo.findOne({ where: { id: topicId }, order: { createdAt: 'DESC' } })
    if (!topic) {
      throw new NotFoundException('Không tìm thấy câu hỏi nào theo chủ đề này!')
    }

    let survey = await this.surveyRepo.findOne({
      where: { status: enumData.SurveyStatus.Doing.code, isDeleted: false, questions: { topicId: topicId } },
      relations: { questions: true },
      order: { createdAt: 'DESC' },
    })
    if (!survey) {
      throw new NotFoundException('Không tìm thấy câu hỏi nào theo chủ đề này!')
    }
    let lstSurveyQuestion: any = await this.surveyQuesRepo.find({
      where: { surveyId: survey.id, isDeleted: false, surveyMemberId: IsNull() },
    })
    if (lstSurveyQuestion.length > 0) {
      let lstSurveyQuestionId = lstSurveyQuestion.map((x: { questionId: any }) => x.questionId)
      if (lstSurveyQuestionId.length > 0) {
        let lstQuestion: any = await this.surveyQuestionListDetailRepo.find({
          where: {
            id: In(lstSurveyQuestionId),
            isDeleted: false,
          },
          relations: { childs: true },
          order: {
            sort: 'ASC',
            createdAt: 'ASC',
            childs: { sort: 'ASC', createdAt: 'ASC' },
          },
        })

        for (let question of lstQuestion) {
          if (question.questionListDetail) {
            question.__questionlistDetails__ = JSON.parse(question.questionListDetail)
            question.__questionlistDetails__ = question.__questionlistDetails__.sort((a, b) => a.sort - b.sort)
            delete question.questionListDetail
          }
          const lstChilds = await question.childs
          for (let child of lstChilds) {
            if (child.questionListDetail) {
              child.__questionlistDetails__ = JSON.parse(child.questionListDetail)
              child.__questionlistDetails__ = child.__questionlistDetails__.sort((a, b) => a.sort - b.sort)
              delete child.questionListDetail
            }
          }
        }

        let lstQuestionId: any[] = lstQuestion.map((x) => x.id)
        let lstQuestionParent: any[] = lstQuestion.filter((x) => x.parentId !== null)
        let mapQuestionDistinct = lstQuestionParent.mapAndDistinct((x) => x.parentId)
        for (let item of mapQuestionDistinct) {
          if (item == null) continue
          let question = lstQuestion.find((x) => x.id == item)
          if (question) {
            let lstChilds = await question.childs
            lstChilds = lstChilds.filter((x) => lstQuestionId.includes(x.id))
            let lstChildId = lstChilds.map((x) => x.id)
            lstQuestion = lstQuestion.filter((x) => !lstChildId.includes(x.id))
            delete question.__childs__
            question.childs = lstChilds
            if (lstChilds.length > 0) question.type = null
          }
        }
        const surveyInfo: any = survey
        delete surveyInfo.__questions__
        return {
          survey: surveyInfo,
          data: lstQuestion,
        }
      }
    }
    const surveyInfo: any = survey
    delete surveyInfo.__questions__
    return {
      survey: survey,
      data: [],
    }
  }
  public async findDetail(user: UserDto, data: FilterOneDto) {
    const entity: any = await this.repo.findOne({
      where: { id: data.id },
      relations: { histories: true },
    })
    if (!entity) throw new Error('Chủ đề không tồn tại')

    /** Lấy lịch sử */
    let lstHitory = await entity.histories
    for (let item of lstHitory) {
      const user: any = await this.userRepo.findOne({ where: { id: item.createdBy } })
      if (user) {
        item.createdByName = user?.username
      }
    }
    entity.lstHitory = lstHitory.sort((a: any, b: any) => (a.createdAt > b.createdAt ? 1 : -1))
    delete entity.__histories__
    return entity
  }
}
