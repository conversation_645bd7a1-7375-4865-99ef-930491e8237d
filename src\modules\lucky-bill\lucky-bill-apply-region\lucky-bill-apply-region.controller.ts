import { Controller, Post, Body, UseGuards, Param, Get } from '@nestjs/common'
import { LuckyBillApplyRegionService } from './lucky-bill-apply-region.service'
import { CreateLuckyBillApplyRegionDto } from './dto/create-lucky-bill-apply-region.dto'
import { CurrentUser, JwtAuthGuard } from '../../common'
import { UserDto } from '../../../dto'
import { UpdateLuckyBillApplyRegionDto } from './dto/update-lucky-bill-apply-region.dto'
import { ListLuckyBillApplyRegionDto } from './dto/list-lucky-bill.dto'

@Controller('lucky-bill-apply-region')
@UseGuards(JwtAuthGuard)
export class LuckyBillApplyRegionController {
  constructor(private readonly luckyBillApplyRegionService: LuckyBillApplyRegionService) {}
  //create
  @Post('create')
  create(@Body() createLuckyBillApplyRegionDto: CreateLuckyBillApplyRegionDto, @CurrentUser() user: UserDto) {
    return this.luckyBillApplyRegionService.create(createLuckyBillApplyRegionDto, user)
  }

  //list
  @Post('list')
  list(@Body('luckyBillConfigId') luckyBillConfigId: string) {
    return this.luckyBillApplyRegionService.list(luckyBillConfigId)
  }

  //update
  @Post('update')
  update(@Body() updateLuckyBillApplyRegionDto: UpdateLuckyBillApplyRegionDto, @CurrentUser() user: UserDto) {
    return this.luckyBillApplyRegionService.update(updateLuckyBillApplyRegionDto, user)
  }

  //delete
  @Post('delete/:id')
  delete(@Param('id') id: string, @CurrentUser() user: UserDto) {
    return this.luckyBillApplyRegionService.delete(id, user)
  }
}
