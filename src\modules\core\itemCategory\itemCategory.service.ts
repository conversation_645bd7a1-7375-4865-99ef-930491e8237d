import { Injectable, ConflictException, NotFoundException } from '@nestjs/common'
import { ItemCategoryRepository, ItemGroupRepository, ItemTypeRepository, MediaRepository } from '../../../repositories'
import {
  ERROR_NOT_FOUND_DATA,
  ERROR_CODE_TAKEN,
  UPDATE_ACTIVE_SUCCESS,
  CREATE_SUCCESS,
  UPDATE_SUCCESS,
  IMPORT_SUCCESS,
  enumData,
} from '../../../constants'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { In, Like } from 'typeorm'
import { ItemCategoryCreateDto, ItemCategoryUpdateDto } from './dto'
import { ItemCategoryEntity } from '../../../entities'
import { ItemCategoryCreateExcelDto } from './dto/itemCategoryCreateExcel.dto'

@Injectable()
export class ItemCategoryService {
  constructor(
    private readonly repo: ItemCategoryRepository,
    private readonly itemTypeRepo: ItemTypeRepository,
    private readonly itemGroupRepo: ItemGroupRepository,
    private mediaRepo: MediaRepository,
  ) {}

  public async find(data: any) {
    const whereCon: any = { isDeleted: false }
    if (data.name) whereCon.name = Like(`%${data.name}%`)
    if (data.code) whereCon.code = Like(`%${data.code}%`)
    if (data.lstId?.length > 0) whereCon.id = In(data.lstId)
    if (data.lstCode?.length > 0) whereCon.code = In(data.lstCode)
    if (data.lstItemTypeId?.length > 0) whereCon.itemTypeId = In(data.lstItemTypeId)
    if (data.itemTypeId) whereCon.itemTypeId = data.itemTypeId
    const rs = await this.repo.find({ where: whereCon, order: { name: 'ASC' } })
    const ids = rs.map((val) => val.id)
    const imgs = await this.mediaRepo.find({ where: { productId: In(ids) } })

    const lstItemCategory = rs.map((item) => {
      const imgLst = imgs.filter((i) => i.productId == item.id)
      return {
        ...item,
        images: imgLst,
      }
    })

    return lstItemCategory
  }

  /** Trả ra danh sách category và group của category */
  public async findCategoryByType(typeId: string) {
    const rs = await this.repo.find({ where: { itemTypeId: typeId, isDeleted: false } })
    const categoryIds = rs.map((c) => c.id)
    const groupLst = await this.itemGroupRepo.find({ where: { itemCategoryId: In(categoryIds) } })
    const groupIds = groupLst.map((g) => g.id)

    const imgs = await this.mediaRepo.find({ where: { productId: In([...categoryIds, ...groupIds]) } })

    const mappingRs = rs.map((val) => {
      const groupItems = groupLst.filter((i) => i.itemCategoryId === val.id)
      const mappingMediaGroup = groupItems.map((g) => {
        const img = imgs.find((i) => i.productId == g.id)
        return { ...g, image: img?.url || '' }
      })
      const imgCate = imgs.find((i) => i.productId == val.id)
      return {
        ...val,
        image: imgCate?.url || '',
        groupItems: mappingMediaGroup,
      }
    })

    return mappingRs
  }

  public async createData(user: UserDto, data: ItemCategoryCreateDto) {
    const checkCodeExist = await this.repo.findOne({ where: { code: data.code } })
    if (checkCodeExist) throw new ConflictException(ERROR_CODE_TAKEN)
    if (data.itemTypeId) {
      const checkItemType = await this.itemTypeRepo.findOne({ where: { id: data.itemTypeId, isDeleted: false } })
      if (!checkItemType) throw new Error(ERROR_NOT_FOUND_DATA + 'Loại sản phẩm')
    }
    const newEntity = this.repo.create({ ...data })
    newEntity.createdBy = user.id
    newEntity.createdAt = new Date()
    await this.repo.insert(newEntity)
    if (data.lstMediaItemType && data.lstMediaItemType.length > 0) {
      let lstImages: any = []
      for (let item of data.lstMediaItemType) {
        item.productId = newEntity.id
        item.url = item.url
        item.table = enumData.MediaTable.ItemCategory.code
        lstImages.push(item)
      }
      await this.mediaRepo.insert(lstImages)
    }
    return { message: CREATE_SUCCESS }
  }

  public async updateData(user: UserDto, data: ItemCategoryUpdateDto) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    if (data.itemTypeId) {
      const checkItemType = await this.itemTypeRepo.findOne({ where: { id: data.itemTypeId, isDeleted: false } })
      if (!checkItemType) throw new Error(ERROR_NOT_FOUND_DATA + 'Loại sản phẩm')
    }
    if (entity.code != data.code) {
      const checkCodeExist = await this.repo.findOne({ where: { code: data.code } })
      if (checkCodeExist) throw new ConflictException(ERROR_CODE_TAKEN)
    }
    entity.name = data.name
    entity.code = data.code
    entity.itemTypeId = data.itemTypeId
    entity.description = data.description

    entity.updatedAt = new Date()
    entity.updatedBy = user.id
    await this.repo.update(entity.id, entity)

    if (data.lstMediaItemType && data.lstMediaItemType.length > 0) {
      await this.mediaRepo.delete({ productId: entity.id, table: enumData.MediaTable.ItemCategory.code })
      let lstImages: any = []
      for (let item of data.lstMediaItemType) {
        item.productId = entity.id
        item.url = item.url
        item.table = enumData.MediaTable.ItemCategory.code
        lstImages.push(item)
      }
      await this.mediaRepo.insert(lstImages)
    }

    return { message: UPDATE_SUCCESS }
  }

  public async pagination(data: PaginationDto) {
    let whereCon: any = {}
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.itemTypeId) whereCon.itemTypeId = data.where.itemTypeId
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    const result: any = await this.repo.findAndCount({
      where: whereCon,
      order: { createdAt: 'DESC', code: 'DESC' },
      skip: data.skip,
      take: data.take,
      relations: { itemType: true },
    })
    const dicMedia: any = {}
    {
      const lstMedia = await this.mediaRepo.find({ where: { table: enumData.MediaTable.ItemCategory.code } })
      lstMedia.forEach((x) => (dicMedia[x.productId] = x))
    }
    for (let item of result[0]) {
      item.media = dicMedia[item.id]
    }
    return result
  }

  public async updateIsDelete(id: string, user: UserDto) {
    const entity = await this.repo.findOne({ where: { id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    await this.repo.update({ id: entity.id }, { isDeleted: !entity.isDeleted })
    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  async findOne(data: FilterOneDto) {
    const whereCon: any = { id: data.id, isDeleted: false }
    return await this.repo.findOneBy(whereCon)
  }

  public async createDataExcel(data: ItemCategoryCreateExcelDto[], user: UserDto) {
    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(ItemCategoryEntity)
      const dicCode: any = {}
      {
        const lstItemCategory: any[] = await repo.find({
          where: { isDeleted: false },
          select: { id: true, code: true },
        })
        lstItemCategory.forEach((c) => (dicCode[c.code] = c))
      }
      const dictItemType: any = {}
      {
        const lstItemType: any[] = await this.itemTypeRepo.find({ where: { isDeleted: false }, select: { id: true, code: true } })
        lstItemType.forEach((x) => (dictItemType[x.code] = x.id))
      }
      const dicCodeFile: any = {}
      for (const [idx, item] of data.entries() as any) {
        if (dicCodeFile[item.code]) throw new Error(`[ Dòng ${idx + 1} - Mã Loại item [${item.code}] trùng với dòng ${dicCodeFile[item.code]} ]`)
        if (item.itemTypeCode && !dictItemType[item.itemTypeCode])
          throw new Error(`[ Dòng ${idx + 1} - Mã Loại sản phẩm [${item.itemTypeCode}] không tìm thấy ]`)
        item.itemTypeId = dictItemType[item.itemTypeCode] || null
        delete item.itemTypeCode

        if (!dicCode[item.code]) {
          const newItemCategory: ItemCategoryEntity = repo.create({
            code: item.code,
            name: item.name,
            itemTypeId: item.itemTypeId,
            description: item.description,
            createdAt: new Date(),
            createdBy: user.id,
          })
          await repo.insert(newItemCategory)

          dicCodeFile[item.code] = idx + 1
          let newMedia: any = {}
          if (item.url) {
            newMedia.productId = newItemCategory.id
            newMedia.url = item.url
            newMedia.table = enumData.MediaTable.ItemCategory.code
            await this.mediaRepo.insert(newMedia)
          }
        } else {
          const newItemCategory: ItemCategoryEntity = repo.create({
            code: item.code,
            name: item.name,
            itemTypeId: item.itemTypeId,
            description: item.description,
            createdAt: new Date(),
            createdBy: user.id,
          })

          await repo.update(dicCode[item.code].id, newItemCategory)

          dicCodeFile[item.code] = idx + 1

          await this.mediaRepo.delete({ productId: dicCode[item.code].id, table: enumData.MediaTable.ItemCategory.code })
          let newMedia: any = {}
          if (item.url) {
            newMedia.productId = dicCode[item.code].id
            newMedia.url = item.url || ''
            newMedia.table = enumData.MediaTable.ItemCategory.code
            await this.mediaRepo.insert(newMedia)
          }
        }
      }
    })
    return { message: IMPORT_SUCCESS }
  }
}
