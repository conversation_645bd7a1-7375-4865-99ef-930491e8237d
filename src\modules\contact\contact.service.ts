import { Injectable } from '@nestjs/common'
import { PaginationDto } from '../../dto'
import { ContactRepository } from '../../repositories/core/contact.repository'
import { ContactDto } from './dto/contact.dto'
import { ContactEntity } from '../../entities/core/contact.entity'

@Injectable()
export class ContactService {
  constructor(private readonly contactRepo: ContactRepository) {}

  async getContactPagination(dt: PaginationDto) {
    let [data, total] = await this.contactRepo.findAndCount({
      order: { createdAt: 'DESC' },
      skip: dt.skip || 0,
      take: dt.take || 10,
    })
    return { data, total }
  }

  async addContact(data: ContactDto) {
    let new_contact = this.contactRepo.create(data)
    return await this.contactRepo.save(new_contact)
  }

  async updateReadStatus(data: any) {
    let entity = await this.contactRepo.findOne({ where: { id: data.id } })
    entity.isRead = true
    return await this.contactRepo.save(entity)
  }
}
