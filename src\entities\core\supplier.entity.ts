import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn, OneToMany, Index, BeforeUpdate, BeforeInsert, OneToOne } from 'typeorm'
import { CityEntity } from './city.entity'
import { DistrictEntity } from './district.entity'
import { WardEntity } from './ward.entity'
import { ApiProperty } from '@nestjs/swagger'
import { compare, hash } from 'bcrypt'
import { PWD_SALT_ROUNDS } from '../../constants'
import { SupplierHistoryEntity } from './supplierHistory.entity'
import { UserEntity } from './user.entity'

@Entity('supplier')
export class SupplierEntity extends BaseEntity {
  // Mã doanh nghiệp
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
    unique: false,
  })
  code: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  // Tên giao dịch
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  dealName: string

  // Tên viết tắt
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  abbreviateName: string

  // Năm thành lập công ty
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  createYear: string

  // Loại hình doanh nghiệp
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  companyType: string

  // Quốc gia
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  nation: string

  // Người đại diện pháp luật
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  represen: string

  // Tên giám đốc
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  positionRepresen: string

  // số fax
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  faxRepresen: string

  // lưu ý người đại diện pháp luật
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  noteRepresen: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  phone: string

  // Email
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  email: string

  // sơ lược quá trình hình thành
  @Column({
    type: 'text',
    nullable: true,
  })
  description: string

  // tập quán mua bán
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  tradingHabits: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  address: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  wardId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  districtId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  cityId: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  status: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  parentId: string

  // thông tin tài khoản ngân hàng
  @Column({ type: 'text', nullable: true })
  infoA: string

  @ManyToOne(() => WardEntity, (p) => p.suppliers)
  @JoinColumn({ name: 'wardId', referencedColumnName: 'id' })
  ward: Promise<WardEntity>

  @ManyToOne(() => DistrictEntity, (p) => p.suppliers)
  @JoinColumn({ name: 'districtId', referencedColumnName: 'id' })
  district: Promise<DistrictEntity>

  @ManyToOne(() => CityEntity, (p) => p.suppliers)
  @JoinColumn({ name: 'cityId', referencedColumnName: 'id' })
  city: Promise<CityEntity>

  /** Lịch sử NCC */
  @OneToMany(() => SupplierHistoryEntity, (p) => p.supplier)
  supplierHistorys: Promise<SupplierHistoryEntity[]>

  /** Tên đăng nhập */
  @ApiProperty()
  @Column({ nullable: true })
  @Index({ unique: true })
  username: string

  /** Mật khẩu */
  @ApiProperty()
  @Column({ nullable: true })
  password: string

  /** Là 3PL */
  @ApiProperty()
  @Column({ nullable: true })
  is3PL: boolean

  /** Là nhà cung cấp */
  @ApiProperty()
  @Column({ nullable: true })
  isSupplier: boolean

  /** Là nhà phân phối */
  @ApiProperty()
  @Column({ nullable: true })
  isDistributor: boolean

  userId: string
  @OneToMany(() => UserEntity, (p) => p.supplier, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId', referencedColumnName: 'id' })
  user: Promise<UserEntity>

  @BeforeInsert()
  @BeforeUpdate()
  async hashPassword() {
    if (this.password) {
      const hashedPassword = await hash(this.password, PWD_SALT_ROUNDS)
      this.password = hashedPassword
    }
  }

  comparePassword(candidate: string) {
    return compare(candidate, this.password)
  }
}
