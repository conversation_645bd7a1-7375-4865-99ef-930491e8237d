import { ApiProperty } from '@nestjs/swagger'
import { IsArray, IsEnum, IsNotEmpty, IsOptional, IsString, Min, ValidateNested } from 'class-validator'
import { NSLuckyBill } from '../../../../constants/NSLuckyBill'
import { CreateLuckyBillApplyRegionDto } from '../../lucky-bill-apply-region/dto/create-lucky-bill-apply-region.dto'

export class CreateLuckyBillConfigDto {
  @ApiProperty({ description: 'Tên cấu hình' })
  @IsNotEmpty()
  @IsString()
  name: string

  @ApiProperty({ description: 'Giá trị cấu hình' })
  @IsNotEmpty()
  @Min(0)
  value: number

  @ApiProperty({ description: 'Ngày bắt đầu áp dụng' })
  @IsNotEmpty()
  applyDateFrom: Date

  @ApiProperty({ description: '<PERSON><PERSON><PERSON> kết thúc áp dụng' })
  @IsNotEmpty()
  applyDateTo: Date

  @ApiProperty({ description: 'Cơ chế quay' })
  @IsEnum(NSLuckyBill.ELuckyDrawMechanism)
  @IsOptional()
  luckyDrawMechanism?: NSLuckyBill.ELuckyDrawMechanism

  @ApiProperty({ description: 'Thời gian quay' })
  frequency?: Date

  @ApiProperty({ description: 'Mô tả' })
  @IsOptional()
  description?: string

  //list apply
  @ApiProperty({ description: 'Danh sách áp dụng' })
  @IsOptional()
  @IsArray()
  @IsNotEmpty({ each: true })
  @ValidateNested({ each: true })
  applyRegions: CreateLuckyBillApplyRegionDto[]
}
