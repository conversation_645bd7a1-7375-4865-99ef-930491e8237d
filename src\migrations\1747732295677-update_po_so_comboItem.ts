import { MigrationInterface, QueryRunner } from "typeorm";

export class updatePoSoComboItem1747732295677 implements MigrationInterface {
    name = 'updatePoSoComboItem1747732295677'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "purchase_order_so" ADD "productInCombo" uuid`);
        await queryRunner.query(`ALTER TABLE "purchase_order_so" ADD "isCombo" boolean`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "purchase_order_so" DROP COLUMN "isCombo"`);
        await queryRunner.query(`ALTER TABLE "purchase_order_so" DROP COLUMN "productInCombo"`);
    }

}
