import { BaseEntity } from '../core/base.entity'
import { Entity, Column } from 'typeorm'

/** <PERSON><PERSON><PERSON> sử gửi email */
@Entity('s_email_history')
export class EmailHistoryEntity extends BaseEntity {
  /** Trạng thái */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  status: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  type: string

  /** Đếm số lần gửi email */
  @Column({
    nullable: false,
    default: 0,
  })
  count: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
    default: '',
  })
  toAddresses: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  ccAddresses: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  bccAddresses: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
    default: '',
  })
  subject: string

  @Column({
    type: 'text',
    nullable: false,
  })
  body_text: string

  @Column({
    type: 'text',
    nullable: false,
  })
  body_html: string

  @Column({
    type: 'json',
    nullable: false,
  })
  result: any

  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  companyId: string
}
