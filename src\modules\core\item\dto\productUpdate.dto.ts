import { IsNotEmpty, IsOptional, IsUUID } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { ProductCreateDto } from './productCreate.dto'

/** Interface Cập nhật quốc gia. */
export class ProductUpdateDto extends ProductCreateDto {
  @ApiProperty({ description: 'Id quốc gia.', example: 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx' })
  @IsNotEmpty()
  @IsUUID()
  id: string
}

export class ProductExpiryDateDto {
  @ApiProperty({ description: 'Id của đối tượng' })
  @IsNotEmpty({ message: 'Id không được để trống' })
  productId?: string

  @ApiProperty({ description: 'Hạn sử dụng' })
  @IsOptional()
  expiryDate: Date

  @ApiProperty({ description: 'Id của kho' })
  @IsOptional()
  warehouseId: string
}
