import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { BusinessOrderService } from './business-order.service';
import { CreateBusinessOrderDto } from './dto/create-business-order.dto';
import { UpdateBusinessOrderDto } from './dto/update-business-order.dto';

@Controller('business-order')
export class BusinessOrderController {
  constructor(private readonly businessOrderService: BusinessOrderService) {}

  @Post()
  create(@Body() createBusinessOrderDto: CreateBusinessOrderDto) {
    return this.businessOrderService.create(createBusinessOrderDto);
  }

  @Get()
  findAll() {
    return this.businessOrderService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.businessOrderService.findOne(+id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateBusinessOrderDto: UpdateBusinessOrderDto) {
    return this.businessOrderService.update(+id, updateBusinessOrderDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.businessOrderService.remove(+id);
  }
}
