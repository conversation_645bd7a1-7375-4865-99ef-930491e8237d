import { ConflictException, Injectable, NotFoundException } from '@nestjs/common'
import { customAlphabet } from 'nanoid'
import { Equal, IsNull, Like, Not } from 'typeorm'
import { QuestionCreateDto, QuestionCreateMasterDataDto, QuestionListDetailCreateDto, QuestionListDetailUpdateDto, QuestionUpdateDto } from './dto'
import { QuestionListDetailRepository, QuestionRepository, SurveyQuestionRepository, TopicRepository } from '../../../repositories/survey'
import { PaginationDto, UserDto } from '../../../dto'
import { CREATE_SUCCESS, DELETE_SUCCESS, ERROR_NOT_FOUND_DATA, UPDATE_ACTIVE_SUCCESS, UPDATE_SUCCESS } from '../../../constants'
import { QuestionEntity, QuestionListDetailEntity, SurveyQuestionEntity, TopicEntity } from '../../../entities/survey'
const nanoid = customAlphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890', 10)
@Injectable()
export class QuestionService {
  constructor(
    private readonly repo: QuestionRepository,
    private topicRepo: TopicRepository,
    private questionListDetailRepo: QuestionListDetailRepository,
    private surveyQuestionRepo: SurveyQuestionRepository,
  ) {}

  /**
   * Tìm câu hỏi theo id
   * @param user
   * @param questionId
   * @returns
   */
  public async findOne(user: UserDto, data: { questionId: string }) {
    return await this.repo.findOne({
      where: {
        id: data.questionId,
      },
      relations: {
        questionlistDetails: true,
      },
    })
  }

  public async create_data(user: UserDto, data: QuestionCreateDto) {
    const topic = await this.topicRepo.findOne({ where: { id: data.topicId, isDeleted: false } })
    if (!topic) throw new ConflictException('Chủ đề này đã bị xóa hoặc ngừng hoạt động. vui lòng kiểm tra lại')

    const code = topic.code + '_' + `${nanoid()}`
    data.code = code
    data.topicId = topic.id

    const newEntity = this.repo.create(data)
    //newEntity.createdBy = user.id
    newEntity.createdAt = new Date()
    if (newEntity.sort == null) throw new Error('Thứ tự sắp xếp không thể để trống')
    await this.repo.save(newEntity)

    return { message: CREATE_SUCCESS }
  }

  public async update_data(user: UserDto, data: QuestionUpdateDto) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    const topic = await this.topicRepo.findOne({ where: { id: data.topicId, isDeleted: false } })
    if (!topic) throw new ConflictException('Chủ đề này đã bị xóa hoặc ngừng hoạt động. vui lòng kiểm tra lại')
    entity.name = data.name
    if (data.sort !== null) {
      entity.sort = data.sort
    }
    entity.isHighlight = data.isHighlight
    entity.hightlightValue = data.hightlightValue
    entity.isRequired = data.isRequired
    entity.type = data.type
    entity.level = data.level
    entity.description = data.description
    entity.updatedBy = user.id
    entity.updatedAt = new Date()
    entity.img = data.img
    await entity.save()

    return { message: UPDATE_SUCCESS }
  }

  public async pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = { parentId: IsNull() }
    if (data.where.name) data.where.name = Like(`%${data.where.name}%`)
    if (data.where.topicId) whereCon.topicId = data.where.topicId
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    const res = await this.repo.find({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      relations: { questionlistDetails: true, childs: { questionlistDetails: true } },
      order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } },
    })
    if (res.length === 0) return res
    delete whereCon.parentId
    const total = await this.repo.count({ where: whereCon })
    return [res, total]
  }

  public async update_active(user: UserDto, id: string) {
    const entity = await this.repo.findOne({ where: { id } })
    if (!entity) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    entity.isDeleted = !entity.isDeleted
    await this.repo.update(entity.id, { isDeleted: entity.isDeleted })
    if (entity.level === 1) {
      let lstChild = await this.repo.find({ where: { parentId: entity.id }, select: { id: true } })
      for (let child of lstChild) {
        await this.repo.update(child.id, { isDeleted: entity.isDeleted })
      }
    }
    if (entity.level === 2) {
      if (entity.isDeleted === false) {
        let parent = await this.repo.findOne({ where: { id: entity.parentId }, select: { id: true } })
        if (parent) {
          await this.repo.update(parent.id, { isDeleted: false })
        }
      }
    }
    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  public async question_find(data: { topicId: string; isGetRelation: boolean }) {
    const whereCon: any = { topicId: data.topicId, level: 1, isDeleted: false }
    let objRelation = {}
    if (data.isGetRelation) {
      objRelation = { questionlistDetails: true, childs: { questionlistDetails: true } }
    }
    const res = await this.repo.find({
      where: whereCon,
      order: { sort: 'ASC', createdAt: 'ASC' },
      relations: { questionlistDetails: true, childs: { questionlistDetails: true } },
    })

    return res
  }

  public async questionlistdetail_pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = {}
    if (data.where.questionId) whereCon.questionId = data.where.questionId
    if (data.where.name) data.where.name = Like(`%${data.where.name}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    return await this.questionListDetailRepo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { sort: 'ASC' },
    })
  }

  //#region questionListDetail
  public async questionlistdetail_create_data(user: UserDto, data: QuestionListDetailCreateDto) {
    let entity = await this.questionListDetailRepo.create(data)
    const questionEntity = await this.questionListDetailRepo.findOne({ where: { questionId: data.questionId }, order: { value: 'DESC' } })
    if (entity.value !== 0) {
      if (questionEntity && questionEntity.value > 0) entity.value = questionEntity.value + 1
      else entity.value = 1
    }
    await this.questionListDetailRepo.save(entity)
    return { id: entity.id, message: CREATE_SUCCESS }
  }

  public async questionlistdetail_update_data(user: UserDto, data: QuestionListDetailUpdateDto) {
    const entity = await this.questionListDetailRepo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    entity.name = data.name
    entity.sort = data.sort
    await entity.save()

    return { id: entity.id, message: UPDATE_SUCCESS }
  }

  public async questionlistdetail_update_active(user: UserDto, id: string) {
    const entity = await this.questionListDetailRepo.findOne({ where: { id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    await this.questionListDetailRepo.update(id, { isDeleted: !entity.isDeleted })
    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  public async delete_all(user: UserDto, topicId: string) {
    return this.repo.manager.transaction(async (manager) => {
      const questionListDetailRepo = manager.getRepository(QuestionListDetailEntity)
      const questionTechRepo = manager.getRepository(QuestionEntity)

      const surveyQuestionRepo = manager.getRepository(SurveyQuestionEntity)
      const check = await surveyQuestionRepo.find({ where: { topicId: topicId, isDeleted: false } })
      if (check.length > 0) throw new Error('Không thể xóa do câu hỏi đã tồn tại trong phiếu khảo sát. Vui lòng kiểm tra lại')

      const level = 1
      const lstQuestionLv1 = await questionTechRepo.find({ where: { topicId, level } })
      if (lstQuestionLv1.length > 0) {
        for (const questionLv1 of lstQuestionLv1) {
          const lstQuestionLv2 = await questionLv1.childs
          if (lstQuestionLv2.length > 0) {
            for (const questionLv2 of lstQuestionLv2) {
              // xóa cấu hình danh sách lv2
              await questionListDetailRepo.delete({ questionId: questionLv2.id })
            }
          }
          // xóa lv2
          await questionTechRepo.delete({ parentId: questionLv1.id })

          // xóa cấu hình danh sách lv1
          await questionListDetailRepo.delete({ questionId: questionLv1.id })
        }
        // xóa lv1
        await questionTechRepo.delete({ topicId })
      }

      return { message: DELETE_SUCCESS }
    })
  }

  public async createMasterData(data: QuestionCreateMasterDataDto[]) {
    for (let item of data) {
      const topic = await this.topicRepo.findOne({ where: { code: item.topicCode, isDeleted: false } })
      if (!topic) throw new ConflictException('Chủ đề này đã bị xóa hoặc ngừng hoạt động. vui lòng kiểm tra lại')

      const question = new QuestionEntity()
      question.topicId = topic.id
      question.createdBy = item.createdBy
      question.createdAt = new Date()
      question.name = item.name
      question.code = item.code
      question.isRequired = item.isRequired
      question.type = item.type
      question.isHighlight = item.isHighlight
      question.hightlightValue = item.hightlightValue
      question.sort = item.sort
      question.level = item.level
      question.description = item.description
      question.parentId = item.parentId
      let qs = await this.repo.save(question)
      if (item.lstDetail && item.lstDetail.length > 0) {
        for (let x of item.lstDetail) {
          let questionDetail = new QuestionListDetailEntity()
          questionDetail.createdAt = new Date()
          questionDetail.createdBy = item.createdBy
          questionDetail.questionId = qs.id
          questionDetail.name = x.name
          await this.questionListDetailRepo.save(questionDetail)
        }
      }
      if (item.childs && item.childs.length > 0) {
        for (let y of item.childs) {
          const questionChild = new QuestionEntity()
          questionChild.topicId = topic.id
          questionChild.createdBy = item.createdBy
          questionChild.createdAt = new Date()
          questionChild.parentId = qs.id
          questionChild.name = y.name
          questionChild.code = y.code
          questionChild.isRequired = y.isRequired
          questionChild.type = y.type
          questionChild.isHighlight = y.isHighlight
          questionChild.hightlightValue = y.hightlightValue
          questionChild.sort = y.sort
          questionChild.level = y.level
          questionChild.description = y.description
          let questionChildRs = await this.repo.save(questionChild)

          if (y.__questionListDetails__ && y.__questionListDetails__.length > 0) {
            for (let z of y.__questionListDetails__) {
              let questionDetailChild = new QuestionListDetailEntity()
              questionDetailChild.createdAt = new Date()
              questionDetailChild.createdBy = item.createdBy
              questionDetailChild.questionId = questionChildRs.id
              questionDetailChild.name = z.name
              questionDetailChild.value = z.value
              await this.questionListDetailRepo.save(questionDetailChild)
            }
          }
        }
      }
    }

    return { message: CREATE_SUCCESS }
  }

  public async import(topicId: string, user: UserDto, data: { lstDataTable1: any[]; lstDataTable2: any[] }) {
    const check = await this.surveyQuestionRepo.find({ where: { topicId: topicId, isDeleted: false } })

    return this.repo.manager.transaction(async (manager) => {
      const questionRepo = manager.getRepository(QuestionEntity)
      const questionListDetailRepo = manager.getRepository(QuestionListDetailEntity)
      const topicRepo = manager.getRepository(TopicEntity)
      const topic = await topicRepo.findOne({ where: { id: topicId, isDeleted: false } })
      if (!topic) throw new Error('Chủ đề này không tồn tại. Vui lòng kiểm tra lại !')
      // add lv1
      var lstDataLv1 = data.lstDataTable1.filter((c: any) => c.level == 1)
      for (const item of lstDataLv1) {
        if (!item.id) {
          const objQuestionNew = new QuestionEntity()
          objQuestionNew.createdBy = user.id
          objQuestionNew.topicId = topicId
          objQuestionNew.level = 1
          objQuestionNew.sort = item.sort || 0
          objQuestionNew.code = topic.code + '_' + `${nanoid()}`
          objQuestionNew.name = item.name
          objQuestionNew.type = item.type
          objQuestionNew.isRequired = item.isRequired
          objQuestionNew.createdAt = new Date()
          const objServiceTech = await questionRepo.save(objQuestionNew)
          item.id = objServiceTech.id

          const lstDetail = data.lstDataTable2.filter((c: any) => c.zenListId == item.zenId)
          if (lstDetail.length > 0) {
            let i = 1
            for (const detail of lstDetail) {
              const detailNew = new QuestionListDetailEntity()
              detailNew.createdAt = new Date()
              detailNew.createdBy = user.id
              detailNew.questionId = item.id
              detailNew.name = detail.nameList
              detailNew.sort = detail.sortList
              detailNew.value = i
              if (detail.isOther === true) detailNew.value = 0
              i++
              await questionListDetailRepo.save(detailNew)
            }
          }
        }
      }

      // add lv2
      var lstDataLv2 = data.lstDataTable1.filter((c: any) => c.level == 2)
      for (const item of lstDataLv2) {
        if (!item.id) {
          const objQuestionNew = new QuestionEntity()
          objQuestionNew.createdBy = user.id
          objQuestionNew.createdAt = new Date()
          objQuestionNew.topicId = topicId
          objQuestionNew.level = 2
          objQuestionNew.sort = item.sort || 0
          objQuestionNew.code = topic.code + '_' + `${nanoid()}`
          objQuestionNew.name = item.name
          objQuestionNew.type = item.type
          objQuestionNew.isRequired = item.isRequired
          const parent = lstDataLv1.find((c: any) => c.zenId == item.parentZenId)
          if (parent) objQuestionNew.parentId = parent.id
          const objServiceTech = await questionRepo.save(objQuestionNew)
          item.id = objServiceTech.id

          const lstDetail = data.lstDataTable2.filter((c: any) => c.zenListId == item.zenId)
          if (lstDetail.length > 0) {
            let i = 1
            for (const detail of lstDetail) {
              const detailNew = new QuestionListDetailEntity()
              detailNew.createdAt = new Date()
              detailNew.createdBy = user.id
              detailNew.questionId = item.id
              detailNew.name = detail.nameList
              detailNew.sort = detail.sortList
              detailNew.value = i
              if (detail.isOther === true) detailNew.value = 0
              i++
              await questionListDetailRepo.save(detailNew)
            }
          }
        }
      }
    })
  }
}
