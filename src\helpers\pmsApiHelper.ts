import { HttpService } from '@nestjs/axios'
import { lastValueFrom } from 'rxjs'

export class PmsApiHelper {
  private url = process.env.HOST_PMS_SETTING
  private httpService: HttpService
  private headers = {
    'pms-api-key': `Apikey ${process.env.PMS_API_KEY}`,
  }

  constructor(httpService: HttpService) {
    this.httpService = httpService
  }
  async createPO(data: any) {
    return new Promise((resolve, reject) => {
      const request = this.httpService.post(`${this.url}/po/create_po_platform`, data, { headers: this.headers })
      lastValueFrom(request)
        .then((res) => {
          resolve(res?.data)
        })
        .catch((err: any) => {
          reject('Có lỗi xảy ra khi đồng bộ PO')
        })
    })
  }
}

export const pmsApiHelper = new PmsApiHelper(new HttpService())
