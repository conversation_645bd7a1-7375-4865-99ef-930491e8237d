import { Controller, UseGuards, Request, Post, Body, Param, Get } from '@nestjs/common'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { Request as IRequest } from 'express'
import { CurrentUser, JwtAuthGuard } from '../../common'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { DistrictCreateDto, DistrictCreateExcelDto, DistrictOneDto, DistrictUpdateDto } from './dto'
import { DistrictService } from './district.service'

@ApiBearerAuth()
@ApiTags('Geo District')
@UseGuards(JwtAuthGuard)
@Controller('district')
export class DistrictController {
  constructor(private readonly service: DistrictService) {}

  @Post('find')
  async find(@Body() data: any) {
    return await this.service.find(data)
  }

  @Post('load_data')
  async loadData(@Body() data: { id?: string }) {
    return await this.service.loadData(data)
  }

  @Get('load_district_by_cityId/:cityId')
  async loadDistrictByCityId(@Param('cityId') cityId: string) {
    return await this.service.loadDistrictByCityId(cityId)
  }

  @Post('pagination')
  async pagination(@Body() data: PaginationDto) {
    return await this.service.pagination(data)
  }

  @Post('create_data')
  async createData(@CurrentUser() user: UserDto, @Body() data: DistrictCreateDto) {
    return await this.service.createData(user, data)
  }

  @Post('update_data')
  async updateData(@CurrentUser() user: UserDto, @Body() data: DistrictUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @Post('update_active')
  async updateActive(@Body() data: any, @CurrentUser() user: UserDto) {
    return await this.service.updateIsDelete(data.id, user)
  }

  @Post('master_data')
  async masterData(@Request() req: IRequest) {
    return await this.service.createMasterData()
  }

  @Post('create_data_excel')
  async createDataExcel(@CurrentUser() user: UserDto, @Body() data: DistrictCreateExcelDto[]) {
    return await this.service.createDataExcel(user, data)
  }

  @Post('find_one')
  async findOne(@Body() data: FilterOneDto) {
    return await this.service.findOne(data)
  }

  @Post('load_district_by_city')
  async loadDistrictByCity(@Body() data: DistrictOneDto) {
    return await this.service.loadDistrictByCity(data)
  }
}
