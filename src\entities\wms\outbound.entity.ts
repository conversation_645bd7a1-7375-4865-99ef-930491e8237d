import { BaseEntity } from '../core/base.entity'
import { Entity, Column, OneToMany, ManyToOne, JoinColumn } from 'typeorm'
import { OutboundHistoryEntity, WarehouseEntity, OutboundDetailEntity, WarehouseTransferEntity, CheckInventoryEntity } from '.'

@Entity('outbound')
export class OutboundEntity extends BaseEntity {
  /** Mã phiếu */
  @Column({ type: 'varchar', length: 36, nullable: true })
  code: string

  /** Loại phiếu */
  @Column({ type: 'varchar', length: 36, nullable: true })
  type: string

  /** Id đơn hàng */
  @Column({ type: 'varchar', length: 36, nullable: true })
  orderId: string

  /** Mã đơn hàng */
  @Column({ type: 'varchar', length: 36, nullable: true })
  orderCode: string

  /** Tên khách hàng của đơn hàng */
  @Column({ type: 'varchar', length: 50, nullable: true })
  customerName: string

  /** <PERSON><PERSON>y tạo đơn hàng */
  @Column({ type: 'timestamptz', nullable: true })
  orderCreatedAt: Date

  /**
   * Ngày soạn hàng
   * Lấy thời gian user thao tác Xác nhận soạn đơn hàng
   *  */
  @Column({ type: 'varchar', length: 36, nullable: true })
  preparedBy: string

  /**
   * Ngày soạn hàng
   * Lấy thời gian user thao tác Xác nhận soạn đơn hàng
   *  */
  @Column({ type: 'timestamptz', nullable: true })
  preparedAt: Date

  /** Ngày duyệt phiếu xuất kho */
  @Column({ type: 'timestamptz', nullable: true })
  approvedDate: Date

  /**
   * Người duyệt
   * Lấy id người nhấn nút duyệt
   *  */
  @Column({ type: 'varchar', length: 36, nullable: true })
  approvedBy: string

  /** Tổng giá trị đơn hàng */
  @Column({ type: 'int', nullable: true, default: 0 })
  totalOrderMoney: number

  /** Số lượng kiện hàng */
  @Column({ type: 'int', nullable: true, default: 0 })
  packageQuantity: number

  /** Trạng thái phiếu xuất - enumData.OutboundStatus */
  @Column({ type: 'varchar', length: 36, nullable: true, default: 'NEW' })
  status: string

  /** Id phiếu chuyển kho (Dành cho module chuyển kho) */
  @Column({ type: 'varchar', nullable: true })
  warehouseTransferId: string
  @ManyToOne(() => WarehouseTransferEntity, (p) => p.outbounds)
  @JoinColumn({ name: 'warehouseTransferId', referencedColumnName: 'id' })
  warehouseTransfer: Promise<WarehouseTransferEntity>

  /** Id phiếu kiểm kho (Dành cho module kiểm kho) */
  @Column({ type: 'varchar', nullable: true })
  checkInventoryId: string
  @ManyToOne(() => CheckInventoryEntity, (p) => p.outbounds)
  @JoinColumn({ name: 'checkInventoryId', referencedColumnName: 'id' })
  checkInventory: Promise<CheckInventoryEntity>

  /** Kho xuất */
  @Column({ type: 'varchar', nullable: true })
  warehouseId: string
  @ManyToOne(() => WarehouseEntity, (p) => p.outbounds)
  @JoinColumn({ name: 'warehouseId', referencedColumnName: 'id' })
  warehouse: Promise<WarehouseEntity>

  /** Nhân viên (Để trừ tồn kho nhân viên) */
  @Column({ type: 'varchar', length: 36, nullable: true })
  employeeId: string

  /** Ghi chú */
  @Column({ type: 'text', nullable: true })
  description: string

  /** Danh sách sản phẩm trong phiếu xuất kho */
  @OneToMany(() => OutboundDetailEntity, (p) => p.outbound)
  outboundDetails: Promise<OutboundDetailEntity[]>

  /** Lịch sử thay đổi */
  @OneToMany(() => OutboundHistoryEntity, (p) => p.outbound)
  histories: Promise<OutboundHistoryEntity[]>
}
