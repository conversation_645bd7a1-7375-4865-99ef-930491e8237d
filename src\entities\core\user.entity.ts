import { <PERSON>umn, <PERSON><PERSON><PERSON>, Before<PERSON><PERSON>rt, BeforeUpdate, OneToOne, JoinColumn, OneToMany, ManyToOne } from 'typeorm'
import { compare, hash } from 'bcrypt'
import { PWD_SALT_ROUNDS } from '../../constants'
import { BaseEntity } from './base.entity'
import { EmployeeEntity } from './employee.entity'
import { PermissionEntity } from './permission.entity'
import { SupplierEntity } from './supplier.entity'

@Entity({ name: 'user' })
export class UserEntity extends BaseEntity {
  @Column({ type: 'varchar', length: 50, nullable: false, unique: true })
  username: string

  @Column({ name: 'password', type: 'text', nullable: false })
  password: string

  @Column({ type: 'varchar', length: 50, nullable: false })
  type: string

  @Column({ type: 'varchar', length: 36, nullable: true })
  companyId: string

  @Column({ type: 'varchar', nullable: true })
  employeeId: string

  @OneToOne((type) => EmployeeEntity, (p) => p.user)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>

  @Column({ type: 'varchar', length: 36, nullable: true })
  partnerId: string

  @Column({ type: 'text', nullable: true })
  fcmToken: string

  @Column({ type: 'varchar', length: 250, nullable: true })
  email: string

  @Column({ type: 'varchar', length: 50, nullable: true })
  phone: string

  /** Chứng minh nhân dân */
  @Column({ type: 'varchar', length: 50, nullable: true })
  cardId: string

  /** ngày sinh */
  @Column({ type: 'varchar', length: 100, nullable: true })
  birthdate: string

  /** Họ và Tên */
  @Column({ type: 'varchar', length: 250, nullable: true })
  fullName: string

  @Column({ type: 'varchar', length: 250, nullable: true })
  address: string

  /** Nhân thông báo hay không ?*/
  @Column({ nullable: true, default: true })
  isAllowMessage: boolean

  //vendor
  @Column({ type: 'varchar', length: 50, nullable: true })
  vendor: string

  @OneToMany(() => PermissionEntity, (permission) => permission.user)
  permission: Promise<PermissionEntity[]>

  @Column({ type: 'uuid', nullable: true })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.user, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: SupplierEntity

  @BeforeInsert()
  @BeforeUpdate()
  async hashPassword() {
    if (this.password) {
      const hashedPassword = await hash(this.password, PWD_SALT_ROUNDS)
      this.password = hashedPassword
    }
  }

  comparePassword(candidate: string) {
    return compare(candidate, this.password)
  }
}
