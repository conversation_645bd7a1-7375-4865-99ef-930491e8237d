import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>umn, ManyToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { ItemEntity } from './item.entity'
import { NSItemGroup } from '../../constants/NSItemGroup'
import { ItemCategoryEntity } from './itemCategory.entity'

/** Nhóm sản phẩm */
@Entity('item_group')
export class ItemGroupEntity extends BaseEntity {
  /** Mã */
  @Column({ type: 'varchar', length: 50, nullable: false })
  code: string

  /** Tên */
  @Column({ type: 'varchar', length: 250, nullable: false })
  name: string

  /** Type của item group (category của product) */
  @Column({ type: 'varchar', length: 50, nullable: true, default: NSItemGroup.EGroupType.PRODUCT })
  groupType: NSItemGroup.EGroupType

  /** <PERSON><PERSON> chú */
  @Column({ type: 'text', nullable: true })
  description: string

  @Column({ type: 'varchar', nullable: true })
  itemCategoryId: string
  @ManyToOne(() => ItemCategoryEntity, (p) => p.itemGroups)
  @JoinColumn({ name: 'itemCategoryId', referencedColumnName: 'id' })
  itemCategory: Promise<ItemEntity>

  /** Danh sách các sản phẩm thuộc Nhóm này */
  @OneToMany(() => ItemEntity, (p) => p.itemGroup)
  items: Promise<ItemEntity[]>
}
