import { IsNotEmpty, <PERSON>S<PERSON>, IsEmail, IsOptional } from 'class-validator'

export class SupplierCreateExcelDto {
  @IsNotEmpty()
  @IsString()
  code: string

  @IsNotEmpty()
  @IsString()
  name: string

  email: string

  description: string

  address: string

  wardCode: string

  districtCode: string

  cityCode: string

  phone: string

  username: string

  password: string

  isDistributor: boolean

  isSupplier: boolean

  is3PL: boolean
}
