import { Controller, UseGuards, Post, Body, Req } from '@nestjs/common'
import { CheckInventoryService } from './checkInventory.service'
import { CheckInventoryCreateByExcelDto, CheckInventoryCreateDto, CheckInventoryUpdateDto } from './dto'
import { CurrentUser } from '../../common/decorators'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { Request as IRequest } from 'express'
import { JwtAuthGuard } from '../../common/guards'

@ApiBearerAuth()
@ApiTags('CheckInventory')
@UseGuards(JwtAuthGuard)
@Controller('check_inventory')
export class CheckInventoryController {
  constructor(private readonly service: CheckInventoryService) { }

  @Post('find')
  async find(@Body() data: any) {
    return await this.service.find(data)
  }

  @Post('find_detail')
  async findDetail(@Body() data: FilterOneDto, @Req() req: IRequest) {
    return await this.service.findDetail(data, req)
  }

  @Post('get_auto_gen_code')
  async getCodeAutoGen() {
    return await this.service.getCodeAutoGen()
  }

  @Post('pagination')
  async pagination(@Body() data: PaginationDto, @Req() req: IRequest) {
    return await this.service.pagination(data, req)
  }

  @Post('update_approve')
  async updateApprove(@Body() data: FilterOneDto, @Req() req: IRequest) {
    return await this.service.updateApprove(data, req)
  }

  @Post('update_cancel')
  async updateCancel(@Body() data: FilterOneDto, req?: IRequest) {
    return await this.service.updateCancel(data, req)
  }

  @Post('create_data')
  async createData(@Body() data: CheckInventoryCreateDto, @Req() req: IRequest) {
    return await this.service.createData(data, req)
  }

  @Post('update_data')
  async updateData(@Body() data: CheckInventoryUpdateDto, @Req() req: IRequest) {
    return await this.service.updateData(data, req)
  }

  @Post('create_data_by_excel')
  async createDataByExcel(@Body() data: CheckInventoryCreateByExcelDto[], @Body('createBy') user: any, req: IRequest) {
    return await this.service.createDataByExcel(data, user, req)
  }
}
