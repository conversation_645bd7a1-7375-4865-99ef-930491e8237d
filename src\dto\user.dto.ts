import { ApiProperty } from '@nestjs/swagger'
import { IsOptional, IsString, IsUUID } from 'class-validator'

/** Interface user */
export class UserDto {
  @ApiProperty({ description: 'Id user', example: 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx' })
  @IsUUID()
  id: string

  @ApiProperty({ description: 'Tài khoản', example: 'userX' })
  @IsString()
  username: string

  @ApiProperty({ description: 'Loại tài khoản' })
  @IsString()
  type?: string

  @ApiProperty({ description: 'Id công ty', example: 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx' })
  companyId?: string

  @ApiProperty({ description: 'Là admin?', example: 'false' })
  isAdmin: boolean

  userId?: string

  lstStore?: StoreDto[]

  @IsString()
  employeeId?: string

  @IsOptional()
  @IsString()
  supplierId?: string | null

  is3PL?: boolean
  isSupplier?: boolean
  isDistributor?: boolean
}

export class StoreDto {
  id: string

  name: string
}
