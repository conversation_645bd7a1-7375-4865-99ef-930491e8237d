import { BaseEntity } from '../core/base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { OutboundEntity } from './outbound.entity'

@Entity('outbound_history')
export class OutboundHistoryEntity extends BaseEntity {
  /** Phiếu xuất kho */
  @Column({ type: 'varchar', nullable: true })
  outboundId: string
  @ManyToOne(() => OutboundEntity, (p) => p.histories)
  @JoinColumn({ name: 'outboundId', referencedColumnName: 'id' })
  outbound: Promise<OutboundEntity>

  /** <PERSON><PERSON> chú */
  @Column({ type: 'text', nullable: true })
  description: string
}
