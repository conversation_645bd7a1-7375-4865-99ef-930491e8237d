import { ConflictException, Injectable, NotFoundException } from '@nestjs/common'
import { Like } from 'typeorm'
import { CREATE_SUCCESS, enumData, ERROR_CODE_TAKEN, ERROR_NOT_FOUND_DATA, UPDATE_ACTIVE_SUCCESS, UPDATE_SUCCESS } from '../../../constants'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { CategoriesEntity, SurveyHistoryEntity } from '../../../entities/survey'
import { CategoriesRepository, SurveyHistoryRepository, SurveyRepository, TopicRepository, UserSurveyRepository } from '../../../repositories/survey'
import { CategoriesCreateDto, CategoriesUpdateDto } from './dto'
import { coreHelper } from '../../../helpers'

@Injectable()
export class CategoriesService {
  constructor(
    private readonly repo: CategoriesRepository,
    private topicRepo: TopicRepository,
    private surveyRepo: SurveyRepository,
    private surveyHistoryRepo: SurveyHistoryRepository,
    private readonly userRepo: UserSurveyRepository,
  ) {}

  /** Hàm tìm kiếm */
  public async find(data: { isDeleted?: boolean; type?: string }) {
    let whereCon: any = {}
    if (data.isDeleted != undefined) whereCon.isDeleted = data.isDeleted
    if (data.type != undefined) whereCon.type = data.type
    let res: any = await this.repo.find({ where: whereCon, order: { createdAt: 'DESC', name: 'ASC' } })
    return res
  }

  /**
   * Hàm thêm mới danh mục
   * @param user User Token
   * @param id Id của danh mục
   * @param code Mã
   * @param name Tên
   * @param type danh mục (enumData: ReasonType)
   * @param description Ghi chú
   * @returns message: Tạo mới thành công
   */
  public async createData(user: UserDto, data: CategoriesCreateDto) {
    const category: Partial<CategoriesEntity> = data
    category.code = await this.codeDefault()
    const newEntity = this.repo.create(data)
    newEntity.createdAt = new Date()
    await newEntity.save()
    const history = new SurveyHistoryEntity()
    history.categoryId = newEntity.id
    history.createdAt = new Date()
    history.createdBy = user.id
    history.description = `Nhân viên [${user.username}] vừa tạo danh mục có mã là [${newEntity.code}] `
    history.status = enumData.CategoryStatus.New.code
    await this.surveyHistoryRepo.save(history)

    return { message: CREATE_SUCCESS }
  }

  private async codeDefault() {
    const objData = await this.repo.findOne({
      where: { code: Like(`CATEGORY_%`) },
      order: { code: 'DESC' },
    })
    let sortString = '0'
    if (objData) {
      sortString = objData.code.substring(9, 13)
    }
    const lastSort = parseInt(sortString)
    sortString = ('000' + (lastSort + 1)).slice(-4)

    return 'CATEGORY_' + sortString
  }

  /**
   * Hàm cập nhật danh mục
   * @param user User Token
   * @param id Id của danh mục
   * @param code Mã
   * @param name Tên
   * @param type danh mục (enumData: ReasonType)
   * @param description Ghi chú
   * @returns message: Cập nhật thành công
   */
  public async updateData(user: UserDto, data: CategoriesUpdateDto) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    // entity.updatedBy = user.id
    entity.updatedAt = new Date()
    entity.name = data.name
    entity.description = data.description
    await entity.save()
    const history = new SurveyHistoryEntity()
    history.categoryId = entity.id
    history.createdAt = new Date()
    history.createdBy = user.id
    history.description = `Nhân viên [${user.username}] vừa update danh mục có mã là [${entity.code}] `
    history.status = enumData.CategoryStatus.Update.code
    await this.surveyHistoryRepo.save(history)

    return { message: UPDATE_SUCCESS }
  }

  /**
   * Hàm phân trang
   * @param relations mối quan hệ tới những bảng khác trong db
   * @param where các điều kiện tìm kiếm
   * @param order thứ tự sắp xếp kết quả trả về theo 1 cột nào đó
   * @param skip dùng để phẩn trang, chỉ định vị trí băt đầu của kết quả trả về
   * @param take dùng để phẩn trang, giới hạn kết quả trả về
   * @returns
   */
  public async pagination(data: PaginationDto) {
    const whereCon: any = {}
    if (data.where.isDeleted) whereCon.isDeleted = data.where.isDeleted
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    const res: any = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
      relations: { topics: true },
    })
    for (const item of res[0]) {
      item.numTopic = item.__topics__?.filter((x) => x.isDeleted === false)?.length || 0
      delete item.__topics__
    }
    return res
  }

  /**
   * Hàm cập nhật trạng thái hoạt động của danh mục
   * @param user User Token
   * @param idStr id của danh mục
   * @returns message: Cập nhật trạng thái thành công
   */
  public async updateActive(user: UserDto, idStr: string) {
    const entity = await this.repo.findOne({ where: { id: idStr } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    // let companyCate = (await entity.companyCategories).filter((x) => x.isDeleted === false || x.isSync === true)
    // if (companyCate && companyCate.length > 0)
    //   throw new Error(`Không thể ngưng hoạt động danh mục được vì danh mục này đã có trong công ty và dử liệu đã được đồng bộ. Vui lòng kiểm tra lại`)
    await this.repo.update(idStr, { isDeleted: !entity.isDeleted, updatedBy: user.id, updatedAt: new Date() })
    const history = new SurveyHistoryEntity()
    history.categoryId = entity.id
    history.createdAt = new Date()
    history.createdBy = user.id
    history.description = `Nhân viên [${user.username}] vừa update trạng thái danh mục có mã là [${entity.code}] `
    history.status = enumData.CategoryStatus.Update.code
    await this.surveyHistoryRepo.save(history)

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  public async createDataByExcel(user: UserDto, data: CategoriesCreateDto[]) {
    await this.repo.manager.transaction('READ UNCOMMITTED', async (trans) => {
      const lstTask = []
      const repo = trans.getRepository(CategoriesEntity)
      // Danh sách danh mục
      const dictCategory: any = {}
      {
        const lstPacking = await repo.find({ where: { isDeleted: false } })
        lstPacking.forEach((c) => (dictCategory[c.code] = c.id))
      }
      for (let item of data) {
        const categories = new CategoriesEntity()
        categories.createdBy = user.id
        categories.createdAt = new Date()
        categories.code = await this.codeDefault()
        categories.name = item.name
        categories.description = item.description
        await repo.save(categories)
      }
      await Promise.all(lstTask)
    })
    return { message: CREATE_SUCCESS }
  }

  /** Tìm tên thành phố theo mã hoặc Id */
  public async findOne(user: UserDto, data: { id?: string; code?: string }) {
    const whereCon: any = { isDeleted: false }
    if (data.code) whereCon.code = Like(`%${data.code}%`)
    if (data.id) whereCon.id = data.id
    return await this.repo.findOne({ where: whereCon, order: { createdAt: 'DESC' } })
  }
  /** Tìm danh sách danh mục theo type  */
  public async findCategoryByType() {
    return await this.topicRepo.find({ where: { isDeleted: false }, order: { createdAt: 'DESC' } })
  }
  public async findDetail(user: UserDto, data: FilterOneDto) {
    const entity: any = await this.repo.findOne({
      where: { id: data.id },
      relations: { histories: true },
    })
    if (!entity) throw new Error('Danh mục không tồn tại')

    /** Lấy lịch sử */
    let lstHitory = await entity.histories
    for (let item of lstHitory) {
      const user: any = await this.userRepo.findOne({ where: { id: item.createdBy } })
      if (user) {
        item.createdByName = user?.username
      }
    }
    entity.lstHitory = lstHitory.sort((a: any, b: any) => (a.createdAt > b.createdAt ? 1 : -1))
    delete entity.__histories__
    return entity
  }
}
