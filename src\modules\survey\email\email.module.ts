import { Module } from '@nestjs/common'
import { EmailService } from './email.service'
import { SQSModule } from '../common/sqs/sqs.module'
import { EmailController } from './email.controller'
import { TypeOrmExModule } from '../../../typeorm'
import { EmailHistoryRepository } from '../../../repositories/survey'
import { EmployeeRepository, PurchaseOrderRepository, SupplierRepository } from '../../../repositories'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([EmailHistoryRepository, PurchaseOrderRepository, SupplierRepository, EmployeeRepository]),
    SQSModule,
  ],
  providers: [EmailService],
  controllers: [EmailController],
  exports: [EmailService],
})
export class EmailModule {}
