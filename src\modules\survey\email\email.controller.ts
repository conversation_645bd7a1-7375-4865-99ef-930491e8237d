import { Body, Controller, Post, Req, UseGuards } from '@nestjs/common'
import { EmailSendPasswordDto } from './dto'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { EmailService } from './email.service'
import { EmailSendDto } from './dto/emailSend.dto'
import { CurrentUser, JwtAuthGuard } from '../../common'
import { UserDto } from '../../../dto'

@ApiBearerAuth()
@ApiTags('Email')
@Controller('email')
@UseGuards(JwtAuthGuard)
export class EmailController {
  constructor(private readonly service: EmailService) {}

  @ApiOperation({ summary: 'Gửi mật khẩu qua email đăng ký tài khoản khi user quên mật khẩu' })
  @Post('send_password')
  public async sendPassword(@Body() data: EmailSendPasswordDto) {
    return await this.service.sendPassword(data)
  }

  //send email
  @ApiOperation({ summary: 'Gửi email' })
  @Post('send_email')
  public async sendEmail(@Body() data: EmailSendDto) {
    return await this.service.sendEmail(data)
  }
}
