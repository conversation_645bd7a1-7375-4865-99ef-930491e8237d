import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { CompanyService } from './company.service'
import { CompanyCreateDto, CompanyCreateExcelDto, CompanyUpdateDto } from './dto'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { PaginationDto, UserDto } from '../../../dto'
import { IdDto } from '../../../dto/id.dto'
import { JwtAuthGuard } from '../../common/guards'
import { CurrentUser } from '../../common/decorators'

@ApiBearerAuth()
@ApiTags('Company')
@Controller('company')
export class CompanyController {
  constructor(private readonly service: CompanyService) {}

  @ApiOperation({ summary: 'Lấy ds công ty (phân trang)' })
  @UseGuards(JwtAuthGuard)
  @Post('pagination')
  public async pagination(@Body() data: PaginationDto) {
    return await this.service.pagination(data)
  }

  @ApiOperation({ summary: 'Tạo công ty' })
  @UseGuards(JwtAuthGuard)
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: CompanyCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật công ty' })
  @UseGuards(JwtAuthGuard)
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: CompanyUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động công ty' })
  @UseGuards(JwtAuthGuard)
  @Post('update_active')
  public async updateActive(@CurrentUser() user: UserDto, @Body() data: IdDto) {
    return await this.service.updateActive(user, data)
  }

  @ApiOperation({ summary: 'Lấy tất cả user của công ty' })
  @UseGuards(JwtAuthGuard)
  @Post('load_user')
  public async loadUser(@Body() data: { companyId: string; isDeleted?: boolean }) {
    return await this.service.loadUser(data)
  }

  @ApiOperation({ summary: 'Lấy tất cả danh mục của công ty' })
  @UseGuards(JwtAuthGuard)
  @Post('load_categories')
  public async loadCategories(@Body() data: { companyId: string; isDeleted?: boolean }) {
    return await this.service.loadCategories(data)
  }

  @ApiOperation({ summary: 'Tạo mới bằng excel' })
  @UseGuards(JwtAuthGuard)
  @Post('create_data_by_excel')
  public async createDataByExcel(@CurrentUser() user: UserDto, @Body() data: CompanyCreateExcelDto[]) {
    return await this.service.createDataByExcel(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động công ty' })
  @UseGuards(JwtAuthGuard)
  @Post('update_active_company_cate')
  public async updateActiveCompanyCate(@CurrentUser() user: UserDto, @Body() data: IdDto) {
    return await this.service.updateActiveCompanyCate(user, data)
  }

  // @ApiOperation({ summary: 'Tạo mới bằng excel' })
  // @UseGuards(JwtAuthGuard)
  // @Post('sync_data')
  // public async syncDataTopic(@CurrentUser() user: UserDto, @Body() data: { companyId: string; categoriesId?: string }) {
  //   return await this.service.syncDataTopic(user, data)
  // }

  @ApiOperation({ summary: 'Hàm tìm kiếm' })
  @UseGuards(JwtAuthGuard)
  @Post('find')
  public async find(@CurrentUser() user: UserDto, @Body() data: { isDeleted?: boolean }) {
    return await this.service.find(user, data)
  }
}
