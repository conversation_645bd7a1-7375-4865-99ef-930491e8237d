import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsString, IsBoolean, IsNumber } from 'class-validator'

export class QuestionCreateDto {
  @ApiProperty({ description: 'Tên câu hỏi' })
  @IsNotEmpty({ message: 'Tên không được trống' })
  @IsString()
  name: string

  @ApiProperty({ description: 'Mã câu hỏi' })
  code: string

  @ApiProperty({ description: 'Bắt buộc nhập' })
  @IsNotEmpty({ message: 'isRequired không được trống' })
  @IsBoolean()
  isRequired: boolean

  @ApiProperty({ description: 'Kiểu dữ liệu' })
  @IsNotEmpty({ message: 'Kiểu dữ liệu không được trống' })
  @IsString()
  type: string

  @ApiPropertyOptional()
  isHighlight: boolean
  @ApiPropertyOptional()
  hightlightValue: number
  @ApiPropertyOptional()
  sort: number

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  level: number

  @ApiPropertyOptional()
  description: string

  @ApiPropertyOptional()
  parentId: string

  @ApiProperty({ description: 'Id chủ đề' })
  @IsNotEmpty({ message: 'Topic không được trống' })
  @IsString()
  topicId: string

  @ApiPropertyOptional()
  img: string
}
