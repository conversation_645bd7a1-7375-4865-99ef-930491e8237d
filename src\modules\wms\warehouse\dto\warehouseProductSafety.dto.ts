import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator'

export class WarehouseProductSafetyCreateDto {
  @ApiProperty({ description: 'Kho' })
  @IsNotEmpty()
  @IsString()
  warehouseId: string

  @ApiProperty({ description: 'Sản phẩm' })
  @IsNotEmpty()
  @IsString()
  productId: string

  @ApiProperty({ description: 'Số lượng tối thiểu' })
  @IsOptional()
  quantityMin: number

  @ApiProperty({ description: 'Số lượng tối đa' })
  @IsOptional()
  quantityMax: number
}

export class WarehouseProductSafetyImportDto {
  @ApiProperty({ description: 'Kho' })
  @IsNotEmpty()
  @IsString()
  warehouseCode: string

  // @ApiProperty({ description: 'Sản phẩm' })
  // @IsOptional()
  // productCode: string

  @ApiProperty({ description: 'Sản phẩm' })
  @IsOptional()
  productName: string

  @ApiProperty({ description: 'Số lượng tối thiểu' })
  @IsOptional()
  quantityMin: number

  @ApiProperty({ description: 'Số lượng tối đa' })
  @IsOptional()
  quantityMax: number
}

export class IncreaseQuantityOrderDto {
  @ApiProperty({ description: 'Sản phẩm' })
  @IsNotEmpty()
  @IsString()
  productId: string

  @ApiProperty({ description: 'Số lượng' })
  @IsNotEmpty()
  @IsNumber()
  quantity: number
}
