import { Body, Controller, Post, Get, Req, UploadedFile, UploadedFiles, UseGuards, UseInterceptors } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { Request as IRequest } from 'express'
import { ApeAuthGuard } from '../common/guards'
import { MobileAppService } from './mobileApp.service'
// import { surveyAuthApiHelper } from '../../helpers'
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { CurrentUser } from '../../common/decorators'
import { JwtAuthGuard } from '../../common/guards'
import { AuthService } from '../../core/auth/auth.service'
import { UpdatePasswordDto } from '../../core/auth/dto'
import { UserRegisterDto } from '../../core/auth/dto/register.dto'
import { CategoriesService } from '../categories/categories.service'
import { TopicService } from '../topic/topic.service'
import { SurveyAnswerQuestionDto, SurveyFilterOneDto } from './dto'
/** App nhân viên */
@ApiBearerAuth()
@ApiTags('MobileApp')
@Controller('mobile_app')
export class MobileAppController {
  constructor(
    private readonly service: MobileAppService,
    private readonly cateService: CategoriesService,
    private topicService: TopicService,
    private authService: AuthService,
  ) {}

  @ApiOperation({ summary: 'Lấy danh sách các danh mục' })
  @UseGuards(ApeAuthGuard)
  @Post('categories/find')
  public async categories(@CurrentUser() user: UserDto, @Body() data: any) {
    return await this.cateService.find(data)
  }

  @ApiOperation({ summary: 'Lấy bộ câu hỏi đầu tiên trong category' })
  @UseGuards(ApeAuthGuard)
  @Post('categories/find_question_of_first_topic')
  public async findQuestionOfFirstTopic(@Body() data: { topicId: string }) {
    return await this.service.findQuestionOfFirstTopic(data)
  }

  @ApiOperation({ summary: 'Lấy danh sách các danh mục có phân trang' })
  @UseGuards(ApeAuthGuard)
  @Post('categories/pagination')
  public async categoriesPagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.cateService.pagination(data)
  }

  @ApiOperation({ summary: 'Lấy danh sách hội thảo' })
  @UseGuards(ApeAuthGuard)
  @Post('topic/find')
  public async topicFind(@CurrentUser() user: UserDto, @Body() data: any) {
    return await this.topicService.find(data)
  }

  @ApiOperation({ summary: 'Lấy danh sách hội thảo có phân trang' })
  @UseGuards(ApeAuthGuard)
  @Post('topic/pagination')
  public async topicPagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.topicService.pagination(data)
  }

  @ApiOperation({ summary: 'Đăng nhập user' })
  @Post('login')
  public async login(@Body() data: { username: string; password: string }) {
    return await this.authService.loginSurveyApp(data)
  }

  @ApiOperation({ summary: 'Đăng nhập userFarm' })
  @Post('loginFarm')
  public async loginFarm(@Body() data: { phone: string; password: string }) {
    return await this.authService.loginSurveyAppFarm(data)
  }

  @ApiOperation({ summary: 'Đăng ký user' })
  @Post('register')
  public async register(@Body() data: UserRegisterDto) {
    return await this.authService.register(data)
  }

  @ApiOperation({ summary: 'Xác thực token user' })
  @Post('validate_token')
  @UseGuards(JwtAuthGuard)
  public async validate_token(@CurrentUser() user: UserDto) {
    return await this.authService.validateToken(user)
  }

  @ApiOperation({ summary: 'User đổi mật khẩu' })
  @Post('update_password_user_survey')
  @UseGuards(JwtAuthGuard)
  public async updatePasswordSurvey(@CurrentUser() user: UserDto, @Body() data: UpdatePasswordDto) {
    return await this.authService.updatePasswordSurvey(user, data)
  }

  @ApiOperation({ summary: 'Danh sách Phiếu khảo sát có phân trang' })
  @UseGuards(ApeAuthGuard)
  @Post('survey_pagination')
  public async surveyPagination(@CurrentUser() user: UserDto, @Req() req: IRequest, @Body() data: PaginationDto) {
    return await this.service.surveyPagination(user, data)
  }

  @ApiOperation({ summary: 'Lấy ds các câu hỏi' })
  @UseGuards(ApeAuthGuard)
  @Post('survey_load_question')
  public async surveyLoadQuestion(@CurrentUser() user: UserDto, @Req() req: IRequest, @Body() data: SurveyFilterOneDto) {
    return await this.service.surveyQuestion(user, data)
  }

  @ApiOperation({ summary: 'khách hàng trả lời câu hỏi' })
  @UseGuards(ApeAuthGuard)
  @Post('survey_answer_question')
  public async surveyAnswerQuestion(
    @CurrentUser() user: UserDto,
    @Req() req: IRequest,
    @Body()
    data: SurveyAnswerQuestionDto,
  ) {
    return await this.service.surveyAnswerQuestion(data)
  }

  @ApiOperation({ summary: 'Lấy ds người có trong phiếu khảo sát' })
  @UseGuards(ApeAuthGuard)
  @Post('survey_load_member')
  public async surveyLoadMember(@CurrentUser() user: UserDto, @Req() req: IRequest, @Body() data: SurveyFilterOneDto) {
    return await this.service.surveyLoadMember(user, data)
  }

  @ApiOperation({ summary: 'Lấy chi tiết trong phiếu khảo sát' })
  @UseGuards(ApeAuthGuard)
  @Post('survey_find_detail')
  public async surveyFindDetail(@CurrentUser() user: UserDto, @Req() req: IRequest, @Body() data: FilterOneDto) {
    return await this.service.surveyFindDetail(user, data, req)
  }
  @ApiOperation({ summary: 'Upload single file S3' })
  @Post('upload_single_s3')
  @UseInterceptors(FileInterceptor('file'))
  async uploadSingle(@UploadedFile() file: Express.Multer.File) {
    return await this.service.uploadSingle(file)
  }

  @ApiOperation({ summary: 'Upload multi file S3' })
  @Post('upload_multi_s3')
  @UseInterceptors(FilesInterceptor('files'))
  async uploadFile(@UploadedFiles() files: Array<Express.Multer.File>) {
    return await this.service.uploadFile(files)
  }
  @ApiOperation({ summary: 'Load all phiếu khảo sát' })
  @Post('find_all')
  public async findAll(@Body() data: any) {
    return await this.service.findAll(data)
  }
  @ApiOperation({ summary: 'Phân trang User' })
  @Post('pagination')
  public async pagination(@Body() data: PaginationDto) {
    return await this.service.pagination(data)
  }
}
