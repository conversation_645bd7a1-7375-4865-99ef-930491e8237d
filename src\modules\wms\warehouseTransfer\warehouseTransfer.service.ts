import { Injectable } from '@nestjs/common'
import {
  WarehouseTransferRepository,
  WarehouseRepository,
  ItemDetailRepository,
  ItemRepository,
  WarehouseProductRepository,
  WarehouseProductDetailRepository,
} from '../../../repositories'
import { FilterIdDto, FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { EntityManager, Equal, In, Like, Not, Raw } from 'typeorm'
import { CREATE_SUCCESS, ERROR_NOT_FOUND_DATA, UPDATE_SUCCESS, enumData } from '../../../constants'
import * as moment from 'moment'
import { coreHelper } from '../../../helpers'
import {
  InboundCreateFromOutsideDto,
  InboundDetailCreateFromOutsideDto,
  WarehouseTransferCreateByExcelDto,
  WarehouseTransferCreateDto,
  WarehouseTransferUpdateDto,
} from './dto'
import {
  ItemDetailEntity,
  ItemEntity,
  WarehouseProductDetailEntity,
  WarehouseProductEntity,
  WarehouseTransferDetailEntity,
  WarehouseTransferEntity,
  WarehouseTransferHistoryEntity,
} from '../../../entities'
import { v4 as uuidv4 } from 'uuid'
import { Request as IRequest } from 'express'
import { OutboundCreateDto, OutboundDetailCreateDto } from '../outbound/dto'
import { OutboundService } from '../outbound/outbound.service'
import { InboundService } from '../inbound/inbound.service'
import { omsApiHelper } from '../../../helpers/omsApiHelper'
interface WarehouseTransferEntityExtend extends WarehouseTransferEntity {
  __warehouseTransferDetails__?: any
}
@Injectable()
export class WarehouseTransferService {
  constructor(
    private readonly repo: WarehouseTransferRepository,
    private readonly warehouseRepo: WarehouseRepository,
    private readonly productDetailRepo: ItemDetailRepository,
    private readonly productRepo: ItemRepository,
    private readonly warehouseProductRepo: WarehouseProductRepository,
    private readonly warehouseProductDetailRepo: WarehouseProductDetailRepository,
    private readonly outboundService: OutboundService,
    private readonly inboundService: InboundService,
    private warehouseRepository: WarehouseRepository,
  ) {}

  /** Hàm tìm kiếm danh sách phiếu chuyển kho */
  async find(data: any) {
    return await this.repo.find(data)
  }

  /** Hàm tìm thông tin chi tiết phiếu chuyển kho */
  async findDetail(data: FilterOneDto, req: IRequest) {
    const entity: any = await this.repo.findOne({
      where: { id: data.id, isDeleted: false },
      relations: { warehouseTransferDetails: true, histories: true, fromWarehouse: true, toWarehouse: true },
    })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    for (let od of entity.__warehouseTransferDetails__) {
      od.__product__ = await this.productRepo.findOne({ where: { id: od.productId }, relations: { unit: true, details: true } })
    }

    entity.lstTransferWarehouseDetail = entity?.__warehouseTransferDetails__
    entity.lstHistory = entity.__histories__

    entity.fromWarehouseName = entity?.__fromWarehouse__?.name
    entity.toWarehouseName = entity?.__toWarehouse__?.name

    entity.statusName = enumData.WarehouseTransferStatus[entity.status]?.name
    entity.statusColor = enumData.WarehouseTransferStatus[entity.status]?.color

    for (let od of entity.lstTransferWarehouseDetail) {
      od.productName = od?.__product__?.name
      od.productCode = od?.__product__?.code
      od.unitName = od?.__product__?.__unit__?.name

      od.lstProductDetail = od?.__product__?.__details__ || []

      for (let pd of od.lstProductDetail) pd.expiryDateFmt = moment(pd.expiryDate).format('DD/MM/YYYY')
    }

    delete entity.__warehouseTransferDetails__
    delete entity.__histories__
    delete entity.__fromWarehouse__
    delete entity.__toWarehouse__
    const memberCreate: any = (await omsApiHelper.getMemberByListId(req, [entity.createdBy])) || []
    const memberPrepare: any = (await omsApiHelper.getMemberByListId(req, [entity.preparedBy])) || []
    const memberApprove: any = (await omsApiHelper.getMemberByListId(req, [entity.approvedBy])) || []
    entity.createdByName = memberCreate[0]?.fullName
    entity.preparedByName = memberPrepare[0]?.fullName
    entity.approveByName = memberApprove[0]?.fullName
    return entity
  }

  /** Hàm phân trang chuyển kho */
  async pagination(data: PaginationDto, req: IRequest, userlogin?: UserDto) {
    const whereCon: any = {}
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.fromWarehouseId) whereCon.fromWarehouseId = data.where.fromWarehouseId
    if (data.where.toWarehouseId) whereCon.toWarehouseId = data.where.toWarehouseId
    // if (data.where.createdAt) whereCon.createdAt = Equal(new Date(data.where.createdAt))
    if (data.where.createdAt) {
      const formattedDate = new Date(data.where.createdAt).toISOString().split('T')[0]
      whereCon.createdAt = Raw((alias) => `DATE(${alias}) = '${formattedDate}'`)
    }
    if (data.where.storeId) {
      const warehouse = await this.warehouseRepository.findOne({ where: { storeId: data.where.storeId } })
      if (warehouse) {
        whereCon.fromWarehouseId = warehouse.id
      }
    }
    // if (userlogin?.type === enumData.UserType.PartnerEmp.code && userlogin.lstStore && userlogin.lstStore.length > 0) {
    //   const lstStoreId = userlogin.lstStore.map((e) => e.id)
    //   const lstWarehouse = await this.warehouseRepo.find({ where: { storeId: In(lstStoreId) } })
    //   if (lstWarehouse.length > 0) {
    //     const lstWarehouseId = lstWarehouse.map((e) => e.id)
    //     if (whereCon.fromWarehouseId) {
    //       lstWarehouseId.push(whereCon.fromWarehouseId)
    //       whereCon.fromWarehouseId = In(lstWarehouseId)
    //     } else {
    //       whereCon.fromWarehouseId = In(lstWarehouseId)
    //     }

    //     if (whereCon.toWarehouseId) {
    //       lstWarehouseId.push(whereCon.toWarehouseId)
    //       whereCon.toWarehouseId = In(lstWarehouseId)
    //     } else {
    //       whereCon.toWarehouseId = In(lstWarehouseId)
    //     }
    //   }
    // }

    if (data.where.status) {
      if (typeof data.where.status == 'string') {
        data.where.status = [data.where.status]
      }
      whereCon.status = In(data.where.status)
    }

    // Tìm người tạo phiếu
    if (data.where.createdByName) {
      // let lstPreparedUserId: string[] = await authApiHelper.getLstUserIdByName(req, { name: data.where.createdByName })
      // if (lstPreparedUserId.length > 0) whereCon.createdBy = In(lstPreparedUserId)
      // else return [[], 0]
    }

    // Tìm shipper nhận phiếu
    if (data.where.receivedByName) {
      // let lstPreparedUserId: string[] = await authApiHelper.getLstUserIdByName(req, { name: data.where.receivedByName })
      // if (lstPreparedUserId.length > 0) whereCon.receivedBy = In(lstPreparedUserId)
      // else return [[], 0]
    }

    whereCon.warehouseTransferDetails = {}
    // whereCon.warehouseTransferDetails.product = {}
    // if (data.where.brandId) {
    //   whereCon.warehouseTransferDetails.product.brandId = Like(`%${data.where.brandId}%`)
    // }

    const [lst, total]: any = await this.repo.findAndCount({
      where: whereCon,
      order: { createdAt: 'DESC' },
      skip: data.skip,
      take: data.take,
      relations: { fromWarehouse: true, toWarehouse: true },
    })
    if (lst.length == 0) return { data: [], total: 0 }

    // Người tạo phiếu
    let dicCreate: Record<string, any> = {}
    {
      // const lstCreatedUserId = coreHelper.getDistinctArrayByKey(res[0], 'createdBy')
      // const lstUser: any[] = await authApiHelper.getLstUserName(req, { lstUserId: lstCreatedUserId })
      // dicCreate = coreHelper.arrayToObject(lstUser, 'id')
    }

    // Người tạo phiếu
    let dicReceived: Record<string, any> = {}
    {
      // const lstReceivedUserId = coreHelper.getDistinctArrayByKey(res[0], 'receivedBy')
      // const lstUser: any[] = await authApiHelper.getLstUserName(req, { lstUserId: lstReceivedUserId })
      // dicReceived = coreHelper.arrayToObject(lstUser, 'id')
    }

    for (let e of lst) {
      e.createdByName = dicCreate[e.createdBy]?.name ?? ''
      e.receivedByName = dicReceived[e.receivedBy]?.name ?? ''
      e.fromWarehouseName = e.__fromWarehouse__.name
      e.toWarehouseName = e.__toWarehouse__.name
      e.statusName = enumData.WarehouseTransferStatus[e.status].name
      e.statusColor = enumData.WarehouseTransferStatus[e.status].color
      delete e.__fromWarehouse__
      delete e.__toWarehouse__
    }

    const listMemIds = lst.map((val) => val.createdBy)
    const member: any[] = (await omsApiHelper.getMemberByListId(req, listMemIds)) || []

    const listMemReceivedIds = lst.map((val) => val.receivedBy)
    const memberReceived: any[] = (await omsApiHelper.getMemberByListId(req, listMemReceivedIds)) || []
    const mappingResult = lst.map((val) => {
      const memCreate: any = member.find((m: any) => m.id === val.createdBy)
      const memApproved: any = memberReceived.find((m: any) => m.id === val.receivedBy)

      return {
        ...val,
        createdByName: memCreate?.fullName,
        approvedByName: memApproved?.fullName,
      }
    })

    return { data: mappingResult, total }
  }

  /** Hàm lấy mã tự sinh */
  async getCodeAutoGen(): Promise<{ code: string }> {
    // Sinh code theo quy tắc ddmmyyy_xxxx (xxxx tăng dần) VD: 01102023_0001
    const curDate = moment(new Date()).format('DDMMYYYY')

    let genCode: string
    // Đếm số lượng phiếu chuyển kho trong ngày
    let count = await this.repo.count({ where: { code: Like(`CK_${curDate}_%`) }, select: { id: true } })
    if (!count) count = 0

    genCode = this.genCode(count)

    return { code: genCode }
  }

  private genCode(count: number, plusString: string = ''): string {
    const curDate = moment(new Date()).format('DDMMYYYY')
    let numberString: string = '0001'
    let genCode: string
    const incrementedNumber = Number(numberString) + count
    const newLastPart = String(incrementedNumber).padStart(numberString.length, '0')
    genCode = `${curDate}_${newLastPart}`
    if (plusString.length > 0) genCode = `${plusString}_${curDate}_${newLastPart}`
    return genCode
  }

  /** Hàm tạo lịch sử của phiếu chuyển kho */
  async createHistory(data: { description: string; warehouseTransferId: string }, manager: EntityManager) {
    const wtHistory = new WarehouseTransferHistoryEntity()
    wtHistory.createdAt = new Date()
    wtHistory.warehouseTransferId = data.warehouseTransferId
    wtHistory.description = data.description
    await manager.getRepository(WarehouseTransferHistoryEntity).insert(wtHistory)
  }

  /** Hàm tạo mới chuyển kho */
  async createData(data: WarehouseTransferCreateDto, req: IRequest) {
    const checkFromWarehouse = await this.warehouseRepo.findOne({ where: { id: data.fromWarehouseId, isDeleted: false }, select: { id: true } })
    if (!checkFromWarehouse) throw new Error(`Không tìm thấy kho đi hoặc kho đi đã bị ngưng hoạt động!`)

    const checkToWarehouse = await this.warehouseRepo.findOne({ where: { id: data.toWarehouseId, isDeleted: false }, select: { id: true } })
    if (!checkToWarehouse) throw new Error(`Không tìm thấy kho đến hoặc kho đến đã bị ngưng hoạt động!`)

    // Danh sách hạn sử dụng đã format dạng YYYY-MM-DD
    let lstProductDetailId: string[] = data.lstTransferWarehouseDetail.map((wtd) => wtd.productDetailId)

    // Danh sách id sản phẩm
    const lstProductId = data.lstTransferWarehouseDetail.mapAndDistinct((wtd) => wtd.productId)

    // Dic Product
    let dicProduct: any = {}
    {
      const lstProduct = await this.productRepo.find({
        where: { id: In(lstProductId) },
        select: { id: true, code: true, name: true, quantity: true, quantityLock: true },
      })
      dicProduct = coreHelper.arrayToObject(lstProduct)
    }

    const dicProductDetail: any = {}
    {
      let lstProductDetail: ItemDetailEntity[] = []
      lstProductDetail = await this.productDetailRepo.find({
        where: {
          id: In(lstProductDetailId),
        },
        select: { id: true, itemId: true, expiryDate: true, quantity: true, quantityLock: true, quantityLockEmp: true, lotNumber: true },
      })
      if (lstProductDetail.length == 0) throw new Error(`Không tìm thấy danh sách sản phẩm trong kho`)

      // Số lượng: quantity
      // Số lượng đã lên đơn tạm: quantityLock
      // Số lượng lock khi phân kho: quantityLockEmp

      for (let pd of lstProductDetail) {
        dicProductDetail[pd.id] = pd
      }
    }

    let dicWarehouseProduct: any = {}
    let dicWarehouseProductDetail: any = {}
    {
      const lstWarehouseProductDetail = await this.warehouseProductDetailRepo.find({
        where: { productDetailId: In(lstProductDetailId), productId: In(lstProductId), warehouseId: data.fromWarehouseId, isDeleted: false },
        select: {
          id: true,
          warehouseProductId: true,
          warehouseId: true,
          productId: true,
          productDetailId: true,
          expiryDate: true,
          quantity: true,
          quantityLock: true,
          manufactureDate: true,
        },
      })
      let lstWarehouseProductId: string[] = []
      for (let wpd of lstWarehouseProductDetail) {
        dicWarehouseProductDetail[wpd.warehouseId + wpd.productId + wpd.productDetailId] = wpd
        lstWarehouseProductId.push(wpd.warehouseProductId)
      }

      const lstWarehouseProduct = await this.warehouseProductRepo.find({
        where: { id: In(lstWarehouseProductId), isDeleted: false },
        select: { id: true, warehouseId: true, productId: true, quantity: true, quantityLock: true },
      })

      for (let wp of lstWarehouseProduct) dicWarehouseProduct[wp.warehouseId + wp.productId] = wp
    }

    for (let obd of data.lstTransferWarehouseDetail) {
      let productDetail = dicProductDetail[obd.productDetailId]
      if (!productDetail) throw new Error(`Không tìm thấy sản phẩm trong kho`)

      const product = dicProduct[obd.productId]
      if (!product) throw new Error(`Không tìm thấy sản phẩm thực`)

      const warehouseProduct = dicWarehouseProduct[data.fromWarehouseId + obd.productId]
      if (!warehouseProduct) throw new Error(`Không tìm thấy sản phẩm trong kho đi vật lý`)

      const warehouseProductDetail = dicWarehouseProductDetail[data.fromWarehouseId + obd.productId + productDetail.id]
      if (!warehouseProductDetail) throw new Error(`Không tìm thấy chi tiết sản phẩm trong kho đi vật lý`)

      // #region kiểm tra điều kiện trước khi trừ tồn kho
      {
        if (obd.quantity > +productDetail.quantity - +productDetail.quantityLock)
          if (!productDetail)
            throw new Error(
              `số lượng xuất của sản phẩm ${product.name} ${obd.quantity} lớn hơn số lượng tồn kho ${
                +productDetail.quantity - +productDetail.quantityLock
              }!`,
            )

        if (obd.quantity > +product.quantity - +product.quantityLock)
          if (!product)
            throw new Error(
              `số lượng xuất của sản phẩm ${product.name} ${obd.quantity} lớn hơn số lượng tồn kho ${+product.quantity - +product.quantityLock}!`,
            )

        if (obd.quantity > +warehouseProductDetail.quantity - +warehouseProductDetail.quantityLock)
          if (!warehouseProductDetail)
            throw new Error(
              `số lượng xuất của sản phẩm ${product.name} ${obd.quantity} lớn hơn số lượng tồn kho ${
                +warehouseProductDetail.quantity - +warehouseProductDetail.quantityLock
              }!`,
            )

        if (obd.quantity > +warehouseProduct.quantity - +warehouseProduct.quantityLock)
          if (!warehouseProduct)
            throw new Error(
              `số lượng xuất của sản phẩm ${product.name} ${obd.quantity} lớn hơn số lượng tồn kho ${
                +warehouseProduct.quantity - +warehouseProduct.quantityLock
              }!`,
            )
      }
    }

    // Sinh code theo quy tắc ddmmyyy_xxxx (xxxx tăng dần) VD: 01102023_0001
    const curDate = moment(new Date()).format('DDMMYYYY')

    let genCode: string
    // Đếm số lượng phiếu xuất kho trong ngày
    let count = await this.repo.count({ where: { code: Like(`CK_${curDate}_%`) }, select: { id: true } })
    if (!count) count = 0

    genCode = this.genCode(count, 'CK')

    await this.repo.manager.transaction(async (trans) => {
      const lstTask: WarehouseTransferDetailEntity[] = []
      const repo = trans.getRepository(WarehouseTransferEntity)
      const warehouseTransferDetailRepo = trans.getRepository(WarehouseTransferDetailEntity)
      const warehouseTransfer = new WarehouseTransferEntity()
      warehouseTransfer.id = uuidv4()
      warehouseTransfer.code = genCode
      warehouseTransfer.fromWarehouseId = data.fromWarehouseId
      warehouseTransfer.toWarehouseId = data.toWarehouseId
      warehouseTransfer.description = data?.description ?? null
      warehouseTransfer.createdBy = data.createBy
      await repo.insert(warehouseTransfer)

      for (let detail of data.lstTransferWarehouseDetail) {
        const warehouseTransferDetail = new WarehouseTransferDetailEntity()
        warehouseTransferDetail.warehouseTransferId = warehouseTransfer.id
        warehouseTransferDetail.productId = detail.productId
        warehouseTransferDetail.productDetailId = detail.productDetailId
        warehouseTransferDetail.unitId = detail.unitId
        warehouseTransferDetail.expiryDate = new Date(detail.expiryDate)
        warehouseTransferDetail.manufactureDate = detail?.manufactureDate ? new Date(detail.manufactureDate) : null
        warehouseTransferDetail.lotNumber = detail.lotNumber
        warehouseTransferDetail.inventory = detail.inventory
        warehouseTransferDetail.quantity = detail.quantity
        warehouseTransferDetail.createdBy = data.createBy
        lstTask.push(warehouseTransferDetail)
      }

      const member: any = await omsApiHelper.getMemberByListId(req, [data.createBy])

      const description = `Nhân viên ${member[0]?.fullName} tạo mới phiếu chuyển kho`
      await this.createHistory({ warehouseTransferId: warehouseTransfer.id, description }, trans)

      await warehouseTransferDetailRepo.insert(lstTask)
    })

    return { message: CREATE_SUCCESS }
  }

  /** Hàm cập nhật chuyển kho */
  async updateData(data: WarehouseTransferUpdateDto, req: IRequest) {
    const checkFromWarehouse = await this.warehouseRepo.findOne({ where: { id: data.fromWarehouseId, isDeleted: false }, select: { id: true } })
    if (!checkFromWarehouse) throw new Error(`Không tìm thấy kho đi hoặc kho đi đã bị ngưng hoạt động!`)

    const checkToWarehouse = await this.warehouseRepo.findOne({ where: { id: data.toWarehouseId, isDeleted: false }, select: { id: true } })
    if (!checkToWarehouse) throw new Error(`Không tìm thấy kho đến hoặc kho đến đã bị ngưng hoạt động!`)

    const check = await this.repo.findOne({ where: { id: data.id, isDeleted: false } })
    if (!check) throw new Error(`Không tìm thấy phiếu chuyển kho hoặc phiếu chuyển kho đã bị ngưng hoạt động!`)

    if (check.status != enumData.WarehouseTransferStatus.NEW.code)
      throw new Error(`Chỉ có thể cập nhật phiếu chuyển kho ở trạng thái [ ${enumData.WarehouseTransferStatus.NEW.name} ]`)

    await this.repo.manager.transaction(async (trans) => {
      const lstTask: WarehouseTransferDetailEntity[] = []
      const repo = trans.getRepository(WarehouseTransferEntity)
      const warehouseTransferDetailRepo = trans.getRepository(WarehouseTransferDetailEntity)
      check.fromWarehouseId = data.fromWarehouseId
      check.toWarehouseId = data.toWarehouseId
      check.description = data?.description ?? null
      check.updatedBy = data.updateBy
      await repo.update(check.id, check)

      // Xoá sản phẩm trong phiếu chuyển kho
      await warehouseTransferDetailRepo.delete({ warehouseTransferId: check.id })

      // Thêm sản phẩm vào phiếu chuyển kho
      for (let detail of data.lstTransferWarehouseDetail) {
        const warehouseTransferDetail = new WarehouseTransferDetailEntity()
        warehouseTransferDetail.warehouseTransferId = check.id
        warehouseTransferDetail.productId = detail.productId
        warehouseTransferDetail.productDetailId = detail.productDetailId
        warehouseTransferDetail.unitId = detail.unitId
        warehouseTransferDetail.expiryDate = new Date(detail.expiryDate)
        warehouseTransferDetail.manufactureDate = detail?.manufactureDate ? new Date(detail.manufactureDate) : null
        warehouseTransferDetail.lotNumber = detail.lotNumber
        warehouseTransferDetail.inventory = detail.inventory
        warehouseTransferDetail.quantity = detail.quantity
        warehouseTransferDetail.createdBy = data.updateBy
        warehouseTransferDetail.updatedBy = data.updateBy
        warehouseTransferDetail.updatedAt = new Date()
        lstTask.push(warehouseTransferDetail)
      }

      const member: any = await omsApiHelper.getMemberByListId(req, [data.updateBy])

      const description = `Nhân viên ${member[0]?.fullName} cập nhật phiếu chuyển kho`
      await this.createHistory({ warehouseTransferId: check.id, description }, trans)

      await warehouseTransferDetailRepo.insert(lstTask)
    })

    return { message: UPDATE_SUCCESS }
  }

  /** Hàm duyệt phiếu chuyển kho */
  async updateApprove(data: FilterOneDto, req?: IRequest) {
    const entity: WarehouseTransferEntityExtend = await this.repo.findOne({ where: { id: data.id }, relations: { warehouseTransferDetails: true } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    if (entity.status != enumData.WarehouseTransferStatus.NEW.code)
      throw new Error(`Chỉ có thể duyệt phiếu ở trạng thái [ ${enumData.WarehouseTransferStatus.NEW.name} ]`)

    const checkWarehouse = await this.warehouseRepo.findOne({ where: { id: entity.fromWarehouseId, isDeleted: false } })
    if (!checkWarehouse) throw new Error('Không tìm thấy kho đi')

    // Danh sách sản phẩm trong phiếu chuyển kho
    const lstWarehouseTransferDetail = entity.__warehouseTransferDetails__

    // Danh sách hạn sử dụng đã format dạng YYYY-MM-DD
    let lstProductDetailId: string[] = lstWarehouseTransferDetail.map((wtd) => wtd.productDetailId)

    // Danh sách id sản phẩm
    const lstProductId = lstWarehouseTransferDetail.mapAndDistinct((wtd) => wtd.productId)

    // Dic Product
    let dicProduct: any = {}
    {
      const lstProduct = await this.productRepo.find({
        where: { id: In(lstProductId), isDeleted: false },
        select: { id: true, code: true, name: true, quantity: true, quantityLock: true, quantityLockEmp: true },
      })
      dicProduct = coreHelper.arrayToObject(lstProduct)
    }

    const dicProductDetail: any = {}
    {
      const lstProductDetail: ItemDetailEntity[] = await this.productDetailRepo.find({
        where: {
          id: In(lstProductDetailId),
        },
        select: { id: true, itemId: true, expiryDate: true, quantity: true, quantityLock: true, quantityLockEmp: true },
      })

      if (lstProductDetail.length == 0) throw new Error(`Không tìm thấy danh sách sản phẩm trong kho`)

      // Số lượng: quantity
      // Số lượng đã lên đơn tạm: quantityLock
      for (let pd of lstProductDetail) {
        // Tạo dic dicProductDetail
        dicProductDetail[pd.id] = pd
      }
    }

    let dicWarehouseProduct: any = {}
    let dicWarehouseProductDetail: any = {}
    {
      const lstWarehouseProductDetail = await this.warehouseProductDetailRepo.find({
        where: { productDetailId: In(lstProductDetailId), productId: In(lstProductId), warehouseId: entity.fromWarehouseId, isDeleted: false },
        select: {
          id: true,
          warehouseProductId: true,
          warehouseId: true,
          productId: true,
          productDetailId: true,
          expiryDate: true,
          quantity: true,
          quantityLock: true,
        },
      })
      let lstWarehouseProductId: string[] = []
      for (let wpd of lstWarehouseProductDetail) {
        dicWarehouseProductDetail[wpd.warehouseId + wpd.productId + wpd.productDetailId] = wpd
        lstWarehouseProductId.push(wpd.warehouseProductId)
      }

      const lstWarehouseProduct = await this.warehouseProductRepo.find({
        where: { id: In(lstWarehouseProductId), isDeleted: false },
        select: { id: true, warehouseId: true, productId: true, quantity: true, quantityLock: true },
      })

      for (let wp of lstWarehouseProduct) dicWarehouseProduct[wp.warehouseId + wp.productId] = wp
    }

    await this.repo.manager.transaction(async (trans) => {
      let lstTask: any[] = []
      let lstTaskWarehouseTransferDetail: any[] = []
      const repo = trans.getRepository(WarehouseTransferEntity)
      const productRepo = trans.getRepository(ItemEntity)
      const productDetailRepo = trans.getRepository(ItemDetailEntity)
      const warehouseProductRepo = trans.getRepository(WarehouseProductEntity)
      const warehouseProductDetailRepo = trans.getRepository(WarehouseProductDetailEntity)
      const warehouseTransferRepo = trans.getRepository(WarehouseTransferEntity)
      const warehouseTransferDetailRepo = trans.getRepository(WarehouseTransferDetailEntity)
      // Danh sách sản phẩm trong phiếu chuyển kho
      for (let obd of lstWarehouseTransferDetail) {
        const productDetail = dicProductDetail[obd.productDetailId]
        if (!productDetail) throw new Error(`Không tìm thấy sản phẩm trong kho`)

        const product = dicProduct[obd.productId]
        if (!product) throw new Error(`Không tìm thấy sản phẩm thực`)

        const warehouseProduct = dicWarehouseProduct[entity.fromWarehouseId + obd.productId]
        if (!warehouseProduct) throw new Error(`Không tìm thấy sản phẩm trong kho vật lý`)

        const warehouseProductDetail = dicWarehouseProductDetail[entity.fromWarehouseId + obd.productId + productDetail.id]
        if (!warehouseProductDetail) throw new Error(`Không tìm thấy chi tiết sản phẩm trong kho vật lý`)

        // #region chuyển tra điều kiện trước khi trừ tồn kho
        {
          if (obd.quantity > +productDetail.quantity - +productDetail.quantityLock)
            if (!productDetail)
              throw new Error(
                `Khối lượng xuất của sản phẩm ${product.name} ${obd.quantity} lớn hơn khối lượng tồn kho ${
                  +productDetail.quantity - +productDetail.quantityLock
                }!`,
              )

          if (obd.quantity > +product.quantity - +product.quantityLock)
            if (!product)
              throw new Error(
                `Khối lượng xuất của sản phẩm ${product.name} ${obd.quantity} lớn hơn khối lượng tồn kho ${
                  +product.quantity - +product.quantityLock
                }!`,
              )

          if (obd.quantity > +warehouseProductDetail.quantity - +warehouseProductDetail.quantityLock)
            if (!warehouseProductDetail)
              throw new Error(
                `Khối lượng xuất của sản phẩm ${product.name} ${obd.quantity} lớn hơn khối lượng tồn kho ${
                  +warehouseProductDetail.quantity - +warehouseProductDetail.quantityLock
                }!`,
              )

          if (obd.quantity > +warehouseProduct.quantity - +warehouseProduct.quantityLock)
            if (!warehouseProduct)
              throw new Error(
                `Khối lượng xuất của sản phẩm ${product.name} ${obd.quantity} lớn hơn khối lượng tồn kho ${
                  +warehouseProduct.quantity - +warehouseProduct.quantityLock
                }!`,
              )
        }

        // #region Trừ bảng product và productDetail
        // Trừ của productDetail
        productDetail.quantity = +productDetail.quantity - +obd.quantity
        productDetail.quantityLockEmp = +productDetail.quantityLockEmp - +obd.quantity
        productDetail.updatedAt = new Date()
        productDetail.updatedBy = data.approveBy
        lstTask.push(productDetailRepo.update(productDetail.id, productDetail))

        // Trừ của product
        product.quantity = +product.quantity - +obd.quantity
        product.quantityLockEmp = +product.quantityLockEmp - +obd.quantity
        product.updatedAt = new Date()
        product.updatedBy = data.approveBy
        lstTask.push(productRepo.update(product.id, product))
        // #endregion

        // #region Trừ bảng warehouseProduct và warehouseProductDetail
        // Trừ của warehouseProductDetail
        warehouseProductDetail.quantity = +warehouseProductDetail.quantity - +obd.quantity
        warehouseProductDetail.updatedAt = new Date()
        warehouseProductDetail.updatedBy = data.approveBy

        lstTask.push(warehouseProductDetailRepo.update(warehouseProductDetail.id, warehouseProductDetail))

        const lstWarehouseTransferDetail = await warehouseTransferDetailRepo.find({
          where: {
            warehouseTransferId: Not(data.id),
            productDetailId: warehouseProductDetail.productDetailId,
            warehouseTransfer: {
              fromWarehouseId: warehouseProductDetail.warehouseId,
              status: enumData.WarehouseTransferStatus.NEW.code,
            },
          },
          select: { id: true },
          relations: { warehouseTransfer: true },
        })

        lstTaskWarehouseTransferDetail.push(
          warehouseTransferDetailRepo.update({ id: In(lstWarehouseTransferDetail.map((x) => x.id)) }, { inventory: warehouseProductDetail.quantity }),
        )

        // Trừ của warehouseProduct
        warehouseProduct.quantity = +warehouseProduct.quantity - +obd.quantity
        warehouseProduct.updatedAt = new Date()
        warehouseProduct.updatedBy = data.approveBy
        lstTask.push(warehouseProductRepo.update(warehouseProduct.id, warehouseProduct))
        // #endregion
      }
      // #region Tạo phiếu xuất kho

      let lstOutboundDetail: OutboundDetailCreateDto[] = []

      for (let wtd of lstWarehouseTransferDetail) {
        lstOutboundDetail.push({
          productId: wtd.productId,
          expiryDate: new Date(wtd.expiryDate),
          manufactureDate: null,
          lotNumber: wtd.lotNumber,
          inventory: wtd.inventory,
          quantity: wtd.quantity,
          productDetailId: wtd.productDetailId,
        })
      }

      const outboundCreateDto: OutboundCreateDto = {
        type: enumData.OutboundType.CHECK_INVENTORY.code,
        warehouseId: entity.fromWarehouseId,
        warehouseTransferId: entity.id,
        createdAt: new Date(),
        lstOutboundDetail: lstOutboundDetail,
      }

      await this.outboundService.createData(outboundCreateDto, trans, req)
      // #endregion

      // Xoá relation khi update
      delete entity.__warehouseTransferDetails__

      entity.status = enumData.WarehouseTransferStatus.FINISH.code
      entity.approvedBy = data.approveBy
      entity.approvedDate = new Date()
      lstTask.push(repo.update(entity.id, entity))
      await Promise.all(lstTask)
      await Promise.all(lstTaskWarehouseTransferDetail)

      const member: any = await omsApiHelper.getMemberByListId(req, [data.approveBy])

      const description = `Nhân viên ${member[0]?.fullName} duyệt phiếu chuyển kho`
      await this.createHistory({ warehouseTransferId: entity.id, description }, trans)
      return { message: 'Duyệt phiếu chuyển kho thành công!' }
    })
    await this.shipperComfirmTransfer(data, req, true)
    return { message: UPDATE_SUCCESS }
  }

  /** Hàm huỷ phiếu chuyển kho */
  async updateCancel(data: FilterOneDto, req?: IRequest) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    if (entity.status != enumData.WarehouseTransferStatus.NEW.code)
      throw new Error(`Chỉ có thể huỷ phiếu ở trạng thái [ ${enumData.WarehouseTransferStatus.NEW.name} ]`)

    entity.status = enumData.WarehouseTransferStatus.CANCEL.code
    entity.updatedBy = data.approveBy
    await this.repo.update(entity.id, entity)

    const member: any = await omsApiHelper.getMemberByListId(req, [data.approveBy])

    const description = `Nhân viên ${member[0]?.username} huỷ phiếu chuyển kho`
    await this.createHistory({ warehouseTransferId: entity.id, description }, this.repo.manager)

    return { message: 'Huỷ phiếu chuyển kho thành công!' }
  }

  /** Nhân viên chuyển trạng thái phiếu thành [Đã gửi shipper] */
  async sendShipper(data: FilterOneDto, req?: IRequest) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    if (entity.status != enumData.WarehouseTransferStatus.APPROVED.code)
      throw new Error(
        `Nhân viên chỉ có thể chuyển trạng thái phiếu thành [ ${enumData.WarehouseTransferStatus.SENT_SHIPPER.name} ] xác nhận khi phiếu ở trạng thái [ ${enumData.WarehouseTransferStatus.APPROVED.name} ]`,
      )

    entity.status = enumData.WarehouseTransferStatus.SENT_SHIPPER.code
    entity.updatedBy = data.approveBy
    await this.repo.update(entity.id, entity)

    const member: any = await omsApiHelper.getMemberByListId(req, [data.approveBy])

    const description = `Nhân viên ${member[0]?.username} gửi phiếu chuyển kho cho shipper`
    await this.createHistory({ warehouseTransferId: entity.id, description }, this.repo.manager)

    return { message: `Đã gửi shipper thành công!` }
  }

  /** Shipper chuyển trạng thái phiếu thành [Xác nhận] */
  async shipperConfirm(data: FilterOneDto, req?: IRequest) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    if (entity.status != enumData.WarehouseTransferStatus.SENT_SHIPPER.code)
      throw new Error(
        `Shipper chỉ có thể chuyển trạng thái phiếu thành [ ${enumData.WarehouseTransferStatus.SHIPPER_CONFIRMED.name} ] xác nhận khi phiếu ở trạng thái [ ${enumData.WarehouseTransferStatus.SENT_SHIPPER.name} ]`,
      )

    entity.status = enumData.WarehouseTransferStatus.SHIPPER_CONFIRMED.code
    entity.receivedBy = data.approveBy
    entity.receivedAt = new Date()
    await this.repo.update(entity.id, entity)

    const member: any = await omsApiHelper.getMemberByListId(req, [data.approveBy])

    const description = `Shipper ${member[0]?.username} thay đổi trạng thái phiếu thành ${enumData.WarehouseTransferStatus.SHIPPER_CONFIRMED.name}`
    await this.createHistory({ warehouseTransferId: entity.id, description }, this.repo.manager)

    return { message: 'Shipper xác nhận lấy đơn thành công!' }
  }

  /** Shipper chuyển trạng thái phiếu thành [Xác nhận] */
  async shipperConfirmList(data: FilterIdDto, user?: UserDto) {
    return await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(WarehouseTransferEntity)
      let lstTask: any[] = []
      for (let dt of data.lstId) {
        const entity = await this.repo.findOne({ where: { id: dt } })
        if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
        if (entity.status != enumData.WarehouseTransferStatus.SENT_SHIPPER.code)
          throw new Error(
            `Shipper chỉ có thể chuyển trạng thái phiếu [ ${entity.code} ] thành [ ${enumData.WarehouseTransferStatus.SHIPPER_CONFIRMED.name} ] khi phiếu ở trạng thái [ ${enumData.WarehouseTransferStatus.SENT_SHIPPER.name} ]`,
          )

        entity.status = enumData.WarehouseTransferStatus.SHIPPER_CONFIRMED.code
        entity.receivedBy = user?.id
        entity.receivedAt = new Date()
        lstTask.push(repo.update(entity.id, entity))

        const description = `Shipper ${user?.username} thay đổi trạng thái phiếu thành ${enumData.WarehouseTransferStatus.SHIPPER_CONFIRMED.name}`
        await this.createHistory({ warehouseTransferId: entity.id, description }, trans)

        await Promise.all(lstTask)

        return { message: 'Shipper xác nhận lấy đơn thành công!' }
      }
    })
  }

  /** Shipper chuyển trạng thái phiếu thành [Chờ chuyển kho] */
  async shipperWaiting(data: FilterOneDto, user?: UserDto) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    if (entity.status != enumData.WarehouseTransferStatus.SHIPPER_CONFIRMED.code)
      throw new Error(
        `Shipper chỉ có thể chuyển trạng thái phiếu thành [ ${enumData.WarehouseTransferStatus.WAITING.name} ] khi phiếu ở trạng thái [ ${enumData.WarehouseTransferStatus.SHIPPER_CONFIRMED.name} ]`,
      )

    entity.status = enumData.WarehouseTransferStatus.WAITING.code
    entity.updatedBy = user?.id
    await this.repo.update(entity.id, entity)

    const description = `Shipper ${user?.username} thay đổi trạng thái phiếu thành ${enumData.WarehouseTransferStatus.WAITING.name}`
    await this.createHistory({ warehouseTransferId: entity.id, description }, this.repo.manager)

    return { message: 'Shipper chờ chuyển kho thành công!' }
  }

  /** Shipper chuyển trạng thái phiếu thành [Chờ chuyển kho] */
  async shipperWaitingList(data: FilterIdDto, user?: UserDto) {
    return await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(WarehouseTransferEntity)
      let lstTask: any[] = []
      for (let dt of data.lstId) {
        const entity = await this.repo.findOne({ where: { id: dt } })
        if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
        if (entity.status != enumData.WarehouseTransferStatus.SHIPPER_CONFIRMED.code)
          throw new Error(
            `Shipper chỉ có thể chuyển trạng thái phiếu [ ${entity.code} ] thành [ ${enumData.WarehouseTransferStatus.WAITING.name} ] khi phiếu ở trạng thái [ ${enumData.WarehouseTransferStatus.SHIPPER_CONFIRMED.name} ]`,
          )

        entity.status = enumData.WarehouseTransferStatus.WAITING.code
        entity.receivedBy = user?.id
        entity.receivedAt = new Date()
        lstTask.push(repo.update(entity.id, entity))

        const description = `Shipper ${user?.username} thay đổi trạng thái phiếu thành ${enumData.WarehouseTransferStatus.WAITING.name}`
        await this.createHistory({ warehouseTransferId: entity.id, description }, trans)

        await Promise.all(lstTask)

        return { message: 'Shipper chờ chuyển kho thành công!' }
      }
    })
  }

  /** Shipper chuyển trạng thái phiếu thành [Đang chuyển] */
  async shipperDelivering(data: FilterOneDto, req?: IRequest) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    if (entity.status != enumData.WarehouseTransferStatus.WAITING.code)
      throw new Error(
        `Shipper chỉ có thể chuyển trạng thái phiếu thành [ ${enumData.WarehouseTransferStatus.DELIVERING.name} ] khi phiếu ở trạng thái [ ${enumData.WarehouseTransferStatus.WAITING.name} ]`,
      )

    entity.status = enumData.WarehouseTransferStatus.DELIVERING.code
    entity.updatedBy = data.approveBy
    await this.repo.update(entity.id, entity)

    const member: any = await omsApiHelper.getMemberByListId(req, [data.approveBy])

    const description = `Shipper ${member[0]?.username} thay đổi trạng thái phiếu thành ${enumData.WarehouseTransferStatus.DELIVERING.name}`
    await this.createHistory({ warehouseTransferId: entity.id, description }, this.repo.manager)

    return { message: 'Shipper bắt đầu phát thành công!' }
  }

  /** Shipper chuyển trạng thái phiếu thành [Đang chuyển] */
  async shipperDeliveringList(data: FilterIdDto, user?: UserDto) {
    if (data.lstId.length == 0) throw new Error(`Vui lòng chọn ít nhất một phiếu!`)
    const dicWareseHouseTransfer: Record<string, any> = {}
    {
      const lstWareseHouseTransfer = await this.repo.find({ where: { id: In(data.lstId) } })
      for (let wht of lstWareseHouseTransfer) dicWareseHouseTransfer[wht.id] = wht
    }
    return await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(WarehouseTransferEntity)
      let lstTask: any[] = []
      for (let dt of data.lstId) {
        const entity = dicWareseHouseTransfer[dt]
        if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
        if (entity.status != enumData.WarehouseTransferStatus.WAITING.code)
          throw new Error(
            `Shipper chỉ có thể chuyển trạng thái phiếu [ ${entity.code} ] thành [ ${enumData.WarehouseTransferStatus.DELIVERING.name} ] khi phiếu ở trạng thái [ ${enumData.WarehouseTransferStatus.WAITING.name} ]`,
          )

        entity.status = enumData.WarehouseTransferStatus.DELIVERING.code
        entity.receivedBy = user?.id
        entity.receivedAt = new Date()
        lstTask.push(repo.update(entity.id, entity))

        const description = `Shipper ${user?.username} thay đổi trạng thái phiếu thành ${enumData.WarehouseTransferStatus.DELIVERING.name}`
        await this.createHistory({ warehouseTransferId: entity.id, description }, trans)

        await Promise.all(lstTask)

        return { message: 'Shipper bắt đầu phát thành công!' }
      }
    })
  }

  /** Shipper chuyển trạng thái phiếu thành [Hoàn thành] */
  async shipperFinish(data: FilterOneDto, req?: IRequest) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    if (entity.status != enumData.WarehouseTransferStatus.DELIVERING.code)
      throw new Error(
        `Shipper chỉ có thể chuyển trạng thái phiếu thành [ ${enumData.WarehouseTransferStatus.FINISH.name} ] khi phiếu ở trạng thái [ ${enumData.WarehouseTransferStatus.DELIVERING.name} ]`,
      )

    entity.status = enumData.WarehouseTransferStatus.FINISH.code
    entity.updatedBy = data.approveBy
    await this.repo.update(entity.id, entity)

    const member: any = await omsApiHelper.getMemberByListId(req, [data.approveBy])

    const description = `Shipper ${member[0]?.username} thay đổi trạng thái phiếu thành ${enumData.WarehouseTransferStatus.FINISH.name}`
    await this.createHistory({ warehouseTransferId: entity.id, description }, this.repo.manager)

    return { message: 'Shipper xác nhận giao thành công!' }
  }

  /** Nhân viên chuyển trạng thái phiếu thành [Xác nhận đã chuyển] */
  async shipperComfirmTransfer(data: FilterOneDto, req: IRequest, isPrivate: boolean = false) {
    const entity: WarehouseTransferEntityExtend = await this.repo.findOne({ where: { id: data.id }, relations: { warehouseTransferDetails: true } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    if (entity.status != enumData.WarehouseTransferStatus.FINISH.code)
      throw new Error(
        `Shipper chỉ có thể chuyển trạng thái phiếu thành [ ${enumData.WarehouseTransferStatus.CONFIRMED_TRANSFER.name} ] khi phiếu ở trạng thái [ ${enumData.WarehouseTransferStatus.FINISH.name} ]`,
      )

    // Danh sách sản phẩm trong phiếu chuyển kho
    const lstWarehouseTransferDetail = entity.__warehouseTransferDetails__

    // chuyển tra kho đến
    const checkWarehouse = await this.warehouseRepo.findOne({ where: { id: entity.toWarehouseId, isDeleted: false } })
    if (!checkWarehouse) throw new Error('Không tìm thấy kho đến')

    // Danh sách hạn sử dụng đã format dạng YYYY-MM-DD
    let lstProductDetailId: string[] = lstWarehouseTransferDetail.map((wtd) => wtd.productDetailId)

    // Danh sách id sản phẩm
    const lstProductId = lstWarehouseTransferDetail.mapAndDistinct((wtd) => wtd.productId)

    // Dic Product
    let dicProduct: any = {}
    {
      const lstProduct = await this.productRepo.find({
        where: { id: In(lstProductId) },
        select: { id: true, code: true, name: true, quantity: true, quantityLock: true, quantityLockEmp: true },
      })
      dicProduct = coreHelper.arrayToObject(lstProduct)
    }

    const dicProductDetail: any = {}
    {
      const lstProductDetail: ItemDetailEntity[] = await this.productDetailRepo.find({
        where: {
          id: In(lstProductDetailId),
        },
        select: { id: true, itemId: true, expiryDate: true, quantity: true, quantityLock: true, quantityLockEmp: true, manufactureDate: true },
      })

      if (lstProductDetail.length == 0) throw new Error(`Không tìm thấy danh sách sản phẩm trong kho`)

      // Số lượng: quantity
      // Số lượng đã lên đơn tạm: quantityLock
      for (let pd of lstProductDetail) {
        // Tạo dic dicProductDetail
        dicProductDetail[pd.id] = pd
      }
    }

    let dicWarehouseProduct: any = {}
    let dicWarehouseProductDetail: any = {}
    {
      const lstWarehouseProductDetail = await this.warehouseProductDetailRepo.find({
        where: { productDetailId: In(lstProductDetailId), productId: In(lstProductId), warehouseId: entity.toWarehouseId, isDeleted: false },
        select: {
          id: true,
          warehouseProductId: true,
          warehouseId: true,
          productId: true,
          productDetailId: true,
          expiryDate: true,
          quantity: true,
          quantityLock: true,
        },
      })
      let lstWarehouseProductId: string[] = []
      for (let wpd of lstWarehouseProductDetail) {
        dicWarehouseProductDetail[wpd.warehouseId + wpd.productId + wpd.productDetailId] = wpd
        lstWarehouseProductId.push(wpd.warehouseProductId)
      }

      const lstWarehouseProduct = await this.warehouseProductRepo.find({
        where: { id: In(lstWarehouseProductId), isDeleted: false },
        select: { id: true, warehouseId: true, productId: true, quantity: true, quantityLock: true },
      })

      for (let wp of lstWarehouseProduct) dicWarehouseProduct[wp.warehouseId + wp.productId] = wp
    }

    return await this.repo.manager.transaction(async (trans) => {
      let lstTask: any[] = []
      const repo = trans.getRepository(WarehouseTransferEntity)
      const productRepo = trans.getRepository(ItemEntity)
      const productDetailRepo = trans.getRepository(ItemDetailEntity)
      const warehouseProductRepo = trans.getRepository(WarehouseProductEntity)
      const warehouseProductDetailRepo = trans.getRepository(WarehouseProductDetailEntity)

      entity.status = enumData.WarehouseTransferStatus.CONFIRMED_TRANSFER.code
      entity.updatedBy = data.approveBy

      // #region Cộng tồn kho

      // Danh sách sản phẩm trong phiếu chuyển kho
      for (let obd of lstWarehouseTransferDetail) {
        const productDetail = dicProductDetail[obd.productDetailId]
        if (!productDetail) throw new Error(`Không tìm thấy sản phẩm trong kho`)

        const product = dicProduct[obd.productId]
        if (!product) throw new Error(`Không tìm thấy sản phẩm thực`)

        let warehouseProduct: WarehouseProductEntity = dicWarehouseProduct[entity.toWarehouseId + obd.productId]
        // Tạo mới sản phẩm trong kho vật lý
        if (!warehouseProduct) {
          warehouseProduct = new WarehouseProductEntity()
          warehouseProduct.id = uuidv4()
          warehouseProduct.warehouseId = entity.toWarehouseId
          warehouseProduct.productId = product.id
          warehouseProduct.quantity = obd.quantity
          warehouseProduct.productCode = product.code
          warehouseProduct.productName = product.name

          lstTask.push(warehouseProductRepo.insert(warehouseProduct))
        }
        // Cộng của warehouseProduct
        else {
          warehouseProduct.quantity = +warehouseProduct.quantity + +obd.quantity
          warehouseProduct.updatedAt = new Date()
          warehouseProduct.updatedBy = data.approveBy
          lstTask.push(warehouseProductRepo.update(warehouseProduct.id, warehouseProduct))
        }

        let warehouseProductDetail: WarehouseProductDetailEntity = dicWarehouseProductDetail[entity.toWarehouseId + obd.productId + productDetail.id]
        // Tạo mới chi tiết sản phẩm trong kho vật lý
        if (!warehouseProductDetail) {
          warehouseProductDetail = new WarehouseProductDetailEntity()
          warehouseProductDetail.warehouseProductId = warehouseProduct.id
          warehouseProductDetail.warehouseId = entity.toWarehouseId
          warehouseProductDetail.productId = product.id
          warehouseProductDetail.productDetailId = productDetail.id
          warehouseProductDetail.manufactureDate = productDetail.manufactureDate
          warehouseProductDetail.expiryDate = new Date(obd.expiryDate)
          warehouseProductDetail.quantity = obd.quantity
          warehouseProductDetail.productCode = product.code
          warehouseProductDetail.productName = product.name
          lstTask.push(warehouseProductDetailRepo.insert(warehouseProductDetail))
        }
        // Cộng của warehouseProductDetail
        else {
          warehouseProductDetail.quantity = +warehouseProductDetail.quantity + +obd.quantity
          warehouseProductDetail.updatedAt = new Date()
          warehouseProductDetail.updatedBy = data.approveBy
          lstTask.push(warehouseProductDetailRepo.update(warehouseProductDetail.id, warehouseProductDetail))
        }

        // #region Cộng bảng product và productDetail
        // Cộng của productDetail
        productDetail.quantity = +productDetail.quantity + +obd.quantity
        productDetail.quantityLockEmp = +productDetail.quantityLockEmp + +obd.quantity
        productDetail.updatedAt = new Date()
        productDetail.updatedBy = data.approveBy
        lstTask.push(productDetailRepo.update(productDetail.id, productDetail))

        // Cộng của product
        product.quantity = +product.quantity + +obd.quantity
        product.quantityLockEmp = +product.quantityLockEmp + +obd.quantity
        product.updatedAt = new Date()
        product.updatedBy = data.approveBy
        lstTask.push(productRepo.update(product.id, product))
        // #endregion
      }
      // #region Tạo phiếu nhập kho

      let lstDetail: InboundDetailCreateFromOutsideDto[] = []

      for (let wtd of lstWarehouseTransferDetail) {
        lstDetail.push({
          productId: wtd.productId,
          productDetailId: wtd.productDetailId,
          productName: dicProduct[wtd.productId].name,
          productCode: dicProduct[wtd.productId].code,
          unitId: wtd.unitId,
          expiryDate: new Date(wtd.expiryDate),
          manufactureDate: wtd.manufactureDate ? new Date(wtd.manufactureDate) : null,
          lotNumber: wtd.lotNumber,
          inventory: wtd.inventory,
          quantity: wtd.quantity,
        })
      }

      const inboundCreateDto: InboundCreateFromOutsideDto = {
        type: enumData.InboundType.WAREHOUSE_TRANSFER.code,
        warehouseId: entity.toWarehouseId,
        warehouseTransferId: entity.id,
        lstDetail: lstDetail,
      }

      await this.inboundService.createDataApproved(inboundCreateDto, req, trans)
      // #endregion

      // #endregion

      // Xoá relation để update
      delete entity.__warehouseTransferDetails__

      await repo.update(entity.id, entity)
      const member: any = await omsApiHelper.getMemberByListId(req, [data.approveBy])

      if (!isPrivate) {
        const description = `Shipper ${member[0]?.username} thay đổi trạng thái phiếu thành ${enumData.WarehouseTransferStatus.CONFIRMED_TRANSFER.name}`
        await this.createHistory({ warehouseTransferId: entity.id, description }, trans)
      }

      return { message: 'Shipper xác nhận đã chuyển thành công!' }
    })
  }

  /** Hàm tạo mới phiếu chuyển kho */
  async createDataByExcel(data: WarehouseTransferCreateByExcelDto[], createBy?: any, req?: IRequest) {
    const lstData = data
    if (lstData.length < 1) throw new Error('Vui lòng điền ít nhất 1 dòng thông tin chuyển kho!')

    for (let dt of lstData) {
      dt.fromWarehouseCode = dt.fromWarehouseCode.toString().trim()
      dt.toWarehouseCode = dt.toWarehouseCode.toString().trim()
      dt.productCode = dt.productCode.toString().trim()
    }

    let dicWarehouse: any = {}
    let dicWarehouseProduct: any = {}
    let dicWarehouseProductDetail: any = {}
    {
      const lstFromWarehouseCode = lstData.mapAndDistinct((it) => it.fromWarehouseCode)
      const lstToWarehouseCode = lstData.mapAndDistinct((it) => it.toWarehouseCode)
      const lstWarehouse: any[] = await this.warehouseRepo.find({
        where: { code: In([...lstFromWarehouseCode, ...lstToWarehouseCode]), isDeleted: false },
        relations: { products: { details: true } },
      })
      if (lstWarehouse.length == 0) throw new Error(`Không tìm thấy danh sách kho theo mã kho trong file excel!`)
      dicWarehouse = coreHelper.arrayToObject(lstWarehouse, 'code')

      for (let w of lstFromWarehouseCode) if (!dicWarehouse[w]) throw new Error(`Mã kho đi [ ${w} ] không tồn tại hoặc đã bị ngưng hoạt động!`)
      for (let w of lstToWarehouseCode) if (!dicWarehouse[w]) throw new Error(`Mã kho đến [ ${w} ] không tồn tại hoặc đã bị ngưng hoạt động!`)

      const lstFromWarehouse: any[] = await this.warehouseRepo.find({
        where: { code: In(lstFromWarehouseCode), isDeleted: false },
        relations: { products: { details: true } },
      })
      for (let w of lstFromWarehouse) {
        dicWarehouse[w.code] = w
        for (let p of w.__products__) {
          dicWarehouseProduct[w.code + p.productCode] = p
          for (let detail of p.__details__)
            dicWarehouseProductDetail[w.code + p.productCode + +moment(new Date(detail.expiryDate)).format('DDMMYYYY')] = p
        }
      }
    }

    let dicProduct: any = {}
    const dicProductDetail: any = {}
    {
      const lstProductCode = lstData.mapAndDistinct((it) => it.productCode)
      const lstProduct: any[] = await this.productRepo.find({
        where: { code: In(lstProductCode) },
        select: { id: true, code: true, name: true, quantity: true, quantityLock: true },
        relations: { details: true },
      })
      if (lstProduct.length == 0) throw new Error(`Không tìm thấy danh sách sản phẩm theo mã sản phẩm trong file excel!`)
      dicProduct = coreHelper.arrayToObject(lstProduct, 'code')

      for (let pc of lstProductCode) if (!dicProduct[pc]) throw new Error(`Mã sản phẩm [ ${pc} ] không tồn tại hoặc đã bị ngưng hoạt động!`)

      // Tạo dicProductDetail của mỗi product
      for (let p of lstProduct) for (let pd of p.__details__) dicProductDetail[p.code + moment(new Date(pd.expiryDate)).format('DDMMYYYY')] = pd
    }

    for (let obd of lstData) {
      let productDetail = dicProductDetail[obd.productCode + moment(new Date(obd.expiryDate)).format('DDMMYYYY')]
      if (!productDetail) throw new Error(`Không tìm thấy sản phẩm thực [ ${obd.productCode} ]`)

      const product = dicProduct[obd.productCode]
      if (!product) throw new Error(`Không tìm thấy sản phẩm [ ${obd.productCode} ]`)

      const warehouseProduct = dicWarehouseProduct[obd.fromWarehouseCode + obd.productCode]
      if (!warehouseProduct) throw new Error(`Không tìm thấy sản phẩm [ ${obd.productCode} ] trong kho vật lý [ ${obd.fromWarehouseCode} ]`)

      const warehouseProductDetail =
        dicWarehouseProductDetail[obd.fromWarehouseCode + obd.productCode + +moment(new Date(obd.expiryDate)).format('DDMMYYYY')]
      if (!warehouseProductDetail)
        throw new Error(
          `Không tìm thấy chi tiết sản phẩm [ ${obd.productCode} ] có hạn sử dụng [ ${moment(obd.expiryDate).format(
            'DD/MM/YYYY',
          )} ] trong kho vật lý [ ${obd.fromWarehouseCode} ]`,
        )
    }

    // #endregion

    let inputData: any[] = []
    // Loại bỏ khoảng trắng
    // Gộp các dòng chung mã code với nhau
    for (let dt of lstData) {
      let findCode = inputData.find((i) => i.code == dt.code)
      if (findCode) {
        findCode.lstTransferWarehouseDetail.push({
          productCode: dt.productCode,
          expiryDate: dt.expiryDate,
          quantity: dt.quantity,
        })
        findCode.description = dt?.description ?? findCode?.description ?? null
      } else {
        findCode = {}
        findCode.code = dt.code
        findCode.fromWarehouseCode = dt.fromWarehouseCode
        findCode.toWarehouseCode = dt.toWarehouseCode
        findCode.lstTransferWarehouseDetail = [
          {
            productCode: dt.productCode,
            expiryDate: dt.expiryDate,
            quantity: dt.quantity,
          },
        ]
        findCode.description = dt?.description ?? null
        inputData.push(findCode)
      }
    }

    // Sinh code theo quy tắc ddmmyyy_xxxx (xxxx tăng dần) VD: 01102023_0001
    const curDate = moment(new Date()).format('DDMMYYYY')

    let genCode: string
    // Đếm số lượng phiếu chuyển kho trong ngày
    let count = await this.repo.count({ where: { code: Like(`CK_${curDate}_%`) }, select: { id: true } })
    if (!count) count = 0
    return await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(WarehouseTransferEntity)
      const wtDetailRepo = trans.getRepository(WarehouseTransferDetailEntity)

      for (let data of inputData) {
        genCode = this.genCode(count, 'CK')

        const lstTask: WarehouseTransferDetailEntity[] = []
        const warehouseTransfer = new WarehouseTransferEntity()
        warehouseTransfer.id = uuidv4()
        warehouseTransfer.code = genCode
        warehouseTransfer.fromWarehouseId = dicWarehouse[data.fromWarehouseCode].id
        warehouseTransfer.toWarehouseId = dicWarehouse[data.toWarehouseCode].id
        warehouseTransfer.createdAt = new Date()
        warehouseTransfer.createdBy = createBy
        warehouseTransfer.description = data?.description ?? null
        await repo.insert(warehouseTransfer)
        for (let detail of data.lstTransferWarehouseDetail) {
          const product = dicProduct[detail.productCode]
          const productDetail = dicProductDetail[detail.productCode + moment(new Date(detail.expiryDate)).format('DDMMYYYY')]
          const wtDetail = new WarehouseTransferDetailEntity()
          const warehouseProductDetail =
            dicWarehouseProductDetail[data.fromWarehouseCode + detail.productCode + +moment(new Date(detail.expiryDate)).format('DDMMYYYY')]
          wtDetail.warehouseTransferId = warehouseTransfer.id
          wtDetail.productId = product.id
          wtDetail.productDetailId = productDetail.id
          wtDetail.expiryDate = new Date(detail.expiryDate)
          wtDetail.manufactureDate = productDetail?.manufactureDate ? new Date(productDetail.manufactureDate) : null
          wtDetail.lotNumber = productDetail?.lotNumber ?? null
          wtDetail.inventory = warehouseProductDetail?.quantity ?? 0
          wtDetail.quantity = detail.quantity
          wtDetail.createdBy = createBy
          lstTask.push(wtDetail)
        }

        const member: any = await omsApiHelper.getMemberByListId(req, [createBy])

        await wtDetailRepo.insert(lstTask)
        let description: string = `Nhân viên ${member[0]?.username} tạo mới phiếu chuyển kho`

        await this.createHistory({ warehouseTransferId: warehouseTransfer.id, description }, trans)

        count++
      }

      return { message: `Tạo mới DS phiếu chuyển kho bằng excel thành công!` }
    })
  }
}
