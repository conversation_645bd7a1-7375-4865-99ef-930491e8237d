import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmExModule } from "../../typeorm";
import { PurchaseOrderModule } from "../core/purchaseOrder/purchaseOrder.module";
import { PurchaseOrderItemRepository, PurchaseOrderRepository } from "../../repositories/core";
import { IntegrationController } from "./controller/integration.controller";
import { IntegrationService } from "./services/integration.service";

@Module({
  imports: [TypeOrmExModule.forCustomRepository([
    PurchaseOrderRepository,
    PurchaseOrderItemRepository
  ]),
    PurchaseOrderModule
  ],
  controllers: [IntegrationController],
  providers: [IntegrationService],
  exports: []
})
export class IntegrationModule { }