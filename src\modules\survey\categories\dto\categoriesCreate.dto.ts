import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'

export class CategoriesCreateDto {
  @ApiProperty({ description: 'Tên' })
  @IsNotEmpty()
  @IsString()
  name: string

  @ApiProperty({ description: '<PERSON><PERSON> chú' })
  description: string

  @ApiProperty({ description: 'Nhóm người khảo sát' })
  type: string

  @ApiProperty({ description: 'Là đuôi' })
  isTail: boolean
}
