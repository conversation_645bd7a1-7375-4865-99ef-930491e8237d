import { Enti<PERSON>, Column, <PERSON>T<PERSON><PERSON><PERSON>, Join<PERSON><PERSON>um<PERSON> } from 'typeorm'
import { BaseEntity } from '../core/base.entity'
import { CheckInventoryDetailEntity } from '.'

/** Item trong kiểm kho */
@Entity('check_inventory_detail_by_employee')
export class CheckInventoryDetailByEmployeeEntity extends BaseEntity {
  /** Sản phẩm thực */
  @Column({ type: 'varchar', nullable: false })
  checkInventoryDetailId: string
  @ManyToOne(() => CheckInventoryDetailEntity, (p) => p.checkInventoryDetailByEmployees)
  @JoinColumn({ name: 'checkInventoryDetailId', referencedColumnName: 'id' })
  checkInventoryDetail: Promise<CheckInventoryDetailEntity>

  /** Id nhân viên */
  @Column({ type: 'varchar', length: 36, nullable: false })
  employeeId: string

  @Column({ type: 'varchar', length: 50, nullable: false })
  employeeCode: string

  @Column({ type: 'varchar', length: 100, nullable: false })
  employeeName: string

  /** <PERSON><PERSON> lượng tồn trên hệ thống */
  @Column({ nullable: true, default: 0 })
  inventory: number

  /** Số lượng tồn thực tế */
  @Column({ nullable: true, default: 0 })
  quantity: number

  /** Chênh lệch */
  @Column({ nullable: true, default: 0 })
  quantityDiff: number
}
