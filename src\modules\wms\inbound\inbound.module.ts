import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import { InboundService } from './inbound.service'
import { InboundController } from './inbound.controller'
import {
  InboundDetailRepository,
  InboundHistoryRepository,
  InboundRepository,
  UserRepository,
  ItemRepository,
  WarehouseRepository,
  ItemPriceRepository,
  PurchaseOrderRepository,
  PurchaseOrderItemRepository,
  DeliveryNoteLastMileProductRepository,
  DeliveryNoteChildDetailRepository,
  PurchaseOrderSaleOrderRepository,
  SupplierRepository,
  DeliveryNoteRepository,
  DeliveryNoteChildRepository,
  UnitRepository,
} from '../../../repositories'
import { WarehouseModule } from '../warehouse/warehouse.module'
import { InboundPublicController } from './inboundPublic.controller'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      InboundRepository,
      WarehouseRepository,
      InboundDetailRepository,
      InboundHistoryRepository,
      ItemRepository,
      UserRepository,
      ItemPriceRepository,
      PurchaseOrderRepository,
      PurchaseOrderItemRepository,
      PurchaseOrderSaleOrderRepository,
      DeliveryNoteLastMileProductRepository,
      DeliveryNoteChildDetailRepository,
      SupplierRepository,
      DeliveryNoteRepository,
      DeliveryNoteChildRepository,
      UnitRepository,
    ]),
    WarehouseModule,
  ],
  controllers: [InboundController, InboundPublicController],
  providers: [InboundService],
  exports: [InboundService],
})
export class InboundModule { }
