import { Injectable } from '@nestjs/common'
import { In } from 'typeorm'
import { CREATE_SUCCESS, UPDATE_ACTIVE_SUCCESS } from '../../../constants'
import { MediaRepository } from '../../../repositories'
import { FilterOneDto, UserDto } from '../../../dto'
import { MediaCreateDto, MediaFilterDto } from './dto'

@Injectable()
export class MediaService {
  constructor(private readonly repo: MediaRepository) {}

  /** <PERSON>h sách hình ảnh tài xế  */
  async find(data: MediaFilterDto, user: UserDto) {
    const whereCon: any = { isDeleted: false }
    if (data.productId) whereCon.productId = data.productId
    if (data.type) whereCon.type = data.type
    if (data.lstProductId?.length > 0) whereCon.productId = In(data.lstProductId)
    return await this.repo.find({ where: whereCon })
  }

  /** Thêm mới hình ảnh sản phẩm  */
  public async createData(user: UserDto, data: MediaCreateDto[]) {
    for (let item of data) {
      if (!item.url || !item.name) throw new Error(`Vui lòng upload ảnh`)
      const newEntity = this.repo.create(item)
      newEntity.createdAt = new Date()
      newEntity.createdBy = user.id
      await this.repo.insert(newEntity)
    }
    return { message: CREATE_SUCCESS }
  }

  /** Cập nhật xóa toàn bộ ảnh của sản phẩm  */
  public async updateDelete(user: UserDto, data: FilterOneDto) {
    //await this.repo.delete({ productId: data.id, isDeleted: false })
    return { message: UPDATE_ACTIVE_SUCCESS }
  }
}
