import { WarehouseTransferEntity } from '.'
import { BaseEntity } from '../core/base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'

/** Sản phẩm trong phiếu chuyển kho */
@Entity('warehouse_transfer_detail')
export class WarehouseTransferDetailEntity extends BaseEntity {
  /** Phiếu chuyển kho */
  @Column({ type: 'varchar', nullable: false })
  warehouseTransferId: string
  @ManyToOne(() => WarehouseTransferEntity, (p) => p.warehouseTransferDetails)
  @JoinColumn({ name: 'warehouseTransferId', referencedColumnName: 'id' })
  warehouseTransfer: Promise<WarehouseTransferEntity>

  /** Sản phẩm */
  @Column({ type: 'varchar', length: 36, nullable: false })
  productId: string

  /** Mã sp */
  @Column({ type: 'varchar', length: 50, nullable: true })
  productCode: string

  /** Tên sp */
  @Column({ type: 'varchar', length: 250, nullable: true })
  productName: string

  /** Sản phẩm thực kiểm kho */
  @Column({ type: 'varchar', length: 36, nullable: true })
  productDetailId: string

  /** Đơn vị tính (Có số lượng đơn vị cơ sở) */
  @Column({ type: 'varchar', length: 36, nullable: true })
  unitId: string

  /** Mã dvt */
  @Column({ type: 'varchar', length: 50, nullable: true })
  unitCode: string

  /** Tên dvt */
  @Column({ type: 'varchar', length: 250, nullable: true })
  unitName: string

  /** Hạn sử dụng */
  @Column({ type: 'timestamptz', nullable: true })
  expiryDate: Date

  /** Ngày sản xuất (fill theo hạn sử dụng) */
  @Column({ type: 'timestamptz', nullable: true })
  manufactureDate: Date

  /**
   * Autofill theo hạn sử dụng
   * Nếu cùng 1 HSD nhưng có nhiều số lô → cho phép chọn số lô thuộc hạn sử dụng đó
   */
  @Column({ type: 'varchar', length: 36, nullable: true })
  lotNumber: string

  /** Tồn kho vật lý */
  @Column({ type: 'int', nullable: true, default: 0 })
  inventory: number

  /** Số lượng xuất */
  @Column({ type: 'int', nullable: true, default: 0 })
  quantity: number

  /** Số lượng thực xuất */
  @Column({ type: 'int', nullable: true, default: 0 })
  realQuantity: number
}
