import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import { UserRepository } from '../../../repositories/core/user.repository'
import { StoreController } from './store.controller'
import { StoreService } from './store.service'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([UserRepository])],
  controllers: [StoreController],
  providers: [StoreService],
  exports: [StoreService],
})
export class StoreModule {}
