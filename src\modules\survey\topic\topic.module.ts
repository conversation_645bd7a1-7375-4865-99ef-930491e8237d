import { Module } from '@nestjs/common'
import {
  CategoriesRepository,
  QuestionRepository,
  SurveyHistoryRepository,
  SurveyQuestionListDetailRepository,
  SurveyQuestionRepository,
  SurveyRepository,
  TopicRepository,
  UserSurveyRepository,
} from '../../../repositories/survey'
import { TypeOrmExModule } from '../../../typeorm'
import { SurveyModule } from '../survey/survey.module'
import { TopicController } from './topic.controller'
import { TopicService } from './topic.service'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      TopicRepository,
      CategoriesRepository,
      SurveyRepository,
      SurveyQuestionRepository,
      QuestionRepository,
      SurveyQuestionListDetailRepository,
      SurveyHistoryRepository,
      UserSurveyRepository,
    ]),
    SurveyModule,
  ],
  controllers: [TopicController],
  providers: [TopicService],
  exports: [TopicService],
})
export class TopicModule {}
