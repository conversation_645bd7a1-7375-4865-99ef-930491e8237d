import { MigrationInterface, QueryRunner } from "typeorm";

export class updateEntityPoSo1747713764398 implements MigrationInterface {
    name = 'updateEntityPoSo1747713764398'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_c0118074257e9d3aba08c314f7"`);
        await queryRunner.query(`ALTER TABLE "purchase_order_so" ADD "productId" uuid`);
        await queryRunner.query(`ALTER TABLE "purchase_order_so" ADD "quantity" integer`);
        await queryRunner.query(`CREATE INDEX "IDX_c0118074257e9d3aba08c314f7" ON "purchase_order_so" ("poId", "soId") `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_c0118074257e9d3aba08c314f7"`);
        await queryRunner.query(`ALTER TABLE "purchase_order_so" DROP COLUMN "quantity"`);
        await queryRunner.query(`ALTER TABLE "purchase_order_so" DROP COLUMN "productId"`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_c0118074257e9d3aba08c314f7" ON "purchase_order_so" ("poId", "soId") `);
    }

}
