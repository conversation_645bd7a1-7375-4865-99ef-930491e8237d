import { Injectable } from '@nestjs/common'
import { In, Like, Not } from 'typeorm'
import { Request as IRequest } from 'express'
import { ItemPriceRepository, ItemRepository } from '../../../repositories'
import { FilterIdDto, PaginationDto, UserDto } from '../../../dto'
import { CREATE_SUCCESS, enumData, ERROR_NOT_FOUND_DATA, IMPORT_SUCCESS, UPDATE_ACTIVE_SUCCESS, UPDATE_SUCCESS } from '../../../constants'
import { ItemEntity, ItemPriceEntity } from '../../../entities'
import { ItemPriceCreateDto, ItemPriceImportDto, ItemPriceUpdateDto, ItemPriceUpdatePriceDto } from './dto'
import { coreHelper } from '../../../helpers'
@Injectable()
export class ItemPriceService {
  constructor(
    private readonly repo: ItemPriceRepository,
    private readonly itemRepo: ItemRepository, // private inboundDetailRepo: InboundDetailRepository,
  ) {}

  /** Lấy ds  */
  public async find(data: any) {
    const whereCon: any = { isDeleted: false }
    if (data.name) whereCon.name = Like(`%${data.name}%`)
    if (data.code) whereCon.code = Like(`%${data.code}%`)
    if (data.lstId?.length > 0) whereCon.id = In(data.lstId)
    if (data.lstItemId?.length > 0) whereCon.itemId = In(data.lstItemId)
    return await this.repo.find({ where: whereCon })
  }

  /** Tạo mới */
  public async createData(user: UserDto, data: ItemPriceCreateDto, req: IRequest): Promise<any> {
    const [foundItem, foundBrand] = await Promise.all([
      this.itemRepo.findOne({ where: { id: data.itemId, isDeleted: false } }),
      // await authApiHelper.findOneBrand(req, { id: data.brandId }),
      this.repo.findOne({ where: { itemId: data.itemId, isDeleted: false, isFinal: true } }),
    ])
    if (!foundItem) throw new Error('Không tìm thấy sản phẩm')
    // if (!foundBrand) throw new Error('Không tìm thấy thương hiệu')
    // if (isTakenItem) throw new Error('Sản phẩm đã có thiết lập giá')
    let newPrice = this.repo.create({ ...data })
    newPrice = await this.repo.save(newPrice)

    return { message: CREATE_SUCCESS }
  }

  /** Cập nhật */
  public async updateData(user: UserDto, data: ItemPriceUpdateDto, req: IRequest): Promise<any> {
    const [foundItem, foundBrand] = await Promise.all([
      this.itemRepo.findOne({ where: { id: data.itemId, isDeleted: false } }),
      // await authApiHelper.findOneBrand(req, { id: data.brandId }),
      this.repo.findOne({ where: { id: data.id, isDeleted: false, isFinal: true } }),
    ])
    if (!foundItem) throw new Error('Không tìm thấy sản phẩm')
    // if (!foundBrand) throw new Error('Không tìm thấy thương hiệu')
    // if (!foundPrice) throw new Error('Không tìm thấy thiết lập giá')
    // if (foundPrice.itemId !== data.itemId) {
    //   const isTakenItem = await this.repo.findOne({ where: { itemId: data.itemId, isDeleted: false, isFinal: true } })
    //   if (isTakenItem) throw new Error('Sản phẩm đã có thiết lập giá')
    // }
    // if (+foundPrice.priceCapital === +data.priceCapital && +foundPrice.priceInput === +data.priceInput && +foundPrice.priceSell === +data.priceSell) {
    //   foundPrice.itemId = data.itemId
    //   foundPrice.brandId = data.brandId
    //   foundPrice.priceCapital = data.priceCapital
    //   foundPrice.priceInput = data.priceInput
    //   foundPrice.priceSell = data.priceSell
    //   foundPrice.description = data.description
    //   foundPrice.updatedAt = new Date()
    //   foundPrice.updatedBy = user.id
    //   await this.repo.save(foundPrice)
    //   const type = enumData.Type.ChinhSua.code
    //   const functionType = enumData.ActionLog.Setting_Item_Price.code
    //   await authApiHelper.createActionLog(req, {
    //     type,
    //     functionType,
    //     code: `Cập nhật thiết lập giá và số tiền là : ${foundPrice.priceSell}`,
    //   })
    //   return { message: CREATE_SUCCESS }
    // }
    const priceId = data.id
    delete data.id
    let newPriceEntity = this.repo.create({ ...data, createdAt: new Date(), createdBy: user.id })
    delete newPriceEntity.id
    delete newPriceEntity.updatedAt
    delete newPriceEntity.updatedBy
    const [newPrice, _oldPrice] = await Promise.all([
      this.repo.save(newPriceEntity),
      this.repo.update({ id: priceId }, { updatedAt: new Date(), updatedBy: user.id, isFinal: false }),
    ])

    return { message: CREATE_SUCCESS, updatedEntity: newPrice }
  }

  public async pagination(data: PaginationDto, req: IRequest) {
    const whereCon: any = {}
    if (data.where.itemId) whereCon.itemId = data.where.itemId
    if (data.where.code) whereCon.item = { code: Like(`%${data.where.code}%`) }
    if (data.where.name) whereCon.item = { ...whereCon.item, name: Like(`%${data.where.name}%`) }
    if (data.where.brandId) whereCon.item = { ...whereCon.item, brandId: data.where.brandId }

    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    if (data.where.isFinal === true || data.where.isFinal === false) whereCon.isFinal = data.where.isFinal

    const res: any = await this.repo.findAndCount({
      where: whereCon,
      relations: { item: true },
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC', priceSell: 'DESC' },
    })

    if (res[0].length == 0) return [[], 0]

    for (let item of res[0]) {
      item.itemCode = item.__item__?.code
      item.itemName = item.__item__?.name
      delete item.__item__
    }
    return res
  }

  public async updateActive(id: string, user: UserDto, req: IRequest) {
    const entity = await this.repo.findOne({ where: { id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    entity.isDeleted = !entity.isDeleted
    entity.createdBy = user.id
    entity.updatedAt = new Date()
    await this.repo.save(entity)

    return { message: UPDATE_ACTIVE_SUCCESS, updatedEntity: entity }
  }

  public async updateListPrice(user: UserDto, data: ItemPriceUpdatePriceDto[], req: IRequest) {
    if (!data || data.length === 0) throw new Error('Hãy chọn một dòng data')
    const lstId = data.map((item) => item.id)
    const mapItemPrice: Map<string, any> = new Map(data.map((item) => [item.id, item]))
    const lstPrice = await this.repo.find({ where: { itemId: In(lstId), isDeleted: false, isFinal: true } })

    const lstTask = []
    const lstIfCreated: any = {}
    if (lstPrice && lstPrice.length === 0) {
      const dictItem: any = {}
      {
        const lstId = coreHelper.selectDistinct(data, 'id')
        const lstPrice: any = await this.itemRepo.find({
          where: { id: In(lstId), isDeleted: false },
          order: { createdAt: 'DESC' },
        })
        for (const item of lstPrice) dictItem[item.id] = item
      }

      await this.repo.manager.transaction(async (trans) => {
        const repo = trans.getRepository(ItemPriceEntity)
        for (let item of data) {
          if (!lstIfCreated[item.id]) {
            lstIfCreated[item.id] = item.id

            const newPrice = new ItemPriceEntity()
            newPrice.itemId = item.id
            newPrice.brandId = dictItem[item.id]?.brandId
            newPrice.priceSell = item?.priceSell
            newPrice.priceOriginal = item?.priceOriginal
            newPrice.createdAt = new Date()
            newPrice.createdBy = user.id
            lstTask.push(repo.insert(newPrice))
          }
        }
      })
    } else {
      await this.repo.manager.transaction(async (trans) => {
        const repo = trans.getRepository(ItemPriceEntity)
        await repo.update({ itemId: In(lstId) }, { isFinal: false })
        for (let item of lstPrice) {
          if (!lstIfCreated[item.id]) {
            lstIfCreated[item.id] = item.id
            const newPrice = new ItemPriceEntity()
            newPrice.itemId = item.itemId
            newPrice.brandId = item.brandId
            newPrice.priceCapital = item.priceCapital
            newPrice.priceInput = item?.priceInput
            newPrice.priceOriginal = mapItemPrice.get(item.itemId)?.priceOriginal
            newPrice.priceSell = mapItemPrice.get(item.itemId)?.priceSell
            newPrice.description = item.description
            newPrice.createdAt = new Date()
            newPrice.createdBy = user.id

            lstTask.push(repo.insert(newPrice))
          }
          // break
        }
      })
      await Promise.all(lstTask)
    }

    return { message: UPDATE_SUCCESS }
  }

  public async findForEdit(id: string, req: IRequest) {
    const [foundPrice, lstPrice] = await Promise.all([
      this.repo.findOne({ where: { id } }),
      this.repo.find({ where: { isFinal: true, isDeleted: false } }),
    ])
    if (!foundPrice) throw new Error(ERROR_NOT_FOUND_DATA)
    const lstItemId = []
    for (let price of lstPrice) {
      if (price.itemId !== foundPrice.itemId) lstItemId.push(price.itemId)
    }

    foundPrice.priceCapital = +foundPrice.priceCapital
    foundPrice.priceInput = +foundPrice.priceInput
    foundPrice.priceSell = +foundPrice.priceSell
    return { data: foundPrice }
  }

  public async findForAdd(req: IRequest) {
    const lstPrice = await this.repo.find({ where: { isFinal: true, isDeleted: false } })
    const lstItemId = lstPrice.map((price) => price.itemId)

    return {}
  }

  public async findItem(id: string) {
    const [foundItem] = await Promise.all([
      this.itemRepo.findOne({ where: { id } }),
      // this.inboundDetailRepo.findOne({ where: { itemId: id, isDeleted: false }, order: { createdAt: 'DESC' }, relations: { inbound: true } }),
    ])
    if (!foundItem) return foundItem

    // // let inbound = await inboundDetail?.inbound
    // let priceInput = 0
    // if (inbound?.status !== enumData.InboundStatus.Approved.code) {
    //   priceInput = 0
    // } else {
    //   // priceInput = inboundDetail?.buyPrice
    // }
    return { ...foundItem }
  }

  /** Lấy thiết lập giá cho sp mới nhất */
  public async findItemPrice(data: FilterIdDto) {
    const itemPriceObj = await this.repo.findOne({
      where: { itemId: data.itemId, isDeleted: false, isFinal: true },
      order: { createdAt: 'DESC' },
    })
    return itemPriceObj
  }

  async createDataExcel(user: UserDto, data: ItemPriceImportDto[], req: IRequest) {
    await this.repo.manager.transaction(async (trans) => {
      const itemPriceRepo = trans.getRepository(ItemPriceEntity)
      const itemRepo = trans.getRepository(ItemEntity)
      const dicCode: any = {}
      {
        const lstCode = data.map((item) => item.code).filter((value, index, self) => self.indexOf(value) === index)
        const items: any[] = await itemRepo.find({
          where: { code: In(lstCode) },
          select: { id: true, code: true },
        })
        items.forEach((c) => (dicCode[c.code] = c))
      }

      const lstTask: any[] = []
      for (const [idx, item] of data.entries()) {
        if (!dicCode[item.code]) throw new Error(`[ Dòng ${idx + 1} - Mã sản phẩm [${item.code}] không tồn tại ]`)

        const checkItemPrice = await itemPriceRepo.findOne({ where: { isDeleted: false, itemId: dicCode[item.code].id, isFinal: true } })

        if (checkItemPrice) {
          checkItemPrice.priceSell = item.priceSell
          checkItemPrice.priceOriginal = item.priceOriginal
          checkItemPrice.updatedBy = user.id
          checkItemPrice.updatedAt = new Date()
          // console.log(checkItemPrice, item)
          lstTask.push(itemPriceRepo.save(checkItemPrice))
        } else {
          const newItemPrice = new ItemPriceEntity()
          newItemPrice.itemId = dicCode[item.code].id
          newItemPrice.brandId = dicCode[item.code].brandId
          newItemPrice.priceSell = item.priceSell
          newItemPrice.priceOriginal = item.priceOriginal
          newItemPrice.createdBy = user.id
          newItemPrice.createdAt = new Date()
          newItemPrice.isFinal = true

          lstTask.push(itemPriceRepo.insert(newItemPrice))
        }

        // const type = enumData.Type.ThemMoi.code
        // const functionType = enumData.ActionLog.Setting_Item_Price.code
        // await authApiHelper.createActionLog(req, { type, functionType, code: item.code })
      }

      await Promise.all(lstTask)
    })

    return { message: IMPORT_SUCCESS }
  }
}
