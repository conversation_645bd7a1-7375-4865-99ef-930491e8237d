import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from '../core/base.entity'
import { QuestionEntity } from './s_question.entity'

/** <PERSON> tiết câu hỏi loại danh sách */

@Entity('s_question_list_detail')
export class QuestionListDetailEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    nullable: false,
  })
  value: number

  @Column({
    nullable: true,
    default: 0,
  })
  sort: number

  /** Danh mục */
  @Column({
    type: 'varchar',
    nullable: false,
  })
  questionId: string
  @ManyToOne(() => QuestionEntity, (p) => p.questionlistDetails)
  @JoinColumn({ name: 'questionId', referencedColumnName: 'id' })
  question: Promise<QuestionEntity>
}
