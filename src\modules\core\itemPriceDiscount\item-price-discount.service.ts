import { Injectable } from '@nestjs/common'
import { CreateItemPriceDiscountDto, ListCreateItemPriceDiscountDto } from './dto/create-item-price-discount.dto'
import { ListUpdateItemPriceDiscountDto, UpdateItemPriceDiscountDto } from './dto/update-item-price-discount.dto'
import { ItemPriceDiscountRepository } from '../../../repositories/core/itemPriceDiscount.repository'
import { In } from 'typeorm'

@Injectable()
export class ItemPriceDiscountService {
  constructor(private readonly itemPriceDiscountRepository: ItemPriceDiscountRepository) {}

  async createDiscountPriceList(body: ListCreateItemPriceDiscountDto) {
    const { items } = body
    //check quantity bị trùng, số lượng đạt được giảm giá phải lớn hơn 0
    const itemLstIds = items.map((item) => item.itemId)

    const lstItemPriceDiscount = await this.itemPriceDiscountRepository.find({
      where: { itemId: In(itemLstIds), isDeleted: false },
    })
    items.reduce((acc, item) => {
      if (item.targetQuantity <= 0) {
        throw new Error(`Số lượng đạt được giảm giá phải lớn hơn 0 ${item.itemId}`)
      }
      if (
        acc.find((i) => i.itemId === item.itemId && i.targetQuantity === item.targetQuantity) ||
        lstItemPriceDiscount.find((i) => i.itemId === item.itemId && i.targetQuantity == item.targetQuantity)
      ) {
        throw new Error(`Số lượng đạt được giảm giá bị trùng ${item.itemId} số lượng ${item.targetQuantity}`)
      }
      acc.push(item)
      return acc
    }, [])
    //lưu 1 danh sách items
    const result = await this.itemPriceDiscountRepository.save(items)

    return result
  }

  findByItemId(itemId: string) {
    const result = this.itemPriceDiscountRepository.find({ where: { itemId, isDeleted: false }, order: { targetQuantity: 'ASC' } })
    return result
  }

  deleteById(id: string) {
    return this.itemPriceDiscountRepository.update({ id }, { isDeleted: true })
  }

  findOne(id: number) {
    return `This action returns a #${id} itemPriceDiscount`
  }

  async updateDiscountPriceList(updateItemPriceDiscountDto: ListUpdateItemPriceDiscountDto) {
    const { items } = updateItemPriceDiscountDto
    //check quantity bị trùng, số lượng đạt được giảm giá phải lớn hơn 0
    const itemLstIds = items.map((item) => item.itemId)

    const lstItemPriceDiscount = await this.itemPriceDiscountRepository.find({
      where: { itemId: In(itemLstIds), isDeleted: false },
    })

    //check xem list có được phép update hay không
    items.reduce((acc, item) => {
      if (item.targetQuantity <= 0) {
        throw new Error(`Số lượng đạt được giảm giá phải lớn hơn 0 ${item.itemId}`)
      }
      if (
        acc.find((i) => i.itemId === item.itemId && i.targetQuantity === item.targetQuantity) ||
        lstItemPriceDiscount.find((i) => i.itemId === item.itemId && i.targetQuantity == item.targetQuantity && i.id !== item.id)
      ) {
        throw new Error(`Số lượng đạt được giảm giá bị trùng ${item.itemId} số lượng ${item.targetQuantity}`)
      }
      acc.push(item)
      return acc
    }, [])

    for (let item of updateItemPriceDiscountDto.items) {
      await this.itemPriceDiscountRepository.update(
        { id: item.id, itemId: item.itemId },
        { priceOriginal: item.priceOriginal, targetQuantity: item.targetQuantity },
      )
    }
    return updateItemPriceDiscountDto
  }

  remove(id: number) {
    return `This action removes a #${id} itemPriceDiscount`
  }
}
