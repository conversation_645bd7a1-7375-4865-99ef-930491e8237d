import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import { ItemGroupController } from './itemGroup.controller'
import { ItemGroupService } from './itemGroup.service'
import { ItemCategoryRepository, ItemGroupRepository, MediaRepository } from '../../../repositories'
import { ItemGroupPublicController } from './itemGroupPublic.controller'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([ItemGroupRepository, MediaRepository, ItemCategoryRepository])],
  controllers: [ItemGroupController, ItemGroupPublicController],
  providers: [ItemGroupService],
  exports: [ItemGroupService],
})
export class ItemGroupModule { }
