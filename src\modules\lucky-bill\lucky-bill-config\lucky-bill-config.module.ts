import { Modu<PERSON> } from '@nestjs/common'
import { LuckyBillConfigService } from './lucky-bill-config.service'
import { LuckyBillConfigController } from './lucky-bill-config.controller'
import { TypeOrmExModule } from '../../../typeorm'
import { LuckyBillConfigRepository } from '../../../repositories/lucky-bill/lucky-bill-config.repository'
import { LuckyBillApplyRegionRepository } from '../../../repositories/lucky-bill/lucky-bill-apply-region.repository'
import { LuckyBillApplyRegionModule } from '../lucky-bill-apply-region/lucky-bill-apply-region.module'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([LuckyBillConfigRepository, LuckyBillApplyRegionRepository]), LuckyBillApplyRegionModule],
  controllers: [LuckyBillConfigController],
  providers: [LuckyBillConfigService],
})
export class LuckyBillConfigModule {}
