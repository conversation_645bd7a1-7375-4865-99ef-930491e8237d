import { Module } from '@nestjs/common'
import { DepartmentService } from './department.service'
import { DepartmentController } from './department.controller'
import { EmployeeModule } from '../employee/employee.module'
import { AuthModule } from '../auth/auth.module'
import { TypeOrmExModule } from '../../../typeorm'
import { DepartmentRepository, EmployeeRepository } from '../../../repositories'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([DepartmentRepository, EmployeeRepository]), EmployeeModule, AuthModule],
  controllers: [DepartmentController],
  providers: [DepartmentService],
  exports: [DepartmentService],
})
export class DepartmentModule {}
