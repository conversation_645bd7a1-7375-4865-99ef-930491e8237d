import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { JwtAuthGuard } from '../../common/guards'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { CurrentUser } from '../../common/decorators'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { NewsCategoryService } from './newsCategory.service'
import { NewsCategoryCreateDto, NewsCategoryUpdateDto } from './dto'
@ApiBearerAuth()
@ApiTags('News category')
@UseGuards(JwtAuthGuard)
@Controller('news_category')
export class NewsCategoryController {
  constructor(private readonly service: NewsCategoryService) {}

  @Post('find')
  public async find(@Body() data: any) {
    return await this.service.find(data)
  }

  @Post('pagination')
  public async pagination(@Body() data: PaginationDto) {
    return await this.service.pagination(data)
  }

  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: NewsCategoryCreateDto) {
    return await this.service.createData(user, data)
  }

  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: NewsCategoryUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @Post('update_active')
  public async updateActive(@Body() data: any, @CurrentUser() user: UserDto) {
    return await this.service.updateIsDelete(data.id, user)
  }

  @Post('find_one')
  public async findOne(@Body() data: FilterOneDto) {
    return await this.service.findOne(data)
  }

  @Post('create_data_excel')
  public async createDataExcel(@Body() data: NewsCategoryCreateDto[], @CurrentUser() user: UserDto) {
    return await this.service.createDataExcel(data, user)
  }
}
