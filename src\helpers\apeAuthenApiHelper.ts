import { HttpService } from '@nestjs/axios'

class ApeAuthenApiHelper {
  private url = process.env.APE_AUTHEN_API
  private httpService: HttpService

  headers = {
    'x-api-key': process.env.APE_AUTHEN_API_KEY,
  }
  constructor(httpService: HttpService) {
    this.httpService = httpService
  }
  async register(data: any) {
    return this.httpService.post(`${this.url}/api/public/account/register`, data, { headers: this.headers }).subscribe()
  }
}

export const apeAuthenApiHelper = new ApeAuthenApiHelper(new HttpService())
