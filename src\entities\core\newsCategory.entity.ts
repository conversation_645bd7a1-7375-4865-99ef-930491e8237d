import { Column, Entity, OneToMany } from 'typeorm'
import { enumData } from '../../constants'
import { BaseEntity } from './base.entity'
import { NewsEntity } from './news.entity'

/** <PERSON> tức */
@Entity('news_category')
export class NewsCategoryEntity extends BaseEntity {
  /** Mã danh mục tin tức*/
  @Column({
    type: 'varchar',
    length: 100,
    nullable: false,
  })
  code: string

  /** Tên danh mục tin tức*/
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  type: string

  /** <PERSON>h sách các sản phẩm thuộc loại này */
  @OneToMany(() => NewsEntity, (p) => p.newsCategory)
  news: Promise<NewsEntity[]>
}
