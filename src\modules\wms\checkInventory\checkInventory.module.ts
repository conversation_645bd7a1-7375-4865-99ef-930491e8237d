import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import { CheckInventoryService } from './checkInventory.service'
import { CheckInventoryController } from './checkInventory.controller'
import {
  CheckInventoryRepository,
  InboundRepository,
  OutboundRepository,
  ItemDetailRepository,
  ItemRepository,
  WarehouseProductDetailRepository,
  WarehouseProductRepository,
  WarehouseRepository,
} from '../../../repositories'
import { InboundModule } from '../inbound/inbound.module'
import { OutboundModule } from '../outbound/outbound.module'
import { CheckInventoryPublicController } from './checkInventoryPublic.controller'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      CheckInventoryRepository,
      WarehouseRepository,
      ItemDetailRepository,
      ItemRepository,
      WarehouseProductRepository,
      WarehouseProductDetailRepository,
      InboundRepository,
      OutboundRepository,
    ]),
    InboundModule,
    OutboundModule,
  ],
  controllers: [CheckInventoryController, CheckInventoryPublicController],
  providers: [CheckInventoryService],
  exports: [CheckInventoryService],
})
export class CheckInventoryModule {}
