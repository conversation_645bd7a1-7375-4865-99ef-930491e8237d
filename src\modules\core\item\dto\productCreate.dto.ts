import { ApiProperty } from '@nestjs/swagger'
import { IsBoolean, IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator'
import { NSItem } from '../../../../constants/NSItem'

export class ProductCreateDto {
  @IsNotEmpty()
  @IsString()
  name: string

  @IsNotEmpty()
  @IsString()
  code: string

  @IsOptional()
  brandId: string

  @IsOptional()
  expiryDate: string

  @IsOptional()
  unitId: string

  @IsOptional()
  itemTypeId: string

  @IsOptional()
  itemGroupId: string

  @IsOptional()
  itemCategoryId: string

  @IsOptional()
  isOpenSale: boolean

  @IsOptional()
  osCode: string

  @IsOptional()
  specifications: string

  @IsOptional()
  isCombo: boolean

  @IsOptional()
  barCode: string

  @IsOptional()
  companyName: string

  @IsOptional()
  quantityUnit: number

  @IsOptional()
  packingId: string

  @IsOptional()
  dateFrom: Date

  @IsOptional()
  dateTo: Date

  lstMediaProduct?: MediaDto[]

  lstProductCombo?: ProductComboDto[]

  description: string

  kg: number

  @ApiProperty({ description: 'thể tích' })
  @IsOptional()
  cbm: number

  @ApiProperty({ description: 'dài' })
  @IsOptional()
  length: number

  @ApiProperty({ description: 'rộng' })
  @IsOptional()
  width: number

  @ApiProperty({ description: 'cao' })
  @IsOptional()
  height: number

  @ApiProperty({ description: '%VAT' })
  @IsOptional()
  vat: number

  @ApiProperty({ description: 'Giá bán' })
  @IsOptional()
  sellPrice: number

  @IsOptional()
  supplierId: string

  @IsOptional()
  poUnitId: string

  @IsOptional()
  buyTaxId: string

  @IsOptional()
  sellTaxId: string

  @ApiProperty({ description: 'Trạng thái có thể đặt hàng khi tồn kho bằng 0' })
  @IsOptional()
  canPreOrder?: boolean

  @ApiProperty({ description: 'Cờ sản phẩm có bán theo kỳ' })
  @IsOptional()
  isPeriodSale?: boolean

  @ApiProperty({ description: 'Các nền tảng được phép đặt hàng' })
  @IsOptional()
  orderPlatformType?: string
}

export class MediaDto {
  @ApiProperty({ description: 'Id sản phẩm' })
  @IsString()
  productId?: string

  @ApiProperty({ description: 'Đường dẩn của file' })
  @IsNotEmpty({ message: 'Đường dẩn của file không được để trống' })
  @IsString()
  url: string

  content: string
  table: string

  @ApiProperty({ description: 'Tên file' })
  @IsNotEmpty({ message: 'Tên file không được để trống' })
  @IsString()
  name: string
}

export class ProductComboDto {
  productId?: string

  @ApiProperty({ description: 'Số lượng' })
  quantity: number
}
