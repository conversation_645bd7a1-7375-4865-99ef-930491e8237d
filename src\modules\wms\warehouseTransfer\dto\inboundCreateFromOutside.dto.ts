import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString, IsOptional } from 'class-validator'

export class InboundCreateFromOutsideDto {
  @ApiProperty({ description: 'Id kho' })
  @IsNotEmpty()
  @IsString()
  warehouseId: string

  @ApiProperty({ description: 'Id phiếu chuyển kho' })
  @IsOptional()
  @IsString()
  warehouseTransferId?: string

  @ApiProperty({ description: 'Id phiếu kiểm kho' })
  @IsOptional()
  @IsString()
  checkInventoryId?: string

  @IsNotEmpty()
  type: string

  @IsOptional()
  description?: string

  lstDetail: InboundDetailCreateFromOutsideDto[]
}

export class InboundDetailCreateFromOutsideDto {
  /** Sản phẩm trong kho đi */
  @IsNotEmpty()
  @IsString()
  productId: string

  /** Sản phẩm trong kho đi */
  @IsNotEmpty()
  @IsString()
  productDetailId: string

  /** Sản phẩm trong kho đi */
  @IsNotEmpty()
  @IsString()
  productName: string

  /** Sản phẩm trong kho đi */
  @IsNotEmpty()
  @IsString()
  productCode: string

  /** Đơn vị cơ sở */
  @IsNotEmpty()
  @IsString()
  unitId: string

  /** Hạn sử dụng */
  @IsNotEmpty()
  expiryDate: Date

  /** Ngày sản xuất */
  @IsOptional()
  manufactureDate?: Date

  /** Số lô */
  @IsNotEmpty()
  @IsString()
  lotNumber: string

  /** Số lô */
  @IsOptional()
  @IsString()
  isCombo?: boolean

  /** Tồn kho vật lý */
  @IsNotEmpty()
  inventory: number

  @IsNotEmpty()
  quantity: number

  /** Giá vốn */
  @IsOptional()
  costPrice?: number
}
