import { Injectable, ConflictException, NotFoundException } from '@nestjs/common'
import { In, Like } from 'typeorm'
import {
  CREATE_SUCCESS,
  ERROR_CODE_TAKEN,
  ERROR_NOT_FOUND_DATA,
  IMPORT_SUCCESS,
  UPDATE_ACTIVE_SUCCESS,
  UPDATE_SUCCESS,
  enumData,
} from '../../../constants'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { CityRepository, RegionCityRepository } from '../../../repositories'
import { CityCreateDto, CityUpdateDto } from './dto'

@Injectable()
export class CityService {
  constructor(
    private readonly repo: CityRepository,
    private readonly regionCityRepo: RegionCityRepository

  ) { }

  /** Lấy ds tỉnh thành */
  async find(data: any) {
    const whereCon: any = { isDeleted: false }
    if (data.name) whereCon.name = Like(`%${data.name}%`)
    if (data.code) whereCon.code = Like(`%${data.code}%`)
    if (data.lstId?.length > 0) whereCon.id = In(data.lstId)
    if (data.lstCode?.length > 0) whereCon.code = In(data.lstCode)
    return await this.repo.find({ where: whereCon })
  }

  /** Lấy danh sách tỉnh thành theo regionId */
  async loadCitiesByRegionId(regionId: string) {
    const regionCities = await this.regionCityRepo.find({
      where: { regionId, isDeleted: false },
    });
    const cities = await this.repo.find();
    return regionCities.map(rc => {
      const city = cities.find(c => c.id === rc.cityId);
      return {
        ...rc,
        id: rc.cityId,
        name: city ? city.name : null,
      };
    });
  }

  /** Lấy ds tỉnh thành */
  async loadData() {
    return await this.repo.find({ where: { isDeleted: false }, select: { id: true, code: true, name: true } })
  }

  async createData(user: UserDto, data: CityCreateDto): Promise<any> {
    const checkCodeExist = await this.repo.findOne({ where: { code: data.code } })
    if (checkCodeExist) throw new ConflictException(ERROR_CODE_TAKEN)

    const entity = this.repo.create({ ...data, createdBy: user.id })
    await this.repo.insert(entity)

    return { message: CREATE_SUCCESS }
  }

  async updateData(user: UserDto, data: CityUpdateDto) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    if (data.code != entity.code) {
      const checkCodeExist = await this.repo.findOne({ where: { code: data.code } })
      if (checkCodeExist) throw new ConflictException(ERROR_CODE_TAKEN)
    }

    entity.code = data.code
    entity.name = data.name
    entity.area = data.area
    entity.updatedBy = user.id
    const updatedEntity = await this.repo.update(entity.id, entity)

    // const type = enumData.Type.ChinhSua.code
    // const functionType = enumData.ActionLog.Setting_City.code
    // const res = { type: type, functionType: functionType, code: updatedEntity.code }
    // await this.actionService.createData(res, user)

    return { message: UPDATE_SUCCESS }
  }

  async pagination({ where, skip, take }: PaginationDto) {
    let whereCon: any = {}
    if (where.name) whereCon.name = Like(`%${where.name}%`)
    if (where.area) whereCon.area = Like(`%${where.area}%`)
    if (where.code) whereCon.code = Like(`%${where.code}%`)
    if (where.isDeleted != undefined) whereCon.isDeleted = where.isDeleted
    let res: any = await this.repo.findAndCount({
      where: whereCon,
      order: { createdAt: 'DESC', code: 'ASC' },
      skip,
      take,
    })
    if (res.length == 0) return [[], 0]
    return res
  }

  async updateIsDelete(id: string, user: UserDto) {
    const entity = await this.repo.findOne({ where: { id } })
    if (!entity) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    entity.isDeleted = !entity.isDeleted
    await this.repo.update(entity.id, entity)

    // const type = enumData.Type.ChinhSua.code
    // const functionType = enumData.ActionLog.Setting_City.code
    // await this.actionService.createData({type, functionType, code: entity.code}, user)

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  async createDataExcel(user: UserDto, data: CityCreateDto[]): Promise<any> {
    const existingCodes = new Set((await this.repo.find({ where: { isDeleted: false }, select: ['code'] })).map((c) => c.code))

    const newEntityList = data.filter((item) => !existingCodes.has(item.code)).map((item) => this.repo.create({ ...item, createdBy: user.id }))

    await this.repo.insert(newEntityList)

    // await Promise.all(
    //   newEntityList.map((entity) =>
    //     this.actionService.createData(
    //       {
    //         type: enumData.Type.ThemMoi.code,
    //         functionType: enumData.ActionLog.Setting_City.code,
    //         code: entity.code,
    //       },
    //       user,
    //     ),
    //   ),
    // )

    return { message: IMPORT_SUCCESS }
  }

  async findOne(data: FilterOneDto) {
    const whereCon: any = { id: data.id, isDeleted: false }
    return await this.repo.findOneBy(whereCon)
  }
}
