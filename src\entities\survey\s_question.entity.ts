import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, <PERSON>To<PERSON>ne, OneToMany } from 'typeorm'
import { BaseEntity } from '../core/base.entity'
import { QuestionListDetailEntity } from './s_questionListDetail.entity'
import { TopicEntity } from './s_topic.entity'

/**
 *  Câu hỏi */

@Entity('s_question')
export class QuestionEntity extends BaseEntity {
  @Column({
    nullable: false,
    default: 0,
  })
  sort: number

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  /** <PERSON><PERSON> bắt buộc nhập hay không */
  @Column({
    nullable: false,
    default: false,
  })
  isRequired: boolean

  /** Kiểu dữ liệu: string - number - cal. Nếu cal thì cho phép tạo công thức con*/
  @Column({
    nullable: false,
    default: 'string',
  })
  type: string

  /** <PERSON><PERSON><PERSON> độ */
  @Column({
    nullable: false,
    default: 1,
  })
  level: number

  /** <PERSON><PERSON> tả */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  /** Id của công thức cha */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  parentId: string
  /** Cha */
  @ManyToOne(() => QuestionEntity, (p) => p.childs)
  @JoinColumn({ name: 'parentId', referencedColumnName: 'id' })
  parent: QuestionEntity

  /** Con - 1 công thức sẽ có thể có nhiều con */
  @OneToMany(() => QuestionEntity, (p) => p.parent)
  childs: Promise<QuestionEntity[]>

  /** Thuộc tính của tiêu chí thể hệ Doanh nghiệp sẽ được highlight màu xanh nếu đạt giá trị X khi xếp hạng năng lực */
  @Column({
    nullable: false,
    default: false,
  })
  isHighlight: boolean

  @Column({
    nullable: true,
  })
  hightlightValue: number

  /** Con - 1 công thức sẽ có thể có nhiều detail list */
  @OneToMany(() => QuestionListDetailEntity, (p) => p.question)
  questionlistDetails: Promise<QuestionListDetailEntity[]>

  /** Chủ đề */
  @Column({
    type: 'varchar',
    nullable: false,
  })
  topicId: string
  @ManyToOne(() => TopicEntity, (p) => p.questions)
  @JoinColumn({ name: 'topicId', referencedColumnName: 'id' })
  topic: Promise<TopicEntity>

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  img: string
}
