import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator'

export class OutboundCreateDto {
  @ApiProperty({ description: 'Loại phiếu' })
  @IsNotEmpty()
  @IsString()
  type: string

  @ApiProperty({ description: 'Kho vật lý' })
  @IsNotEmpty()
  @IsString()
  warehouseId: string

  @ApiProperty({ description: 'Mã kho vật lý' })
  warehouseCode?: string

  @ApiProperty({ description: 'Id nhân viên (Để trừ tồn kho của nhân viên)' })
  // @IsString()
  employeeId?: string

  @ApiProperty({ description: 'Id phiếu chuyển kho' })
  @IsOptional()
  @IsString()
  warehouseTransferId?: string

  @ApiProperty({ description: 'Id phiếu kiểm kho' })
  @IsOptional()
  @IsString()
  checkInventoryId?: string

  @ApiProperty({ description: 'Id đơn hàng' })
  @IsOptional()
  @IsString()
  orderId?: string

  @ApiProperty({ description: 'Mã đơn hàng' })
  @IsOptional()
  @IsString()
  orderCode?: string

  @ApiProperty({ description: 'Tên khách hàng của đơn hàng' })
  @IsOptional()
  @IsString()
  customerName?: string

  @ApiProperty({ description: 'Ngày tạo phiếu' })
  @IsNotEmpty()
  @IsString()
  createdAt: Date

  @ApiProperty({ description: 'Ngày soạn hàng' })
  @IsOptional()
  @IsString()
  preparationDate?: Date

  @ApiProperty({ description: 'Người soạn hàng' })
  @IsOptional()
  @IsString()
  preparedBy?: string

  @ApiProperty({ description: 'Ghi chú' })
  @IsOptional()
  @IsString()
  description?: string

  @ApiProperty({ description: 'Số lượng kiện hàng' })
  @IsOptional()
  packageQuantity?: number

  @ApiProperty({ description: 'Danh sách sản phẩm trong kho vật lý' })
  lstOutboundDetail: OutboundDetailCreateDto[]

  @ApiProperty({ description: 'ID người tạo phiếu xuất kho' })
  @IsOptional()
  @IsUUID()
  createBy?: string
}

export class OutboundDetailCreateDto {
  @ApiProperty({ description: 'Sản phẩm' })
  @IsNotEmpty()
  @IsString()
  productId: string

  @ApiProperty({ description: 'Mã sản phẩm' })
  @IsOptional()
  @IsString()
  productCode?: string

  @ApiProperty({ description: 'Hạn sử dụng' })
  @IsOptional()
  expiryDate: Date

  @ApiProperty({ description: 'Ngày sản xuất' })
  @IsOptional()
  manufactureDate: Date

  @ApiProperty({ description: 'Số lô' })
  @IsOptional()
  @IsString()
  lotNumber: string

  @ApiProperty({ description: 'Tồn kho' })
  @IsOptional()
  inventory: number

  @ApiProperty({ description: 'Số lượng xuất' })
  @IsOptional()
  quantity: number

  /** Dành cho tạo mới phiếu xuất từ đơn hàng */
  @ApiProperty({ description: 'Id của chi tiết sản phẩm' })
  @IsOptional()
  @IsString()
  productDetailId?: string
}
