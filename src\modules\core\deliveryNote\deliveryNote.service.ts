import { omsApiHelper } from './../../../helpers/omsApiHelper'
import { Injectable } from '@nestjs/common'
import {
  CityRepository,
  DeliveryNotePackingRefRepository,
  DeliveryNotePackingRepository,
  DeliveryNoteRepository,
  DeliveryNoteTrackingRepository,
  DistrictRepository,
  EmployeeRepository,
  InboundRepository,
  ItemComboRepository,
  ItemDetailRepository,
  ItemPriceRepository,
  ItemRepository,
  PurchaseOrderItemRepository,
  PurchaseOrderRepository,
  PurchaseOrderSaleOrderRepository,
  RegionCityRepository,
  SupplierRepository,
  UserRepository,
  WardRepository,
  WarehouseProductDetailRepository,
  WarehouseProductRepository,
  WarehouseRepository,
} from '../../../repositories'
import {
  CreateDeliveryNoteDto,
  DetailTrackingDto,
  FilterDeliverNoteDto,
  PackingListDto,
  TrackingListSupplierDto,
  UpdateDeliveryNoteDto,
  UpdateFileTrackingDto,
} from './dto/deliveryNote.dto'
import { CREATE_SUCCESS, enumData, NSPo, NSRecurringOrder, NSWarehouse, UPDATE_SUCCESS } from '../../../constants'
import { coreHelper } from '../../../helpers'
import {
  DeliveryNoteChildDetailRepository,
  DeliveryNoteChildRepository,
  DeliveryNoteLastMileProductRepository,
} from '../../../repositories/core/deliveryNoteChild.repository'
import { Request as IRequest } from 'express'
import { Between, In, IsNull, Like, Not, Raw } from 'typeorm'
import { InboundEntity } from '../../../entities'
import { v4 as uuidv4 } from 'uuid'
import { InboundService } from '../../wms/inbound/inbound.service'
import { OutboundService } from '../../wms/outbound/outbound.service'
import { WarehouseService } from '../../wms/warehouse/warehouse.service'
import * as moment from 'moment'

@Injectable()
export class DeliveryNoteService {
  constructor(
    private readonly regionCity: RegionCityRepository,
    private readonly deliveryNoteRepo: DeliveryNoteRepository,
    private readonly supplierRepo: SupplierRepository,
    private readonly deliveryNoteTrackingRepo: DeliveryNoteTrackingRepository,
    private readonly purchaseOrderRepo: PurchaseOrderRepository,
    private readonly purchaseOrderItemRepo: PurchaseOrderItemRepository,
    private readonly purchaseOrderSoRepo: PurchaseOrderSaleOrderRepository,
    private readonly deliveryNoteChildRepo: DeliveryNoteChildRepository,
    private readonly deliveryNoteChildDetailRepo: DeliveryNoteChildDetailRepository,
    private readonly deliveryNoteLastMileProductRepo: DeliveryNoteLastMileProductRepository,
    private readonly warehouseRepo: WarehouseRepository,
    private readonly warehouseProductRepo: WarehouseProductRepository,
    private readonly warehouseProductDetailRepo: WarehouseProductDetailRepository,
    private readonly inboundRepo: InboundRepository,
    private readonly itemRepo: ItemRepository,
    private readonly itemDetailRepo: ItemDetailRepository,
    private readonly itemPriceRepo: ItemPriceRepository,
    private readonly inboundService: InboundService,
    private readonly outboundService: OutboundService,
    private readonly productDetailRepo: ItemDetailRepository,
    private readonly warehouseService: WarehouseService,
    private readonly deliveryNotePackingRepo: DeliveryNotePackingRepository,
    private readonly deliveryNotePackingRefRepo: DeliveryNotePackingRefRepository,
    private readonly userRepository: UserRepository,
    private readonly employeeRepository: EmployeeRepository,
    private readonly cityRepository: CityRepository,
    private readonly wardRepository: WardRepository,
    private readonly districtRepository: DistrictRepository,
    private readonly warehouseRepository: WarehouseRepository,
    private readonly itemComboRepo: ItemComboRepository,
  ) { }

  async createDeliveryNote(data: CreateDeliveryNoteDto) {
    try {
      const { purchaseOrderId, createdBy } = data

      const checkPo = await this.purchaseOrderRepo.findOne({ where: { id: purchaseOrderId } })
      if (!checkPo) {
        throw new Error(`Không tìm thấy PO với mã ${purchaseOrderId}`)
      }

      const countDeliveryNote = await this.deliveryNoteRepo.count()
      const deliveryNoteCode = coreHelper.generateDeliveryNoteCode(countDeliveryNote)
      const deliveryNoteData = {
        code: deliveryNoteCode,
        poId: [purchaseOrderId],
        createdBy,
        totalAmountVat: checkPo.totalAmountVat,
        totalAmount: checkPo.totalAmount,
      }
      const dataObj = await this.deliveryNoteRepo.save(deliveryNoteData)

      return { data: dataObj, messaging: CREATE_SUCCESS }
    } catch (error) {
      throw new Error(error)
    }
  }

  public async updateDeliveryNote(body: UpdateDeliveryNoteDto) {
    const { deliveryNoteId, ...data } = body
    const check = await this.deliveryNoteRepo.findOne({ where: { id: deliveryNoteId } })
    if (!check) {
      throw new Error(`Không tìm thấy phiếu giao nhận`)
    }
    return await this.deliveryNoteRepo.update({ id: deliveryNoteId }, data)
  }

  async paginationDeliveryNote(params: FilterDeliverNoteDto) {
    try {
      const { code, deliveryStatus, dateFrom, dateTo, pageIndex, pageSize } = params

      let query = `
        SELECT DISTINCT d.*
        FROM delivery_note AS d
        INNER JOIN purchase_order AS po 
        ON po.id = ANY (SELECT jsonb_array_elements_text(d."poId")::uuid)
        AND po.status = 'COMPLETE'
        WHERE d."isDeleted" = false
      `

      if (code) query += ` AND d."code" LIKE '%${code}%'`
      if (deliveryStatus) query += ` AND d."deliveryStatus" = '${deliveryStatus}'`
      if (dateFrom) query += ` AND d."createdAt" >= '${new Date(dateFrom).toISOString()}'`
      if (dateTo) query += ` AND d."createdAt" <= '${new Date(dateTo).toISOString()}'`
      query += ` ORDER BY d."createdAt" DESC`

      const rs = await this.deliveryNoteRepo.queryPagination(query, { pageIndex, pageSize })
      for (let item of rs.data) {
        item.poLst = await this.purchaseOrderRepo.find({ where: { id: In(item.poId), status: NSPo.EPoStatus.COMPLETE } })
        //truyển poType để phân biệt phiếu giao nhận của NCC VNPost
        if (!item.purchaseOrderType) {
          item.purchaseOrderType = item.poLst[0].purchaseOrderType
        }
      }

      return rs
    } catch (error) {
      throw new Error(error)
    }
  }

  async paginationSupplierDeliveryNote(params: FilterDeliverNoteDto) {
    try {
      const { code, deliveryStatus, dateFrom, dateTo, pageIndex, pageSize } = params

      let query = `
        SELECT DISTINCT d.*
        FROM delivery_note AS d
        INNER JOIN purchase_order AS po 
        ON po.id = ANY (SELECT jsonb_array_elements_text(d."poId")::uuid)
        AND po.status = 'COMPLETE'
        WHERE d."isDeleted" = false
      `

      if (code) query += ` AND d."code" LIKE '%${code}%'`
      if (deliveryStatus) query += ` AND d."deliveryStatus" = '${deliveryStatus}'`
      if (dateFrom) query += ` AND d."createdAt" >= '${new Date(dateFrom).toISOString()}'`
      if (dateTo) query += ` AND d."createdAt" <= '${new Date(dateTo).toISOString()}'`
      query += ` ORDER BY d."createdAt" DESC`

      const rs = await this.deliveryNoteRepo.queryPagination(query, { pageIndex, pageSize })
      for (let item of rs.data) {
        item.poLst = await this.purchaseOrderRepo.find({
          where: { id: In(item.poId), status: NSPo.EPoStatus.COMPLETE, purchaseOrderType: NSPo.EPoType.SUPPLIER },
        })

        if (item.poLst && item.poLst.length != 0) {
          item.purchaseOrderType = NSPo.EPoType.SUPPLIER
        } else {
          rs.data = rs.data.filter((val) => val.id != item.id)
        }
      }

      return { data: rs.data, total: rs.data.length }
    } catch (error) {
      throw new Error(error)
    }
  }

  async detailDeliveryNote(id: string) {
    try {
      const checkDeliveryNote = await this.deliveryNoteRepo.findOne({ where: { id } })
      if (!checkDeliveryNote) {
        throw new Error(`Không tìm thấy Delivery Note với mã ${id}`)
      }

      const purchaseOrders = await this.purchaseOrderRepo.find({ where: { id: In(checkDeliveryNote.poId), status: NSPo.EPoStatus.COMPLETE } })
      const poIds = purchaseOrders.map((val) => val.id)

      const deliveryNoteTracking = await this.deliveryNoteTrackingRepo.find({
        where: { deliveryNoteId: checkDeliveryNote.id, poId: In(poIds) },
        order: { poId: 'ASC', step: 'ASC' },
      })

      const suppliers = await this.supplierRepo.find({
        relations: { ward: true, district: true, city: true },
      })

      const mapDetails = await Promise.all(
        deliveryNoteTracking.map(async (val) => {
          const supF = suppliers.find((supFrom) => supFrom.id === val.from)
          const supT = suppliers.find((supTo) => supTo.id === val.to)
          const po = purchaseOrders.find((p) => p.id === val.poId)

          const wardFrom = await supF?.ward
          const districtFrom = await supF?.district
          const cityFrom = await supF?.city

          const wardTo = await supT?.ward
          const districtTo = await supT?.district
          const cityTo = await supT?.city

          // Địa chỉ kho
          const warehouse = await this.warehouseRepo.findOne({ where: { id: val.warehouseId } })
          const wardWarehouse = await this.wardRepository.findOne({ where: { id: warehouse.wardId } })
          const districtWarehouse = await this.districtRepository.findOne({ where: { id: warehouse.districtId } })
          const cityWarehouse = await this.cityRepository.findOne({ where: { id: warehouse.cityId } })
          const region = await this.regionCity.findOne({ where: { cityId: warehouse.cityId } })

          return {
            ...val,
            poCode: po.purchaseOrderCode,
            regionId: region.regionId,
            fromDetail: {
              name: supF?.name,
              phone: supF?.phone,
              address: supF?.address,
              email: supF?.email,
              ward: wardFrom?.name,
              district: districtFrom?.name,
              city: cityFrom?.name,
            },
            toDetail: {
              name: supT?.name,
              phone: supT?.phone,
              address: supT?.address,
              email: supT?.email,
              ward: wardTo?.name,
              district: districtTo?.name,
              city: cityTo?.name,
            },
            warehouseDetail: {
              name: warehouse?.name,
              phone: warehouse?.phone,
              address: warehouse?.address,
              ward: wardWarehouse?.name,
              district: districtWarehouse?.name,
              city: cityWarehouse?.name,
            },
          }
        }),
      )

      const result = {
        ...checkDeliveryNote,
        receiving: mapDetails.filter((val) => val.trackingType === NSPo.EDeliveryTrackingType.RECEIVING),
        delivery: mapDetails.filter((val) => val.trackingType === NSPo.EDeliveryTrackingType.DELIVERY),
        // packing: mapDetails.filter((val) => val.trackingType === NSPo.EDeliveryTrackingType.PACKAGING),
      }

      return result
    } catch (error) {
      throw new Error(error)
    }
  }

  async detailTracking(body: DetailTrackingDto, req: IRequest) {
    try {
      const { id } = body
      const checkTracking = await this.deliveryNoteTrackingRepo.findOne({ where: { id } })
      if (!checkTracking) {
        throw new Error(`Không tìm thấy Tracking với mã ${id}`)
      }

      const po = await this.purchaseOrderRepo.findOne({ where: { id: checkTracking.poId, status: NSPo.EPoStatus.COMPLETE } })
      if (!po) {
        throw new Error(`Không tìm thấy PO với mã ${checkTracking.poId}`)
      }

      // Thông tin danh sản phẩm của PO
      const poItems = await this.purchaseOrderItemRepo.find({
        where: {
          purchaseOrderId: po.id,
          warehouseId: checkTracking.warehouseId,
          distributorId: checkTracking.from,
        },
      })

      // Thông tin po tham chiếu so
      const soLst = await this.purchaseOrderSoRepo.find({
        where: {
          poId: po.id,
          warehouseId: checkTracking.warehouseId,
          status: NSPo.EPoStatus.APPROVED,
        },
      })

      const soIds = Array.from(new Set(soLst.map((val) => val.soId)))
      const orderLst = await omsApiHelper.getOrderByIds(req, { orderListIds: soIds })

      const productLst = orderLst.data.map((val) => val.products).flat()
      const productIds = productLst.map((val) => val.productId)

      const comboLst = await this.itemComboRepo.find({ where: { itemId: In(productIds) } }) // Lấy toàn bộ thành phần của các product là Combo
      const productInCombos = comboLst.map((val) => val.itemInComboId)

      const uniqueProductIds = Array.from(new Set([...productIds, ...productInCombos].flat()))
      const products = await this.itemRepo.find({
        where: { id: In(uniqueProductIds) },
        select: { id: true, code: true, name: true },
        relations: { unit: true, pOUnit: true, prices: true },
      })

      let mappingSoLst = []
      for (const val of soLst) {
        const sod = orderLst.data.find((o) => o.id == val.soId)
        const productInSO = sod.products
        let itemInCombo = []

        for (const product of productInSO) {
          if (product.isCombo) {
            const combo = products.find((i) => i.id == product.productId)
            const itemPrice = (await combo.prices).find((i) => i.isFinal == true)
            const itemInComboLst = await this.itemComboRepo.find({ where: { itemId: combo.id } })
            const _itemPO = poItems.find((i) => i.comboId == product.productId)
            if (!_itemPO) continue
            itemInCombo = await Promise.all(
              itemInComboLst.map(async (i) => {
                const itemInCombo = products.find((item) => item.id == i.itemInComboId)
                const itemPrice = (await itemInCombo.prices).find((i) => i.isFinal == true)
                const itemPO = poItems.find((i) => i.itemId == itemInCombo.id && i.comboId == product.productId)
                if (itemPO) {
                  const unit = await itemInCombo.unit
                  const poUnit = await itemInCombo.pOUnit
                  const totalQuantity = i.quantity * product.quantity
                  const totalAmount =
                    checkTracking.trackingType === NSPo.EDeliveryTrackingType.DELIVERY
                      ? totalQuantity * itemPO.sellPrice
                      : totalQuantity * itemPrice.priceSell

                  return {
                    id: itemInCombo.id,
                    code: itemInCombo.code,
                    name: itemInCombo.name,
                    unit: unit.name,
                    baseUnit: unit.baseUnit,
                    poUnit: poUnit.name,
                    quantity: totalQuantity, // Số lượng thành phần trong combo
                    sellPrice: checkTracking.trackingType === NSPo.EDeliveryTrackingType.DELIVERY ? itemPO.sellPrice : itemPrice.priceSell, // Giá từ PO
                    vat: itemPO.vat,
                    totalAmount: totalAmount, // Thành tiền bán chung
                    totalAmountVat: totalAmount + totalAmount * (itemPO.vat / 100), // Thành tiền bán chung + VAT
                  }
                }
              }),
            )
            const totalAmount =
              checkTracking.trackingType === NSPo.EDeliveryTrackingType.DELIVERY
                ? product.quantity * _itemPO.sellPrice
                : product.quantity * itemPrice.priceSell
            mappingSoLst.push({
              soId: val.soId,
              soCode: sod.code,
              items: [
                {
                  id: combo.id,
                  code: combo.code,
                  name: combo.name,
                  quantity: product.quantity, // Số lượng từ SO
                  price: +productInSO.totalPrice, // Giá từ SO
                  isCombo: true,
                  sellPrice: checkTracking.trackingType === NSPo.EDeliveryTrackingType.DELIVERY ? +_itemPO.sellPrice : +itemPrice.priceSell, // Giá từ PO
                  vat: _itemPO.vat,
                  totalAmount: totalAmount, // Thành tiền bán chung
                  totalAmountVat: totalAmount + totalAmount * (_itemPO.vat / 100), // Thành tiền bán chung + VAT
                  itemInCombo,
                },
              ],
            })
          } else {
            const item = products.find((i) => i.id == product.productId)
            const itemPO = poItems.find((i) => i.itemId == product.productId)
            if (itemPO) {
              const itemPrice = (await item.prices).find((i) => i.isFinal == true)
              const unit = await item.unit
              const poUnit = await item.pOUnit
              const totalAmount =
                checkTracking.trackingType === NSPo.EDeliveryTrackingType.DELIVERY
                  ? product.quantity * itemPO.sellPrice
                  : product.quantity * itemPrice.priceSell

              mappingSoLst.push({
                soId: val.soId,
                soCode: sod.code,
                items: [
                  {
                    id: item.id,
                    code: item.code,
                    name: item.name,
                    quantity: product.quantity,
                    price: productInSO.totalPrice, // Giá từ SO
                    unit: unit.name,
                    baseUnit: unit.baseUnit,
                    poUnit: poUnit.name,
                    isCombo: false,
                    sellPrice: checkTracking.trackingType === NSPo.EDeliveryTrackingType.DELIVERY ? +itemPO.sellPrice : +itemPrice.priceSell, // Giá từ PO
                    vat: itemPO.vat,
                    totalAmount: totalAmount, // Thành tiền giá bán chungAdd commentMore actions
                    totalAmountVat: totalAmount + totalAmount * (itemPO.vat / 100), // Thành tiền bán chung + VAT
                  },
                ],
              })
            }
          }
        }
      }

      // Duyệt mảng mappingSoLst, nếu trùng soId thì sum items lại
      const mappingSoLstFinal = mappingSoLst.reduce((acc, cur) => {
        const existingSo = acc.find((so) => so.soId == cur.soId)
        if (existingSo) {
          existingSo.items = existingSo.items.concat(cur.items)
        } else {
          acc.push(cur)
        }
        return acc
      }, [])

      // Thông tin balanceLife
      const createBy = po.createdBy
      const user = await this.userRepository.findOne({ where: { id: createBy } })
      const employee = await this.employeeRepository.findOne({ where: { id: user.employeeId } })

      const city = await this.cityRepository.findOne({ where: { id: employee.cityId }, select: { name: true } })
      const district = await this.districtRepository.findOne({ where: { id: employee.districtId }, select: { name: true } })
      const ward = await this.wardRepository.findOne({ where: { id: employee.wardId }, select: { name: true } })

      const blf = {
        name: employee.name,
        phone: employee.phone,
        email: employee.email,
        address: employee.address,
        city: city.name,
        district: district.name,
        ward: ward.name,
      }

      // Suppliers
      const supplier = await this.supplierRepo.findOne({
        where: { id: checkTracking.supplierId },
        relations: { ward: true, district: true, city: true },
      })
      const supWard = await supplier.ward
      const supDistrict = await supplier.district
      const supCity = await supplier.city

      const sup = {
        name: supplier.name,
        phone: supplier.phone,
        email: supplier.email,
        address: supplier.address,
        ward: supWard.name,
        district: supDistrict.name,
        city: supCity.name,
      }

      // Warehouse
      const warehouses = await this.warehouseRepo.findOne({ where: { id: checkTracking.warehouseId } })
      const wardWH = await this.wardRepository.findOne({ where: { id: warehouses.wardId }, select: { name: true } })
      const districtWH = await this.districtRepository.findOne({ where: { id: warehouses.districtId }, select: { name: true } })
      const cityWH = await this.cityRepository.findOne({ where: { id: warehouses.cityId }, select: { name: true } })

      const mappingWH = {
        name: warehouses.name,
        phone: warehouses.phone,
        address: warehouses.address,
        ward: wardWH.name,
        district: districtWH.name,
        city: cityWH.name,
      }

      // Distributor
      const distributor = await this.supplierRepo.findOne({
        where: { id: checkTracking.from },
        relations: { ward: true, district: true, city: true },
      })
      const disWard = await distributor.ward
      const disDistrict = await distributor.district
      const disCity = await distributor.city
      const mappingDis = {
        name: distributor.name,
        phone: distributor.phone,
        email: distributor.email,
        address: distributor.address,
        ward: disWard.name,
        district: disDistrict.name,
        city: disCity.name,
      }

      // Delivery
      const deliveryId = checkTracking.to
      const delivery = await this.supplierRepo.findOne({ where: { id: deliveryId }, relations: { ward: true, district: true, city: true } })
      const deliveryWard = await delivery.ward
      const deliveryDistrict = await delivery.district
      const deliveryCity = await delivery.city

      const deli = {
        name: delivery.name,
        phone: delivery.phone,
        email: delivery.email,
        address: delivery.address,
        ward: deliveryWard.name,
        district: deliveryDistrict.name,
        city: deliveryCity.name,
      }

      return {
        balanceLife: blf,
        delivery: deli,
        supplier: sup,
        warehouse: mappingWH,
        distributor: mappingDis,
        soLst: mappingSoLstFinal,
        purchaseOrder: po,
      }
    } catch (error) {
      throw new Error(error)
    }
  }

  async createDeliveryNoteChild(deliveryNoteId: string, purchaseOrderId: string[], warehouseId: string, req: IRequest, warehouseIdManual?: string) {
    try {
      let rs = []
      const poIds = purchaseOrderId // Danh sách PO ID

      const purchaseOrderDetails = await this.purchaseOrderItemRepo.find({
        where: {
          purchaseOrderId: In(poIds),
          warehouseId: warehouseId,
        },
      })
      const partnerIdLst = Array.from(new Set(purchaseOrderDetails.map((val) => val.partnerId))).filter((i) => i) // Lấy danh sách id bưu cục / khách hàng

      const partners = (await omsApiHelper.lstPartnerByCodes(req, { ids: partnerIdLst })) || [] // Tìm danh sách partner theo partner ids

      const mappingPoIds: string[] = Array.from(new Set(poIds)) // Có được danh sách PO ID
      rs = await this.createDeliveryChildDetail(mappingPoIds, deliveryNoteId, warehouseId, partners, req, warehouseIdManual)
      return rs
    } catch (err) {
      throw new Error(err)
    }
  }

  async updateTracking(id: string, req: IRequest) {
    return await this.deliveryNoteTrackingRepo.manager.transaction(async (trans) => {
      const inboundRepoTran = trans.getRepository(InboundEntity)
      try {
        const checkTrackingID = await this.deliveryNoteTrackingRepo.findOne({ where: { id } })
        if (!checkTrackingID) {
          throw new Error('Không tìm thấy ID tracking')
        }

        const poCheck = await this.purchaseOrderRepo.findOne({ where: { id: checkTrackingID.poId, status: NSPo.EPoStatus.COMPLETE } })
        if (!poCheck) {
          throw new Error(`PO [${poCheck.purchaseOrderCode}] chưa được xác nhận từ Nhà cung cấp`)
        }

        if (
          checkTrackingID.receivingStatus === NSPo.EDeliveryTracking.SENT ||
          checkTrackingID.receivingStatus === NSPo.EDeliveryTracking.RECEIVED ||
          checkTrackingID.receivingStatus === NSPo.EDeliveryTracking.PACKED
        ) {
          throw new Error('Phiếu này đã hoàn thành')
        }

        const countLastPoint = await this.deliveryNoteTrackingRepo.count({
          where: { isLast: true, deliveryNoteId: checkTrackingID.deliveryNoteId, isDeleted: false },
        })
        const countDelivery = await this.deliveryNoteTrackingRepo.count({
          where: {
            deliveryNoteId: checkTrackingID.deliveryNoteId,
            trackingType: NSPo.EDeliveryTrackingType.DELIVERY,
            warehouseId: checkTrackingID.warehouseId,
            isDeleted: false,
          },
        })

        // Step 1. Nhà cung cấp -> 3PL
        if (checkTrackingID.step == 1) {
          const check = await this.createInbound(poCheck, checkTrackingID.from, checkTrackingID.warehouseId, req) // Tạo phiếu nhập kho cho 3PL
          if (check.message) {
            await this.updateTrackingStatus(id, NSPo.EDeliveryTracking.SENT)
            if (countLastPoint > 1) {
              await this.updateDeliveryNoteStatus(checkTrackingID.deliveryNoteId, NSPo.EDeliveryStatus.PARTIALLY_SENT)
            } else {
              await this.updateDeliveryNoteStatus(checkTrackingID.deliveryNoteId, NSPo.EDeliveryStatus.SENT)
            }
          } else {
            throw new Error('Lỗi tạo phiếu xuất kho 3PL')
          }
          // Tạo phiếu nhận
          await this.createDeliveryTracking(checkTrackingID)
        } else {
          const previousStep = await this.deliveryNoteTrackingRepo.findOne({
            where: {
              step: checkTrackingID.step - 1,
              poId: checkTrackingID.poId,
              from: checkTrackingID.from, // Cùng NPP
              receivingStatus: In([NSPo.EDeliveryTracking.PENDING, NSPo.EDeliveryTracking.DELIVERY_PENDING]),
            },
          })

          if (previousStep) {
            throw new Error(`PO ${poCheck.purchaseOrderCode} này chưa được giao`)
          } else {
            if (!checkTrackingID.files) throw new Error(`Vui lòng cập nhật chứng từ nhận hàng`)
            // Approve phiếu nhập kho 3PL
            const inbound = await inboundRepoTran.findOne({
              where: {
                poId: checkTrackingID.poId,
                warehouseId: checkTrackingID.warehouseId,
                createdBy: checkTrackingID.from,
              },
            })
            if (!inbound) throw new Error(`Không tìm thấy phiếu nhập kho`)
            await this.inboundService.approveData({ id: inbound.id, approveBy: checkTrackingID.to }, req)

            await this.updateTrackingStatus(id, NSPo.EDeliveryTracking.RECEIVED)

            const [countAllStepDistributor, countAllStepReceived, countAllStepReceivedWH, countAllReceived, countAllDelivery, countAllReceiving] =
              await Promise.all([
                this.deliveryNoteTrackingRepo.count({
                  where: {
                    deliveryNoteId: checkTrackingID.deliveryNoteId,
                    trackingType: NSPo.EDeliveryTrackingType.RECEIVING,
                    isLast: true,
                    isDeleted: false,
                    from: checkTrackingID.from,
                  },
                }),
                this.deliveryNoteTrackingRepo.count({
                  where: {
                    deliveryNoteId: checkTrackingID.deliveryNoteId,
                    trackingType: NSPo.EDeliveryTrackingType.RECEIVING,
                    isLast: true,
                    receivingStatus: NSPo.EDeliveryTracking.RECEIVED,
                    isDeleted: false,
                    from: checkTrackingID.from,
                  },
                }),
                this.deliveryNoteTrackingRepo.count({
                  where: {
                    deliveryNoteId: checkTrackingID.deliveryNoteId,
                    trackingType: NSPo.EDeliveryTrackingType.RECEIVING,
                    isLast: true,
                    receivingStatus: NSPo.EDeliveryTracking.RECEIVED,
                    warehouseId: checkTrackingID.warehouseId,
                    isDeleted: false,
                  },
                }),
                this.deliveryNoteTrackingRepo.count({
                  where: {
                    deliveryNoteId: checkTrackingID.deliveryNoteId,
                    trackingType: NSPo.EDeliveryTrackingType.RECEIVING,
                    receivingStatus: NSPo.EDeliveryTracking.RECEIVED,
                    isLast: true,
                    isDeleted: false,
                  },
                }),

                this.deliveryNoteTrackingRepo.count({
                  where: {
                    deliveryNoteId: checkTrackingID.deliveryNoteId,
                    trackingType: NSPo.EDeliveryTrackingType.DELIVERY,
                    isDeleted: false,
                  },
                }),
                this.deliveryNoteTrackingRepo.count({
                  where: {
                    deliveryNoteId: checkTrackingID.deliveryNoteId,
                    trackingType: NSPo.EDeliveryTrackingType.RECEIVING,
                    isLast: true,
                    receivingStatus: NSPo.EDeliveryTracking.RECEIVED,
                    isDeleted: false,
                  },
                }),
              ])

            let poLst = await this.deliveryNoteRepo.findOne({ where: { id: checkTrackingID.deliveryNoteId }, select: ['id', 'poId'] })
            let poLstWithWH: any = []

            // Lấy danh sách PO của phiếu giao nhận có warehouse trùng với phiếu nhận
            const poLstIds = poLst.poId.map((val) => val)
            const details = await this.purchaseOrderItemRepo.find({ where: { purchaseOrderId: In(poLstIds) } })

            for (let poId of poLstIds) {
              const detail = details.filter((val) => val.purchaseOrderId === poId)
              const poWarehouseId = Array.from(new Set(detail.map((val) => val.warehouseId)))

              //Tạo list PO có warehouseId trùng với phiếu nhận
              if (poWarehouseId.includes(checkTrackingID.warehouseId)) {
                poLstWithWH.push({
                  id: poId,
                  warehouseId: poWarehouseId,
                })
              }
            }

            // Kiểm tra trạng thái của PO trong danh sách PO trùng warehouse
            let completedPo = await this.purchaseOrderRepo.count({
              where: { id: In(poLstWithWH.map((val: any) => val.id)), status: NSPo.EPoStatus.COMPLETE },
            })
            // Tạo phiếu xuất kho cho Supplier
            // Kiểm tra toàn bộ phiếu nhận của 1 distributorId (from) đã nhận hết thì mới tạo phiếu outbound
            if (countAllStepDistributor == countAllStepReceived) {
              const rs = await this.createOutbound(poCheck.id, checkTrackingID.warehouseId, checkTrackingID.from, req) // Tạo phiếu xuất kho cho Supplier NCC theo từng NPP
              if (!rs.message) {
                throw new Error('Lỗi tạo phiếu xuất kho Supplier')
              }
            }

            if (countDelivery == countAllStepReceivedWH && completedPo == poLstWithWH.length) {
              await this.updateDeliveryNoteStatus(checkTrackingID.deliveryNoteId, NSPo.EDeliveryStatus.RECEIVED)
            } else {
              await this.updateDeliveryNoteStatus(checkTrackingID.deliveryNoteId, NSPo.EDeliveryStatus.PARTIALLY_RECEIVED)
            }

            // Nếu số lượng phiếu nhận "đã nhận" = số lượng phiếu giao "đã giao"
            // Nếu số lượng PO có warehouseId trùng với phiếu nhận đã hoàn thành = tổng số lượng PO có warehouseId trùng với phiếu nhận
            if (poCheck.purchaseOrderType !== NSPo.EPoType.SUPPLIER) {
              if (countDelivery == countAllStepReceivedWH && completedPo == poLstWithWH.length) {
                // Tạo phiếu đóng gói
                await this.createPackingNote(checkTrackingID.deliveryNoteId, checkTrackingID.to, checkTrackingID.warehouseId)
                if (countAllReceived == countAllDelivery) {
                  await this.updateDeliveryNoteStatus(checkTrackingID.deliveryNoteId, NSPo.EDeliveryStatus.PENDING_PACKING)
                } else {
                  await this.updateDeliveryNoteStatus(checkTrackingID.deliveryNoteId, NSPo.EDeliveryStatus.PACKAGING_PENDING)
                }
              }
            } else if (poCheck.purchaseOrderType === NSPo.EPoType.SUPPLIER) {
              if (countAllDelivery == countAllReceiving && completedPo == poLstWithWH.length) {
                const poListWithWH = poLstWithWH.map((val: any) => val.id)
                await this.createDeliveryNoteChild(checkTrackingID.deliveryNoteId, poListWithWH, checkTrackingID.warehouseId, req)
                await this.updateDeliveryNoteStatus(checkTrackingID.deliveryNoteId, NSPo.EDeliveryStatus.COMPLETE)
              } else {
                const poListWithWH = poLstWithWH.map((val: any) => val.id)
                await this.createDeliveryNoteChild(checkTrackingID.deliveryNoteId, poListWithWH, checkTrackingID.warehouseId, req)
                await this.updateDeliveryNoteStatus(checkTrackingID.deliveryNoteId, NSPo.EDeliveryStatus.PARTIALLY_RECEIVED)
              }
            } else {
              // Tạo Lastmile cho PO Manual
              const poListWithWH = poLstWithWH.map((val: any) => val.id)
              if (countAllDelivery == countAllReceiving && completedPo == poLstWithWH.length) {
                await this.createDeliveryNoteChild(checkTrackingID.deliveryNoteId, poListWithWH, checkTrackingID.warehouseId, req, poCheck.warehouseId)
                await this.updateDeliveryNoteStatus(checkTrackingID.deliveryNoteId, NSPo.EDeliveryStatus.COMPLETE)
              }
            }
          }
        }

        return { message: UPDATE_SUCCESS }
      } catch (error) {
        throw new Error(`Có lỗi xảy ra: ${error.message}`)
      }
    })
  }

  // Tạo phiếu nhận khi phiếu giao đã gửi
  private async createDeliveryTracking(tracking: any) {
    const trackingDL = await this.deliveryNoteTrackingRepo.findOne({
      where: { id: tracking.id },
    })

    await this.deliveryNoteTrackingRepo.save({
      deliveryNoteId: trackingDL.deliveryNoteId,
      poId: trackingDL.poId,
      poGroup: trackingDL.poGroup,
      from: trackingDL.from,
      to: trackingDL.to,
      step: +trackingDL.step + 1,
      isLast: true,
      isPacking: false,
      code: trackingDL.code,
      trackingType: NSPo.EDeliveryTrackingType.RECEIVING,
      expectedDate: trackingDL.expectedDate,
      supplierId: trackingDL.supplierId,
      warehouseId: trackingDL.warehouseId,
    })
  }

  async updateFileTracking(data: UpdateFileTrackingDto) {
    try {
      const { id, files } = data
      const trackingDL = await this.deliveryNoteTrackingRepo.findOne({ where: { id } })
      if (!trackingDL) {
        throw new Error(`Không tìm thấy phiếu giao nhận tracking`)
      }
      // Cập nhật chứng từ phiếu nhập kho
      await this.inboundRepo.update({ poId: trackingDL.poId }, { files })
      return await this.deliveryNoteTrackingRepo.update({ id }, { files })
    } catch (error) {
      throw new Error(error)
    }
  }

  async checkPoApproved(id: string) {
    // Lấy danh sách PO của phiếu giao nhận
    const poLst = await this.deliveryNoteRepo.findOne({
      where: { id },
      select: ['poId'],
    })
    const poIds = poLst.poId
    const checkPoLst = await this.purchaseOrderRepo.find({ where: { id: In(poIds), status: NSPo.EPoStatus.COMPLETE } })
    return {
      count: checkPoLst.length,
      data: checkPoLst,
    }
  }

  /** Portal NCC, 3PL */
  // Lấy phiếu giao từ NCC/NPP -> 3pl
  async listTrackingPoSupplier(params: TrackingListSupplierDto) {
    try {
      const { supplierId, dateFrom, dateTo, receivingStatus, toId, poCode, deliveryCode, ...param } = params
      let whereCon = `dnt."isDeleted" = false `
      if (receivingStatus) whereCon += ` AND dnt."receivingStatus" = '${receivingStatus}'`
      if (toId) whereCon += ` AND dnt."to" = '${toId}'`
      if (poCode) whereCon += ` AND po."purchaseOrderCode" = '${poCode}'`
      if (deliveryCode) whereCon += ` AND dnt."code" = '${deliveryCode}'`

      if (dateFrom && dateTo) {
        whereCon += ` AND dnt."createdAt" BETWEEN '${new Date(dateFrom).toISOString()}' AND '${new Date(dateTo).toISOString()}'`
      }

      const suppliers = await this.supplierRepo.find({
        relations: { ward: true, district: true, city: true },
      })
      const sup = suppliers.find((val) => val.id === supplierId)
      if (sup.isSupplier) {
        whereCon += ` AND dnt."supplierId" = '${supplierId}' AND dnt."trackingType" = '${NSPo.EDeliveryTrackingType.DELIVERY}'`
      } else if (sup.isDistributor) {
        whereCon += ` AND dnt."from" = '${supplierId}' AND dnt."trackingType" = '${NSPo.EDeliveryTrackingType.DELIVERY}'`
      }

      const query = `
          SELECT dnt.*, po."purchaseOrderCode",po."purchaseOrderType"
          FROM delivery_note_tracking AS dnt
          INNER JOIN purchase_order as po ON dnt."poId" = po."id"
          WHERE po."status" = 'COMPLETE' AND dnt."isPacking" = false
          AND ${whereCon}
          ORDER BY dnt."createdAt" DESC, po."purchaseOrderCode" ASC
        `
      const { data, total } = await this.deliveryNoteTrackingRepo.queryPagination(query, { ...param })

      const mapLstDL = await Promise.all(
        data.map(async (val) => {
          const supF = suppliers.find((supFrom) => supFrom.id === val.from)
          const supT = suppliers.find((supTo) => supTo.id === val.to)

          const wardFrom = await supF?.ward
          const districtFrom = await supF?.district
          const cityFrom = await supF?.city

          const wardTo = await supT?.ward
          const districtTo = await supT?.district
          const cityTo = await supT?.city
          const warehouse = await this.warehouseRepo.findOne({ where: { id: val.warehouseId }, select: { cityId: true } })
          const region = await this.regionCity.findOne({ where: { cityId: warehouse.cityId } })

          return {
            ...val,
            regionId: region.regionId,
            fromDetail: {
              name: supF?.name,
              phone: supF?.phone,
              address: supF?.address,
              email: supF?.email,
              ward: wardFrom?.name,
              district: districtFrom?.name,
              city: cityFrom?.name,
            },
            toDetail: {
              name: supT?.name,
              phone: supT?.phone,
              address: supT?.address,
              email: supT?.email,
              ward: wardTo?.name,
              district: districtTo?.name,
              city: cityTo?.name,
            },
          }
        }),
      )

      return { data: mapLstDL, total: total }
    } catch (error) {
      throw new Error(error)
    }
  }

  // Lấy phiếu nhận của 3PL
  async listTrackingPo3PL(params: TrackingListSupplierDto) {
    try {
      const { supplierId, expectedDateFrom, expectedDateTo, dateFrom, dateTo, receivingStatus, toId, poCode, deliveryCode, ...param } = params
      let whereCon = `dnt."isDeleted" = false `
      if (receivingStatus) whereCon += ` AND dnt."receivingStatus" = '${receivingStatus}'`
      if (toId) whereCon += ` AND dnt."to" = '${toId}'`
      if (poCode) whereCon += ` AND po."purchaseOrderCode" = '${poCode}'`
      if (deliveryCode) whereCon += ` AND dnt."code" = '${deliveryCode}'`

      if (dateFrom && dateTo) {
        whereCon += ` AND dnt."createdAt" BETWEEN '${new Date(dateFrom).toISOString()}' AND '${new Date(dateTo).toISOString()}'`
      }

      if (expectedDateFrom && expectedDateTo) {
        whereCon += ` AND dnt."expectedDate" BETWEEN '${new Date(expectedDateFrom).toISOString()}' AND '${new Date(expectedDateTo).toISOString()}'`
      }

      const suppliers = await this.supplierRepo.find({
        relations: { ward: true, district: true, city: true },
      })

      whereCon += ` AND dnt."to" = '${supplierId}' AND dnt."trackingType" = '${NSPo.EDeliveryTrackingType.RECEIVING}'` // is3PL
      const query = `
          SELECT dnt.*, po."purchaseOrderCode",po."purchaseOrderType"
          FROM delivery_note_tracking AS dnt
          INNER JOIN purchase_order as po ON dnt."poId" = po."id"
          WHERE po."status" = 'COMPLETE' AND dnt."isPacking" = false
          AND ${whereCon}
          ORDER BY dnt."createdAt" DESC, po."purchaseOrderCode" ASC
      `
      const { data, total } = await this.deliveryNoteTrackingRepo.queryPagination(query, { ...param })

      const mapLstDL = await Promise.all(
        data.map(async (val) => {
          const supF = suppliers.find((supFrom) => supFrom.id === val.from)
          const supT = suppliers.find((supTo) => supTo.id === val.to)

          const wardFrom = await supF.ward
          const districtFrom = await supF.district
          const cityFrom = await supF.city

          const wardTo = await supT.ward
          const districtTo = await supT.district
          const cityTo = await supT.city
          const warehouse = await this.warehouseRepo.findOne({ where: { id: val.warehouseId }, select: { cityId: true } })
          const region = await this.regionCity.findOne({ where: { cityId: warehouse.cityId } })

          return {
            ...val,
            regionId: region.regionId,
            fromDetail: {
              name: supF.name,
              phone: supF.phone,
              address: supF.address,
              email: supF.email,
              ward: wardFrom.name,
              district: districtFrom.name,
              city: cityFrom.name,
            },
            toDetail: {
              name: supT.name,
              phone: supT.phone,
              address: supT.address,
              email: supT.email,
              ward: wardTo.name,
              district: districtTo.name,
              city: cityTo.name,
            },
          }
        }),
      )

      return { data: mapLstDL, total: total }
    } catch (error) {
      throw new Error(error)
    }
  }

  // Lấy phiếu đóng gói từ deliveryNotePackingRepo
  async listPackingTracking(params: PackingListDto) {
    const { packingUnitId, packingStatus, packingDate, deliveryNoteId, dateFrom, dateTo, ...param } = params

    const wheres: any = { isDeleted: false }
    if (deliveryNoteId) wheres.deliveryNoteId = deliveryNoteId
    if (packingUnitId) wheres.packingUnitId = packingUnitId
    if (packingStatus) wheres.packingStatus = packingStatus
    if (packingDate) wheres.packingDate = packingDate
    if (dateFrom && dateTo) wheres.packingDate = Between(new Date(dateFrom), new Date(dateTo))

    const { data, total } = await this.deliveryNotePackingRepo.findPagination(
      {
        where: wheres,
        order: { createdAt: 'DESC' },
      },
      param,
    )

    // Mapping lấy ra thông tin của packingUnitId
    const packingUnits = await this.supplierRepo.find({
      where: { id: In(data.map((val) => val.packingUnitId)) },
      relations: { ward: true, district: true, city: true },
    })

    // Mapping với deliveryNoteTrackingRefRepo lấy ra danh sách PO
    // Mapping PO với purchaseOrderRepo
    const packingRef = await this.deliveryNotePackingRefRepo.find({
      where: { packingId: In(data.map((val) => val.id)) },
    })
    const poIds = Array.from(new Set(packingRef.map((val) => val.poId)))
    const purchaseOrders = await this.purchaseOrderRepo.find({
      where: { id: In(poIds), status: NSPo.EPoStatus.COMPLETE },
    })

    // Danh sách kho
    const warehouseIds = Array.from(new Set(data.map((val) => val.warehouseId)))
    const warehouses = await this.warehouseRepo.find({ where: { id: In(warehouseIds) } })

    // Map danh sách PO vào data
    const mapLstDL = await Promise.all(
      data.map(async (val) => {
        const packingUnit = packingUnits.find((sup) => sup.id === val.packingUnitId)
        const ward = await packingUnit.ward
        const district = await packingUnit.district
        const city = await packingUnit.city

        // Lấy danh sách poId từ packingRef
        const poLst = packingRef.filter((v) => v.packingId === val.id).map((val) => val.poId)
        const poDetailLst = purchaseOrders.filter((val) => poLst.includes(val.id))

        const warehouse = warehouses.find((w) => w.id === val.warehouseId)
        const wardWarehouse = warehouse ? await this.wardRepository.findOne({ where: { id: warehouse.wardId } }) : null
        const districtWarehouse = warehouse ? await this.districtRepository.findOne({ where: { id: warehouse.districtId } }) : null
        const cityWarehouse = warehouse ? await this.cityRepository.findOne({ where: { id: warehouse.cityId } }) : null

        return {
          ...val,
          packingUnitDetail: {
            name: packingUnit?.name,
            phone: packingUnit?.phone,
            address: packingUnit?.address,
            email: packingUnit?.email,
            ward: ward?.name,
            district: district?.name,
            city: city?.name,
          },
          warehouseDetail: {
            name: warehouse?.name,
            phone: warehouse?.phone,
            address: warehouse?.address,
            ward: wardWarehouse?.name,
            district: districtWarehouse?.name,
            city: cityWarehouse?.name,
          },
          poDetailLst,
        }
      }),
    )

    return { data: mapLstDL, total: total }
  }

  // Chi tiết phiếu đóng gói
  async detailPackingNote(id: string, req: IRequest) {
    try {
      const checkPackingNote = await this.deliveryNotePackingRepo.findOne({ where: { id } })
      if (!checkPackingNote) {
        throw new Error(`Không tìm thấy Packing Note với mã ${id}`)
      }

      // Mapping lấy ra thông tin của packingUnitId
      const packingUnits = await this.supplierRepo.findOne({
        where: { id: checkPackingNote.packingUnitId },
        relations: { ward: true, district: true, city: true },
      })

      const packingRef = await this.deliveryNotePackingRefRepo.find({
        where: { packingId: id },
      })
      const poIds = Array.from(new Set(packingRef.map((val) => val.poId)))
      const purchaseOrders = await this.purchaseOrderRepo.find({
        where: { id: In(poIds), status: NSPo.EPoStatus.COMPLETE },
      })

      // Lấy thêm soIds từ purchaseOrders
      const soIds = (await this.purchaseOrderSoRepo.find({ where: { poId: In(poIds), warehouseId: checkPackingNote.warehouseId } })).map(
        (val) => val.soId,
      )
      const uniqueSoIds = Array.from(new Set(soIds))
      const soLst = await omsApiHelper.getOrderByIds(req, { orderListIds: uniqueSoIds })
      const soLstData = soLst.data

      // Mapping lại soLst.data.product, nếu là isCombo thì lấy ra danh sách itemInCombo
      for (let so of soLstData) {
        for (let product of so.products) {
          if (product.isCombo) {
            product.items = await this.itemComboRepo.find({ where: { itemId: product.productId } })
            product.unitName = 'COMBO'
            for (let item of product.items) {
              let itemDetails = await this.itemRepo.findOne({ where: { id: item.itemInComboId }, relations: { unit: true, pOUnit: true } })
              const unitDetails = await itemDetails.unit
              const poUnitDetails = await itemDetails.pOUnit
              item.productName = itemDetails.name
              item.productCode = itemDetails.code
              item.unitId = unitDetails.id
              item.unitName = unitDetails.name
              item.baseUnit = unitDetails.baseUnit
              item.poUnitName = poUnitDetails.name
            }
          } else {
            let itemDetails = await this.itemRepo.findOne({ where: { id: product.productId }, relations: { unit: true, pOUnit: true } })
            product.unitName = (await itemDetails.unit).name
          }
        }
      }

      // Lấy thông tin kho packingUnitId (warehouse 3PL)
      const packingUnitWarehouse: any = await this.warehouseRepo.findOne({ where: { id: checkPackingNote.warehouseId } })

      packingUnitWarehouse.ward = await this.wardRepository.findOne({ where: { id: packingUnitWarehouse.wardId } })
      packingUnitWarehouse.district = await this.districtRepository.findOne({ where: { id: packingUnitWarehouse.districtId } })
      packingUnitWarehouse.city = await this.cityRepository.findOne({ where: { id: packingUnitWarehouse.cityId } })

      const packingUnitDetail = {
        name: packingUnits.name,
        phone: packingUnits.phone,
        address: packingUnits.address,
        email: packingUnits.email,
        ward: (await packingUnits.ward).name,
        district: (await packingUnits.district).name,
        city: (await packingUnits.city).name,
      }

      const createBy = purchaseOrders[0].createdBy
      const user = await this.userRepository.findOne({ where: { id: createBy } })
      const employee = await this.employeeRepository.findOne({ where: { id: user.employeeId } })

      const city = await this.cityRepository.findOne({ where: { id: employee.cityId }, select: { name: true } })
      const district = await this.districtRepository.findOne({ where: { id: employee.districtId }, select: { name: true } })
      const ward = await this.wardRepository.findOne({ where: { id: employee.wardId }, select: { name: true } })

      const blf = {
        name: employee.name,
        phone: employee.phone,
        email: employee.email,
        address: employee.address,
        city: city.name,
        district: district.name,
        ward: ward.name,
      }

      return {
        ...checkPackingNote,
        poDetailLst: purchaseOrders,
        soIds: Array.from(new Set(soIds)),
        soData: soLstData,
        delivery: packingUnitDetail,
        warehouse: packingUnitWarehouse,
        balanceLife: blf,
        combos: [],
        items: [],
      }
    } catch (error) {
      throw new Error(error)
    }
  }

  //#region HELPER
  /** Helper Create Delivery Note Child and Delivery Note Child Detail */
  async createDeliveryChildDetail(poId: string[], deliveryNoteId: string, warehouseId: string, partners: any[], req: IRequest, warehouseIdManual?: string) {
    return await this.deliveryNoteChildRepo.manager.transaction(async () => {
      const purchaseOrderDetails = await this.purchaseOrderItemRepo.find({
        where: {
          purchaseOrderId: In(poId),
          warehouseId: warehouseId,
        },
      })
      const partnerIdLst = Array.from(new Set(purchaseOrderDetails.map((val) => val.partnerId))).filter((i) => i) // Lấy danh sách id bưu cục / khách hàng
      // Kiểm tra partnerIdLst
      if (partnerIdLst.length == 0) {
        throw new Error('Không tìm thấy danh sách partnerId')
      }

      // Get date create info deliveryNoteId
      const deliveryNote = await this.deliveryNoteRepo.findOne({ where: { id: deliveryNoteId } })

      const newDNCDetail = []
      const countDeliveryNoteChild = await this.deliveryNoteChildRepo.count()
      const newDeliveryNoteChild = await this.deliveryNoteChildRepo.save({
        deliveryNoteId: deliveryNoteId,
        thirdPartyId: purchaseOrderDetails[0].deliveryId,
        code: coreHelper.generateDeliveryNoteChildCode(countDeliveryNoteChild),
      })

      const lmDLNoteDetail = await this.deliveryNoteChildDetailRepo.count({
        where: {
          createdAt: Between(new Date(new Date().setHours(0, 0, 0, 0)), new Date(new Date().setHours(23, 59, 59, 999))),
        },
      })

      const poSoLst = (await this.purchaseOrderSoRepo.find({ where: { poId: In(poId), warehouseId: warehouseId } })).map((i) => i.soId)
      const soLstIds = Array.from(new Set(poSoLst))

      if (soLstIds.length > 0) {
        // Lấy ra thông tin của SO để tạo phiếu
        const soLst = await omsApiHelper.getOrderByIds(req, { orderListIds: soLstIds })
        const soLstData = soLst.data

        for (const so of soLstData) {
          const newLmCode = coreHelper.generateDeliveryNoteCode(lmDLNoteDetail + newDNCDetail.length + 1, 'GR')
          const checkIsMBC = partners.find((i) => i.id === so.partnerId && so.addressType == 'MBC') // Kiểm tra customerId có phải là partnerId không
          // Nếu soId đã được mang đi tạo deliveryNoteChildDetailRepo thì bỏ qua
          if (newDNCDetail.find((i) => JSON.parse(i.soId).includes(so.id))) {
            continue
          }

          if (so.addressType === 'MBC') {
            const soIds = soLstData.filter((s) => s.partnerId === checkIsMBC.id && s.addressType == 'MBC').map((val) => val.id)
            const totalAmount = soLstData
              .filter((s) => s.partnerId === checkIsMBC.id && s.addressType == 'MBC')
              .reduce((sum, cur) => sum + Number(cur.totalPrice), 0)
            const totalSellPrice = soLstData
              .filter((s) => s.partnerId === checkIsMBC.id && s.addressType == 'MBC')
              .reduce((sum, cur) => sum + Number(cur.totalPrice), 0)

            const provinceCode = checkIsMBC?.provinceCode
            const districtCode = checkIsMBC?.districtCode
            const wardCode = checkIsMBC?.wardCode
            const fullAddress = await this.getFullAddress(provinceCode, districtCode, wardCode)

            newDNCDetail.push({
              code: newLmCode,
              deliveryNoteChildId: newDeliveryNoteChild.id,
              partnerName: checkIsMBC?.name,
              partnerId: checkIsMBC?.id,
              partnerPhone: checkIsMBC?.phone,
              partnerAddress: fullAddress,
              totalAmount: totalAmount,
              totalSellPrice: totalSellPrice,
              soId: JSON.stringify(soIds), // Format về jsonstring
              type: NSPo.DeliveryLastMileType.MBC,
              expectedDate: moment(deliveryNote?.createdAt).add(14, 'days').format('YYYY-MM-DD HH:mm:ss'),
              sendStatus: NSPo.EDeliveryNoteInboundStatus.DELIVERING,
            })
          } else {
            newDNCDetail.push({
              code: newLmCode,
              deliveryNoteChildId: newDeliveryNoteChild.id,
              partnerName: so?.customerName,
              partnerId: so?.customerId,
              partnerPhone: so?.customerPhone,
              partnerAddress: so?.shippingAddress,
              totalAmount: +so?.totalPrice,
              totalSellPrice: +so?.totalPrice,
              soId: JSON.stringify([so?.id]), // Format về jsonstring
              type: NSPo.DeliveryLastMileType.CUSTOMER,
              expectedDate: so?.estimatedShippedDate,
              sendStatus: NSPo.EDeliveryNoteInboundStatus.DELIVERING,
            })
          }
        }
        await this.deliveryNoteChildDetailRepo.save(newDNCDetail)
      } else {
        // Tạo phiếu lm theo kho parnertId
        for (const partnerId of partnerIdLst) {
          const newLmCode = coreHelper.generateDeliveryNoteCode(lmDLNoteDetail + newDNCDetail.length + 1, 'GR')
          const partnerInfo = partners.find((i) => i.id === partnerId)
          // Lấy danh sách item trong poItems của partnerId
          const poItems = purchaseOrderDetails.filter((i) => i.partnerId === partnerId)
          const totalAmount = poItems.reduce((sum, cur) => sum + Number(cur.totalAmount), 0)
          const totalSellPrice = poItems.reduce((sum, cur) => sum + Number(cur.totalAmount), 0)
          const fullAddress = await this.getFullAddress(partnerInfo?.provinceCode, partnerInfo?.districtCode, partnerInfo?.wardCode)
          newDNCDetail.push({
            code: newLmCode,
            deliveryNoteChildId: newDeliveryNoteChild.id,
            partnerName: partnerInfo?.name,
            partnerId: partnerInfo?.id,
            partnerPhone: partnerInfo?.phone,
            partnerAddress: fullAddress,
            totalAmount: totalAmount,
            totalSellPrice: totalSellPrice,
            soId: JSON.stringify([]), // Format về jsonstring
            type: NSPo.DeliveryLastMileType.MBC,
            expectedDate: moment(deliveryNote?.createdAt).add(14, 'days').format('YYYY-MM-DD HH:mm:ss'),
            sendStatus: NSPo.EDeliveryNoteInboundStatus.DELIVERING,
          })
          await this.deliveryNoteChildDetailRepo.save(newDNCDetail)
        }
      }
      return newDNCDetail
    })
  }

  private async updateTrackingStatus(id: string, status: NSPo.EDeliveryTracking) {
    await this.deliveryNoteTrackingRepo.update(id, { receivingStatus: status })
  }

  private async updateDeliveryNoteStatus(deliveryNoteId: string, status: NSPo.EDeliveryStatus) {
    await this.deliveryNoteRepo.update({ id: deliveryNoteId }, { deliveryStatus: status })
  }
  //#endregion

  //#region Tạo phiếu nhập kho 3PL
  private async createInbound(po: any, distributorId: string, warehouseId: string, req: IRequest) {
    let poItems: any = await this.purchaseOrderItemRepo.find({ where: { purchaseOrderId: po.id, distributorId: distributorId } })

    poItems = poItems.reduce((acc, item) => {
      const existing = acc.find((val) => val.itemName == item.itemName && Number(val.sellPrice) == Number(item.sellPrice))
      if (!existing) {
        acc.push({ ...item })
      } else {
        existing.quantityBasicUnit = Number(existing.quantityBasicUnit) + Number(item.quantityBasicUnit)
      }
      return acc
    }, [])

    const totalAmount = poItems.reduce((sum, item) => sum + Number(item.totalAmount), 0) // Tổng tiền PO
    const itemIds = Array.from([...new Set(poItems.map((val) => val.itemId)), ...new Set(poItems.map((val) => val.comboId))]).filter((i) => i)
    const itemPrices = await this.itemPriceRepo.find({ where: { itemId: In(itemIds), isFinal: true } })

    // Kiểm tra thông tin kho
    const checkWarehouse = await this.warehouseRepo.findOne({ where: { id: warehouseId, type: NSWarehouse.EWarehouseType['3PL'], isDeleted: false } })
    if (!checkWarehouse) throw new Error('Không tìm thấy kho 3PL nhập kho')

    const code = await this.inboundService.codeDefault()
    // Tạo mới phiếu
    const newInbound: any = {}
    newInbound.code = code
    newInbound.warehouseId = warehouseId
    newInbound.poId = po.id
    newInbound.poCode = po.purchaseOrderCode
    newInbound.totalPrice = +totalAmount
    newInbound.totalPriceVND = +totalAmount
    newInbound.description = 'Phiếu nhập kho 3PL tạo từ NCC'
    newInbound.createdAt = new Date()
    newInbound.createBy = distributorId
    newInbound.status = enumData.InboundStatus.New.code
    newInbound.id = uuidv4()
    newInbound.exchangeRate = 1

    const lstDetail = []

    const itemLst = await this.itemRepo.find({ where: { id: In(itemIds) } })

    poItems = poItems.map((val) => {
      const item = itemLst.find((i) => i.id == val.itemId)
      return {
        ...val,
        itemCode: item.code,
        itemName: item.name,
      }
    })
    // Kiểm tra loại PO để nhập kho Combo hay Item
    if (po.purchaseOrderType === NSPo.EPoType.WITHCOMBO) {
      for (const item of poItems) {
        const price = itemPrices.find((c) => c.itemId === item.itemId) // Lấy ra gía bán chung của sản phẩm thành phần

        const inboundDetailNew: any = {}
        inboundDetailNew.productId = item.itemId
        inboundDetailNew.productName = item.itemName
        inboundDetailNew.productCode = item.itemCode
        inboundDetailNew.quantity = +item.quantityBasicUnit
        inboundDetailNew.totalQuantity = +item.quantityBasicUnit

        inboundDetailNew.buyPrice = +item.totalAmount // Tiền từ POP
        inboundDetailNew.buyPriceVND = +item.totalAmount // Tiền từ PO
        inboundDetailNew.price = +price.priceSell // Tiền giá bán chung
        inboundDetailNew.priceVND = +price.priceSell // Tiền giá bán chung
        inboundDetailNew.totalPrice = +price.priceSell * +item.quantityBasicUnit
        inboundDetailNew.totalPriceVND = +price.priceSell * +item.quantityBasicUnit
        inboundDetailNew.expiryDate = new Date('2500-01-01')
        inboundDetailNew.manufactureDate = new Date('2000-01-01')

        lstDetail.push(inboundDetailNew)
      }
      newInbound['lstDetail'] = lstDetail
      await this.inboundService.createData(newInbound, req)
    } else if (
      po.purchaseOrderType === NSPo.EPoType.OTHER ||
      po.purchaseOrderType === NSPo.EPoType.SUPPLIER ||
      po.purchaseOrderType === NSPo.EPoType.MANUAL
    ) {
      // Đơn lẻ
      const mappingIds = Array.from(new Set([...itemIds]))
      const items = await this.itemRepo.find({ where: { id: In(mappingIds) } })
      const itemPrices = await this.itemPriceRepo.find({ where: { itemId: In(itemIds), isFinal: true } })

      // Tạo chi tiết inbound với quantity đã được cộng dồn
      for (let item of poItems) {
        const _itemInfo = items.find((i) => i.id === item.itemId)
        const _price = itemPrices.find((i) => i.itemId === item.itemId)

        const inboundDetailNew: any = {}
        inboundDetailNew.poDetailId = item.id
        inboundDetailNew.productId = _itemInfo.id
        inboundDetailNew.productName = _itemInfo.name
        inboundDetailNew.productCode = _itemInfo.code
        inboundDetailNew.quantity = +item.quantityBasicUnit
        inboundDetailNew.totalQuantity = +item.quantityBasicUnit

        inboundDetailNew.price = +_price.priceSell // Giá bán chung
        inboundDetailNew.priceVND = +_price.priceSell // Giá bán chung
        inboundDetailNew.buyPrice = +item.sellPrice // Giá mua từ PO
        inboundDetailNew.buyPriceVND = +item.sellPrice // Giá mua từ PO

        inboundDetailNew.totalPrice = +item.sellPrice * +inboundDetailNew.quantity // Thành tiền giá bán chung
        inboundDetailNew.totalPriceVND = +_price.priceSell * +inboundDetailNew.quantity // Thành tiền giá mua từ PO
        inboundDetailNew.expiryDate = new Date('2500-01-01')
        inboundDetailNew.manufactureDate = new Date('2000-01-01')

        lstDetail.push(inboundDetailNew)
      }

      newInbound['lstDetail'] = lstDetail
      await this.inboundService.createData(newInbound, req)
    }

    return { message: 'SUCCESS' }
  }
  //#endregion

  //#region Tạo phiếu xuất kho cho NCC
  private async createOutbound(poId: string, warehouse3PLId: string, distributorId: string, req: IRequest) {
    const po = await this.purchaseOrderRepo.findOne({ where: { id: poId, status: NSPo.EPoStatus.COMPLETE } })
    if (!po) throw new Error('Không tìm thấy PO')
    const poItems = await this.purchaseOrderItemRepo.find({ where: { purchaseOrderId: poId, distributorId: distributorId } }) // Cùng NPP

    const itemIds = Array.from(new Set(poItems.map((val) => val.itemId)))
    const productLst = await this.itemRepo.find({ where: { id: In([...itemIds]) } }) // Bỏ comboIds

    // Kho của 3PL
    const wh = await this.warehouseRepo.findOne({ where: { id: warehouse3PLId } }) // Kho của 3PL
    if (!wh) throw new Error('Không tìm thấy kho của 3PL')

    // Kho của NCC
    const whSupplier = await this.warehouseRepo.findOne({ where: { storeId: po.supplierId } })
    if (!whSupplier) throw new Error('Không tìm thấy kho của NCC')
    const productDetailLst = await this.warehouseProductDetailRepo.find({ where: { productId: In([...itemIds]), warehouseId: whSupplier.id } }) // Bỏ comboIds

    const code = await this.inboundService.codeDefault()
    const newOutbound: any = {}
    newOutbound.code = code
    newOutbound.warehouseId = whSupplier.id
    newOutbound.type = enumData.OutboundType.INTERNAL_WAREHOUSE.code
    newOutbound.poId = po.id
    newOutbound.poCode = po.purchaseOrderCode
    newOutbound.description = 'Phiếu xuất kho tạo khi NCC gửi hàng tới 3PL'
    newOutbound.createdAt = new Date()
    newOutbound.createBy = whSupplier.storeId
    newOutbound.status = enumData.OutboundStatus.NEW.code
    newOutbound.id = uuidv4()
    newOutbound.exchangeRate = 1

    const lstDetail = []
    for (const product of productLst) {
      const idDetail = productDetailLst.find((i) => i.productId === product.id)
      const quantity = poItems.filter((i) => i.itemId === product.id).reduce((sum, current) => sum + Number(current.quantityBasicUnit), 0)
      lstDetail.push({
        productId: product.id,
        productDetailId: idDetail?.productDetailId,
        productName: product.name,
        productCode: product.code,
        quantity: quantity,
        inventory: idDetail?.quantity || 0, // Số lượng tồn kho
        expiryDate: new Date('2500-01-01'),
        manufactureDate: new Date('2000-01-01'),
      })
    }

    if (lstDetail.length === 0) throw new Error('Không tìm thấy sản phẩm trong PO')

    // Tạo phiếu xuất kho
    newOutbound['lstOutboundDetail'] = lstDetail
    await this.outboundService.createDataApproved(newOutbound, req, null)

    // Cập nhật lại trường quantityOrder sau xuất kho NCC
    for (const product of lstDetail) {
      const current = await this.warehouseProductRepo.findOne({ where: { warehouseId: whSupplier.id, productId: product.productId } })
      await this.warehouseProductRepo.update(
        { id: current.id },
        {
          quantityOrder: +current.quantityOrder - +product.quantity,
        },
      )
    }
    return { message: CREATE_SUCCESS }
  }
  //#endregion

  //#region Tạo phiếu đóng gói deliveryNotePackingRepo Và cập nhật trạng thái phiếu đóng gói
  private async createPackingNote(deliveryNoteId: string, packingUnitId: string, warehouseId: string) {
    // Lấy ra danh sách warehouseId của deliveryNoteId
    const warehouseIds = await this.deliveryNoteTrackingRepo.find({
      where: { deliveryNoteId, warehouseId },
      select: ['warehouseId'],
    })
    if (warehouseIds.length == 0) throw new Error('Không tìm thấy kho của phiếu giao nhận')

    // Lấy ra danh sách PO deliveryNoteId
    const poLst = await this.deliveryNoteTrackingRepo.find({
      where: { deliveryNoteId },
      select: ['poId'],
    })
    // Check trạng thái của danh sách PO
    const checkPoLst = await this.purchaseOrderRepo.find({ where: { id: In(poLst.map((val) => val.poId)), status: NSPo.EPoStatus.COMPLETE } })

    // Lấy poItems
    const poItems = await this.purchaseOrderItemRepo.find({
      where: { purchaseOrderId: In(checkPoLst.map((val) => val.id)) },
    })

    // Đếm số phiếu đóng gói trong ngày hiện tại
    let count = await this.deliveryNotePackingRepo.count({
      where: {
        createdAt: Between(new Date(new Date().setHours(0, 0, 0, 0)), new Date(new Date().setHours(23, 59, 59, 999))),
      },
    })

    const uniqueWarehouseIds = Array.from(new Set(warehouseIds.map((val) => val.warehouseId)))
    // Duyệt danh sách kho để tạo phiếu đóng gói theo kho 3PL
    for (const warehouseId of uniqueWarehouseIds) {
      count++
      const code = coreHelper.generateDeliveryNoteCode(count, 'PL')
      const newPackingNote = {
        code,
        packingUnitId,
        deliveryNoteId,
        packingDate: new Date(),
        packingStatus: NSPo.EDeliveryTracking.PACKAGING_PENDING,
        warehouseId: warehouseId,
      }
      const newPL = await this.deliveryNotePackingRepo.save(newPackingNote)

      // Lấy poIds từ poItems với warehouseId
      const poIds = Array.from(new Set(poItems.filter((val) => val.warehouseId == warehouseId).map((val) => val.purchaseOrderId)))

      // Tạo tham chiếu PL tới PO
      for (const poId of poIds) {
        await this.deliveryNotePackingRefRepo.save({
          packingId: newPL.id,
          poId: poId,
        })
      }
    }
  }

  // Cập nhật trạng thái phiếu đóng gói
  async updatePackingStatus(packingId: string, req: IRequest) {
    return await this.deliveryNotePackingRepo.manager.transaction(async (trans) => {
      // Kiểm tra phiếu
      const check = await this.deliveryNotePackingRepo.findOne({ where: { id: packingId } })
      if (!check) {
        throw new Error('Không tìm thấy phiếu đóng gói')
      }
      if (check.packingStatus != NSPo.EDeliveryTracking.PACKAGING_PENDING) {
        throw new Error('Phiếu đóng gói đã được đóng gói')
      }

      // Tạo phiếu giao nhận con
      const poLst = await this.deliveryNoteRepo.findOne({
        where: { id: check.deliveryNoteId, isDeleted: false },
        select: ['poId'],
      })

      // Chỉ lấy poId được approve
      const checkPoLst = await this.purchaseOrderRepo.find({ where: { id: In(poLst.poId), status: NSPo.EPoStatus.COMPLETE } })
      if (checkPoLst.length == 0) {
        throw new Error('Không tìm thấy danh sách PO hợp lệ')
      }
      poLst.poId = checkPoLst.map((val) => val.id)

      await this.createDeliveryNoteChild(check.deliveryNoteId, [...poLst.poId], check.warehouseId, req)

      // Cập nhật trạng thái của đơn hàng
      const soLst = await this.purchaseOrderSoRepo.find({
        where: { poId: In(poLst.poId), warehouseId: check.warehouseId },
        select: ['soId'],
      })
      if (soLst.length > 0) {
        const soIds = Array.from(new Set(soLst.map((val) => val.soId)))
        const rs = await omsApiHelper.updateSOStatus(req, { ids: soIds, status: NSRecurringOrder.EStatus.DELIVERING })
        if (!rs?.message) {
          throw new Error(`Lỗi cập nhật danh sách SO`)
        }
      }

      // Đóng gói Combo
      const poIds = poLst.poId
      const soIds = soLst.map((val) => val.soId)
      for (const poId of poIds) {
        await this.packingProduct(poId, check.warehouseId, req, soIds)
      }
      // Cập nhật phiếu đóng gói
      await this.deliveryNotePackingRepo.update(packingId, { packingStatus: NSPo.EDeliveryTracking.PACKED })

      // Cập nhật phiếu cha
      // Kiểm tra toàn bộ phiếu đóng gói đã hoàn thành
      const checkPackingNote = await this.deliveryNotePackingRepo.find({
        where: { deliveryNoteId: check.deliveryNoteId },
        select: ['packingStatus'],
      })
      if (checkPackingNote.length == checkPackingNote.filter((val) => val.packingStatus == NSPo.EDeliveryTracking.PACKED).length) {
        await this.updateDeliveryNoteStatus(check.deliveryNoteId, NSPo.EDeliveryStatus.PACKED)
      } else {
        await this.updateDeliveryNoteStatus(check.deliveryNoteId, NSPo.EDeliveryStatus.PACKAGING_PENDING)
      }
      return { message: UPDATE_SUCCESS }
    })
  }
  //#endregion

  //#region Đóng gói Combo và trừ tồn kho 3PL
  private async packingProduct(poId: string, warehouseId: string, req: IRequest, soIds: string[]) {
    return await this.deliveryNotePackingRepo.manager.transaction(async (trans) => {
      // Danh sách combo trong PO
      const poInfo = await this.purchaseOrderRepo.findOne({ where: { id: poId, status: NSPo.EPoStatus.COMPLETE } })
      if (!poInfo) throw new Error('Không tìm thấy PO')
      const poCombos = await this.purchaseOrderItemRepo.find({ where: { purchaseOrderId: poId, warehouseId } })
      const comboIds = Array.from(new Set(poCombos.map((c) => c.comboId))).filter((i) => i)
      const items = await this.itemRepo.find({ where: { id: In(comboIds) } })
      const itemPrices = await this.itemPriceRepo.find({ where: { itemId: In(comboIds), isFinal: true } })
      const whInfo = await this.warehouseRepo.findOne({ where: { id: warehouseId } }) // Kho 3PL

      // TODO 1. Duyệt danh sách sản phẩm để trừ tồn kho 3PL
      const poItems = await this.purchaseOrderItemRepo.find({ where: { purchaseOrderId: poId, comboId: In(comboIds), warehouseId } })
      for (const comboId of comboIds) {
        const itemLst = poItems.filter((val) => val.comboId == comboId)
        const lstItemIds = itemLst.map((val) => val.itemId)

        const productDetails = await this.productDetailRepo.find({
          where: {
            itemId: In(lstItemIds),
            expiryDate: Raw((alias) => `DATE(${alias}) = '2500-01-01'`),
            manufactureDate: Raw((alias) => `DATE(${alias}) = '2000-01-01'`),
          },
        })

        const products = await this.itemRepo.find({ where: { id: In(lstItemIds) } })

        for (const item of itemLst) {
          const thirdPartyId = item.deliveryId

          // #region Trừ bảng product và productDetail
          // Trừ của productDetail
          const productDetail = productDetails.find((val) => val.itemId == item.itemId)

          await this.productDetailRepo.update(productDetail.id, {
            quantity: +productDetail.quantity - +item.quantityBasicUnit,
            quantityLockEmp: +productDetail.quantityLockEmp + +item.quantityBasicUnit,
            updatedAt: new Date(),
            updatedBy: thirdPartyId,
          })

          // Trừ của product
          const product = products.find((val) => val.id == item.itemId)
          await this.itemRepo.update(product.id, {
            quantity: +product.quantity - +item.quantityBasicUnit,
            quantityLockEmp: +product.quantityLockEmp + +item.quantityBasicUnit,
            updatedAt: new Date(),
            updatedBy: thirdPartyId,
          })

          // #endregion

          /** Product trong kho 3PL chỉ có 1 date */
          // Trừ của warehouseProduct
          const whpd = await this.warehouseProductDetailRepo.findOne({ where: { warehouseId: warehouseId, productId: item.itemId } })
          await this.warehouseProductRepo.update(whpd.id, {
            quantity: +whpd.quantity - +item.quantityBasicUnit,
            quantityExport: +whpd.quantityExport + +item.quantityBasicUnit,
            updatedAt: new Date(),
            updatedBy: thirdPartyId,
          })

          // Trừ của warehouseProduct
          const whp = await this.warehouseProductRepo.findOne({ where: { warehouseId: warehouseId, productId: item.itemId } })
          await this.warehouseProductRepo.update(whp.id, {
            quantity: +whp.quantity - +item.quantityBasicUnit,
            quantityExport: +whp.quantityExport + +item.quantityBasicUnit,
            updatedAt: new Date(),
            updatedBy: thirdPartyId,
          })
        }
      }

      // TODO 2. Nhập kho combo 3PL
      // TODO poso

      const orderLst = await omsApiHelper.getOrderByIds(req, { orderListIds: soIds })
      const orderLstData = orderLst.data
      let lstInbound = []

      const code = await this.inboundService.codeDefault()
      const lstDetails = []
      for (const order of orderLstData) {
        for (const _product of order.products) {
          if (!_product.isCombo) continue
          const poItems = poCombos.filter((p) => p.comboId === _product.productId && p.purchaseOrderId === poId && p.warehouseId === warehouseId)
          if (poItems.length == 0) continue
          const price = itemPrices.find((p) => p.itemId === _product.productId)

          const itemInComboLst = await this.itemComboRepo.find({ where: { itemId: _product.productId } })
          let pricesComboPo = 0
          for (let i = 0; i < itemInComboLst.length; i++) {
            const item = itemInComboLst[i]
            const itemInPo = poItems.find((p) => p.itemId == item.itemInComboId)
            pricesComboPo += +itemInPo.totalAmount
          }
          const combo = items.find((i) => i.id === _product.productId)
          lstDetails.push({
            productId: combo.id,
            productName: combo.name,
            productCode: combo.code,
            quantity: _product.quantity,
            totalQuantity: _product.quantity,
            price: +price?.priceSell, // Giá bán chung
            priceVND: +price?.priceSell,
            totalPrice: +_product.quantity * +price?.priceSell, // Thành tiền giá bán chung
            totalPriceVND: +_product.quantity * +price?.priceSell, // Thành tiền giá mua từ PO
            buyPrice: +pricesComboPo, // Giá từ PO
            buyPriceVND: +pricesComboPo, // Giá từ PO
            manufactureDate: new Date('2000-01-01'),
            expiryDate: new Date('2500-01-01'),
          })
        }
      }
      const mappingLstDetails = lstDetails.reduce((acc, cur) => {
        const existing = acc.find((val) => val.productId === cur.productId)
        if (!existing) {
          acc.push({ ...cur })
        } else {
          existing.quantity += cur.quantity
          existing.totalQuantity += cur.totalQuantity
          existing.totalPrice += cur.totalPrice
          existing.totalPriceVND += cur.totalPriceVND
        }
        return acc
      }, [])

      const totalAmount = mappingLstDetails.reduce((sum, item) => sum + Number(item.totalPrice), 0)

      const newInbound: any = {}
      newInbound.code = code
      newInbound.warehouseId = warehouseId
      newInbound.poId = poInfo.id
      newInbound.files = poInfo.files
      newInbound.poCode = poInfo.purchaseOrderCode
      newInbound.totalPrice = +totalAmount
      newInbound.totalPriceVND = +totalAmount
      newInbound.description = 'Phiếu nhập kho gom thành phần thành Combo'
      newInbound.createdAt = new Date()
      newInbound.createBy = whInfo.storeId
      newInbound.status = enumData.InboundStatus.New.code
      newInbound.id = uuidv4()
      newInbound.exchangeRate = 1
      newInbound.lstDetail = mappingLstDetails

      if (newInbound.lstDetail.length > 0) {
        const rs = await this.inboundService.createData(newInbound, req)
        lstInbound.push(rs.data)
      }

      // TODO 3. Duyệt phiếu nhập kho combo
      // Duyệt danh sách phiếu nhập kho combo, 1 phiếu 1 combo
      for (const inbound of lstInbound) {
        await this.inboundService.approveData({ id: inbound.id, approveBy: inbound.createdBy }, req)
      }
      return { message: UPDATE_SUCCESS }
    })
  }
  //#endregion

  // Lấy full địa chỉ bằng provinceCode, districtCode, wardCode
  public async getFullAddress(provinceCode: string, districtCode: string, wardCode: string) {
    const city = await this.cityRepository.findOne({ where: { code: provinceCode } })

    const district = await this.districtRepository.findOne({ where: { code: districtCode } })

    const ward = await this.wardRepository.findOne({ where: { code: wardCode } })

    const fullAddress = `${ward?.name}, ${district?.name}, ${city?.name}`

    return fullAddress
  }
}
