import { Entity, Column, PrimaryColumn, OneToMany } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from './base.entity';
import { PoScheduleHistoryEntity } from './poScheduleHistory.entity';

@Entity('po_schedule')
export class PoScheduleEntity extends BaseEntity {
  @ApiProperty({ example: 'DS1212250000001', description: 'Mã lịch hàng về' })
  @Column({ type: 'varchar', length: 255, nullable: false })
  code: string;

  @ApiProperty({ example: 'Unilever', description: 'Nh<PERSON> cung cấp (NCC)' })
  @Column({ type: 'varchar', length: 255, nullable: false })
  supplier: string;

  @Column({ type: 'varchar', length: 36, nullable: true })
  supplierId: string

  @ApiProperty({
    example: '[UNILEVER101194396] DARLIE- LO 2 KEM ĐÁNH RĂNG DOUBLE ACTION 225G (NEW)',
    description: '<PERSON>ê<PERSON> sản phẩm phân phối',
  })
  @Column({ type: 'text', nullable: false })
  productName: string;

  @Column({ type: 'varchar', length: 36, nullable: true })
  productId: string

  @ApiProperty({ example: 10000000, description: 'Số lượng dự kiến phân phối' })
  @Column({ type: 'int', nullable: false })
  estimatedQuantity: number;

  @ApiProperty({ example: 10000000, description: 'Số lượng phân phối' })
  @Column({ type: 'int', nullable: false })
  distributedQuantity: number;

  @ApiProperty({ example: '13h 45p 18/02/2024', description: 'Thời gian tạo' })
  @Column({ type: 'timestamptz', nullable: false })
  createdAt: Date;

  @ApiProperty({ example: '31/12/2024 - 1/01/2025', description: 'Thời gian phân phối' })
  @Column({ type: 'varchar', length: 255, nullable: true })
  distributionTime: string;

  @ApiProperty({ example: 'Mới tạo', description: 'Trạng thái' })
  @Column({ type: 'varchar', length: 255, nullable: false })
  status: string;

  @OneToMany(() => PoScheduleHistoryEntity, (history) => history.poSchedule)
  histories: PoScheduleHistoryEntity[];
}
