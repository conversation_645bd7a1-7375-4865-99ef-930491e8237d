import { Controller, UseGuards, Request, Post, Body, Get, Param } from '@nestjs/common'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { Request as IRequest } from 'express'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { JwtAuthGuard, CurrentUser } from '../../common'
import { CityCreateDto, CityUpdateDto } from './dto'
import { CityService } from './city.service'

@ApiBearerAuth()
@ApiTags('Geo City')
@UseGuards(JwtAuthGuard)
@Controller('city')
export class CityController {
  constructor(private readonly service: CityService) { }

  @Post('find')
  async find(@Body() data: any) {
    return await this.service.find(data)
  }

  @Post('load_data')
  async loadData() {
    return await this.service.loadData()
  }

  @Get('load_city_by_regionId/:regionId')
  async loadCitiByRegionId(@Param('regionId') regionId: string) {
    return await this.service.loadCitiesByRegionId(regionId)
  }

  @Post('pagination')
  async pagination(@Body() data: PaginationDto) {
    return await this.service.pagination(data)
  }

  @Post('create_data')
  async createData(@CurrentUser() user: UserDto, @Body() data: CityCreateDto) {
    return await this.service.createData(user, data)
  }

  @Post('update_data')
  async updateData(@CurrentUser() user: UserDto, @Body() data: CityUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @Post('update_active')
  async updateActive(@Body() data: any, @CurrentUser() user: UserDto) {
    return await this.service.updateIsDelete(data.id, user)
  }

  @Post('create_data_excel')
  async createDataExcel(@CurrentUser() user: UserDto, @Body() data: CityCreateDto[]) {
    return await this.service.createDataExcel(user, data)
  }

  @Post('find_one')
  async findOne(@Body() data: FilterOneDto) {
    return await this.service.findOne(data)
  }
}
