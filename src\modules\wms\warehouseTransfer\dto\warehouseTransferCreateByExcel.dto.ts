import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString } from 'class-validator'

export class WarehouseTransferCreateByExcelDto {
  @ApiProperty({ description: 'Mã phiếu' })
  @IsNotEmpty()
  @IsString()
  code: string

  @ApiProperty({ description: 'Kho đi' })
  @IsNotEmpty()
  @IsString()
  fromWarehouseCode: string

  @ApiProperty({ description: 'Kho đến' })
  @IsNotEmpty()
  @IsString()
  toWarehouseCode: string

  @ApiProperty({ description: 'Ghi chú' })
  @IsOptional()
  description?: string

  @ApiProperty({ description: 'Sản phẩm' })
  @IsNotEmpty()
  @IsString()
  productCode: string

  @ApiProperty({ description: 'Hạn sử dụng' })
  @IsOptional()
  expiryDate: Date

  @ApiProperty({ description: 'Số lượng kiểm' })
  @IsOptional()
  quantity: number
}
