import { MigrationInterface, QueryRunner } from "typeorm";

export class updatePoNewPackingDelivery1747214197682 implements MigrationInterface {
    name = 'updatePoNewPackingDelivery1747214197682'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "delivery_note_packing" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "createdBy" character varying(36), "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT now(), "updatedBy" character varying(36), "isDeleted" boolean NOT NULL DEFAULT false, "code" character varying, "packingUnitId" uuid NOT NULL, "deliveryNoteId" uuid NOT NULL, "packingDate" TIMESTAMP WITH TIME ZONE, "packingStatus" character varying NOT NULL DEFAULT 'PACKAGING_PENDING', CONSTRAINT "PK_76f3ca698e783c99b5afb790ba1" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_023dd422bc45fbddc2e634b047" ON "delivery_note_packing" ("packingUnitId") `);
        await queryRunner.query(`CREATE INDEX "IDX_3cbf7912a9e9dc033efa6e46d4" ON "delivery_note_packing" ("deliveryNoteId") `);
        await queryRunner.query(`CREATE TABLE "delivery_note_packing_ref" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "createdBy" character varying(36), "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT now(), "updatedBy" character varying(36), "isDeleted" boolean NOT NULL DEFAULT false, "poId" uuid NOT NULL, "packingId" uuid NOT NULL, CONSTRAINT "PK_71f86f30f7066fee8f21c955d05" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_3147d168afd043429a648280b9" ON "delivery_note_packing_ref" ("poId") `);
        await queryRunner.query(`CREATE INDEX "IDX_c6308fcccc6619b5fbc39ee857" ON "delivery_note_packing_ref" ("packingId") `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_c6308fcccc6619b5fbc39ee857"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_3147d168afd043429a648280b9"`);
        await queryRunner.query(`DROP TABLE "delivery_note_packing_ref"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_3cbf7912a9e9dc033efa6e46d4"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_023dd422bc45fbddc2e634b047"`);
        await queryRunner.query(`DROP TABLE "delivery_note_packing"`);
    }

}
