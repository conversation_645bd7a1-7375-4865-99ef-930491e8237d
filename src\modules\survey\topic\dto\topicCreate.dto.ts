import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'

export class TopicCreateDto {
  @ApiProperty({ description: 'Tên chủ đề' })
  @IsNotEmpty({ message: 'tên chủ đề không được trống' })
  @IsString()
  name: string

  @ApiProperty({ description: 'Id danh mục' })
  @IsString()
  categoryId: string

  @ApiPropertyOptional({ description: 'Là chủ đề đuôi' })
  isTail?: boolean

  @ApiProperty({ description: '<PERSON>hi chú' })
  description: string
}
