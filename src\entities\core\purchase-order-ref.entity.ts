import { Column, Entity, Index } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { BaseEntity } from './base.entity'
import { NSPo } from '../../constants';

/** <PERSON><PERSON>ng tham chiếu PO với SO */
@Entity('purchase_order_ref')
@Index(['poId', 'poIdRef'])
export class PurchaseOrderRefEntity extends BaseEntity {
    @ApiProperty({ description: 'Id PO cha' })
    @Column("uuid")
    @Index()
    poId: string;

    @ApiProperty({ description: 'Id PO con' })
    @Column("uuid")
    poIdRef: string
}

