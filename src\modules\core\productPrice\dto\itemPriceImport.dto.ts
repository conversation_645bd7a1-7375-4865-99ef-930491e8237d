import { IsNotEmpty, IsString } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'

export class ItemPriceImportDto {
  @ApiProperty({ description: 'Mã ' })
  @IsNotEmpty({ message: '<PERSON>ã không được để trống' })
  @IsString()
  code: string

  @IsNotEmpty()
  @IsNotEmpty({ message: '<PERSON><PERSON><PERSON> bán không được để trống' })
  priceSell: number

  @IsNotEmpty()
  @IsNotEmpty({ message: '<PERSON><PERSON><PERSON> gốc không được để trống' })
  priceOriginal: number
}
