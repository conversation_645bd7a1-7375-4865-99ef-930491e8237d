import { Repository } from 'typeorm'
import { CustomRepository } from '../../typeorm'
import {
  PurchaseOrderEntity,
  PurchaseOrderItemEntity,
  PurchaseOrderHistory,
  PurchaseOrderSalesOrderEntity,
  PurchaseOrderApproveEntity,
  PurchaseOrderChildEntity
} from '../../entities/core/'
import { BaseRepository } from '../base.repo'

@CustomRepository(PurchaseOrderEntity)
export class PurchaseOrderRepository extends BaseRepository<PurchaseOrderEntity> { }

@CustomRepository(PurchaseOrderItemEntity)
export class PurchaseOrderItemRepository extends Repository<PurchaseOrderItemEntity> { }

@CustomRepository(PurchaseOrderHistory)
export class PurchaseOrderHistoryRepository extends Repository<PurchaseOrderHistory> { }

@CustomRepository(PurchaseOrderSalesOrderEntity)
export class PurchaseOrderSaleOrderRepository extends Repository<PurchaseOrderSalesOrderEntity> { }

@CustomRepository(PurchaseOrderApproveEntity)
export class PurchaseOrderApproveRepository extends Repository<PurchaseOrderApproveEntity> { }

@CustomRepository(PurchaseOrderChildEntity)
export class PurchaseOrderChildRepository extends Repository<PurchaseOrderChildEntity> { }
