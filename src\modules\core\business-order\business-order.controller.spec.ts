import { Test, TestingModule } from '@nestjs/testing';
import { BusinessOrderController } from './business-order.controller';
import { BusinessOrderService } from './business-order.service';

describe('BusinessOrderController', () => {
  let controller: BusinessOrderController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [BusinessOrderController],
      providers: [BusinessOrderService],
    }).compile();

    controller = module.get<BusinessOrderController>(BusinessOrderController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
