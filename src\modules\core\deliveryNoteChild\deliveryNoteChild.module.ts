import { Module } from "@nestjs/common";
import { TypeOrmExModule } from "../../../typeorm";
import { DeliveryNoteChildDetailRepository, DeliveryNoteChildRepository, DeliveryNoteLastMileProductRepository } from "../../../repositories/core/deliveryNoteChild.repository";
import { DeliveryNoteChildService } from "./deliveryNoteChild.service";
import { DeliveryNoteChildController } from "./deliveryNoteChild.controller";
import { CityRepository, DeliveryNoteRepository, DistrictRepository, EmployeeRepository, InboundDetailRepository, InboundRepository, ItemComboRepository, ItemDetailRepository, ItemPriceRepository, ItemRepository, PurchaseOrderItemRepository, PurchaseOrderRepository, PurchaseOrderSaleOrderRepository, SupplierRepository, UserRepository, WardRepository, WarehouseProductDetailRepository, WarehouseRepository } from "../../../repositories";
import { InboundModule } from "../../wms/inbound/inbound.module";
import { DeliveryNoteChildPublicController } from "./deliveryNoteChildPublic.controller";
import { OutboundModule } from "../../wms/outbound/outbound.module";
@Module({
  imports: [TypeOrmExModule.forCustomRepository([
    DeliveryNoteChildRepository,
    PurchaseOrderItemRepository,
    WarehouseRepository,
    ItemPriceRepository,
    ItemRepository,
    PurchaseOrderRepository,
    DeliveryNoteChildDetailRepository,
    DeliveryNoteRepository,
    SupplierRepository,
    InboundRepository,
    InboundDetailRepository,
    ItemDetailRepository,
    WarehouseProductDetailRepository,
    DeliveryNoteLastMileProductRepository,
    UserRepository,
    EmployeeRepository,
    CityRepository,
    WardRepository,
    ItemComboRepository,
    DistrictRepository,
    PurchaseOrderSaleOrderRepository
  ]),
    InboundModule,
    OutboundModule
  ],

  controllers: [DeliveryNoteChildController, DeliveryNoteChildPublicController],
  providers: [DeliveryNoteChildService],
  exports: [DeliveryNoteChildService]
})
export class DeliveryNoteChildModule { }