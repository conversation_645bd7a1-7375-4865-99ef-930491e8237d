import { Controller, UseGuards, Post, Body, Req } from '@nestjs/common'
import { PaginationDto, UserDto } from '../../../dto'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { Request as IRequest } from 'express'
import { CheckInventoryDetailService } from './checkInventoryDetail.service'
import { JwtAuthGuard } from '../../common/guards'
import { CurrentUser } from '../../common'

@ApiBearerAuth()
@ApiTags('CheckInventory')
@UseGuards(JwtAuthGuard)
@Controller('check_inventory_detail')
export class CheckInventoryDetailController {
  constructor(private readonly service: CheckInventoryDetailService) { }
  @Post('pagination')
  async pagination(@Body() data: PaginationDto, @Req() req: IRequest) {
    return await this.service.pagination(data, req)
  }
}
