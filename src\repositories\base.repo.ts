import { FindOneOptions, Repository } from 'typeorm'
import { PageRequest, PageResponse } from '../dto'

const pageSizeDefault = 10
export class BaseRepository<Entity> extends Repository<Entity> {
  async findPagination(options: FindOneOptions<Entity> = {}, pageRequest: PageRequest) {
    const { pageIndex = 1, pageSize = 20 } = pageRequest
    return super
      .findAndCount({
        ...options,
        skip: (pageIndex - 1) * pageSize,
        take: pageSize,
      })
      .then(([data, total]) => ({ data, total }))
  }
  async queryPagination<T = any, F = any>(sqlRoot: string, pageRequest: PageRequest): Promise<PageResponse<T>> {
    let { pageIndex = 1, pageSize = pageSizeDefault } = pageRequest
    if (!pageIndex) {
      pageIndex = 1
    }
    if (!pageSize) {
      pageSize = pageSizeDefault
    }
    let sqlData = ` select * from ( ${sqlRoot} ) temp  `
    const sqlTotal = ` select CAST( count(*) as integer ) as total from ( ${sqlRoot} ) temp  `
    sqlData = sqlData + ` limit ${pageSize} offset  ${(pageIndex - 1) * pageSize}  `
    const res = await Promise.all([this.query(sqlData), this.query(sqlTotal)])
    return {
      data: res[0],
      total: res[1][0].total,
    }
  }

  async queryOne<T = any>(query: string, parameters?: any[]): Promise<T> {
    try {
      const res = await this.query(query, parameters)
      if (!res || !(res instanceof Array) || res.length === 0) {
        return undefined
      }
      return res[0] as T
    } catch (error) {
      return undefined
    }
  }
}
