import { Body, Controller, Post, UseGuards } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import { TopicCreateDto, TopicCreateExcelDto, TopicCreateMasterDataDto, TopicUpdateDto } from './dto'
import { TopicService } from './topic.service'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { CurrentUser } from '../../common/decorators'
import { JwtAuthGuard } from '../../common'
import { ApeAuthGuard } from '../common/guards'
@ApiTags('Topic')
@Controller('topic')
export class TopicController {
  constructor(private readonly service: TopicService) {}

  @ApiOperation({ summary: 'Hàm tìm kiếm' })
  @Post('find')
  public async find(@Body() data: { isDeleted?: boolean }) {
    return await this.service.find(data)
  }

  @ApiOperation({ summary: '<PERSON>àm tìm kiếm kèm khảo sát' })
  @Post('find_with_survey')
  public async findWithSurvey(@Body() data: { isDeleted?: boolean }) {
    return await this.service.findWithSurvey(data)
  }

  @ApiOperation({ summary: 'Hàm phân trang' })
  @Post('pagination')
  public async pagination(@Body() data: PaginationDto) {
    return await this.service.pagination(data)
  }

  @ApiOperation({ summary: 'Hàm tạo mới' })
  @UseGuards(JwtAuthGuard)
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: TopicCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Hàm cập nhật' })
  @UseGuards(JwtAuthGuard)
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: TopicUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Hàm cập nhật trạng thái hoạt động' })
  @Post('update_active')
  @UseGuards(JwtAuthGuard)
  public async updateActive(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateActive(user, data.id)
  }

  @ApiOperation({ summary: 'Hàm tạo mới bằng excel' })
  @UseGuards(JwtAuthGuard)
  @Post('create_data_by_excel')
  public async createDataByExcel(@CurrentUser() user: UserDto, @Body() data: TopicCreateExcelDto[]) {
    return await this.service.createDataByExcel(user, data)
  }

  @ApiOperation({ summary: 'Danh sách danh mục từ master data' })
  @Post('create_master_data')
  public async createMasterData(@Body() data: TopicCreateMasterDataDto[]) {
    return await this.service.createMasterData(data)
  }
  @ApiOperation({ summary: 'chi tiết' })
  @Post('find_detail')
  public async findDetail(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.findDetail(user, data)
  }
}
