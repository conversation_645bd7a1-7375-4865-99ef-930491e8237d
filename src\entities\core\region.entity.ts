import { BaseEntity } from './base.entity'
import { Entity, Column, OneToMany, ManyToOne, JoinColumn } from 'typeorm'
import { RegionCityEntity } from './regionCity.entity'
import { RegionDistrictEntity } from './regionDistrict.entity'
import { RegionWardEntity } from './regionWard.entity'

@Entity('region')
export class RegionEntity extends BaseEntity {
  @Column({ type: 'varchar', length: 250, nullable: true })
  code: string

  @Column({ type: 'varchar', length: 250, nullable: false })
  name: string

  /**
   * chọn 1 tỉnh/thành → Cho phép chọn nhiều quận huyện
   * chọn nhiều tỉnh/thành → Không cho phép chọn quận huyện
   * Quận/huyện tương tự
   */

  /** Danh sách */
  @OneToMany(() => RegionCityEntity, (p) => p.region)
  cities: Promise<RegionCityEntity[]>

  /** Danh sách */
  @OneToMany(() => RegionDistrictEntity, (p) => p.region)
  districts: Promise<RegionDistrictEntity[]>

  /** Danh sách */
  @OneToMany(() => RegionWardEntity, (p) => p.region)
  wards: Promise<RegionWardEntity[]>
}
