import { SupplierRepository } from './../../../repositories/core/supplier.repository';
import { Injectable } from "@nestjs/common";
import { OrderConfigRepository } from "../../../repositories/core/orderConfig.repository";
import { CreateOrderConfigDto, UpdateOrderConfigDto, ListOrderConfigDto } from "./dto/orderConfig.dto";
import { In } from 'typeorm';

@Injectable()
export class OrderConfigService {
  constructor(
    private readonly orderConfigRepo: OrderConfigRepository,
    private readonly supplierRepo: SupplierRepository
  ) { }

  public async listConfig(body: ListOrderConfigDto) {
    const { supplierId, skip, take } = body;
    const whereCon: any = {};
    if (supplierId) whereCon.supplierId = supplierId;
    const [data, total] = await this.orderConfigRepo.findAndCount({
      where: whereCon,
      take,
      skip
    });
    const supplierIds = data.map(val => val.supplierId);
    const supplierLst = await this.supplierRepo.find({ where: { id: In(supplierIds) } });
    const mapData = data.map((val) => {
      const sup = supplierLst.find(s => s.id == val.supplierId);
      return {
        ...val,
        supplierName: sup.name,
        supplierCode: sup.code,
        supplierPhone: sup.phone
      }
    })

    return { data: mapData, total }
  }

  public async createConfig(body: CreateOrderConfigDto) {
    return await this.orderConfigRepo.save(body);
  }

  public async updateConfig(body: UpdateOrderConfigDto) {
    return await this.orderConfigRepo.save(body);
  }


}