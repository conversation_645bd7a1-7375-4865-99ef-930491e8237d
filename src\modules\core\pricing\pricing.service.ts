import { Injectable } from '@nestjs/common'
import { CreatePricingDto } from './dto/create-pricing.dto'
import { UpdatePricingDto } from './dto/update-pricing.dto'
import { PricingRepository } from '../../../repositories/core/pricing.repository'
import { coreHelper } from '../../../helpers'
import { ListPricingDto } from './dto/list-pricing.dto'
import { Between, ILike } from 'typeorm'
import { UserDto } from '../../../dto'
import { ItemRepository, SupplierRepository } from '../../../repositories'
import { PricingEntity } from '../../../entities/core/pricing.entity'
import { ItemEntity, SupplierEntity } from '../../../entities'
import { Import } from 'aws-sdk/clients/datazone'
import { ImportExcelPricingDto } from './dto/import-excel-pricing.dto'

@Injectable()
export class PricingService {
  constructor(
    private readonly pricingRepo: PricingRepository,
    private readonly itemRepo: ItemRepository,
    private readonly supplierRepo: SupplierRepository,
  ) {}

  //Tạo bảng giá mới
  async create(params: CreatePricingDto, user: UserDto) {
    const { price, itemId, supplierId, description } = params

    const item = await this.itemRepo.findOne({ where: { id: itemId, isDeleted: false } })
    if (!item) throw new Error('Không tìm thấy sản phẩm')

    const supplier = await this.supplierRepo.findOne({ where: { id: supplierId, isDeleted: false } })
    if (!supplier) throw new Error('Không tìm thấy nhà cung cấp')
    //check xem đã tồn tại bảng giá cho sản phẩm này chưa
    const checkPricing = await this.pricingRepo.findOne({
      where: { itemId, supplierId, isDeleted: false },
      relations: { supplier: true },
    })
    if (checkPricing) throw new Error(`Bảng giá đã tồn tại cho nhà cung cấp ${(await checkPricing.supplier).name}`)

    const newPricing = this.pricingRepo.create({ price, itemId, supplierId, description })

    newPricing.code = coreHelper.generatePOString(await this.pricingRepo.count(), 'PC')
    newPricing.createdBy = user.id
    newPricing.updatedBy = user.id
    const res = await this.pricingRepo.save(newPricing)
    return { message: 'Thêm mới bảng giá thành công', data: res }
  }

  async update(params: UpdatePricingDto, user: UserDto) {
    const { id, itemId, supplierId } = params

    const item = await this.itemRepo.findOne({ where: { id: itemId, isDeleted: false } })
    if (!item) throw new Error('Không tìm thấy sản phẩm')

    const supplier = await this.supplierRepo.findOne({ where: { id: supplierId, isDeleted: false } })
    if (!supplier) throw new Error('Không tìm thấy nhà cung cấp')

    //check xem bảng giá có tồn tại không
    const pricing = await this.pricingRepo.findOne({ where: { id, isDeleted: false } })
    if (!pricing) throw new Error('Không tìm thấy bảng giá')

    //check xem đã tồn tại bảng giá cho sản phẩm này chưa nếu có sự thay đổi trong itemId và supplierId
    if (pricing.supplierId != supplierId || pricing.itemId != itemId) {
      const checkPricing = await this.pricingRepo.findOne({
        where: { itemId, supplierId, isDeleted: false },
        relations: { supplier: true },
      })
      if (checkPricing) throw new Error(`Bảng giá đã tồn tại cho nhà cung cấp ${(await checkPricing.supplier).name}`)
    }

    await this.pricingRepo.save({ ...params, updatedBy: user.id })

    return { message: 'Cập nhật bảng giá thành công', data: pricing }
  }

  async list(params: ListPricingDto) {
    const { itemId, itemCode, itemName, supplierId, price, isDeleted, updatedAtFrom, updatedAtTo, pageIndex = 1, pageSize = 10 } = params
    const whereCon: any = {}
    if (itemId) whereCon.itemId = itemId
    if (itemCode) whereCon.item = { code: ILike(`%${itemCode}%`) }
    if (itemName) whereCon.item = { ...whereCon.item, name: ILike(`%${itemName}%`) }
    if (supplierId) whereCon.supplierId = supplierId
    if (price) whereCon.price = price
    if (isDeleted != undefined) whereCon.isDeleted = isDeleted
    if (updatedAtFrom && updatedAtTo) whereCon.updatedAt = Between(updatedAtFrom, updatedAtTo)

    const [data, total]: any[] = await this.pricingRepo.findAndCount({
      where: whereCon,
      skip: (pageIndex - 1) * pageSize,
      take: pageSize,
      relations: { item: true, supplier: true },
      order: { isDeleted: 'ASC', updatedAt: 'DESC' },
    })

    for (let pricing of data) {
      const item = pricing.__item__
      const supplier = pricing.__supplier__
      const unit = await item.unit
      pricing.itemUnit = unit?.name
      pricing.itemName = item.name
      pricing.itemCode = item.code
      pricing.supplierName = supplier.name

      delete pricing.__item__
      delete pricing.__supplier__
    }
    return { data, total }
  }

  async listPricingSupplier(params: ListPricingDto, user: UserDto) {
    const { itemId, itemCode, itemName, price, updatedAtFrom, updatedAtTo, pageIndex = 1, pageSize = 10 } = params
    const whereCon: any = {}
    if (!user.supplierId) {
      throw new Error('Bạn không phải nhà cung cấp')
    }
    whereCon.supplierId = user.supplierId
    whereCon.isDeleted = false

    if (itemId) whereCon.itemId = itemId
    if (itemCode) whereCon.item = { code: ILike(`%${itemCode}%`) }
    if (itemName) whereCon.item = { ...whereCon.item, name: ILike(`%${itemName}%`) }
    if (price) whereCon.price = price
    if (updatedAtFrom && updatedAtTo) whereCon.updatedAt = Between(updatedAtFrom, updatedAtTo)

    const [data, total]: any[] = await this.pricingRepo.findAndCount({
      where: whereCon,
      skip: (pageIndex - 1) * pageSize,
      take: pageSize,
      relations: { item: true, supplier: true },
      order: { isDeleted: 'ASC', updatedAt: 'DESC' },
    })

    for (let pricing of data) {
      const item = pricing.__item__
      const supplier = pricing.__supplier__
      const unit = await item.unit
      pricing.itemUnit = unit?.name
      pricing.itemName = item.name
      pricing.itemCode = item.code
      pricing.supplierName = supplier.name

      delete pricing.__item__
      delete pricing.__supplier__
    }
    return { data, total }
  }

  async setActive(id: string, user: UserDto) {
    const pricing = await this.pricingRepo.findOne({ where: { id } })
    if (!pricing) throw new Error('Không tìm thấy bảng giá')

    await this.pricingRepo.update({ id }, { isDeleted: !pricing.isDeleted, updatedBy: user.id })
    return { message: 'Ngưng hoạt động bảng giá thành công' }
  }

  async importExcel(data: ImportExcelPricingDto[], user: UserDto) {
    await this.pricingRepo.manager.transaction(async (trans) => {
      const pricingRepo = trans.getRepository(PricingEntity)
      const itemRepo = trans.getRepository(ItemEntity)
      const supplierRepo = trans.getRepository(SupplierEntity)
      const dicItem: any = {}
      {
        const lstItem = await itemRepo.find({ where: { isDeleted: false }, select: { id: true, code: true } })
        lstItem.forEach((c) => (dicItem[c.code] = c))
      }
      const dicSupplier: any = {}
      {
        const lstSupplier = await supplierRepo.find({ where: { isDeleted: false }, select: { id: true, code: true } })
        lstSupplier.forEach((c) => (dicSupplier[c.code] = c))
      }
      const lstInsert: any[] = []
      const lstUpdate: any[] = []
      for (let item of data) {
        if (!dicItem[item.itemCode]) throw new Error(`Không tìm thấy sản phẩm ${item.itemCode}`)
        if (!dicSupplier[item.supplierCode]) throw new Error(`Không tìm thấy nhà cung cấp ${item.supplierCode}`)

        //check pricing exist
        const checkPricing = await pricingRepo.findOne({
          where: { itemId: dicItem[item.itemCode].id, supplierId: dicSupplier[item.supplierCode].id, isDeleted: false },
        })

        if (checkPricing) {
          checkPricing.price = item.price
          checkPricing.updatedBy = user.id
          checkPricing.updatedAt = new Date()
          lstUpdate.push(checkPricing)
          continue
        }

        let itemId = dicItem[item.itemCode].id
        let supplierId = dicSupplier[item.supplierCode].id
        let code = coreHelper.generatePOString(await pricingRepo.count(), 'PC')
        const newPricing = pricingRepo.create({ ...item, code, createdBy: user.id, updatedBy: user.id, itemId, supplierId })
        newPricing.createdBy = user.id
        newPricing.updatedBy = user.id
        lstInsert.push(newPricing)
      }
      await pricingRepo.insert(lstInsert)
      await pricingRepo.save(lstUpdate)
    })
    return { message: 'Import thành công' }
  }
}
