import { Body, Controller, Post, Req, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { Request as IRequest } from 'express'
// import { surveyAuthApiHelper } from '../../helpers'
import { ApeAuthGuard } from '../common/guards'
import { ImportExcelDto } from './dto/importExcel.dto'
import { SurveyMemberDeleteDto } from './dto/surveyMemberDelete.dto'
import { SurveyMemberService } from './surveyMember.service'
import { CurrentUser } from '../../common/decorators'
import { PaginationDto, UserDto } from '../../../dto'
import { EmployeeEntity, SurveyMemberEntity } from '../../../entities'
import { JwtAuthGuard } from '../../common/guards'

@UseGuards(ApeAuthGuard)
@ApiBearerAuth()
@ApiTags('SurveyMember')
@Controller('survey_member')
export class SurveyMemberController {
  constructor(private service: SurveyMemberService) {}

  @ApiOperation({ summary: 'Danh sách phân trang' })
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Tìm ds nhân viên thuộc phiếu khảo sát' })
  @Post('find_by_survey')
  public async findBySurvey(@CurrentUser() user: UserDto, @Body() data: { surveyId: string }) {
    return await this.service.findBySurvey(user, data)
  }

  @ApiOperation({ summary: 'Lấy ds nhân viên đã hoàn thành bài khảo sát' })
  @Post('get_survey_members_done')
  public async getSurveyMembersDone(@CurrentUser() user: UserDto, @Body() data: { surveyId: string }) {
    return await this.service.getSurveyMembersDone(user, data)
  }
}
