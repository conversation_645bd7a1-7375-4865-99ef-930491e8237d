import { <PERSON><PERSON><PERSON>, Column, ManyTo<PERSON>ne, <PERSON><PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm'
import { BaseEntity } from './base.entity'
import { RegionEntity } from './region.entity'
import { DistrictEntity } from './district.entity'

@Entity('region_district')
export class RegionDistrictEntity extends BaseEntity {
  /** Vùng */
  @Column({ type: 'varchar', nullable: false })
  regionId: string
  @ManyToOne(() => RegionEntity, (p) => p.cities)
  @JoinColumn({ name: 'regionId', referencedColumnName: 'id' })
  region: Promise<RegionEntity>

  /** quận / huyện */
  @Column({ type: 'varchar', nullable: false })
  districtId: string
  @ManyToOne(() => DistrictEntity, (p) => p.id)
  @JoinColumn({ name: 'districtId', referencedColumnName: 'id' })
  district: Promise<DistrictEntity>
}
