import { Column, Entity, Index } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { BaseEntity } from './base.entity'
import { NSPo } from '../../constants';

// Phiếu đóng gói
@Entity('delivery_note_packing')
export class DeliveryNotPackingEntity extends BaseEntity {
  @ApiProperty({ description: "Code" })
  @Column("varchar", { nullable: true })
  code: string;

  @ApiProperty({ description: "Đơn vị đóng gói" })
  @Column("uuid")
  @Index()
  packingUnitId: string;

  @ApiProperty({ description: "Phiếu giao nhận tổng" })
  @Column("uuid")
  @Index()
  deliveryNoteId: string;

  @ApiProperty({ description: "Kho 3PL" })
  @Column("uuid", { nullable: true })
  @Index()
  warehouseId: string

  @ApiProperty({ description: "Ngày đóng gói" })
  @Column("timestamptz", { nullable: true })
  packingDate: Date

  @ApiPropertyOptional({
    description: `Trạng thái Packing ${Object.values(NSPo.EDeliveryTracking).join(' | ')}`,
    enum: NSPo.EDeliveryTracking,
    default: NSPo.EDeliveryTracking.PACKAGING_PENDING,
  })
  @Column({ default: NSPo.EDeliveryTracking.PACKAGING_PENDING })
  packingStatus: NSPo.EDeliveryTracking;
}