import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString, IsEmail } from 'class-validator'

export class EmployeeCreateDto {
  @ApiProperty({ description: 'Mã nhân viên' })
  @IsNotEmpty({ message: 'Mã nhân viên không được trống' })
  code: string

  @ApiProperty({ description: 'Họ và tên' })
  @IsNotEmpty({ message: 'Họ tên không được trống' })
  @IsString()
  name: string

  @ApiProperty({ description: 'Email' })
  @IsNotEmpty()
  @IsEmail(undefined, { message: 'Email không hợp lệ' })
  email: string

  @ApiProperty({ description: 'Id phòng ban' })
  @IsNotEmpty({ message: 'Id phòng ban không được trống' })
  @IsString()
  departmentId: string

  @ApiProperty({ description: '<PERSON><PERSON> tả' })
  @IsOptional()
  description: string

  @ApiProperty({ description: '<PERSON><PERSON> điện thoại' })
  @IsNotEmpty({ message: '<PERSON><PERSON> điện thoại không được trống' })
  @IsString()
  phone: string

  @ApiProperty({ description: 'Tài khoản' })
  @IsNotEmpty({ message: 'Tên đăng nhập không được trống' })
  @IsString()
  username: string

  @ApiProperty({ description: 'Mật khẩu' })
  @IsNotEmpty({ message: 'Mật khẩu không được trống' })
  @IsString()
  password: string

  @ApiPropertyOptional({ description: 'Địa chỉ' })
  @IsOptional()
  address?: string

  @ApiPropertyOptional({ description: 'Tĩnh/Thành phố' })
  @IsOptional()
  cityId?: string;

  @ApiPropertyOptional({ description: 'Phường/Xã' })
  @IsOptional()
  wardId?: string;

  @ApiPropertyOptional({ description: 'Quận/Huyện' })
  @IsOptional()
  districtId?: string;

  @IsOptional()
  @ApiPropertyOptional({ description: 'Quyền quản trị viên' })
  isAdmin?: boolean;
}
