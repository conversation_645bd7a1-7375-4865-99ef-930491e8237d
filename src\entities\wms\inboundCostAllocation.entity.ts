import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from '../core/base.entity'
import { InboundEntity } from './inbound.entity'
import { InboundDetailEntity } from './inboundDetail.entity'

@Entity('inbound_cost_allocation')
export class InboundCostAllocationEntity extends BaseEntity {
  /** Chi phí phân bổ cũ */
  @Column({ nullable: true, default: 0, type: 'float' })
  costAllocationOld: number

  /** Chi phí phân bổ mới */
  @Column({ nullable: true, default: 0, type: 'float' })
  costAllocationNew: number

  /** Id PNK */
  @Column({ type: 'varchar', nullable: true })
  inboundId?: string
  @ManyToOne(() => InboundEntity, (p) => p.costAllocations)
  @JoinColumn({ name: 'inboundId', referencedColumnName: 'id' })
  inbound: Promise<InboundEntity>

  /** Id chi tiế<PERSON> */
  @Column({ type: 'varchar', nullable: true })
  inboundDetailId?: string
  @ManyToOne(() => InboundDetailEntity, (p) => p.costAllocations)
  @JoinColumn({ name: 'inboundDetailId', referencedColumnName: 'id' })
  inboundDetail: Promise<InboundDetailEntity>

  /** Chi phí phân bổ cũ */
  @Column({ nullable: true, default: 0, type: 'float' })
  costAllocationDetailOld: number

  /** Chi phí phân bổ mới */
  @Column({ nullable: true, default: 0, type: 'float' })
  costAllocationDetailNew: number

  /** Ghi chú, ví dụ `Duyệt PNK [${detail.code}]<br>Nhập thêm: ${detail.totalQuantity}<br>HSD ${dateStr}` */
  @Column({ type: 'text', nullable: true })
  description: string
}
