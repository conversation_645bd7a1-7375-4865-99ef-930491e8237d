import { Injectable } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import * as AWS from 'aws-sdk'
import { S3 } from 'aws-sdk'
import { customAlphabet } from 'nanoid'
import * as sharp from 'sharp'
const nanoid = customAlphabet('0123456789abcdefghijklmnopqrstuvwxyz', 10)

@Injectable()
export class UploadFileService {
  AWS_S3_BUCKET_NAME: string
  s3: AWS.S3
  MAX_UPLOAD_FILE_SIZE: number
  constructor(private readonly configService: ConfigService) {
    this.AWS_S3_BUCKET_NAME = this.configService.get<string>('AWS_S3_BUCKET_NAME') || ''
    const ACCESS_KEY_ID = this.configService.get<string>('AWS_S3_ACCESS_KEY_ID')
    const SECRET_ACCESS_KEY = this.configService.get<string>('AWS_S3_SECRET_ACCESS_KEY')

    this.s3 = new AWS.S3({
      accessKeyId: ACCESS_KEY_ID,
      secretAccessKey: SECRET_ACCESS_KEY,
    })

    this.MAX_UPLOAD_FILE_SIZE = Number(this.configService.get<string>('MAX_UPLOAD_FILE_SIZE')) || 1024 * 5
  }

  async uploadSingle(file: Express.Multer.File) {
    // file.buffer = await this.compressImage(file.buffer)
    const current = new Date()
    let temp: string[] = file.originalname ? file.originalname.split('.') : []
    let ext = temp.length > 1 ? `.${temp[temp.length - 1]}` : ''
    let LINK_UPLOAD_S3 = process.env.LINK_UPLOAD_S3
    let fileName = `${current.getFullYear()}${current.getMonth() + 1}${current.getDate()}-${nanoid()}${ext}`
    const key = `${LINK_UPLOAD_S3}/${fileName}`
    const params = {
      Bucket: this.AWS_S3_BUCKET_NAME,
      Key: key, // File name you want to save as in S3
      Body: file.buffer,
      ACL: 'public-read',
      // ContentType: 'image/jpeg',
    }
    return new Promise<any>((resolve, reject) => {
      this.s3.upload(params, (err: any, data: any) => {
        if (err) {
          reject(err)
        } else {
          resolve({ fileName, fileUrl: data.Location })
        }
      })
    })
  }

  async uploadFile(files: Array<Express.Multer.File>) {
    const lstTask = []
    for (const file of files) {
      lstTask.push(this.uploadSingle(file))
    }

    return Promise.all(lstTask)
  }

  async compressImage(buffer: Buffer, targetMaxByte = this.MAX_UPLOAD_FILE_SIZE) {
    const image = sharp(buffer)
    let metadata = await image.metadata()

    const extension = metadata.format
    const quality = 80
    let newBuffer = buffer
    // resize image before compress
    const MAX_WIDTH = 1000

    if (metadata.width > MAX_WIDTH) {
      const newHeight = Math.round((MAX_WIDTH / metadata.width) * metadata.height)
      image.resize(MAX_WIDTH, newHeight, { fit: 'inside' })
    }

    metadata = await image.metadata()

    if (metadata.size > targetMaxByte) {
      const newQuality = Math.round((targetMaxByte / metadata.size) * quality) || 80
      if (newQuality > 100) {
        return buffer
      }
      if (extension === 'jpeg' || extension === 'jpg') {
        newBuffer = await image.jpeg({ quality: newQuality, mozjpeg: true }).rotate().toBuffer()
      }
      if (extension === 'png') {
        newBuffer = await image.png({ quality: newQuality }).rotate().toBuffer()
      }
    }
    return newBuffer
  }

  async uploadSinglePDF(data: any) {
    const current = new Date()
    let LINK_UPLOAD_S3 = process.env.LINK_UPLOAD_S3
    let fileName = `${current.getFullYear()}${current.getMonth() + 1}${current.getDate()}-${nanoid()}.pdf`
    const key = `${LINK_UPLOAD_S3}/${fileName}`
    const params: AWS.S3.Types.PutObjectRequest = {
      Bucket: this.AWS_S3_BUCKET_NAME,
      Key: key, // File name you want to save as in S3
      Body: data,
      ACL: 'public-read',
    }
    return new Promise<any>((resolve, reject) => {
      this.s3.upload(params, (err: any, data: any) => {
        if (err) {
          reject(err)
        } else {
          resolve({ fileName, fileUrl: data.Location })
        }
      })
    })
  }
}
