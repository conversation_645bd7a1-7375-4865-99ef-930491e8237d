import { Injectable, BadRequestException, NotFoundException, UnauthorizedException, ConflictException } from '@nestjs/common'
import { Request as IRequest } from 'express'
import { JwtService } from '@nestjs/jwt'
import { CompanyRepository, SupplierRepository, SurveyMemberRepository, UserRepository, UserSurveyRepository } from '../../../repositories'
import { UpdatePasswordDto } from './dto/update-password.dto'
import { UserDto } from '../../../dto/user.dto'
import { In, IsNull } from 'typeorm'
import { ACTION_SUCCESS, enumData, PWD_SALT_ROUNDS } from '../../../constants'
import {
  CreateUserSurveyDto,
  ForgetAdminDto,
  UpdateEmailAdminto,
  UpdatePasswordAdminto,
  UpdateRoleAdminto,
  UpdateStatusUserSurveyDto,
  UpdateUsernameDto,
  UserLoginDto,
} from './dto'
import { hash } from 'bcrypt'
import { plainToClass } from 'class-transformer'
import { nanoid } from 'nanoid'
import { SurveyMemberEntity, UserSurveyEntity } from '../../../entities'
import { LoginAppDto, ForgetAppDto, RequestFCMTokenDto, UpdateIsAllowMessDto } from '../../survey/mobileApp/dto'
import { UserRegisterDto, UserUpdateDto, UserUpdatePasswordDto } from './dto/register.dto'
import { EmailService } from '../../survey/email/email.service'
import { LoginAppFarmDto } from '../../survey/mobileApp/dto/login-app-farm.dto'
import { size } from 'mathjs'
import axios from 'axios'
import { NSTenantType } from '../../../constants/NSTenantType'
import { apeAuthenApiHelper } from '../../../helpers/apeAuthenApiHelper'

@Injectable()
export class AuthService {
  constructor(
    private readonly jwtService: JwtService,
    private readonly repo: UserRepository,
    private readonly supplierRepo: SupplierRepository,
    private readonly userSurveyRepo: UserSurveyRepository,
    private surveyMemberRepo: SurveyMemberRepository,
    private companyRepo: CompanyRepository,
    private emailService: EmailService,
  ) {}

  public async login(data: UserLoginDto) {
    const userEntity = await this.repo.findOne({
      where: {
        username: data.username,
        type: In([enumData.UserType.Employee.code, enumData.UserType.Admin.code]),
        isDeleted: false,
      },
    })
    if (!userEntity) throw new NotFoundException('Tài khoản không tồn tại!')
    if (userEntity.isDeleted) throw new UnauthorizedException('Tài khoản đã ngưng hoạt động!')
    // if (userEntity.type !== enumData.UserType.Admin.code) throw new UnauthorizedException('Tài khoản không có quyền truy cập!')

    // check pw
    const isPasswordMatch = await userEntity.comparePassword(data.password)
    if (!isPasswordMatch) throw new UnauthorizedException('Mật khẩu không đúng!')

    const { password, ...userWithoutPassword } = userEntity
    const roles = await userEntity.permission
    const returnRoles = roles.map((p) => p.roleCode)

    const enumReturn = JSON.parse(JSON.stringify(enumData))

    return {
      permission: returnRoles,
      enumData: enumReturn,
      accessToken: this.jwtService.sign({ uid: userEntity.id }),
      username: userEntity.username,
      user: userWithoutPassword,
      isAdmin: userEntity.type == enumData.UserType.Admin.code,
    }
  }

  //#region Supplier
  // Login cho nhà cung cấp
  public async loginSupplier(data: UserLoginDto) {
    const supplier = await this.repo.findOne({
      where: {
        username: data.username,
        type: enumData.UserType.Supplier.code,
      },
    })
    if (!supplier) throw new NotFoundException('Tài khoản không tồn tại!')
    if (supplier.isDeleted) throw new UnauthorizedException('Tài khoản đã ngưng hoạt động!')

    // check pw
    const isPasswordMatch = await supplier.comparePassword(data.password)
    if (!isPasswordMatch) throw new UnauthorizedException('Mật khẩu không đúng!')

    const { password, ...userWithoutPassword } = supplier

    const enumReturn = JSON.parse(JSON.stringify(enumData))

    return {
      permisson: [],
      accessToken: this.jwtService.sign({ uid: supplier.id }),
      username: supplier.username,
      user: userWithoutPassword,
      enumData: enumReturn,
      isAdmin: false,
    }
  }

  //#endregion

  /** Tạo user Survey */
  public async createUserSurvey(user: UserDto, data: CreateUserSurveyDto, type?: string) {
    // check username tồn tại hay chưa
    const objCheckUsername = await this.repo.findOne({
      where: { username: data.username },
      select: { id: true },
    })
    if (objCheckUsername) throw new NotFoundException(`Tài khoản đã tồn tại. Vui lòng thử lại với tên khác!`)

    const newUserEntity = this.repo.create({
      ...data,
      // companyId: user.companyId,
      type: type ? type : enumData.UserType.Admin.code,
      createdBy: user?.id,
      createdAt: new Date(),
    })
    const userEntity = await this.repo.save(newUserEntity)
    return { id: userEntity.id }
  }

  /** Admin đổi email user Survey */
  public async updateEmailSurvey_Admin(user: UserDto, data: UpdateEmailAdminto) {
    const userCurrent = await this.repo.findOne({ where: { id: user.id }, select: { id: true, companyId: true } })
    if (!userCurrent?.companyId) throw new NotFoundException('Tài khoản đang thao tác không xác định được Công ty.')

    const userEntity = await this.repo.findOne({
      where: { id: data.userId, companyId: userCurrent.companyId },
      select: { id: true },
    })
    if (!userEntity) throw new NotFoundException('Tài khoản không tồn tại.')

    await this.repo.update(userEntity.id, { email: data.newEmail, updatedAt: new Date(), updatedBy: user.id })

    return { message: 'Đổi email thành công.' }
  }

  /** Xác thực user trang Auth */
  public async validate(username: string, password: string) {
    // check user
    const user = await this.repo.findOne({
      where: { username: username, companyId: IsNull() },
      select: { id: true, password: true },
    })
    if (!user) throw new UnauthorizedException('Tài khoản không tồn tại, vui lòng kiểm tra lại!')
    if (user.isDeleted) throw new UnauthorizedException('Tài khoản đã ngưng hoạt động!')

    // check pw
    const isPasswordMatch = await user.comparePassword(password)
    if (!isPasswordMatch) throw new UnauthorizedException('Mật khẩu không đúng, vui lòng thử lại!')

    // delete pw in obj
    delete user.password

    return user
  }

  /** Kiểm tra để xác thực token Survey gọi */
  async validateToken(user: UserDto) {
    const userEntity: any = await this.repo.findOne({
      where: {
        id: user.id,
        type: In([enumData.UserType.CompanyAdmin.code, enumData.UserType.CompanyEmployee.code, enumData.UserType.Customer.code]),
      },
      // relations: { company: true },
      select: {
        id: true,
        username: true,
        type: true,
        isDeleted: true,
      },
    })

    if (!userEntity) throw new UnauthorizedException('Tài khoản không tồn tại!')
    if (userEntity.isDeleted) throw new UnauthorizedException('Tài khoản đã bị khóa!')

    return {
      id: userEntity.id,
      username: userEntity.username,
      isAdmin: userEntity.type == enumData.UserType.Admin.code,
      userId: userEntity.id,
      enumData: enumData,
    }
  }
  baseApeAuthUrl = process.env.APE_AUTHEN_API
  /** Đăng nhập của trang auth admin */
  public async register(user: UserRegisterDto): Promise<UserDto> {
    const isUserNameTaken = await this.isUserNameTaken(user.username)
    if (isUserNameTaken) throw new ConflictException('Tên tài khoản đã được sử dụng.')
    let newSurveyMemberEntity = new SurveyMemberEntity()
    newSurveyMemberEntity.createdAt = new Date()

    newSurveyMemberEntity.address = user.address
    //supplierId
    if (user.phone) newSurveyMemberEntity.phone = user.phone
    if (user.company) newSurveyMemberEntity.company = user.company
    if (user.tax) newSurveyMemberEntity.tax = user.tax
    if (user.email) newSurveyMemberEntity.email = user.email
    delete user.tax, user.email, user.company, user.phone, user.address
    const newUserEntity = this.userSurveyRepo.create(user as any as UserSurveyEntity)
    newUserEntity.type = user.type
    newUserEntity.cardId = user.identityCard
    const { password, isDeleted, ...createdUserEntity } = await this.userSurveyRepo.save(newUserEntity)

    newSurveyMemberEntity.userId = newUserEntity.id
    await this.surveyMemberRepo.save(newSurveyMemberEntity).then(async (s_user) => {
      const fullNameList = user.fullName.split(' ')
      const domain = fullNameList[0][0] + fullNameList[fullNameList.length - 1][0]
      await apeAuthenApiHelper.register({
        username: user.username,
        password: user.password,
        email: user.email,
        phone: user.phone,
        address: user.address,
        tax: s_user.tax,
        fullName: user.fullName,
        type: NSTenantType.EType.TENANT_MASTER,
        domain: domain,
      })
    })
    const createdUser = plainToClass(UserDto, createdUserEntity)
    return createdUser
  }

  private async isUserNameTaken(username: string) {
    const userEntity = await this.repo.findByUserName(username)
    if (!userEntity) return false
    return true
  }

  /** Đổi mật khẩu user trang auth admin */
  public async updatePassword(user: UserDto, data: UpdatePasswordDto) {
    if (data.newPassword === data.currentPassword) throw new BadRequestException('Mật khẩu mới trùng mật khẩu hiện tại.')

    const userEntity = await this.repo.findOne({ where: { id: user.id }, select: { id: true, password: true } })
    if (!userEntity) throw new Error('Tài khoản không tồn tại.')

    const isCurrentPasswordMatch = await userEntity.comparePassword(data.currentPassword)
    if (!isCurrentPasswordMatch) throw new Error('Sai mật khẩu hiện tại.')

    const hashedPassword = await hash(data.newPassword, PWD_SALT_ROUNDS)
    await this.repo.update(userEntity.id, { password: hashedPassword, updatedAt: new Date(), updatedBy: user.id })

    return { message: 'Đổi mật khẩu thành công.' }
  }

  public async resetPassword(user: UserDto, data: { userName: string; newPassword: string }) {
    const userEntity = await this.repo.findOne({ where: { username: data.userName }, select: { id: true } })
    if (!userEntity) throw new Error('Tài khoản không tồn tại.')

    const hashedPassword = await hash(data.newPassword, PWD_SALT_ROUNDS)
    await this.repo.update(userEntity.id, { password: hashedPassword, updatedAt: new Date(), updatedBy: user.id })

    return { message: 'Đổi mật khẩu thành công.' }
  }

  /** Kiểm tra để xác thực token Auth gọi */
  async validateTokenAuth(user: UserDto) {
    const userEntity: any = await this.repo.findOne({
      where: { id: user.id, type: enumData.UserType.Admin.code },
      // relations: { company: true },
      select: {
        id: true,
        username: true,
        isDeleted: true,
      },
    })

    if (!userEntity) throw new UnauthorizedException('Tài khoản không tồn tại!')
    if (userEntity.isDeleted) throw new UnauthorizedException('Tài khoản đã bị khóa!')

    return {
      id: userEntity.id,
      username: userEntity.username,
    }
  }

  //#region Survey

  /** Lấy companyId từ domain */
  async getcompanyId(data: { domain: string }) {
    const company: any = await this.companyRepo.findOne({
      where: { domain: data.domain, isDeleted: false },
      select: { id: true },
    })
    if (!company) return null
    return company.id
  }

  /** Đăng nhập trang admin Survey */
  // public async loginSurveyAdmin(req: IRequest, data: LoginAdminDto) {
  //   const domain = coreHelper.getDomain(req)
  //   if (!domain) throw new UnauthorizedException('Không thể xác định tên miền truy cập! (code: DOMAIN_ERROR)')
  //   const company: any = await this.companyRepo.findOne({ where: { domain: domain }, select: { id: true, name: true, isDeleted: true, code: true } })
  //   if (!company) throw new UnauthorizedException(`Tên miền ${domain} không có công ty Survey sử dụng!`)
  //   if (company.isDeleted) throw new UnauthorizedException(`Công ty [${company.name}] đã bị khóa!`)

  //   // Chỉ user admin mới có quyền đăng nhập
  //   const userEntity = await this.repo.findOne({
  //     where: {
  //       username: data.username,
  //       type: enumData.UserType.CompanyAdmin.code,
  //       companyId: company.id,
  //     },
  //     select: { id: true, isDeleted: true, type: true, password: true },
  //   })

  //   if (!userEntity) throw new UnauthorizedException('Tài khoản không tồn tại!')
  //   if (userEntity.isDeleted) throw new UnauthorizedException('Tài khoản đã bị khóa!')

  //   // check pw
  //   const isPasswordMatch = await userEntity.comparePassword(data.password)
  //   if (!isPasswordMatch) throw new UnauthorizedException('Mật khẩu không đúng!')

  //   return {
  //     userId: userEntity.id,
  //     accessToken: this.jwtService.sign({ uid: userEntity.id }),
  //     companyId: company.id,
  //     isAdmin: userEntity.type == enumData.UserType.CompanyAdmin.code,
  //     companyCode: company.code,
  //     enumData: enumData,
  //   }
  // }

  /** Đăng nhập app Survey */
  public async loginSurveyApp(data: LoginAppDto) {
    const userEntity = await this.userSurveyRepo.findOne({
      where: {
        username: data.username,
      },
    })

    if (!userEntity) throw new UnauthorizedException('Tài khoản không tồn tại!')
    if (userEntity.isDeleted) throw new UnauthorizedException('Tài khoản đã bị khóa!')

    const surveyMember = await this.surveyMemberRepo.findOne({ where: { userId: userEntity.id } })
    // check pw
    const isPasswordMatch = await userEntity.comparePassword(data.password)
    if (!isPasswordMatch) throw new UnauthorizedException('Mật khẩu không đúng!')

    const { password, ...userWithoutPassword } = userEntity

    return {
      userId: userEntity.id,
      surveyMember: surveyMember,
      surveyMemberId: surveyMember.id,
      accessToken: this.jwtService.sign({ uid: userEntity.id }),
      isAdmin: userEntity.type == enumData.UserType.Admin.code,
      type: userEntity.type,
      enumData: enumData,
      isAllowMessage: userEntity.isAllowMessage,
      user: userWithoutPassword,
    }
  }

  public async loginSurveyAppFarm(data: LoginAppFarmDto) {
    const userEntity = await this.userSurveyRepo.findOne({
      where: {
        phone: data.phone,
      },
    })

    if (!userEntity) throw new UnauthorizedException('Tài khoản không tồn tại!')
    if (userEntity.isDeleted) throw new UnauthorizedException('Tài khoản đã bị khóa!')

    const surveyMember = await this.surveyMemberRepo.findOne({ where: { userId: userEntity.id } })
    // check pw
    const isPasswordMatch = await userEntity.comparePassword(data.password)
    if (!isPasswordMatch) throw new UnauthorizedException('Mật khẩu không đúng!')

    const { password, ...userWithoutPassword } = userEntity

    return {
      userId: userEntity.id,
      surveyMember: surveyMember,
      surveyMemberId: surveyMember.id,
      accessToken: this.jwtService.sign({ uid: userEntity.id }),
      isAdmin: userEntity.type == enumData.UserType.Admin.code,
      type: userEntity.type,
      enumData: enumData,
      isAllowMessage: userEntity.isAllowMessage,
      user: userWithoutPassword,
    }
  }

  /** Quên mật khẩu trang admin Survey */
  public async forgetSurveyAdmin(req: IRequest, data: ForgetAdminDto) {
    const userEntity = await this.repo.findOne({
      where: {
        username: data.username,
        type: In([enumData.UserType.CompanyAdmin.code, enumData.UserType.CompanyEmployee.code, enumData.UserType.Customer.code]),
      },
      select: { id: true, isDeleted: true, email: true },
    })

    if (!userEntity) throw new UnauthorizedException('Tài khoản không tồn tại!')
    if (userEntity.isDeleted) throw new UnauthorizedException('Tài khoản đã bị khóa!')
    if (!userEntity.email) throw new UnauthorizedException('Tài khoản không đăng ký email!')
    if (userEntity.email != data.email) throw new UnauthorizedException(`Email đăng ký tài khoản [${data.username}] không đúng!`)

    const genPassword = nanoid()
    const hashedPassword = await hash(genPassword, PWD_SALT_ROUNDS)
    await this.repo.update(userEntity.id, { password: hashedPassword, updatedAt: new Date() })

    return {
      userId: userEntity.id,
    }
  }

  /** Quên mật khẩu app Survey */
  public async forgetSurveyApp(data: ForgetAppDto) {
    if (!data.companyCode) throw new UnauthorizedException('Không thể xác định mã Công ty! (code: company_CODE_ERROR)')
    const company: any = await this.companyRepo.findOne({ where: { code: data.companyCode }, select: { id: true, name: true, isDeleted: true } })
    if (!company) throw new UnauthorizedException(`Mã Công ty ${data.companyCode} không tồn tại!`)
    if (company.isDeleted) throw new UnauthorizedException(`Công ty [${company.name}] đã bị khóa!`)

    const userEntity = await this.repo.findOne({
      where: {
        username: data.username,
        type: enumData.UserType.CompanyEmployee.code,
        companyId: company.id,
      },
      select: { id: true, isDeleted: true, email: true },
    })

    if (!userEntity) throw new UnauthorizedException('Tài khoản không tồn tại!')
    if (userEntity.isDeleted) throw new UnauthorizedException('Tài khoản đã bị khóa!')
    if (!userEntity.email) throw new UnauthorizedException('Tài khoản không đăng ký email!')
    if (userEntity.email != data.email) throw new UnauthorizedException(`Email đăng ký tài khoản [${data.username}] không đúng!`)

    const genPassword = nanoid()
    const hashedPassword = await hash(genPassword, PWD_SALT_ROUNDS)
    await this.repo.update(userEntity.id, { password: hashedPassword, updatedAt: new Date() })

    // gửi email
    this.emailService.sendPassword({ companyId: company.id, email: userEntity.email, password: genPassword })

    return { message: `Thao tác thành công! Vui lòng kiểm tra email đăng ký tài khoản để lấy mật khẩu mới.` }
  }

  /** Đổi mật khẩu user Survey */
  public async updatePasswordSurvey(user: UserDto, data: UpdatePasswordDto) {
    if (data.newPassword === data.currentPassword) throw new BadRequestException('Trùng mật khẩu cũ.')

    const userEntity = await this.repo.findOne({ where: { id: user.id }, select: { id: true, password: true } })
    if (!userEntity) throw new NotFoundException('Tài khoản không tồn tại.')

    const isCurrentPasswordMatch = await userEntity.comparePassword(data.currentPassword)
    if (!isCurrentPasswordMatch) throw new BadRequestException('Sai mật khẩu cũ.')

    const hashedPassword = await hash(data.newPassword, PWD_SALT_ROUNDS)
    await this.repo.update(userEntity.id, { password: hashedPassword, updatedAt: new Date(), updatedBy: user.id })

    return { message: 'Đổi mật khẩu thành công.' }
  }

  /** Admin đổi mật khẩu user Survey */
  public async updatePasswordSurvey_Admin(user: UserDto, data: UpdatePasswordAdminto) {
    const userCurrent = await this.repo.findOne({ where: { id: user.id }, select: { id: true, companyId: true } })
    if (!userCurrent?.companyId) throw new NotFoundException('Tài khoản đang thao tác không xác định được Công ty.')

    const userEntity = await this.repo.findOne({
      where: { id: data.userId, companyId: userCurrent.companyId },
      select: { id: true },
    })
    if (!userEntity) throw new NotFoundException('Tài khoản không tồn tại.')

    const hashedPassword = await hash(data.newPassword, PWD_SALT_ROUNDS)
    await this.repo.update(userEntity.id, { password: hashedPassword, updatedAt: new Date(), updatedBy: user.id })

    return { message: 'Đổi mật khẩu thành công.' }
  }

  /** Admin đổi vai trò user Survey */
  public async updateRoleSurvey_Admin(user: UserDto, data: UpdateRoleAdminto) {
    const userCurrent = await this.repo.findOne({ where: { id: user.id }, select: { id: true, companyId: true } })
    if (!userCurrent?.companyId) throw new NotFoundException('Tài khoản đang thao tác không xác định được Công ty.')

    const userEntity = await this.repo.findOne({
      where: { id: data.userId, companyId: userCurrent.companyId },
      select: { id: true },
    })
    if (!userEntity) throw new NotFoundException('Tài khoản không tồn tại.')

    await this.repo.update(userEntity.id, { updatedAt: new Date(), updatedBy: user.id })

    return { message: 'Đổi vai trò thành công.' }
  }

  /** Active/Inactive user Survey */
  public async updateStatusUserSurvey(user: UserDto, data: UpdateStatusUserSurveyDto) {
    if (!data.companyId) throw new BadRequestException(`Lỗi ID Công ty!`)
    if (!data.userId) throw new BadRequestException('Lỗi ID tài khoản.')
    if (data.isDeleted == undefined) throw new BadRequestException('Lỗi trạng thái hoạt động.')

    const companyEntity = await this.companyRepo.findOne({
      where: { id: data.companyId },
      select: {
        id: true,
        isDeleted: true,
        numUser: true,
        numUserLimit: true,
      },
    })
    if (!companyEntity) throw new Error(`Công ty không tồn tại`)
    if (companyEntity.isDeleted) throw new Error(`Công ty đã bị khóa`)

    // Tìm user trong Công ty
    const objUser = await this.repo.findOne({
      where: { id: data.userId, companyId: companyEntity.id },
      select: { id: true, isDeleted: true },
    })
    if (!objUser) throw new NotFoundException(`Tài khoản không tồn tại!`)
    if (objUser.isDeleted == data.isDeleted) {
      throw new Error(`Trạng thái hoạt động của User không đồng nhất ở Survey và Auth, vui lòng liên hệ Survey để kiểm tra lại.`)
    }
    if (data.isDeleted == true) {
      await this.companyRepo.update(companyEntity.id, {
        numUser: companyEntity.numUser - 1,
        updatedAt: new Date(),
        updatedBy: user.id,
      })
    } else {
      // if (companyEntity.numUser >= companyEntity.numUserLimit) {
      //   throw new Error(`Số lượng user đã đạt giới hạn: (${companyEntity.numUserLimit} user)!`)
      // }
      await this.companyRepo.update(companyEntity.id, {
        numUser: companyEntity.numUser + 1,
        updatedAt: new Date(),
        updatedBy: user.id,
      })
    }

    await this.repo.update(objUser.id, {
      isDeleted: data.isDeleted,
      updatedAt: new Date(),
      updatedBy: user.id,
    })

    return { message: ACTION_SUCCESS }
  }
  //#endregion

  //#region Portal
  public async loginPortal(data: UserLoginDto) {
    const supplier = await this.supplierRepo.findOne({
      where: {
        username: data.username,
      },
    })
    if (!supplier) throw new NotFoundException('Tài khoản không tồn tại!')
    if (supplier.isDeleted) throw new UnauthorizedException('Tài khoản đã ngưng hoạt động!')

    // check pw
    const isPasswordMatch = await supplier.comparePassword(data.password)
    if (!isPasswordMatch) throw new UnauthorizedException('Mật khẩu không đúng!')

    const { password, ...userWithoutPassword } = supplier

    return {
      permisson: [],
      accessToken: this.jwtService.sign({ uid: supplier.id }),
      username: supplier.username,
      user: userWithoutPassword,
      enumData: enumData,
      isAdmin: false,
    }
  }
  //#endregion

  public async logoutSurveyApp(user: UserDto) {
    return { message: ACTION_SUCCESS }
  }

  public async requestFcmToken(user: UserDto, data: RequestFCMTokenDto) {
    const objUser = await this.repo.findOne({ where: { id: data.userId, isDeleted: false, companyId: user.companyId } })
    if (!objUser) throw new NotFoundException('Tài khoản không tồn tại.')
    return await this.repo.update(objUser.id, { fcmToken: data.fcmToken })
  }

  public async updateAllowMessage(user: UserDto, data: UpdateIsAllowMessDto) {
    const objUser = await this.repo.findOne({ where: { id: data.userId, isDeleted: false, companyId: user.companyId } })
    if (!objUser) throw new NotFoundException('Tài khoản không tồn tại.')
    await this.repo.update(objUser.id, { isAllowMessage: !objUser.isAllowMessage })
    return { message: ACTION_SUCCESS }
  }

  public async updatePasswordPortal(info: UpdatePasswordDto, user: UserDto, req: IRequest) {
    if (info.newPassword === info.currentPassword) throw new BadRequestException('Trùng mật khẩu cũ.')

    const supplierEntity = await this.supplierRepo.findOne({ where: { id: user.id }, select: { id: true } })
    if (!supplierEntity) throw new NotFoundException('Tài khoản không tồn tại.')

    const hashedPassword = await hash(info.newPassword, PWD_SALT_ROUNDS)
    await this.supplierRepo.update(user.id, { password: hashedPassword, updatedBy: user.id })

    return { message: 'Đổi mật khẩu thành công.' }
  }

  public async updateUsername(info: UpdateUsernameDto, user: UserDto, req: IRequest) {
    const newUsername = info.newUsername.trim()
    if (!newUsername || newUsername.length < 3) throw new NotFoundException('Tài khoản mới không hợp lệ')

    const supplierEntity = await this.supplierRepo.findOne({
      where: { id: user.id },
      select: { id: true, username: true, password: true },
    })
    if (!supplierEntity) throw new NotFoundException('Tài khoản không tồn tại.')

    if (newUsername === supplierEntity.username) throw new BadRequestException('Trùng tên đăng nhập cũ.')

    const existSupplierEntity = await this.supplierRepo.findOne({ where: { username: newUsername }, select: { id: true } })
    if (existSupplierEntity) throw new Error(`Tài khoản [${newUsername}] đã tồn tại.`)

    const isCurrentPasswordMatch = await supplierEntity.comparePassword(info.currentPassword)
    if (!isCurrentPasswordMatch) throw new BadRequestException('Sai mật khẩu cũ.')

    await this.supplierRepo.update(user.id, { username: newUsername, updatedBy: user.id })

    return { message: 'Đổi tên đăng nhập thành công.' }
  }
}
