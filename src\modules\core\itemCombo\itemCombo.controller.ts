import { Controller, UseGuards, Post, Body, Req } from '@nestjs/common'
import { FilterOneDto, UserDto } from '../../../dto'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { CurrentUser } from '../../common/decorators'
import { CancelComboDto } from './dto'
import { Request as IRequest } from 'express'
import { ItemComboService } from './itemCombo.service'
import { JwtAuthGuard } from '../../common/guards'
import { AuthGuard } from '../../common/guards/auth.guard'

@ApiBearerAuth()
@ApiTags('ProductCombo')
@Controller('product_combo')
export class ItemComboController {
  constructor(private readonly service: ItemComboService) {}

  @UseGuards(JwtAuthGuard)
  @Post('load_product_in_combo')
  async loadProductInCombo(@Body() data: FilterOneDto) {
    return await this.service.loadProductInCombo(data)
  }

  @UseGuards(AuthGuard)
  @Post('oms/load_product_in_combo')
  async omsLoadProductInCombo(@Body() data: FilterOneDto) {
    return await this.service.loadProductInCombo(data)
  }

  @UseGuards(JwtAuthGuard)
  @Post('cancel_combo')
  async cancelCombo(@Body() data: CancelComboDto, @CurrentUser() user: UserDto, @Req() req: IRequest) {
    return await this.service.cancelCombo(data, user, req)
  }
}
