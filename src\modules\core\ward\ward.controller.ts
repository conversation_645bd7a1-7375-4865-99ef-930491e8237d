import { Controller, UseGuards, Request, Post, Body, Param, Get } from '@nestjs/common'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { Request as IRequest } from 'express'
import { FilterOneDto, UserDto } from '../../../dto'
import { JwtAuthGuard, CurrentUser } from '../../common'
import { WardCreateDto, WardCreateExcelDto, WardUpdateDto } from './dto'
import { WardService } from './ward.service'

@ApiBearerAuth()
@ApiTags('Geo Ward')
@UseGuards(JwtAuthGuard)
@Controller('ward')
export class WardController {
  constructor(private readonly service: WardService) {}

  @Post('find')
  public async find(@Body() data: any) {
    return await this.service.find(data)
  }

  @Post('load_data')
  public async loadData(@Body() data: { id?: string }) {
    return await this.service.loadData(data)
  }

  @Get('load_ward_by_districtId/:districtId')
  public async loadWardByDistrictId(@Param('districtId') districtId: string) {
    return await this.service.loadWardByDistrictId(districtId)
  }

  @Post('pagination')
  public async pagination(@Body() data: any) {
    return await this.service.pagination(data)
  }

  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: WardCreateDto) {
    return await this.service.createData(user, data)
  }

  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: WardUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @Post('update_active')
  public async updateActive(@Body() data: any, @CurrentUser() user: UserDto) {
    return await this.service.updateIsDelete(data.id, user)
  }

  @Post('master_data')
  public async masterData() {
    return await this.service.createMasterData()
  }

  @Post('create_data_excel')
  public async createDataExcel(@CurrentUser() user: UserDto, @Body() data: WardCreateExcelDto[]) {
    return await this.service.createDataExcel(user, data)
  }

  @Post('find_one')
  public async findOne(@Body() data: FilterOneDto) {
    return await this.service.findOne(data)
  }
}
