import { Column, Entity, Index } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { BaseEntity } from './base.entity'
import { NSPo } from '../../constants';

@Entity('delivery_note')
export class DeliveryNoteEntity extends BaseEntity {
  @ApiProperty({ description: 'Mã Phiếu giao nhận' })
  @Column({ unique: true })
  @Index()
  code: string;

  @ApiProperty({ description: 'Danh sách mã PO tham chiếu' })
  @Column("jsonb", { nullable: true })
  @Index()
  poId: string[];

  @ApiProperty({ description: 'Mã PO nhóm' })
  @Column("varchar", { nullable: true })
  @Index()
  poGroupId?: string;

  @ApiPropertyOptional({
    description: `Trạng thái giao ${Object.values(NSPo.EDeliveryStatus).join(' | ')}`,
    enum: NSPo.EDeliveryStatus,
    default: NSPo.EDeliveryStatus.NEWLYCREATED,
  })
  @Column({ default: NSPo.EDeliveryStatus.NEWLYCREATED })
  deliveryStatus?: NSPo.EDeliveryStatus;

  @ApiProperty({ description: "Tống giá bán chung theo toàn bộ PO" })
  @Column('numeric', { precision: 20, scale: 0, default: 0, nullable: true })
  itemTotalAmount: number;

  @ApiProperty({ description: "Tống giá bán chung theo toàn bộ PO VAT" })
  @Column('numeric', { precision: 20, scale: 0, default: 0, nullable: true })
  itemTotalAmountVat: number;

  @ApiPropertyOptional({
    description: `Trạng thái nhận ${Object.values(NSPo.EReceivingStatus).join(' | ')}`,
    enum: NSPo.EReceivingStatus,
    default: NSPo.EReceivingStatus.PENDING,
  })
  @Column({ default: NSPo.EReceivingStatus.PENDING })
  receivingStatus: NSPo.EReceivingStatus = NSPo.EReceivingStatus.PENDING;

  @ApiPropertyOptional({
    description: `Danh sách file đính kèm bên giao`
  })
  @Column("jsonb", { nullable: true })
  filesDelivery: string;

  @ApiPropertyOptional({
    description: `Danh sách file đính kèm bên nhận`
  })
  @Column("jsonb", { nullable: true })
  filesReceiving: string;

  @ApiProperty({ description: 'Ghi chú bên giao' })
  @Column('varchar', { nullable: true })
  noteDelivery: string;

  @ApiProperty({ description: 'Ghi chú bên nhận' })
  @Column('varchar', { nullable: true })
  noteReceiving: string;
}