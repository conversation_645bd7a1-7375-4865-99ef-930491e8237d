import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import { AppService } from '../../app.service'
import {
  EmployeeRepository,
  NotifyRepository,
  QuestionListDetailRepository,
  QuestionRepository,
  SurveyHistoryRepository,
  SurveyMemberRepository,
  SurveyQuestionRepository,
  SurveyRepository,
  TopicRepository,
  UserRepository,
} from '../../../repositories'
import { PubliSurveyController } from './publicSurvey.controller'
import { PublicSurveyService } from './publicSurvey.service'
import { NotifyModule } from '../notify/notify.module'
import { MobileAppModule } from '../mobileApp/mobileApp.module'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      SurveyRepository,
      SurveyQuestionRepository,
      QuestionRepository,
      TopicRepository,
      SurveyMemberRepository,
      SurveyHistoryRepository,
      EmployeeRepository,
      UserRepository,
      QuestionListDetailRepository,
    ]),
    NotifyModule,
    MobileAppModule,
  ],
  controllers: [PubliSurveyController],
  providers: [PublicSurveyService],
  exports: [PublicSurveyService],
})
export class PublicSurveyModule {}
