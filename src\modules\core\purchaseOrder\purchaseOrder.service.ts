import { CityRepository, DistrictRepository, RegionRepository, WardRepository } from './../../../repositories/core/geo.repository'
import { <PERSON>acheKey, Injectable } from '@nestjs/common'
import * as moment from 'moment'
import * as XLSX from 'xlsx'
import {
  PurchaseOrderRepository,
  PurchaseOrderItemRepository,
  SupplierRepository,
  ItemBaseRepository,
  SupplyChainConfigRepository,
  DeliveryNoteRepository,
  ItemPriceRepository,
  ItemComboRepository,
  SupplyChainConfigDetailRepository,
  DeliveryNoteTrackingRepository,
  PurchaseOrderHistoryRepository,
  UserRepository,
  PurchaseOrderSaleOrderRepository,
  UnitRepository,
  SupplyChainConfigApproveRepository,
  PurchaseOrderApproveRepository,
  EmployeeRepository,
  WarehouseRepository,
  InboundRepository,
  InboundDetailRepository,
  WarehouseProductRepository,
  OperationalAreaRepository,
  PurchaseOrderChildRepository,
  ItemTypeRepository,
} from '../../../repositories'
import {
  CreatePOFromSODto,
  CreatePurchaseOrderDto,
  DetailPurchaseOrderDto,
  DistributeViewPODto,
  ExportPoSupplierDto,
  ImportPoRefDto,
  ListHistoryPoDto,
  ListPoDto,
  ListPoItemDto,
  ListPoReq,
  ListPOwithSODto,
  UpdatePurchaseOrderDto,
  UploadPreOrderPODto,
} from './dto'
import { CREATE_SUCCESS, enumData, NSOperational, NSPo, NSRecurringOrder, NSWarehouse, UPDATE_SUCCESS } from '../../../constants'
import { Request as IRequest } from 'express'
import { Between, In, IsNull, Like, Not, Raw } from 'typeorm'
import { omsApiHelper } from '../../../helpers/omsApiHelper'
import { coreHelper } from '../../../helpers'
import { UpdateStatusPurchaseOrderDto } from './dto/updateStatus.dto'
import { InboundService } from '../../wms/inbound/inbound.service'
import { WarehouseService } from '../../wms/warehouse/warehouse.service'
import { DeliveryNoteEntity, ItemEntity } from '../../../entities'
import { NSItem } from '../../../constants/NSItem'
import { POCreatePlatformDto, PoPlatformDto, PoProductPlatformDto } from './dto/createPOPms.dto'
import { Product } from 'aws-sdk/clients/ssm'
import { UserDto } from '../../../dto'
import { pmsApiHelper } from '../../../helpers/pmsApiHelper'
import { EmailService } from '../../survey/email/email.service'
import { PricingRepository } from '../../../repositories/core/pricing.repository'
import { PricingEntity } from '../../../entities/core/pricing.entity'

@Injectable()
export class PurchaseOrderService {
  constructor(
    private readonly purchaseOrderRepo: PurchaseOrderRepository,
    private readonly purchaseOrderItemRepo: PurchaseOrderItemRepository,
    private readonly purchaseOrderHistory: PurchaseOrderHistoryRepository,
    private readonly purchaseOrderSORepo: PurchaseOrderSaleOrderRepository,
    private readonly purchaseOrderApproveRepo: PurchaseOrderApproveRepository,
    private readonly itemRepository: ItemBaseRepository,
    private readonly itemPriceRepo: ItemPriceRepository,
    private readonly itemComboRepo: ItemComboRepository,
    private readonly supplyChainConfigRepository: SupplyChainConfigRepository,
    private readonly supplierRepo: SupplierRepository,
    private readonly deliveryNoteRepo: DeliveryNoteRepository,
    private readonly supplyChainConfigDetailRepo: SupplyChainConfigDetailRepository,
    private readonly supplyChainConfigApproveRepo: SupplyChainConfigApproveRepository,
    private readonly deliveryTrackingRepository: DeliveryNoteTrackingRepository,
    private readonly userRepository: UserRepository,
    private readonly employeeRepository: EmployeeRepository,
    private readonly unitRepository: UnitRepository,
    private readonly cityRepository: CityRepository,
    private readonly wardRepository: WardRepository,
    private readonly districtRepository: DistrictRepository,
    private readonly inboundService: InboundService,
    private readonly inboundRepository: InboundRepository,
    private readonly inboundDetailRepository: InboundDetailRepository,
    private readonly warehouseRepository: WarehouseRepository,
    private readonly warehouseProductRepository: WarehouseProductRepository,
    private readonly regionRepository: RegionRepository,
    private readonly warehouseService: WarehouseService,
    private readonly operationalAreaRepo: OperationalAreaRepository,
    private readonly purchaseOrderChildRepo: PurchaseOrderChildRepository,
    private readonly employeeRepo: EmployeeRepository,
    private readonly emailService: EmailService,
    private readonly pricingRepo: PricingRepository,
  ) { }

  public async findPoItems(params: ListPoItemDto) {
    const { purchaseOrderId, supplierId, regionId } = params
    const poItems = await this.purchaseOrderItemRepo.find({ where: { purchaseOrderId, supplierId, regionId } })
    return { items: poItems }
  }
  /** Lấy danh sách PO */
  public async findAll(params: ListPoDto) {
    const {
      code,
      createBy,
      status,
      dateFrom,
      dateTo,
      pageIndex,
      pageSize,
      supplierId,
      purchaseOrderType,
      approveStatus,
      approverCurrentId,
      deliveryDateFrom,
      deliveryDateTo,
      warehouseFullAddress,
    } = params
    let wheres: any = []
    let wheresTotalAmount: any = []
    wheresTotalAmount.push(`po."approveStatus" = '${NSPo.EPoStatus.APPROVED}'`)

    if (code) {
      wheres.push(`po."purchaseOrderCode" = '${code}'`)
      wheresTotalAmount.push(`po."approveStatus" = '${NSPo.EPoStatus.APPROVED}'`)
    }

    if (warehouseFullAddress) {
      wheres.push(`wh."warehouseFullAddress" ILIKE '%${warehouseFullAddress}%'`)
    }

    if (status && status.length > 0) {
      wheres.push(`po."status" IN (${status.map((s) => `'${s}'`).join(', ')})`)
    }

    if (approveStatus) {
      wheres.push(`po."approveStatus" = '${approveStatus}'`)
    }

    if (approverCurrentId) {
      wheres.push(`po."approverCurrentId" = '${approverCurrentId}'`)
      wheresTotalAmount.push(`po."approverCurrentId" = '${approverCurrentId}'`)
    }

    if (createBy) {
      wheres.push(`po."creatorId" = '${createBy}'`)
      wheresTotalAmount.push(`po."creatorId" = '${createBy}'`)
    }

    if (supplierId) {
      wheres.push(`po."supplierId" = '${supplierId}'`)
      wheresTotalAmount.push(`po."supplierId" = '${supplierId}'`)
    }

    if (purchaseOrderType) {
      wheres.push(`po."purchaseOrderType" = '${purchaseOrderType}'`)
      wheresTotalAmount.push(`po."purchaseOrderType" = '${purchaseOrderType}'`)
    }

    if (dateFrom && dateTo) {
      if (new Date(dateFrom) > new Date(dateTo)) {
        throw new Error('dateFrom must be less than or equal to dateTo')
      }
      wheres.push(`po."createdAt" BETWEEN '${new Date(dateFrom).toISOString()}' AND '${new Date(dateTo).toISOString()}'`)
      wheresTotalAmount.push(`po."createdAt" BETWEEN '${new Date(dateFrom).toISOString()}' AND '${new Date(dateTo).toISOString()}'`)
    }
    if (deliveryDateFrom && deliveryDateTo) {
      if (new Date(deliveryDateFrom) > new Date(deliveryDateTo)) {
        throw new Error('deliveryDateFrom must be less than or equal to deliveryDateTo')
      }
      wheres.push(`po."deliveryDate" BETWEEN '${new Date(deliveryDateFrom).toISOString()}' AND '${new Date(deliveryDateTo).toISOString()}'`)
      wheresTotalAmount.push(
        `po."deliveryDate" BETWEEN '${new Date(deliveryDateFrom).toISOString()}' AND '${new Date(deliveryDateTo).toISOString()}'`,
      )
    }
    const whereClause = wheres.length > 0 ? `WHERE ${wheres.join(' AND ')}` : ''

    try {
      const query = `
        SELECT
          po.id,
          po."purchaseOrderCode",
          po."createdAt",
          po."createdBy",
          po."updatedBy",
          po."status",
          po."approveStatus",
          po.vat,
          po."totalAmount",
          po."totalAmountVat",
          po."purchaseOrderType",
          sup."id" as "supplierId",
          sup."name",
          sup."email",
          sup."phone",
          sup."address",
          ward."name" AS "wardName",
          district."name" AS "districtName",
          city."name" AS "cityName",
          wh."warehouseAddress" AS "warehouseAddress",
          wh."cityName" AS "warehouseCity",
          wh."districtName" AS "warehouseDistrict",
          wh."wardName" AS "warehouseWard",
          wh."warehouseFullAddress",
          sub."supplierName" AS "supplierName",
          u."fullName" AS "creatorName",
          count(poa.*) as "totalApprove",
          json_agg(DISTINCT pos."soId") as "soIds"
        FROM 
          purchase_order po
          LEFT JOIN "purchase_order_approve" poa ON poa."purchaseOrderId" = po.id
          LEFT JOIN "purchase_order_so" pos ON pos."poId" = po.id
          LEFT JOIN "user" u ON po."creatorId"::uuid = u.id
          LEFT JOIN supplier AS sup ON sup."id" = po."supplierId"
          LEFT JOIN ward ON sup."wardId" = ward.id
          LEFT JOIN district ON sup."districtId" = district.id
          LEFT JOIN city ON sup."cityId" = city.id
          LEFT JOIN 
          (SELECT 
          city."name" AS "cityName",  
          district."name" AS "districtName", 
          ward."name" AS "wardName", 
          w."address" AS "warehouseAddress",
          w."id" AS "id",
          CONCAT_WS(', ', 
          w."address", 
          ward."name", 
          district."name", 
          city."name") AS "warehouseFullAddress"
          FROM warehouse as w 
          LEFT JOIN city ON w."cityId" ::uuid = city.id 
          LEFT JOIN district ON w."districtId" ::uuid = district.id 
          LEFT JOIN ward ON w."wardId" :: uuid = ward.id )  wh ON wh."id" = po."warehouseId"
          LEFT JOIN (
          SELECT 
            poi."purchaseOrderId",
            STRING_AGG(DISTINCT sup2."name", ', ') AS "supplierName"
          FROM 
            purchase_order_item poi
            LEFT JOIN supplier AS sup2 ON poi."supplierId" = sup2."id"
          GROUP BY poi."purchaseOrderId"
          ) sub ON sub."purchaseOrderId" = po.id
        ${whereClause}
        GROUP BY 
        sup.ID, ward."name", district."name", city."name", sub."supplierName", sup."id", sup."address",wh."warehouseFullAddress",wh."warehouseAddress",wh."cityName",wh."districtName",wh."wardName", sup."email", sup."phone", sup."address",
        u."fullName", po.id, po."purchaseOrderCode", po."createdBy", po."updatedBy", po."status", po."purchaseOrderType",
        po."approveStatus", po."createdAt", po.vat, po."totalAmount", po."totalAmountVat"
        ORDER BY po."createdAt" DESC
      `

      const data = await this.purchaseOrderRepo.queryPagination(query, { pageIndex, pageSize })

      const whereClauseTotalAmount = wheresTotalAmount.length > 0 ? `${wheresTotalAmount.join(' AND ')}` : ''

      const sumTotalAmountQuery = `
        SELECT 
        SUM("totalAmount") as "totalAmount",
        SUM("totalAmountVat") as "totalAmountVat"
         FROM purchase_order as po WHERE ${whereClauseTotalAmount}  
      `
      const sumTotalAmountData = await this.purchaseOrderRepo.query(sumTotalAmountQuery)
      const totalAmount = sumTotalAmountData[0].totalAmount
      const totalAmountVat = sumTotalAmountData[0].totalAmountVat
      return { ...data, totalAmount, totalAmountVat }
    } catch (error) {
      throw new Error(error)
    }
  }

  //Lấy danh sách PO của NCC
  public async findSupplierPo(params: ListPoDto) {
    const {
      code,
      createBy,
      status,
      dateFrom,
      dateTo,
      pageIndex,
      pageSize,
      supplierId,
      approveStatus,
      approverCurrentId,
      deliveryDateFrom,
      deliveryDateTo,
      warehouseFullAddress,
    } = params
    let wheres: any = []
    wheres.push(`po."purchaseOrderType" = '${NSPo.EPoType.SUPPLIER}'`)

    if (code) wheres.push(`po."purchaseOrderCode" = '${code}'`)
    if (warehouseFullAddress) {
      wheres.push(`wh."warehouseFullAddress" ILIKE '%${warehouseFullAddress}%'`)
    }
    if (status) wheres.push(`po."status" IN (${status.map((s) => `'${s}'`).join(', ')})`)
    if (approveStatus) wheres.push(`po."approveStatus" = '${approveStatus}'`)
    if (approverCurrentId) wheres.push(`po."approverCurrentId" = '${approverCurrentId}'`)
    if (createBy) wheres.push(`po."creatorId" = '${createBy}'`)
    if (supplierId) wheres.push(`po."supplierId" = '${supplierId}'`)
    if (dateFrom && dateTo) {
      if (new Date(dateFrom) > new Date(dateTo)) {
        throw new Error('dateFrom must be less than or equal to dateTo')
      }
      wheres.push(`po."createdAt" BETWEEN '${new Date(dateFrom).toISOString()}' AND '${new Date(dateTo).toISOString()}'`)
    }
    if (deliveryDateFrom && deliveryDateTo) {
      if (new Date(deliveryDateFrom) > new Date(deliveryDateTo)) {
        throw new Error('deliveryDateFrom must be less than or equal to deliveryDateTo')
      }
      wheres.push(`po."deliveryDate" BETWEEN '${new Date(deliveryDateFrom).toISOString()}' AND '${new Date(deliveryDateTo).toISOString()}'`)
    }
    const whereClause = wheres.length > 0 ? `WHERE ${wheres.join(' AND ')}` : ''

    try {
      const query = `
        SELECT
          po.id,
          po."purchaseOrderCode",
          po."createdAt",
          po."createdBy",
          po."updatedBy",
          po."status",
          po."approveStatus",
          po.vat,
          po."totalAmount",
          po."totalAmountVat",
          po."purchaseOrderType",
          sup."id" as "supplierId",
          sup."name",
          sup."email",
          sup."phone",
          sup."address",
          ward."name" AS "wardName",
          district."name" AS "districtName",
          city."name" AS "cityName",
          wh."warehouseFullAddress" AS "warehouseFullAddress",
          sub."supplierName" AS "supplierName",
          u."fullName" AS "creatorName",
          count(poa.*) as "totalApprove",
          json_agg(DISTINCT pos."soId") as "soIds"
        FROM 
          purchase_order po
          LEFT JOIN "purchase_order_approve" poa ON poa."purchaseOrderId" = po.id
          LEFT JOIN "purchase_order_so" pos ON pos."poId" = po.id
          LEFT JOIN "user" u ON po."creatorId"::uuid = u.id
          LEFT JOIN supplier AS sup ON sup."id" = po."supplierId"
          LEFT JOIN ward ON sup."wardId" = ward.id
          LEFT JOIN district ON sup."districtId" = district.id
          LEFT JOIN city ON sup."cityId" = city.id
          LEFT JOIN 
          (SELECT 
          city."name" AS "cityName",  
          district."name" AS "districtName", 
          ward."name" AS "wardName", 
          w."address" AS "warehouseAddress",
          w."id" AS "id",
          CONCAT_WS(', ', 
          w."address", 
          ward."name", 
          district."name", 
          city."name") AS "warehouseFullAddress"
          FROM warehouse as w 
          LEFT JOIN city ON w."cityId" ::uuid = city.id 
          LEFT JOIN district ON w."districtId" ::uuid = district.id 
          LEFT JOIN ward ON w."wardId" :: uuid = ward.id )  wh ON wh."id" = po."warehouseId"
          LEFT JOIN (
          SELECT 
            poi."purchaseOrderId",
            STRING_AGG(DISTINCT sup2."name", ', ') AS "supplierName"
          FROM 
            purchase_order_item poi
            LEFT JOIN supplier AS sup2 ON poi."supplierId" = sup2."id"
          GROUP BY poi."purchaseOrderId"
          ) sub ON sub."purchaseOrderId" = po.id
        ${whereClause}
        GROUP BY 
        sup.ID, ward."name", district."name", city."name", sub."supplierName", sup."id",wh."warehouseFullAddress", sup."name", sup."email", sup."phone", sup."address",
        u."fullName", po.id, po."purchaseOrderCode", po."createdBy", po."updatedBy", po."status", po."purchaseOrderType",
        po."approveStatus", po."createdAt", po.vat, po."totalAmount", po."totalAmountVat"
        ORDER BY po."createdAt" DESC
      `
      const data = await this.purchaseOrderRepo.queryPagination(query, { pageIndex, pageSize })
      return data
    } catch (error) {
      throw new Error(error)
    }
  }

  /** Lấy danh sách PO ver 2  */
  public async purchaseOrderList(params: ListPoReq) {
    const { code, createBy, status, dateFrom, dateTo, take, skip, supplierId, purchaseOrderType, approveStatus, approverCurrentId } = params
    let wheres: any = { approveStatus: NSPo.EPoStatus.APPROVED }

    if (code) wheres.purchaseOrder = code
    if (createBy) wheres.createBy = createBy
    if (status) wheres.status = status
    if (supplierId) wheres.supplierId = supplierId
    if (purchaseOrderType) wheres.purchaseOrderType = purchaseOrderType
    if (approveStatus) wheres.approveStatus = approveStatus
    if (approverCurrentId) wheres.approverCurrentId = approverCurrentId

    // Điều kiện ngày cho createdAt (sử dụng Between hoặc MoreThan/ LessThan)
    if (dateFrom && dateTo) {
      wheres.createdAt = Between(dateFrom, dateTo)
    }

    const [data, total] = await this.purchaseOrderRepo.findAndCount({
      where: wheres,
      take,
      skip,
    })

    const suppliers = await this.supplierRepo.find()
    const province = await this.cityRepository.find({ select: ['id'] })
    const district = await this.districtRepository.find({ select: ['id'] })
    const ward = await this.wardRepository.find({ select: ['id'] })
    const users = await this.userRepository.find({ select: ['id', 'fullName'] })

    const mappingData = data.map((i) => {
      const sup = suppliers.find((s) => s.id == i.supplierId)
      const city = province.find((i) => i.id == sup.cityId)
      const d = district.find((i) => i.id == sup.districtId)
      const w = ward.find((i) => i.id == sup.wardId)
      const user = users.find((u) => u.id == i.creatorId)

      return {
        ...i,
        supplierId: sup.id,
        supplierName: sup.name,
        supplierEmail: sup.email,
        supplierPhone: sup.phone,
        supplierAddress: sup.address,
        wardName: w.name,
        cityName: city.name,
        districtName: d.name,
        creatorName: user.fullName,
      }
    })

    return { data: mappingData, total }
  }

  public async findPurchaseOrderWithPartner(params: ListPoDto) {
    const {
      code,
      createBy,
      status,
      dateFrom,
      dateTo,
      pageIndex,
      pageSize,
      supplierId,
      purchaseOrderType,
      approveStatus,
      deliveryDateFrom,
      deliveryDateTo,
    } = params
    const poLst = await this.purchaseOrderApproveRepo.find({ where: { supplierId, isApproved: true } })
    if (poLst.length == 0) return { data: [], total: 0 }
    const poIds = poLst.map((i) => `'${i.purchaseOrderId}'`).join(', ')

    let wheres: any = []
    if (poIds) wheres.push(`po."id" IN (${poIds})`)
    if (code) wheres.push(`po."purchaseOrderCode" = '${code}'`)
    if (status) wheres.push(`po."status" IN (${status.map((s) => `'${s}'`).join(', ')})`)
    if (approveStatus) wheres.push(`po."approveStatus" = '${approveStatus}'`)
    if (createBy) wheres.push(`po."creatorId" = '${createBy}'`)
    if (purchaseOrderType) wheres.push(`po."purchaseOrderType" = '${purchaseOrderType}'`)
    if (supplierId) wheres.push(`po."supplierId" = '${supplierId}'`)
    if (dateFrom && dateTo) {
      if (new Date(dateFrom) > new Date(dateTo)) {
        throw new Error('dateFrom must be less than or equal to dateTo')
      }
      wheres.push(`po."createdAt" BETWEEN '${new Date(dateFrom).toISOString()}' AND '${new Date(dateTo).toISOString()}'`)
    }
    if (deliveryDateFrom && deliveryDateTo) {
      if (new Date(deliveryDateFrom) > new Date(deliveryDateTo)) {
        throw new Error('deliveryDateFrom must be less than or equal to deliveryDateTo')
      }
      wheres.push(`po."deliveryDate" BETWEEN '${new Date(deliveryDateFrom).toISOString()}' AND '${new Date(deliveryDateTo).toISOString()}'`)
    }

    const whereClause = wheres.length > 0 ? `WHERE ${wheres.join(' AND ')}` : ''

    try {
      const query = `
        SELECT
          po.*,
          sup."id" as "supplierId",
          sup."name",
          sup."email",
          sup."phone",
          sup."address",
          ward."name" AS "wardName",
          district."name" AS "districtName",
          city."name" AS "cityName",
          sub."supplierName" AS "supplierName",
          u."fullName" AS "creatorName",
          count(poa.*) as "totalApprove"
        FROM 
          purchase_order po
          LEFT JOIN "purchase_order_approve" poa ON poa."purchaseOrderId" = po.id
          LEFT JOIN "user" u ON po."creatorId"::uuid = u.id
          LEFT JOIN supplier AS sup ON sup."id" = po."supplierId"
          LEFT JOIN ward ON sup."wardId" = ward.id
          LEFT JOIN district ON sup."districtId" = district.id
          LEFT JOIN city ON sup."cityId" = city.id
          LEFT JOIN (
          SELECT 
            poi."purchaseOrderId",
            STRING_AGG(DISTINCT sup2."name", ', ') AS "supplierName"
          FROM 
            purchase_order_item poi
            LEFT JOIN supplier AS sup2 ON poi."supplierId" = sup2."id"
          GROUP BY poi."purchaseOrderId"
          ) sub ON sub."purchaseOrderId" = po.id
        ${whereClause}
        GROUP BY po.id, sup.id, ward."name",  district."name", city."name",  sub."supplierName", u."fullName"
        ORDER BY po."createdAt" DESC
      `
      const data = await this.purchaseOrderRepo.queryPagination(query, { pageIndex, pageSize })
      return data
    } catch (error) {
      throw new Error(error)
    }
  }

  public async findPurchaseOrderWithPartnerCancel(params: ListPoDto) {
    const {
      code,
      createBy,
      status,
      dateFrom,
      dateTo,
      pageIndex,
      pageSize,
      supplierId,
      purchaseOrderType,
      approveStatus,
      deliveryDateFrom,
      deliveryDateTo,
    } = params
    const poLst = await this.purchaseOrderRepo.find({ where: { supplierId, status: NSPo.EPoStatus.CANCEL } })
    if (poLst.length == 0) return { data: [], total: 0 }
    const poIds = poLst.map((i) => `'${i.id}'`).join(', ')

    let wheres: any = []
    if (poIds) wheres.push(`po."id" IN (${poIds})`)
    if (code) wheres.push(`po."purchaseOrderCode" = '${code}'`)
    if (status) wheres.push(`po."status" IN (${status.map((s) => `'${s}'`).join(', ')})`)
    if (approveStatus) wheres.push(`po."approveStatus" = '${approveStatus}'`)
    if (createBy) wheres.push(`po."creatorId" = '${createBy}'`)
    if (purchaseOrderType) wheres.push(`po."purchaseOrderType" = '${purchaseOrderType}'`)
    if (supplierId) wheres.push(`po."supplierId" = '${supplierId}'`)
    if (dateFrom && dateTo) {
      if (new Date(dateFrom) > new Date(dateTo)) {
        throw new Error('dateFrom must be less than or equal to dateTo')
      }
      wheres.push(`po."createdAt" BETWEEN '${new Date(dateFrom).toISOString()}' AND '${new Date(dateTo).toISOString()}'`)
    }
    if (deliveryDateFrom && deliveryDateTo) {
      if (new Date(deliveryDateFrom) > new Date(deliveryDateTo)) {
        throw new Error('deliveryDateFrom must be less than or equal to deliveryDateTo')
      }
      wheres.push(`po."deliveryDate" BETWEEN '${new Date(deliveryDateFrom).toISOString()}' AND '${new Date(deliveryDateTo).toISOString()}'`)
    }

    const whereClause = wheres.length > 0 ? `WHERE ${wheres.join(' AND ')}` : ''

    try {
      const query = `
        SELECT
          po.*,
          sup."id" as "supplierId",
          sup."name",
          sup."email",
          sup."phone",
          sup."address",
          ward."name" AS "wardName",
          district."name" AS "districtName",
          city."name" AS "cityName",
          sub."supplierName" AS "supplierName",
          u."fullName" AS "creatorName",
          count(poa.*) as "totalApprove"
        FROM 
          purchase_order po
          LEFT JOIN "purchase_order_approve" poa ON poa."purchaseOrderId" = po.id
          LEFT JOIN "user" u ON po."creatorId"::uuid = u.id
          LEFT JOIN supplier AS sup ON sup."id" = po."supplierId"
          LEFT JOIN ward ON sup."wardId" = ward.id
          LEFT JOIN district ON sup."districtId" = district.id
          LEFT JOIN city ON sup."cityId" = city.id
          LEFT JOIN (
          SELECT 
            poi."purchaseOrderId",
            STRING_AGG(DISTINCT sup2."name", ', ') AS "supplierName"
          FROM 
            purchase_order_item poi
            LEFT JOIN supplier AS sup2 ON poi."supplierId" = sup2."id"
          GROUP BY poi."purchaseOrderId"
          ) sub ON sub."purchaseOrderId" = po.id
        ${whereClause}
        GROUP BY po.id, sup.id, ward."name",  district."name", city."name",  sub."supplierName", u."fullName"
        ORDER BY po."createdAt" DESC
      `
      const data = await this.purchaseOrderRepo.queryPagination(query, { pageIndex, pageSize })
      return data
    } catch (error) {
      throw new Error(error)
    }
  }

  /** Lấy lịch sử thao tác PO */
  public async findHistory(param: ListHistoryPoDto) {
    const whereCon: any = { isDeleted: false, purchaseOrderId: param.purchaseOrderId }
    if (param.createdByName) whereCon.createdByName = param.createdByName
    const [data, total] = await this.purchaseOrderHistory.findAndCount({
      where: whereCon,
      order: { createdAt: 'DESC' },
      skip: param.skip,
      take: param.take,
    })
    return { data, total }
  }

  /** Lấy chi tiết PO */
  public async detail(body: DetailPurchaseOrderDto) {
    try {
      const { id, distributorId } = body
      const po = await this.purchaseOrderRepo.findOne({ where: { id } })
      if (!po) {
        throw new Error(`Po id ${id} is not found`)
      }

      const wheres: any = {}
      if (distributorId) {
        wheres.distributorId = distributorId
      }

      let result = {}
      const details = await this.purchaseOrderItemRepo.find({ where: { purchaseOrderId: po.id, ...wheres } })
      const poDelivery = Array.from(new Set(details.map((val) => val.deliveryId)))
      const poDistributor = Array.from(new Set(details.map((val) => val.distributorId)))
      const poWarehouse = Array.from(new Set(details.map((val) => val.warehouseId)))

      const warehouses = await this.warehouseRepository.find({ where: { id: In(poWarehouse) } })
      const suppliers = await this.supplierRepo.find({
        where: { isDeleted: false },
        relations: { ward: true, district: true, city: true },
      })

      const mappingSp: any = await Promise.all(
        suppliers.map(async (val) => {
          const wardMap = await val.ward
          const districtMap = await val.district
          const cityMap = await val.city

          return {
            id: val.id,
            name: val.name,
            phone: val.phone,
            email: val.email,
            address: val.address,
            ward: wardMap?.name,
            district: districtMap?.name,
            city: cityMap?.name,
          }
        }),
      )

      // Map lấy ra địa chỉ ward, district, city của kho
      const mappingWH = await Promise.all(
        warehouses.map(async (val) => {
          const wardMap = await this.wardRepository.findOne({ where: { id: val.wardId }, select: { name: true } })
          const districtMap = await this.districtRepository.findOne({ where: { id: val.districtId }, select: { name: true } })
          const cityMap = await this.cityRepository.findOne({ where: { id: val.cityId }, select: { name: true } })

          return {
            id: val.id,
            name: val.name,
            phone: val.phone,
            address: val.address,
            ward: wardMap?.name,
            district: districtMap?.name,
            city: cityMap?.name,
          }
        }),
      )

      const mappingSupplier = mappingSp.find((val) => val.id == po.supplierId) // Nhà cung cấp
      const mappingDelivery = mappingSp.find((val) => val.id == poDelivery[0]) // 3PL
      const mappingDistributor = poDistributor.map((val) => mappingSp.find((v) => v.id == val)) // Nhà phân phối
      const mappingWarehouse = poWarehouse.map((val) => mappingWH.find((v) => v.id == val)) // Kho nhận hàng

      const createBy = po.createdBy
      const user = await this.userRepository.findOne({ where: { id: createBy } })
      const employee = await this.employeeRepository.findOne({ where: { id: user.employeeId } })

      const city = await this.cityRepository.findOne({ where: { id: employee.cityId }, select: { name: true } })
      const district = await this.districtRepository.findOne({ where: { id: employee.districtId }, select: { name: true } })
      const ward = await this.wardRepository.findOne({ where: { id: employee.wardId }, select: { name: true } })

      const blf = {
        name: employee.name,
        phone: employee.phone,
        email: employee.email,
        address: employee.address,
        city: city.name,
        district: district.name,
        ward: ward.name,
      }

      const unit = await this.unitRepository.find()

      if (po.purchaseOrderType == NSPo.EPoType.WITHCOMBO) {
        const comboIds = Array.from(new Set(details.map((val) => val.comboId)))
        const itemIds = Array.from(new Set(details.map((val) => val.itemId)))
        const combos = await this.itemRepository.find({
          where: {
            isCombo: true,
            id: In(comboIds),
          },
          select: {
            code: true,
            name: true,
            id: true,
          },
        })

        const items = await this.itemRepository.find({
          where: {
            id: In(itemIds),
          },
          select: {
            code: true,
            name: true,
            id: true,
            unitId: true,
            poUnitId: true,
          },
        })

        const productsPrice = await this.itemPriceRepo.find({ where: { itemId: In([...itemIds, ...comboIds]), isFinal: true } })

        // Group by lại theo comboId và partnerId, distributorId
        const uniqueDetail = details.reduce((acc, item) => {
          const existing = acc.find((val) => val.comboId === item.comboId && val.itemId === item.itemId)
          if (existing) {
            existing.quantityCombo += Number(item.quantityCombo)
            existing.quantityBasicUnit += Number(item.quantityBasicUnit)
          } else {
            acc.push({
              comboId: item.comboId,
              itemId: item.itemId,
              partnerId: item.partnerId,
              quantityCombo: item.quantityCombo,
              distributorId: item.distributorId,
              quantityBasicUnit: item.quantityBasicUnit,
            })
          }
          return acc
        }, [])

        // Để xem tổng số lượng combo
        const aggregatedDetails = uniqueDetail.reduce((acc, item) => {
          const existing = acc.find((val) => val.comboId === item.comboId)
          if (!existing) {
            acc.push({ ...item })
          }
          return acc
        }, [])

        const mappingCombo: any = aggregatedDetails.map((val) => {
          const combo = combos.find((cb) => cb.id === val.comboId)
          return {
            comboId: combo.id,
            comboName: combo.name,
            comboCode: combo.code,
            quantityCombo: val.quantityCombo,
          }
        })

        for (let i = 0; i < mappingCombo.length; i++) {
          const combo = mappingCombo[i]
          const itemLst = await this.purchaseOrderItemRepo.find({ where: { purchaseOrderId: po.id, comboId: combo.comboId, ...wheres } })
          const comboPrice = productsPrice.find((val) => val.itemId == combo.comboId)

          const _items = itemLst.map((item) => {
            const itemDetail = items.find((i) => i.id == item.itemId)
            const u = unit.find((i) => i.id == itemDetail.unitId)
            const itemPrice = productsPrice.find((val) => val.itemId == item.itemId)

            return {
              itemId: item.itemId,
              itemCode: itemDetail?.code,
              itemName: itemDetail?.name,
              vat: item.vat,
              quantityBasicUnit: item.quantityBasicUnit,
              sellPrice: +item.sellPrice, // Giá nhập từ PO
              price: +itemPrice.priceSell, // Giá bán chung
              totalAmount: +item.totalAmount,
              totalAmountVat: +item.totalAmountVat,
              partnerId: item.partnerId,
              unit: u?.name,
              baseUnit: u?.baseUnit,
              purchaseUnit: item.purchaseUnit,
            }
          })

          mappingCombo[i] = {
            comboName: combo.comboName,
            comboCode: combo.comboCode,
            comboId: combo.comboId,
            comboPrice: +comboPrice.priceSell,
            quantityCombo: combo.quantityCombo,
            quantityBasicUnit: combo.quantityBasicUnit,
            items: _items,
          }
        }

        result = {
          purchaseOrder: po,
          supplier: mappingSupplier,
          delivery: mappingDelivery,
          distributor: mappingDistributor,
          warehouse: mappingWarehouse,
          balanceLife: blf,
          combos: mappingCombo,
        }
      } else if (po.purchaseOrderType == NSPo.EPoType.MANUAL) {
        const warehouseCenter = await this.warehouseRepository.findOne({ where: { id: po.warehouseId } })
        const itemIds = Array.from(new Set(details.map((val) => val.itemId)))
        const items = await this.itemRepository.find({
          where: {
            id: In(itemIds),
          },
          select: {
            code: true,
            name: true,
            id: true,
          },
        })

        const productsPrice = await this.itemPriceRepo.find({ where: { itemId: In(itemIds), isFinal: true } })

        const mappingDetail = details.map((val) => {
          const item = items.find((i) => i.id == val.itemId)
          const itemPrice = productsPrice.find((p) => p.itemId == val.itemId)
          const u = unit.find((i) => i.id == item.unitId)
          return {
            ...val,
            itemCode: item.code,
            itemName: item.name,
            price: itemPrice.priceSell, // Giá bán chung
            unit: u?.name,
            baseUnit: u?.baseUnit,
            totalAmount: +val.totalAmount,
            totalAmountVat: +val.totalAmountVat,
          }
        })

        result = {
          purchaseOrder: po,
          supplier: mappingSupplier,
          delivery: mappingDelivery, // ! PO Manual có cần đơn vị vận chuyển ?
          distributor: mappingDistributor,
          warehouse: mappingWarehouse, // ! PO Manual không có khoa trung tâm ?
          balanceLife: blf,
          items: mappingDetail,
          warehouseCenter,
        }
      } else {
        // Chi tiết po bán lẻ, xem số lượng của toàn bộ mặc hàng, không phân biệt ở MBC nào
        // Lấy thành phần không có combo
        const detailItemLst = await this.purchaseOrderItemRepo.find({ where: { comboId: IsNull(), purchaseOrderId: po.id, ...wheres } })
        const itemIds = Array.from(new Set(detailItemLst.map((val) => val.itemId)))
        const items = await this.itemRepository.find({
          where: {
            id: In(itemIds),
          },
          select: {
            code: true,
            name: true,
            id: true,
            unitId: true,
            quantityUnit: true,
          },
        })

        const mappingDetail = detailItemLst.map((val) => {
          const item = items.find((i) => i.id == val.itemId)
          const u = unit.find((i) => i.id == item.unitId)
          if (item)
            return {
              ...val,
              ...u,
              quantityUnit: item.quantityUnit,
              itemCode: item.code,
              itemName: item.name,
              sellPrice: +val.sellPrice,
              totalAmount: +val.totalAmount,
              totalAmountVat: +val.totalAmountVat,
            }
        })

        // Tạo object combo lồng thành phần item
        const detailComboLst = await this.purchaseOrderItemRepo.find({ where: { comboId: Not(IsNull()), purchaseOrderId: po.id } })
        const comboIds = Array.from(new Set(detailComboLst.map((val) => val.comboId)))
        const itemInComboIds = Array.from(new Set(detailComboLst.map((val) => val.itemId)))
        const combos = await this.itemRepository.find({
          where: {
            id: In(comboIds),
          },
          select: {
            code: true,
            name: true,
            id: true,
          },
        })

        const comboDetailLst = detailComboLst
          .map((val) => {
            const combo = combos.find((i) => i.id == val.comboId)
            if (combo)
              return {
                ...val,
                comboName: combo.name,
                comboCode: combo.code,
              }
          })
          .filter((id) => id)

        const productsPrice = await this.itemPriceRepo.find({ where: { itemId: In([...itemIds, ...comboIds]), isFinal: true } })

        // Group by lại theo comboId và partnerId
        const uniqueDetail = comboDetailLst.reduce((acc, item) => {
          const existing = acc.find(
            (val) => val.comboId === item.comboId && val.itemId === item.itemId,
            // && val.partnerId === item.partnerId
            // && val.distributorId === item.distributorId,
          )
          if (existing) {
            existing.quantityCombo += Number(item.quantityCombo)
            existing.quantityBasicUnit += Number(item.quantityBasicUnit)
          } else {
            acc.push({ ...item })
          }
          return acc
        }, [])

        // Để xem tổng số lượng combo không phân biệt ở MBC nào
        const comboLstDetails = uniqueDetail.reduce((acc, item) => {
          const existing = acc.find((val) => val.comboId === item.comboId)
          if (!existing) {
            acc.push({ ...item })
          }
          return acc
        }, [])

        const mergeIdLst = Array.from(new Set([...comboIds, ...itemInComboIds]))
        const itemComboLst = await this.itemRepository.find({ where: { id: In(mergeIdLst) } })

        for (let i = 0; i < comboLstDetails.length; i++) {
          const combo = comboLstDetails[i]
          const itemLst = await this.purchaseOrderItemRepo.find({ where: { purchaseOrderId: po.id, comboId: combo.comboId } })
          const comboPrice = productsPrice.find((p) => p.itemId == combo.comboId)

          const _items = itemLst.map((item) => {
            const itemDetail = itemComboLst.find((i) => i.id == item.itemId)
            const u = unit.find((i) => i.id == itemDetail.unitId)
            const itemPrice = productsPrice.find((p) => p.itemId == item.itemId)

            return {
              itemId: item.itemId,
              itemCode: itemDetail?.code,
              itemName: itemDetail?.name,
              vat: item.vat,
              quantityBasicUnit: item.quantityBasicUnit,
              sellPrice: +item.sellPrice, // Giá nhập từ PO
              price: itemPrice, // Giá bán chung
              totalAmount: +item.totalAmount,
              totalAmountVat: +item.totalAmountVat,
              partnerId: item.partnerId,
              unit: u?.name,
              baseUnit: u?.baseUnit,
              quantityUnit: itemDetail.quantityUnit,
              purchaseUnit: item.purchaseUnit,
            }
          })

          comboLstDetails[i] = {
            comboName: combo.comboName,
            comboCode: combo.comboCode,
            comboId: combo.comboId,
            comboPrice: comboPrice.priceSell,
            quantityCombo: combo.quantityCombo,
            items: _items,
          }
        }

        result = {
          purchaseOrder: po,
          supplier: mappingSupplier,
          delivery: mappingDelivery,
          distributor: mappingDistributor,
          warehouse: mappingWarehouse,
          balanceLife: blf,
          combos: comboLstDetails,
          items: mappingDetail,
        }
      }

      // Lấy danh sách SO tham chiếu PO
      const lstSo = await this.purchaseOrderSORepo.find({ where: { poId: po.id }, select: { soId: true } })
      result = { ...result, soIds: Array.from(new Set(lstSo.map((val) => val.soId))) }

      return result
    } catch (error) {
      throw new Error(error)
    }
  }

  public async syncPoPMS(poId: any, req: IRequest) {
    const poBody = await this.purchaseOrderRepo.findOne({ where: { id: poId } })
    if (!poBody) {
      throw new Error(`Không tìm thấy thông tin PO`)
    }
    const supplier = await this.supplierRepo.findOne({ where: { id: poBody.supplierId } })
    const newPmsPo = new POCreatePlatformDto()

    const platformInfor: PoPlatformDto = {
      platformId: process.env.PLATFORM_ID,
      name: enumData.BalanceInfo.name,
      code: enumData.BalanceInfo.code,
    }
    const employeeInfor = await this.employeeRepository.findOne({ where: { id: poBody.createdBy } })
    //mapping data

    newPmsPo.supplier.push({
      supplierId: poBody.supplierId,
      name: supplier.name,
      dealName: supplier.dealName,
      address: supplier.address,
      represen: supplier.represen,
      email: supplier.email,
      phone: supplier.phone,
      createYear: supplier.createYear,
    })
    newPmsPo.type = poBody.purchaseOrderType
    newPmsPo.deliveryDate = poBody.deliveryDate
    newPmsPo.description = poBody.note
    newPmsPo.platform.push(platformInfor)
    newPmsPo.owner.push({
      createdBy: poBody.createdBy,
      name: employeeInfor?.name || 'admin',
      code: employeeInfor?.code || 'admin',
      email: employeeInfor?.email || '',
      phone: employeeInfor?.phone || '',
    })

    //lấy region theo supplychain
    let regionId = (await this.supplyChainConfigDetailRepo.findOne({ where: { id: poBody.supplyChainId } })).regionId
    newPmsPo.region = (await this.regionRepository.findOne({ where: { id: regionId } })).name

    //tìm tất cả item trong PO
    let items = await this.purchaseOrderItemRepo.find({ where: { purchaseOrderId: poBody.id } })
    //gộp các item trùng id
    items = items.reduce((acc, item) => {
      const existing = acc.find((val) => val.itemId == item.itemId)
      if (!existing) {
        acc.push({ ...item })
      } else {
        existing.quantityBasicUnit = Number(existing.quantityBasicUnit) + Number(item.quantityBasicUnit)
        existing.quantityCombo = Number(existing.quantityCombo) + Number(item.quantityCombo)
        existing.totalAmount = Number(existing.totalAmount) + Number(item.totalAmount)
        existing.totalAmountVat = Number(existing.totalAmountVat) + Number(item.totalAmountVat)
      }
      return acc
    }, [])
    let itemDetails: ItemEntity[] = await this.itemRepository.find({
      where: { id: In(items.map((item) => item.itemId)) },
      relations: { itemType: true, itemGroup: true },
    })
    let lstProduct: PoProductPlatformDto[] = []

    for (let item of items) {
      let itemDetail = itemDetails.find((itemDetail) => itemDetail.id == item.itemId)
      let newItem: PoProductPlatformDto
      delete item.id
      newItem = {
        id: item.itemId,
        type: (await itemDetail.itemType)?.name,
        group: (await itemDetail.itemGroup)?.name,
        money: +item.totalAmount,
        // description: itemDetail.description,
        quantity: item.quantityBasicUnit,
        name: itemDetail.name,
        itemCode: itemDetail.code,
        poId: item.purchaseOrderId,
        unit: item.packingSpecification,
        price: +item.sellPrice,
        ...item,
      }
      lstProduct.push(newItem)
    }
    newPmsPo.lstProduct = lstProduct

    await pmsApiHelper.createPO(newPmsPo)
    return newPmsPo
  }

  /** Tạo PO */
  public async createPO(body: CreatePurchaseOrderDto, req: IRequest) {
    return await this.purchaseOrderRepo.manager.transaction(async (trans) => {
      try {
        const { creatorId, deliveryDate, vat, items, purchaseOrderType, warehouseCode } = body
        const supplierArray = Array.from(new Set(items.map((val) => val.supplierId))) // Nhóm id NCC
        const lstSoId = Array.from(new Set(items.flatMap((val) => val.soIdQuantity?.map((m) => m.id) || [])))

        const user: any = await this.userRepository.findOne({ where: { id: creatorId }, relations: ['supplier'] })
        if (!user) {
          throw new Error('Không tìm thấy thông tin người tạo PO')
        }

        if (lstSoId.length > 0) {
          const checkSoId = await this.purchaseOrderSORepo.find({ where: { soId: In(lstSoId) } })
          if (checkSoId.length > 0) {
            const poIds = checkSoId.map((val) => val.poId)
            const checkPo = await this.purchaseOrderRepo.find({ where: { id: In(poIds), status: Not(NSPo.EPoStatus.CANCEL) } })
            if (checkPo.length > 0) {
              throw new Error(`SO đã được tạo PO trước đó`)
            }
          }
        }

        const approveDate = moment(new Date()).add(2, 'days').format('YYYY-MM-DD HH:mm:ss')
        const deliveryDateEst = moment(new Date()).add(5, 'days').format('YYYY-MM-DD HH:mm:ss')

        const rs = []
        const packageList = []
        const chunkSize = 1000
        const chunk = []
        if (purchaseOrderType == NSPo.EPoType.WITHCOMBO) {
          const checkCombo = this.checkComboMultipleSuppliers(items)
          const countPoGroup = await this.countPoGroupToday()
          const poGroupCode = checkCombo.length > 0 ? coreHelper.generatePOString(countPoGroup, 'POG') : ''

          for (const sup of supplierArray) {
            const countPo = await this.purchaseOrderRepo.count()
            const poCode = coreHelper.generatePOString(countPo)

            /** Tách PO theo nhà cung cấp */
            const filterItem = items.filter((val) => val.supplierId === sup)
            if (filterItem.length > 0) {
              const totalAmount = filterItem.reduce((sum, item) => sum + item.totalAmount, 0)
              const totalAmountVat = filterItem.reduce((sum, item) => sum + item.totalAmountVat, 0)
              const mapArrSoId = Array.from(new Set(filterItem.flatMap((val) => val.soIdQuantity.map((m) => m.id) || []).filter((id) => id)))

              // Tìm chuỗi cung ứng dựa vào supplierId
              const regions = Array.from(new Set(filterItem.map((i) => i.regionId)))
              //const supplyChain = await this.getSupplyChain(regions[0], sup)
              const supplyChain = await this.getSupplyChainByDistributor(filterItem[0].distributorId, sup) // Lấy chuỗi cung ứng bằng distributorId và supplierId
              if (!supplyChain) {
                throw new Error(`Không tìm thấy chuỗi cung ứng`)
              }

              // Lấy ra cấu hình duyệt theo chuỗi cung ứng
              const spc = await this.supplyChainConfigApproveRepo.find({
                where: { supplyChainDetailId: supplyChain.supplyChainDetailId, supplyChainId: supplyChain.supplyChainId },
              })
              if (!spc || spc.length == 0) {
                throw new Error(`Không tìm thấy danh sách duyệt chuỗi cung ứng`)
              }
              const approverIds = spc.map((val) => val.approverId)

              const newPO = await this.purchaseOrderRepo.save({
                purchaseOrderCode: poCode,
                approveDate: approveDate,
                creatorId,
                createdBy: creatorId,
                deliveryDate: deliveryDateEst,
                vat: +totalAmountVat - +totalAmount,
                totalAmount,
                totalAmountVat,
                supplierId: sup,
                purchaseOrderGroup: poGroupCode,
                amountSO: mapArrSoId.length,
                supplyChainId: spc[0].supplyChainDetailId, // Lưu theo ID chi tiết chuỗi
                soIds: [],
              })

              rs.push(newPO)

              for (const approver of approverIds) {
                await this.purchaseOrderApproveRepo.save({
                  purchaseOrderId: newPO.id,
                  supplierId: approver,
                })
              }

              for (let i = 0; i < filterItem.length; i++) {
                const product = filterItem[i]
                const soIdQuantity = product.soIdQuantity
                for (let j = 0; j < soIdQuantity.length; j++) {
                  const so = soIdQuantity[j]
                  // Nếu đã có soId thì bỏ qua
                  if (chunk.find((c) => c.soId == so.id && c.poId == newPO.id)) {
                    continue
                  }
                  chunk.push({
                    poId: newPO.id,
                    soId: so.id,
                    orderAddressType: product.addressType,
                    warehouseId: product.warehouseId,
                  })
                }
              }

              const mappingItems = filterItem.map((val) => ({
                purchaseOrderId: newPO.id,
                comboId: val.comboId,
                regionId: val.regionId,
                itemGroupId: val.itemGroupId,
                itemId: val.itemId,
                itemName: val.itemName,
                quantityBasicUnit: val.quantityBasicUnit,
                packingSpecification: val.packingSpecification,
                quantityCombo: val.quantityCombo,
                sellPrice: val.sellPrice, // Lưu giá là giá nhập kho gần nhất
                vat: val.vat,
                totalAmount: val.totalAmount,
                totalAmountVat: val.totalAmountVat,

                partnerId: val.partnerId,
                supplierId: val.supplierId,
                deliveryId: val.deliveryId,
                distributorId: val.distributorId,
                warehouseId: val.warehouseId,
                provinceId: val.provinceId,

                purchaseUnit: val.purchaseUnit,
                itemVat: val.itemVat,
                soIds: val.soIds, // Lưu lại danh sách SO tham chiếu
              }))
              await this.purchaseOrderItemRepo.save(mappingItems)
            }
          }

          // Kiểm tra 1 combo nhiều NCC
          if (checkCombo.length > 0) {
            // const totalAmount = rs.reduce((sum, po) => sum + po.totalAmount, 0)
            // const totalAmountVat = rs.reduce((sum, po) => sum + po.totalAmountVat, 0)
            // const newDL = await this.deliveryNoteRepo.save({
            //   code: coreHelper.generateDeliveryNoteCode(await this.deliveryNoteRepo.count(), 'ASNT'),
            //   poId: rs.map((po) => po.id),
            //   poGroupId: poGroupCode,
            //   itemTotalAmount: totalAmount,
            //   itemTotalAmountVat: totalAmountVat,
            // })
            // for (const po of rs) {
            //   const poItems = await this.purchaseOrderItemRepo.find({ where: { purchaseOrderId: po.id } })
            //   const mappingDelivery = Array.from(new Set(poItems.map((val) => val.deliveryId)))
            //   const supplyChainDetail = await this.getSupplyChainDetails(po.supplierId, poItems)
            //   const mappingDistributor = Array.from(new Set(poItems.map((val) => val.distributorId)));
            //   await this.createDeliveryTracking(newDL.id, po, mappingDelivery, supplyChainDetail, packageList, mappingDistributor)
            // }
          } else {
            const countDeliveryNote = await this.deliveryNoteRepo.count()
            const deliveryNoteCode = coreHelper.generateDeliveryNoteCode(countDeliveryNote, 'ASNT')
            const deliveryNoteData = {
              code: deliveryNoteCode,
              poId: rs.map((po) => po.id),
              itemTotalAmountVat: rs.reduce((sum, po) => sum + po.totalAmountVat, 0),
              itemTotalAmount: rs.reduce((sum, po) => sum + po.totalAmount, 0),
            }
            const dataObj = await this.deliveryNoteRepo.save(deliveryNoteData)
            // Tạo delivery tracking
            const poItems = await this.purchaseOrderItemRepo.find({ where: { purchaseOrderId: In(rs.map((po) => po.id)) } })
            const mappingDistributor = Array.from(new Set(poItems.map((val) => val.distributorId)))
            await this.createDeliveryTracking(dataObj.id, rs, mappingDistributor)
          }

          if (lstSoId.length > 0) {
            await omsApiHelper.updateSOStatus(req, { ids: lstSoId, status: NSRecurringOrder.EStatus.PLACED })
          }
        } else if (purchaseOrderType == NSPo.EPoType.MANUAL) {
          for (const sup of supplierArray) {
            const filterItem = items.filter((val) => val.supplierId === sup)

            const warehouse = await this.warehouseRepository.findOne({ where: { code: warehouseCode } })
            if (!warehouse) {
              throw new Error(`Không tìm thấy thông tin kho trung tâm`)
            }

            if (filterItem.length > 0) {
              const countPo = await this.purchaseOrderRepo.count()
              const poCode = coreHelper.generatePOString(countPo)
              const totalAmount = filterItem.reduce((sum, item) => sum + +item.totalAmount, 0)
              const totalAmountVat = filterItem.reduce((sum, item) => sum + +item.totalAmountVat, 0)
              const mapArrSoId = Array.from(new Set(filterItem.flatMap((val) => val.soIds || []).filter((id) => id)))

              //const regions = Array.from(new Set(filterItem.map((i) => i.regionId)))
              //const supplyChain = await this.getSupplyChain(regions[0], sup)
              const supplyChain = await this.getSupplyChainByDistributor(filterItem[0].distributorId, sup) // Lấy chuỗi cung ứng bằng distributorId và supplierId
              if (!supplyChain) {
                throw new Error(`Không tìm thấy chuỗi cung ứng`)
              }

              // Lấy ra cấu hình duyệt theo chuỗi cung ứng
              const spc = await this.supplyChainConfigApproveRepo.find({
                where: { supplyChainDetailId: supplyChain.supplyChainDetailId, supplyChainId: supplyChain.supplyChainId },
              })
              if (!spc || spc.length == 0) {
                throw new Error(`Không tìm thấy danh sách duyệt chuỗi cung ứng`)
              }

              const approverIds = spc.map((val) => val.approverId)

              const newPO = await this.purchaseOrderRepo.save({
                purchaseOrderType: purchaseOrderType,
                purchaseOrderCode: poCode,
                creatorId,
                createdBy: creatorId,
                approveDate: approveDate,
                deliveryDate: deliveryDateEst,
                vat: +totalAmountVat - +totalAmount,
                totalAmount,
                totalAmountVat,
                supplierId: sup,
                purchaseOrderGroup: '',
                amountSO: mapArrSoId.length,
                supplyChainId: spc[0].supplyChainDetailId, // Lưu theo ID chi tiết chuỗi
                soIds: [],
                warehouseId: warehouse.id,
              })

              rs.push(newPO)

              for (const approver of approverIds) {
                await this.purchaseOrderApproveRepo.save({
                  purchaseOrderId: newPO.id,
                  supplierId: approver,
                })
              }
              const mappingItems = filterItem.map((val) => ({
                purchaseOrderId: newPO.id,
                comboId: val.comboId ?? null,
                regionId: val.regionId,
                itemGroupId: val.itemGroupId,
                itemId: val.itemId,
                itemName: val.itemName,
                quantityBasicUnit: val.quantityBasicUnit,
                packingSpecification: val.packingSpecification,
                quantityCombo: val.quantityCombo ?? 0,
                sellPrice: val.sellPrice,
                vat: val.vat,
                totalAmount: val.totalAmount,
                totalAmountVat: val.totalAmountVat,
                partnerId: val.partnerId,
                supplierId: val.supplierId,
                deliveryId: val.deliveryId,
                distributorId: val.distributorId,
                warehouseId: val.warehouseId, // Kho 3PL
                provinceId: val.provinceId,
                purchaseUnit: val.purchaseUnit,
                itemVat: val.vat,
              }))
              await this.purchaseOrderItemRepo.save(mappingItems)
            }

            // Tạo phiếu giao nhận
            const countDeliveryNote = await this.deliveryNoteRepo.count()
            const deliveryNoteCode = coreHelper.generateDeliveryNoteCode(countDeliveryNote, 'ASNT')
            const deliveryNoteData = {
              code: deliveryNoteCode,
              poId: rs.map((po) => po.id),
              itemTotalAmountVat: rs.reduce((sum, po) => +sum + +po.totalAmountVat, 0),
              itemTotalAmount: rs.reduce((sum, po) => +sum + +po.totalAmount, 0),
            }
            const newDN = await this.deliveryNoteRepo.save(deliveryNoteData)
            // Tạo delivery tracking
            const poItems = await this.purchaseOrderItemRepo.find({ where: { purchaseOrderId: In(rs.map((po) => po.id)) } })
            const mappingDistributor = Array.from(new Set(poItems.map((val) => val.distributorId)))

            await this.createDeliveryTracking(newDN.id, rs, mappingDistributor)
          }
        } else if (purchaseOrderType == NSPo.EPoType.SUPPLIER) {
          const result = await this.createPOSupplier(body, req)
          rs.push(...result)
        } else {
          /** SO bán lẻ */
          // Duyệt items để tạo PO
          for (const sup of supplierArray) {
            const filterItem = items.filter((val) => val.supplierId === sup)
            if (filterItem.length > 0) {
              const countPo = await this.purchaseOrderRepo.count()
              const poCode = coreHelper.generatePOString(countPo)
              const totalAmount = filterItem.reduce((sum, item) => sum + item.totalAmount, 0)
              const totalAmountVat = filterItem.reduce((sum, item) => sum + item.totalAmountVat, 0)
              const mapArrSoId = Array.from(new Set(filterItem.flatMap((val) => val.soIds || []).filter((id) => id)))

              // Tìm chuỗi cung ứng dựa vào supplierId
              const regions = Array.from(new Set(filterItem.map((i) => i.regionId)))
              //const supplyChain = await this.getSupplyChain(regions[0], sup)
              const supplyChain = await this.getSupplyChainByDistributor(filterItem[0].distributorId, sup) // Lấy chuỗi cung ứng bằng distributorId và supplierId
              if (!supplyChain) {
                throw new Error(`Không tìm thấy chuỗi cung ứng`)
              }

              // Lấy ra cấu hình duyệt theo chuỗi cung ứng
              const spc = await this.supplyChainConfigApproveRepo.find({
                where: { supplyChainDetailId: supplyChain.supplyChainDetailId, supplyChainId: supplyChain.supplyChainId },
              })
              if (!spc || spc.length == 0) {
                throw new Error(`Không tìm thấy danh sách duyệt chuỗi cung ứng`)
              }

              const approverIds = spc.map((val) => val.approverId)
              const newPO = await this.purchaseOrderRepo.save({
                purchaseOrderType: NSPo.EPoType.OTHER,
                purchaseOrderCode: poCode,
                creatorId,
                createdBy: creatorId,
                approveDate: approveDate,
                deliveryDate: deliveryDateEst,
                vat: +totalAmountVat - +totalAmount,
                totalAmount,
                totalAmountVat,
                supplierId: sup,
                purchaseOrderGroup: '',
                amountSO: mapArrSoId.length,
                supplyChainId: spc[0].supplyChainDetailId, // Lưu theo ID chi tiết chuỗi
                soIds: [],
              })

              rs.push(newPO)

              for (const approver of approverIds) {
                await this.purchaseOrderApproveRepo.save({
                  purchaseOrderId: newPO.id,
                  supplierId: approver,
                })
              }

              // Lưu lại danh sách SO tham chiếu
              // Duyệt filterItem lấy ra soIdQuantity, tạo dữ liệu tham chiếu theo từng sản phẩm trong SO
              for (let i = 0; i < filterItem.length; i++) {
                const product = filterItem[i]
                const soIdQuantity = product.soIdQuantity
                for (let j = 0; j < soIdQuantity.length; j++) {
                  const so = soIdQuantity[j]
                  // Nếu đã có soId thì bỏ qua
                  if (chunk.find((c) => c.soId == so.id && c.poId == newPO.id)) {
                    continue
                  }
                  chunk.push({
                    poId: newPO.id,
                    soId: so.id,
                    orderAddressType: product.addressType,
                    warehouseId: product.warehouseId,
                  })
                }
              }
              const mappingItems = filterItem.map((val) => ({
                purchaseOrderId: newPO.id,
                comboId: val.comboId ?? null,
                regionId: val.regionId,
                itemGroupId: val.itemGroupId,
                itemId: val.itemId,
                itemName: val.itemName,
                quantityBasicUnit: val.quantityBasicUnit,
                packingSpecification: val.packingSpecification,
                quantityCombo: val.quantityCombo ?? 0,
                sellPrice: val.inputPrice, // Lưu giá là giá nhập kho gần nhất
                vat: val.vat,
                totalAmount: val.totalAmount,
                totalAmountVat: val.totalAmountVat,

                partnerId: val.partnerId,
                supplierId: val.supplierId,
                deliveryId: val.deliveryId,
                distributorId: val.distributorId,
                warehouseId: val.warehouseId,
                provinceId: val.provinceId,

                purchaseUnit: val.purchaseUnit,
                itemVat: val.itemVat,
                soIds: val.soIds, // Lưu lại danh sách SO tham chiếu
              }))
              await this.purchaseOrderItemRepo.save(mappingItems)
            }
          }
          if (lstSoId.length > 0) {
            await omsApiHelper.updateSOStatus(req, { ids: lstSoId, status: NSRecurringOrder.EStatus.PLACED })
          }

          // Thay logic Tạo phiếu giao hàng 1 deliveryNoteRepo sẽ gồm nhiều PO
          const countDeliveryNote = await this.deliveryNoteRepo.count()
          const deliveryNoteCode = coreHelper.generateDeliveryNoteCode(countDeliveryNote, 'ASNT')
          const deliveryNoteData = {
            code: deliveryNoteCode,
            poId: rs.map((po) => po.id),
            itemTotalAmountVat: rs.reduce((sum, po) => +sum + +po.totalAmountVat, 0),
            itemTotalAmount: rs.reduce((sum, po) => +sum + +po.totalAmount, 0),
          }
          const dataObj = await this.deliveryNoteRepo.save(deliveryNoteData)
          // Tạo delivery tracking
          const poItems = await this.purchaseOrderItemRepo.find({ where: { purchaseOrderId: In(rs.map((po) => po.id)) } })
          const mappingDistributor = Array.from(new Set(poItems.map((val) => val.distributorId)))

          await this.createDeliveryTracking(dataObj.id, rs, mappingDistributor)
        }

        // Lưu tham chiếu
        if (chunk.length > 0) {
          await this.purchaseOrderSORepo.save(chunk)
        }

        // Tạo lịch sử
        for (const po of rs) {
          await this.addHistory(po.id, po.purchaseOrderCode, user.username, 'CREATE')
          // Nếu là PO Supplier thì không tạo phiếu nhập kho
          if (po.purchaseOrderType == NSPo.EPoType.SUPPLIER) {
            continue
          } else {
            // Tạo phiếu nhập kho
            await this.inboundSupplierWarehouse(po.id, req)
          }
        }

        return { data: rs, message: CREATE_SUCCESS }
      } catch (error) {
        throw new Error(error?.message || 'Internal Server Error')
      }
    })
  }

  public async createPOSupplier(body: CreatePurchaseOrderDto, req: IRequest) {
    try {
      const { creatorId, deliveryDate, items, purchaseOrderType, warehouseId } = body
      if (!warehouseId) {
        throw new Error('WarehouseId là bắt buộc')
      }
      const user: any = await this.userRepository.findOne({ where: { id: creatorId }, relations: ['supplier'] })
      if (!user) {
        throw new Error('Không tìm thấy thông tin người tạo PO')
      }

      const supplierId = await this.supplierRepo.findOne({ where: { code: 'BA' }, select: ['id'] }).then((res) => res.id)
      const rs = []
      const itemsInWH = items.filter((val) => val.warehouseId == warehouseId)
      const countPo = await this.purchaseOrderRepo.count()
      const poCode = coreHelper.generatePOString(countPo)
      const totalAmount = itemsInWH.reduce((sum, item) => sum + item.totalAmount, 0)
      const totalAmountVat = itemsInWH.reduce((sum, item) => sum + item.totalAmountVat, 0)
      const mapArrSoId = Array.from(new Set(itemsInWH.flatMap((val) => val.soIds || []).filter((id) => id)))

      const newPO = await this.purchaseOrderRepo.save({
        purchaseOrderType: purchaseOrderType,
        purchaseOrderCode: poCode,
        creatorId,
        createdBy: creatorId,
        approveDate: new Date(),
        deliveryDate: deliveryDate,
        vat: +totalAmountVat - +totalAmount,
        totalAmount,
        totalAmountVat,
        supplierId: supplierId,
        purchaseOrderGroup: '',
        amountSO: mapArrSoId.length,
        warehouseId: warehouseId,
        soIds: [],
      })

      rs.push(newPO)

      await this.purchaseOrderApproveRepo.save({
        purchaseOrderId: newPO.id,
        supplierId: supplierId,
      })

      const mappingItems = itemsInWH.map((val) => ({
        purchaseOrderId: newPO.id,
        comboId: val.comboId ?? null,
        regionId: val.regionId,
        itemGroupId: val.itemGroupId,
        itemId: val.itemId,
        itemName: val.itemName,
        itemCode: val.itemCode,
        quantityBasicUnit: val.quantityBasicUnit,
        packingSpecification: val.packingSpecification,
        quantityCombo: val.quantityCombo ?? 0,
        sellPrice: val.inputPrice, // Lưu giá là giá nhập kho gần nhất
        vat: val.vat,
        totalAmount: val.totalAmount,
        totalAmountVat: val.totalAmountVat,
        partnerId: val.partnerId,

        supplierId: val.supplierId,
        deliveryId: val.deliveryId,
        distributorId: val.distributorId,
        warehouseId: val.warehouseId,
        provinceId: val.provinceId,

        purchaseUnit: val.purchaseUnit,
        itemVat: val.itemVat,
      }))
      await this.purchaseOrderItemRepo.save(mappingItems)

      return rs
    } catch (error) {
      throw new Error(error?.message || 'Internal Server Error')
    }
  }

  //#region Tạo PNK cho Supplier
  /** Tạo Phiếu nhập kho cho nhà cung cấp (Xuất xứ) */
  private async inboundSupplierWarehouse(poId: string, req: IRequest) {
    try {
      const po = await this.purchaseOrderRepo.findOne({ where: { id: poId } })
      if (!po) {
        throw new Error(`Không tìm thấy PO`)
      }
      /** Lấy ra thông tin kho supplier của PO */
      const wh = await this.warehouseRepository.findOne({ where: { storeId: po.supplierId } })
      if (!wh) {
        throw new Error(`Không tìm thấy thông tin kho của nhà cung cấp của ${po.purchaseOrderCode}`)
      }

      // Danh sách sản phẩm của PO
      //const poCombos = await this.purchaseOrderItemRepo.find({ where: { purchaseOrderId: poId, comboId: Not(IsNull()) } })
      let poItems: any = await this.purchaseOrderItemRepo.find({ where: { purchaseOrderId: poId } })
      //gộp các item cùng itemName và sellPrice
      poItems = poItems.reduce((acc, item) => {
        const existing = acc.find((val) => val.itemName == item.itemName && Number(val.sellPrice) == Number(item.sellPrice))
        if (!existing) {
          acc.push({ ...item })
        } else {
          existing.quantityBasicUnit = Number(existing.quantityBasicUnit) + Number(item.quantityBasicUnit)
        }
        return acc
      }, [])

      //const comboIds = Array.from(new Set(poCombos.map((val) => val.comboId)))
      const itemIds = poItems.map((val) => val.itemId)

      /** Lấy ra danh sách sản phẩm của kho Supplier */
      const whProductLst = await this.warehouseProductRepository.find({ where: { warehouseId: wh.id, productId: In(itemIds) } })

      const lstDetails = []
      const items = await this.itemRepository.find({
        where: {
          id: In(itemIds),
        },
      })

      poItems = poItems.map((val) => {
        const item = items.find((i) => i.id == val.itemId)
        return {
          ...val,
          itemCode: item.code,
          itemName: item.name,
        }
      })
      const itemPrices = await this.itemPriceRepo.find({ where: { itemId: In([itemIds].flat()), isFinal: true } })
      for (const item of poItems) {
        const price = itemPrices.find((val) => val.itemId === item.itemId)
        // Kiểm tra hàng có trong kho NCC ?
        const checkAvailable = whProductLst.find((w) => w.productId == item.itemId)

        // Nếu là PO đặt trước
        if (po.purchaseOrderType === NSPo.EPoType.MANUAL) {
          // Tiếp tục cộng dồn số lượng sản phẩm theo PO
          lstDetails.push({
            poDetailId: item.id,
            productCode: item.itemCode,
            productName: item.itemName,
            productId: item.itemId,
            quantity: item.quantityBasicUnit,
            totalQuantity: item.quantityBasicUnit,
            price: +price.priceSell, // Giá bán chung
            priceVND: +price.priceSell, // Giá bán chung
            totalPrice: +item.quantityBasicUnit * +price.priceSell, // Số lượng * giá mua PO
            totalPriceVND: +item.quantityBasicUnit * +price.priceSell, // Số lượng * giá mua PO
            buyPrice: +item.sellPrice, //  giá từ PO
            buyPriceVND: +item.sellPrice, // giá từ PO
            manufactureDate: new Date('2000-01-01'),
            expiryDate: new Date('2500-01-01'),
          })
        } else {
          if (checkAvailable) {
            if (checkAvailable.quantity < item.quantityBasicUnit) {
              // Số lượng trong kho nhỏ hơn số lượng lên PO
              const numberDiff = +item.quantityBasicUnit - +checkAvailable.quantity // Phần chênh lệch
              // lstDetails.push({
              //   poDetailId: detail.id,
              //   productCode: item.itemCode,
              //   productName: item.name,
              //   productId: item.itemId,
              //   quantity: quantity,
              //   totalQuantity: quantity,
              //   price: +price.priceSell, // Giá bán chung
              //   priceVND: +price.priceSell,
              //   totalPrice: +quantity * +price.priceSell, // Số lượng * giá bán chung
              //   totalPriceVND: +quantity * +price.priceSell, // Số lượng * giá bán chung
              //   buyPrice: +detail.sellPrice, //  giá từ PO
              //   buyPriceVND: +detail.sellPrice, // giá từ PO
              //   manufactureDate: new Date('2000-01-01'),
              //   expiryDate: new Date('2500-01-01'),
              // })
            }

            lstDetails.push({
              itemId: item.itemId,
              poDetailId: item.id,
              productCode: item.itemCode,
              productName: item.itemName,
              productId: item.itemId,
              quantity: item.quantityBasicUnit,
              totalQuantity: item.quantityBasicUnit,
              price: +price.priceSell, // Giá bán chung
              priceVND: +price.priceSell,
              totalPrice: +item.quantityBasicUnit * +price.priceSell, // Số lượng * giá bán chung
              totalPriceVND: +item.quantityBasicUnit * +price.priceSell, // Số lượng * giá bán chung
              buyPrice: +item.sellPrice, //  giá từ PO
              buyPriceVND: +item.sellPrice, // giá từ PO
              manufactureDate: new Date('2000-01-01'),
              expiryDate: new Date('2500-01-01'),
            })
          } else {
            lstDetails.push({
              itemId: item.itemId,
              poDetailId: item.id,
              productCode: item.itemCode,
              productName: item.itemName,
              productId: item.itemId,
              quantity: item.quantityBasicUnit,
              totalQuantity: item.quantityBasicUnit,
              price: +price.priceSell, // Giá bán chung
              priceVND: +price.priceSell, // Giá bán chung
              totalPrice: +item.quantityBasicUnit * +price.priceSell, // Số lượng * giá bán chung
              totalPriceVND: +item.quantityBasicUnit * +price.priceSell, // Số lượng * giá bán chung
              buyPrice: +item.sellPrice, //  giá từ PO
              buyPriceVND: +item.sellPrice, // giá từ PO
              manufactureDate: new Date('2000-01-01'),
              expiryDate: new Date('2500-01-01'),
            })

            // Chưa có hàng trong kho
          }
        }
      }

      const inbound = {
        poId: poId,
        poCode: po.purchaseOrderCode,
        exchangeRate: 1,
        warehouseId: wh.id,
        currencyCode: 'VND',
        createBy: po.creatorId,
        description: 'Phiếu nhập kho NCC tạo từ PO',
        lstDetail: lstDetails,
      }

      if (lstDetails.length > 0) {
        return await this.inboundService.createData(inbound, req)
      }
      return { message: CREATE_SUCCESS }
    } catch (error) {
      throw new Error(error?.message || 'Internal Server Error')
    }
  }
  //#endregion

  //#region Duyệt PNK cho Supplier
  /** Approve phiếu nhập kho cho nhà cung cấp (Xuất xứ) */
  private async approveInboundSupplierWarehouse(poId: string, req: IRequest) {
    const po = await this.purchaseOrderRepo.findOne({ where: { id: poId } })
    if (!po) {
      throw new Error(`Không tìm thấy PO`)
    }

    // Tìm phiếu nhập kho với poId
    const inbound = await this.inboundRepository.findOne({ where: { poId } })
    if (inbound) {
      const inboundDetail = await this.inboundDetailRepository.find({ where: { inboundId: inbound.id } })
      const mappingProduct = inboundDetail.map((i) => {
        return {
          productId: i.productId,
          quantity: i.quantity,
        }
      })

      // Approve phiếu nhập kho
      await this.inboundService.approveData({ id: inbound.id, approveBy: po.supplierId }, req)
      // Tăng tồn kho NCC
      const rsIncrease = await this.warehouseService.increaseQuantityOrder(mappingProduct, po.purchaseOrderType)
      if (!rsIncrease.message) {
        throw new Error(`Lỗi tăng tồn kho NCC`)
      }
    }
    return { message: 'UPDATE SUCCESS' }
  }
  //#endregion

  //#region Import PO Supplier
  public async importPoSupplier(file: Express.Multer.File, req: IRequest, userId: string) {
    if (!file) {
      throw new Error('File not provided')
    }

    const currentUser = await this.userRepository.findOne({
      where: {
        id: userId,
      },
      relations: { supplier: true },
    })
    const currentSP = await currentUser.supplier

    const data = await this.readFileImportSupplier(file, req)

    // Kiểm tra nếu cùng productCode mà khác Vat thì báo lỗi
    const checkVat = Object.values(data).flatMap((val) =>
      val.items.map((item) => ({
        productCode: item.productCode,
        vat: item.vat,
      })),
    )
    const checkVatDuplicate = checkVat.filter((val, index, self) => {
      return self.some((v, i) => i !== index && v.productCode === val.productCode && v.vat !== val.vat)
    })

    //danh sách các sản phẩm không cho phép đặt hàng
    let lstItemNotAllowOrder: string[] = []
    let lstItemCodeNotAllow: string[] = []
    for (let val of checkVat) {
      if (lstItemCodeNotAllow.includes(val.productCode)) continue

      let item = await this.itemRepository.findOne({
        where: { code: val.productCode, orderPlatformType: Like(`%${NSItem.EOrderPlatformType.AIPROCUREMENT}%`), isDeleted: false },
        select: { orderPlatformType: true },
      })
      if (!item) {
        lstItemCodeNotAllow.push(val.productCode)
        lstItemNotAllowOrder.push(`Sản phẩm ${val.productCode} không tồn tại`)
      }
    }

    if (lstItemNotAllowOrder.length > 0) {
      throw new Error(` </br> ${lstItemNotAllowOrder.join('</br> ')} </br> Vui lòng kiểm tra lại dữ liệu import !!!`)
    }

    if (checkVatDuplicate.length > 0) {
      throw new Error(`Có sản phẩm VAT không giống nhau, vui lòng kiểm tra lại dữ liệu import`)
    }

    const codes = []
    for (const element of Object.values(data)) {
      for (const item of element.items) {
        codes.push(item.productCode)
      }
    }
    const warehouseCodes = Object.keys(data)
    const warehouses = await this.warehouseRepository.find({
      where: { code: In(warehouseCodes) },
      select: { id: true, code: true, name: true, storeId: true },
    })
    const storeIds = Array.from(new Set(warehouses.map((val) => val.storeId)))
    // Lấy thông tin partner oms api helper
    const partners = await omsApiHelper.lstPartnerByCodes(req, { ids: storeIds })
    if (storeIds.length !== partners.length) throw new Error(`Có MBC không hoạt động, vui lòng kiểm tra lại dữ liệu import`)
    const districtCodeLst = partners.map((val) => val.districtCode)

    const mapCodes = Array.from(new Set(codes))
    const items = await this.itemRepository.find({
      relations: { unit: true, itemGroup: true, pOUnit: true, buyTax: true },
    })
    const supplierIds = Array.from(new Set(items.map((val) => val.supplierId)))
    const suppliers = await this.supplierRepo.find({
      where: { id: In(supplierIds) },
      select: { id: true, name: true, code: true },
    })

    const mappingData = Object.entries(data)
    const dataImport = []
    for (const [warehouseCode, wh] of mappingData) {
      const whInfo = warehouses.find((w) => w.code === warehouseCode)
      const partner = partners.find((p) => p.id === whInfo?.storeId)
      if (!partner) throw new Error(`Không tìm thấy Kho MBC cho ${warehouseCode}`)
      const cityCode = partner?.provinceCode
      if (!cityCode) throw new Error(`Không tìm thấy tỉnh thành cho ${partner.name}`)
      const region = await this.getRegionIdByProvinceCode(cityCode)
      if (!region) throw new Error(`Không tìm thấy vùng cho ${cityCode}`)

      const whItems = []
      for (const item of wh.items) {
        const i = items.find((i) => String(i.code) === String(item.productCode))
        if (!i) throw new Error(`Không tìm thấy sản phẩm ${item.productCode}`)
        if (i.isCombo) {
          // Lấy danh sách thành phần trong combo
          const comboItems = await this.itemComboRepo.find({ where: { itemId: i.id } })

          const dictPricing = {}
          {
            const pricing = await this.pricingRepo.find({
              where: { itemId: In(comboItems.map((val) => val.itemInComboId)), supplierId: currentUser.supplierId },
            })
            pricing.forEach((val) => (dictPricing[val.itemId] = val))
          }

          for (const comboItem of comboItems) {
            const comboItemInfo = items.find((i) => i.id === comboItem.itemInComboId)
            if (!comboItemInfo) throw new Error(`Không tìm thấy sản phẩm ${comboItem.itemInComboId}`)

            const group = await comboItemInfo.itemGroup
            const unit = await comboItemInfo.unit
            const poUnit = await comboItemInfo.pOUnit
            const price = await this.itemPriceRepo.findOne({ where: { itemId: comboItemInfo.id, isFinal: true } })

            const sup = suppliers.find((s) => s.id === comboItemInfo?.supplierId)
            if (!sup) throw new Error(`Không tìm thấy nhà cung cấp cho sản phẩm ${comboItemInfo?.code}`)

            // Lấy chuỗi cung ứng
            const supplyChain: any = await this.getSupplyChainForWholesale(currentSP.id, sup.id, region.id)
            if (!supplyChain) throw new Error(`Không tìm thấy chuỗi cung ứng cho sản phẩm ${item.productCode}`)
            const scDetail = supplyChain.supplyChainDetail
            const quantityItemInCombo = +item.quantity * +comboItem.quantity

            const vat = +item.vat
            const totalAmount = +price.priceSell * +quantityItemInCombo
            const totalAmountVat = +totalAmount + +totalAmount * (vat / 100)
            const buyTax = await i.buyTax
            whItems.push({
              warehouseCenterId: whInfo?.id,
              comboId: i.id,
              comboName: i.name,
              comboCode: i.code,
              itemGroupId: group?.id,
              itemName: comboItemInfo?.name,
              itemCode: comboItemInfo?.code,
              itemId: comboItemInfo?.id,

              supplierId: sup?.id,
              supplierCode: sup?.code,
              quantityBasicUnit: quantityItemInCombo,
              quantityCombo: +item.quantity,
              partnerCode: partner?.code,
              partnerName: partner?.name,
              partnerId: whInfo?.storeId,
              totalAmount: totalAmount,
              totalAmountVat: Math.round(totalAmountVat),
              addressType: 'MBC',
              deliveryId: scDetail.deliveryId,
              distributorId: scDetail.distributorId,
              warehouseId: whInfo?.id,
              provinceId: region.cityId,
              regionId: region.id,
              regionName: region.name,
              packingSpecification: unit?.name,
              baseUnit: unit?.baseUnit,
              purchaseUnit: poUnit?.name,
              purchaseBaseUnit: poUnit?.baseUnit,

              vat: +item?.vat || (await i.buyTax)?.percent || 0,
              sellPrice: price.priceSell,
              inputPrice: price.priceInput || dictPricing[comboItemInfo.id]?.price || 0,
            })
          }
        } else {
          const group = await i.itemGroup
          const unit = await i.unit
          const poUnit = await i.pOUnit
          const sup = suppliers.find((s) => s.id === i?.supplierId)
          if (!sup) throw new Error(`Không tìm thấy nhà cung cấp cho sản phẩm ${item.productCode}`)

          const pricing = await this.pricingRepo.findOne({
            where: { itemId: i.id, supplierId: currentUser.supplierId },
          })

          const vat = +item.vat
          const totalAmount = +item.unitPrice * +item.quantity
          const totalAmountVat = +totalAmount + +totalAmount * (vat / 100)

          // Lấy chuỗi cung ứng
          const supplyChain = await this.getSupplyChain(region[0].regionId, sup.id)
          if (!supplyChain) throw new Error(`Không tìm thấy chuỗi cung ứng cho sản phẩm ${item.productCode}`)

          const { distributor, warehouse } = await this.getDistributorAndWarehouseByProvinceCode(
            partner.provinceCode,
            districtCodeLst,
            i.supplierId,
            supplyChain?.deliveryId,
          )
          const buyTax = (await i.buyTax)?.percent

          whItems.push({
            warehouseCenterId: whInfo?.id,
            itemGroupId: group?.id,
            itemName: i?.name,
            itemCode: i?.code,
            itemId: i?.id,

            supplierId: sup?.id,
            supplierCode: sup?.code,
            quantityBasicUnit: item.quantity,
            partnerCode: partner?.code,
            partnerName: partner?.name,
            partnerId: whInfo?.storeId,
            totalAmount: totalAmount,
            totalAmountVat: Math.round(totalAmountVat),
            addressType: 'MBC',
            deliveryId: supplyChain?.deliveryId,
            distributorId: distributor[0], // NPP
            warehouseId: whInfo?.id, // Kho 3PL
            provinceId: region.cityId,
            regionId: region[0].regionId,
            regionName: region.regionName,
            packingSpecification: unit?.name,
            baseUnit: unit?.baseUnit,
            purchaseUnit: poUnit?.name,
            purchaseBaseUnit: poUnit?.baseUnit,

            vat: +item?.vat || (await i.buyTax)?.percent || 0,
            sellPrice: item.unitPrice,
            inputPrice: +item?.unitPrice || +pricing?.price || 0,
          })
        }
      }
      dataImport.push({
        warehouseCode,
        warehouseId: whInfo?.id,
        storeId: whInfo?.storeId,
        warehouseName: whInfo?.name,
        items: whItems,
      })
    }
    const mergeData = coreHelper.mergeItemsToArray(dataImport)

    return {
      purchaseOrderType: NSPo.EPoType.SUPPLIER,
      data: [...dataImport],
      mergedata: mergeData,
    }
  }

  private async readFileImportSupplier(file: Express.Multer.File, req: IRequest) {
    try {
      if (!file) {
        throw new Error('File not provided')
      }

      const workbook = XLSX.read(file.buffer, { type: 'buffer' })
      const result: Record<string, { name: string; items: any[] }> = {}

      for (const sheetName of workbook.SheetNames) {
        const worksheet = workbook.Sheets[sheetName]
        const jsonData: any[][] = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

        if (jsonData.length < 3) continue

        const khoCode = jsonData[0][1] // Cell B1 = mã kho (ví dụ: 'MBC41')
        const khoName = sheetName // Tên sheet làm tên kho

        if (!khoCode) continue

        const rows = jsonData.slice(2) // Dòng 4 trở đi là dữ liệu

        const items = rows
          .filter((row) => row && row.length > 0)
          .map((row) => ({
            stt: row[0],
            productCode: row[1],
            barcode: row[2],
            name: row[3],
            unitPrice: Number(row[4]) || 0,
            quantity: Number(row[5]) || 0,
            vat: Number(row[6]) || null,
          }))

        result[khoCode] = {
          name: khoName,
          items,
        }
      }

      return result
    } catch (error) {
      throw new Error(error.message || 'Error importing file')
    }
  }
  //#endregion

  // Tạo phiếu giao
  private async createDeliveryTracking(deliveryNoteId: string, poLst: any[], mappingDistributor: any[]) {
    const countASN = await this.countTrackingToday()
    const codeASN = coreHelper.generateDeliveryNoteCode(countASN)
    const supplierIds = poLst.map((po) => po.supplierId)

    for (const po of poLst) {
      // Lấy ra ngày tạo của PO
      const poDate = po.createdAt
      const expectedDate = moment(poDate).add(9, 'days').format('YYYY-MM-DD HH:mm:ss')

      for (const distributorId of mappingDistributor) {
        // Kiểm tra NPP có thuộc NCC không
        const checkDistributor = await this.supplierRepo.findOne({
          where: { id: distributorId, parentId: In(supplierIds) },
        })
        if (checkDistributor) {
          // Lấy ra chuỗi cung ứng
          const supplyChain = await this.getSupplyChainByDistributor(distributorId, po.supplierId)
          if (!supplyChain) {
            throw new Error(`Không tìm thấy chuỗi cung ứng`)
          }

          const warehousePO = await this.purchaseOrderItemRepo.findOne({
            where: {
              purchaseOrderId: po.id,
              supplierId: po.supplierId,
              distributorId,
            },
          })
          if (warehousePO) {
            const warehouse = await this.warehouseRepository.findOne({
              where: {
                id: warehousePO.warehouseId,
                type: NSWarehouse.EWarehouseType['3PL'],
                isDeleted: false,
              },
            })

            if (!warehouse) {
              throw new Error(`Không tìm thấy kho của 3PL`)
            }

            if (supplyChain) {
              const newDL = await this.deliveryTrackingRepository.save({
                deliveryNoteId,
                poId: po.id,
                poGroup: po.purchaseOrderGroup,
                from: distributorId,
                to: supplyChain.deliveryId,
                step: 1,
                isLast: false,
                isPacking: false,
                code: codeASN,
                trackingType: NSPo.EDeliveryTrackingType.DELIVERY,
                expectedDate: new Date(expectedDate),
                supplierId: po.supplierId,
                warehouseId: warehouse.id,
              })

              // let pretThirdParty = newDL.from
              // await this.deliveryTrackingRepository.save({
              //   deliveryNoteId,
              //   poId: po.id,
              //   poGroup: po.purchaseOrderGroup,
              //   from: pretThirdParty,
              //   to: supplyChain.deliveryId,
              //   step: 2,
              //   isLast: true,
              //   isPacking: false,
              //   code: codeASN,
              //   trackingType: NSPo.EDeliveryTrackingType.RECEIVING,
              //   expectedDate: new Date(expectedDate),
              //   supplierId: po.supplierId,
              //   warehouseId: warehouse.id,
              // })
            }
          }
        } else {
          throw new Error(`Không tìm thấy NPP thuộc NCC`)
        }
      }
    }
  }

  public async importSO(body: CreatePOFromSODto, req: IRequest) {
    try {
      const { products } = body
      if (!products) {
        throw new Error('Không có sản phẩm để tạo PO')
      }

      const balanceLifeInfo = await this.supplierRepo.findOne({ where: { code: 'BA' }, select: ['id', 'name'] })

      const productIds = Array.from(new Set(products.map((val) => val.productId)))
      const orderToCust = products.filter((p) => p.addressType === 'HOME')
      const orderToPartner = products.filter((p) => p.addressType === 'MBC')
      const supplierLst = await this.supplierRepo.find({ select: ['id', 'name'] })
      const cities = await this.cityRepository.find({ where: { isDeleted: false }, select: ['id', 'code', 'name'] })
      const price = await this.pricingRepo.find({ where: { itemId: In(productIds), supplierId: balanceLifeInfo.id } })

      if (productIds.length == 0) {
        throw new Error(`Vui lòng kiểm tra lại dữ liệu tham chiếu`)
      }

      // Lấy thông tin danh sách sản phẩm
      const productData = await this.itemRepository.find({
        where: {
          id: In(productIds),
        },
        select: {
          code: true,
          name: true,
          id: true,
          isCombo: true,
          supplierId: true,
          vat: true,
          quantityUnit: true,
        },
        relations: {
          unit: true,
          pOUnit: true,
          itemGroup: true,
        },
      })

      if (productData.length !== productIds.length) {
        // throw new Error('Có sản phẩm bị ngưng hoạt động / không tồn tại')
      }

      const distributeCombo = []
      const distributeComboItems = []
      let itemDistribute = []
      let items: any = []
      let combos: any = []

      // Duyệt danh sách products có item là isCombo thì thêm vào combos
      // Nếu trùng comboId thì cộng dồn quantity lên
      for (let i = 0; i < products.length; i++) {
        const product = products[i]
        const productInfo = productData.find((p) => p.id == product.productId)
        if (productInfo.isCombo) {
          const existing = combos.find((val) => val.comboId === productInfo.id)
          if (existing) {
            existing.quantity += +product.quantity
            existing.soIds.push(product.soId)
          } else {
            combos.push({
              comboId: productInfo.id,
              comboName: productInfo.name,
              comboCode: productInfo.code,
              quantity: +product.quantity,
              soIds: [product.soId],
            })
          }
        }
      }

      // SO về MBC
      if (orderToPartner.length > 0) {
        const partnerIds = orderToPartner.map((o) => o.partnerId)
        const partners = await omsApiHelper.lstPartnerByCodes(req, { ids: partnerIds })
        orderToPartner.map((item) => {
          const partner = partners.find((p) => String(p.id) == String(item.partnerId))
          if (partner) {
            const product = productData.find((p) => String(p.id) === String(item.productId))
            const existing = distributeCombo.find((val) => val.productId === item.productId && val.partnerId === item.partnerId)

            if (existing) {
              existing.quantity += item.quantity
            } else {
              const partnerInfo = {
                partnerId: partner ? partner.id : null,
                partnerName: partner ? partner.name : null,
                provinceCode: partner ? partner.provinceCode : null,
              }
              if (product.isCombo) {
                distributeCombo.push({
                  ...item,
                  productId: product.id,
                  comboCode: product.code,
                  comboName: product.name,
                  vat: product.vat,
                  ...partnerInfo,
                })
              } else {
                distributeCombo.push({
                  ...item,
                  productId: product.id,
                  itemCode: product.code,
                  itemName: product.name,
                  vat: product.vat,
                  ...partnerInfo,
                })
              }

              return {
                ...item,
                partnerId: partner ? partner.id : null,
                partnerName: partner ? partner.name : null,
                provinceCode: partner.provinceCode,
              }
            }
          }
        })

        const soIds = orderToPartner.map((i) => i.soId)
        const orderLst = await omsApiHelper.getOrderByIds(req, { orderListIds: soIds })
        const districtCodeLst = orderLst.data.map((val) => val.districtCode)

        const productIsCombo = await this.itemRepository.find({ where: { id: In(productIds), isCombo: true }, select: { id: true } })
        const comboIds = productIsCombo.map((val) => `'${val.id}'`)
        const itemInComboLst = await this.getItemsInComboDetails(comboIds)

        // Duyệt danh sách data tranfer
        for (let i = 0; i < orderToPartner.length; i++) {
          const product = orderToPartner[i]
          const checkIsCombo = productIsCombo.find((val) => val.id == product.productId)
          if (checkIsCombo) {
            for (let y = 0; y < itemInComboLst.length; y++) {
              const item = itemInComboLst[y] // Lấy ra thông tin của combo
              if (item.comboId == product.productId) {
                const partner = partners.find((p) => p.id == product.partnerId)
                // Kiểm tra số lượng item trong combo
                const countItem = await this.itemComboRepo.findOne({ where: { itemId: item.comboId, itemInComboId: item.itemId } })
                if (countItem.quantity == 0) throw new Error(`Cấu hình số lượng item (${item.itemCode}) trong combo (${item.code}) chưa hợp lệ `)
                if (partner) {
                  const existing = distributeComboItems.find(
                    (val) => val.code === item.code && val.itemId === item.itemId && val.partnerId === partner.id && product.soId === val.soId,
                  )
                  //lấy giá theo bảng giá
                  const priceInfo = price.find((p) => p.itemId == item.id)

                  const itemTotalAmount = Number(priceInfo.price) * Number(product.quantity) * Number(countItem.quantity)
                  const itemTotalAmountVat = itemTotalAmount + itemTotalAmount * (item.itemVat / 100)

                  if (existing) {
                    existing.quantityBasicUnit += Number(product.quantity) * Number(countItem.quantity)
                    existing.quantityCombo += product.quantity
                    existing.totalAmount += itemTotalAmount
                    existing.totalAmountVat += itemTotalAmountVat
                  } else {
                    distributeComboItems.push({
                      ...item,
                      id: item.comboId,
                      comboId: item.comboId,
                      sellPrice: +item.sellPrice || 0,
                      inputPrice: +priceInfo.price || 0,
                      quantityBasicUnit: Number(product.quantity) * Number(countItem.quantity), // Nhân với số lượng item trong combo
                      quantityCombo: product.quantity,
                      partnerId: partner ? partner.id : null,
                      distributorCode: '',
                      thirdPartyCode: '',
                      partnerCode: partner.code,
                      partnerName: partner.name,
                      provinceCode: partner.provinceCode,
                      totalAmount: itemTotalAmount,
                      totalAmountVat: itemTotalAmountVat,
                      addressType: product.addressType,
                      soId: product.soId,
                      isCombo: true,
                    })
                  }
                }
              }
            }
          } else {
            const item = productData.find((i) => i.id == product.productId)
            // Lấy giá bán chung hiện tại của item
            // const price = await this.itemPriceRepo.findOne({ where: { itemId: item.id, isFinal: true } })
            // Lấy quy cách đóng gói
            const unit = await item.unit

            // Lấy đơn vị tính đặt hàng
            const poUnit = await item.pOUnit

            // Lấy nhóm sản phẩm
            const group = await item.itemGroup

            const partner = partners.find((p) => p.id == product.partnerId)

            const priceInfo = price.find((p) => p.itemId == item.id)
            const totalPrice = Number(priceInfo?.price) || 0 * Number(product.quantity)

            distributeComboItems.push({
              ...item,
              itemName: item.name,
              itemId: item.id,
              itemGroupName: group?.name,
              itemGroupId: group?.id,
              packingSpecification: unit?.name,
              baseUnit: item.quantityUnit,
              purchaseUnit: poUnit?.name,
              purchaseBaseUnit: poUnit?.baseUnit,
              partnerId: partner ? partner.id : null,
              distributorCode: '', // Từ SO không có code
              thirdPartyCode: '', // Từ SO không có code
              partnerCode: partner.code,
              partnerName: partner.name,
              provinceCode: partner.provinceCode,
              sellPrice: +item.sellPrice || 0,
              inputPrice: +priceInfo?.price || 0,
              quantityBasicUnit: product.quantity,
              totalAmount: totalPrice,
              totalAmountVat: totalPrice,
              addressType: product.addressType,
              soId: product.soId,
              isCombo: false,
            })
          }
        }

        // Clear Key
        for (const element of distributeComboItems) {
          delete element.__unit__
          delete element.__itemGroup__
          delete element.__pOUnit__
        }

        const suppliers = await this.supplierRepo.find({ select: { id: true, name: true, code: true } })
        // Truyền Supply Chain
        // Áp dụng chuỗi cung ứng từng po item
        for (let i = 0; i < partners.length; i++) {
          const partner = partners[i] // Bưu cục
          const cityCode = partner?.provinceCode
          const region = await this.getRegionIdByProvinceCode(cityCode)
          const regionId = region[0].regionId
          const regionName = region[0].regionName

          for (let i = 0; i < distributeComboItems.length; i++) {
            const item = distributeComboItems[i]
            if (!item.supplierId) throw new Error(`${item.name} chưa cấu hình nhà cung cấp`)
            const sp = await this.getSupplyChain(regionId, item.supplierId)
            const nameSupplier = suppliers.find((i) => i.id == item.supplierId)
            if (!sp) throw new Error(`Không tìm thấy Supply Chain cho Vùng: ${regionName} và NCC: ${nameSupplier?.name}`)

            const supplierInfo = supplierLst.find((s) => s.id == item.supplierId)
            if (!supplierInfo) throw new Error(`Không tìm thấy nhà cung cấp cho sản phẩm [${item.itemName}]`)
            const city = cities.find((c) => c.code == cityCode)

            const { distributor, warehouse } = await this.getDistributorAndWarehouseByProvinceCode(
              item.provinceCode,
              districtCodeLst,
              item.supplierId,
              sp?.deliveryId,
            )
            if (distributor.length == 0) throw new Error(`Không tìm thấy nhà NPP ở [ ${city?.name} ] và NCC [ ${supplierInfo?.name} ]`)
            if (warehouse.length == 0) throw new Error(`Không tìm thấy kho trung tâm trong: ${city?.name}`)

            // Lấy ra getDistributorAndWarehouseByProvinceCode
            // const { distributor, warehouse } = await this.getDistributorAndWarehouseByProvinceCode(cityCode, item.supplierId)

            //  if (distributor.length == 0) throw new Error(`Không tìm thấy nhà NPP ở [ ${city?.name} ] và NCC [ ${supplierInfo.name} ]`)
            //  if (warehouse.length == 0) throw new Error(`Không tìm thấy kho trung tâm trong: ${city?.name}`)

            if (partner.id === item.partnerId) {
              itemDistribute.push({
                ...item,
                deliveryId: sp?.deliveryId,
                distributorId: distributor[0], // NPP
                warehouseId: warehouse[0], // Kho
                provinceId: city?.id,
                regionId,
                regionName,
              })
            }
          }
        }

        items = itemDistribute.map((val) => {
          const filterSoId = val.comboId
            ? products.filter((c) => c.productId == val.comboId && c.soId == val.soId)
            : products.filter((c) => c.productId == val.itemId && c.soId == val.soId)

          // Tạo array SO có type {id, quantity}
          const soIdQuantity = filterSoId.map((m) => ({
            id: m.soId,
            quantity: m.quantity,
            isCombo: val.comboId ? true : false,
          }))

          return {
            ...val,
            soIdQuantity,
          }
        })
      }

      // SO về Khách hàng
      if (orderToCust.length > 0) {
        const arrayProduct = []
        // Phải xử lý để gắn chuỗi cung ứng vào
        const soIds = orderToCust.map((i) => i.soId)
        const orderLst = await omsApiHelper.getOrderByIds(req, { orderListIds: soIds })

        // Lấy ra districtCode từ orderLst
        const districtCodeLst = orderLst.data.map((val) => val.districtCode)

        for (const item of orderToCust) {
          const product = productData.find((p) => p.id == item.productId)
          if (product.isCombo) {
            // Là combo
            const itemInComboLst = await this.getItemsInComboDetails([`'${product.id}'`])
            const productIds = itemInComboLst.map((i) => i.itemId) // Danh sách Id sản phẩm trong Combo

            // Danh sách sản phẩm trong combo
            const productLst = await this.itemRepository.find({
              where: {
                id: In(productIds),
              },
              select: {
                code: true,
                name: true,
                id: true,
                isCombo: true,
                supplierId: true,
              },
              relations: {
                unit: true,
                pOUnit: true,
                itemGroup: true,
              },
            })

            const orderInfo = orderLst.data.find((o) => o.id === item.soId)
            //Không lấy theo giá item nữa mà lấy theo giá được cấu hình trong bảng giá
            // const priceProductInCombo = await this.itemPriceRepo.find({ where: { itemId: In(productIds), isFinal: true } })
            for (const itemInCombo of itemInComboLst) {
              const productInfo = productLst.find((p) => p.id === itemInCombo.itemId)
              if (!productInfo) throw new Error(`Không tìm thấy sản phẩm [${itemInCombo.itemId}]`)
              const group = await productInfo.itemGroup
              const unit = await productInfo.unit
              const poUnit = await productInfo.pOUnit

              const supplierInfo = supplierLst.find((s) => s.id == productInfo.supplierId)
              if (!supplierInfo) throw new Error(`Không tìm thấy nhà cung cấp cho sản phẩm [${productInfo.name}]`)

              // Lấy ra getDistributorAndWarehouseByProvinceCode
              //const { distributor, warehouse } = await this.getDistributorAndWarehouseByProvinceCode(orderInfo?.provinceCode, productInfo.supplierId)
              const city = cities.find((c) => c.code == orderInfo?.provinceCode)

              //if (distributor.length == 0) throw new Error(`Không tìm thấy nhà NPP ở [ ${city?.name} ] và NCC [ ${supplierInfo.name} ]`)
              //if (warehouse.length == 0) throw new Error(`Không tìm thấy kho trung tâm trong: ${city?.name}`)

              // const priceInfo = priceProductInCombo.find((p) => p.itemId == itemInCombo.itemId)
              // const totalPrice = Number(priceInfo.priceSell) * Number(itemInCombo.quantityInCombo) * Number(item.quantity)

              const priceInfo = price.find((p) => p.itemId == product.id)
              const totalPrice = Number(priceInfo?.price) || 0 * Number(itemInCombo.quantityInCombo) * Number(item.quantity)

              arrayProduct.push({
                code: product.code,
                comboId: product.id,
                comboName: product.name,
                itemName: productInfo.name,
                itemId: productInfo.id,
                itemGroupName: group?.name,
                itemGroupId: group?.id,

                supplierId: productInfo.supplierId, // 1 combo chỉ có 1 nhà cung cấp
                // distributorId: distributor[0], //NPP
                //warehouseId: warehouse[0], // Kho
                //distributorCode: supplierLst.find((s) => s.id == distributor[0])?.code,
                thirdPartyCode: '', // Từ SO không có code
                provinceId: city?.id, // Tỉnh/TP của KH

                packingSpecification: unit?.name,
                baseUnit: itemInCombo.baseUnit, // Đơn vị cơ sở
                purchaseUnit: poUnit?.name,
                purchaseBaseUnit: poUnit?.baseUnit,
                partnerId: orderInfo?.customerId,
                partnerCode: orderInfo?.customerPhone,
                partnerName: orderInfo?.customerName,
                provinceCode: orderInfo?.provinceCode,
                sellPrice: +productInfo.sellPrice || 0,
                inputPrice: +priceInfo.price || 0,
                quantityBasicUnit: Number(itemInCombo.quantityInCombo) * Number(item.quantity),
                quantityCombo: item.quantity,
                totalAmount: totalPrice,
                totalAmountVat: totalPrice,
                addressType: item.addressType,
                soId: item.soId,
                isCombo: true,
              })
            }
          } else {
            // Là thành phần
            const group = await product.itemGroup
            const unit = await product.unit
            const poUnit = await product.pOUnit
            const orderInfo = orderLst.data.find((o) => o.id === item.soId)

            // Lấy ra getDistributorAndWarehouseByProvinceCode
            //const { distributor, warehouse } = await this.getDistributorAndWarehouseByProvinceCode(orderInfo?.provinceCode, product.supplierId)
            const city = cities.find((c) => c.code == orderInfo?.provinceCode)
            //const supplierInfo = supplierLst.find((s) => s.id == product.supplierId)
            // if (distributor.length == 0) throw new Error(`Không tìm thấy nhà NPP cho Vùng: ${orderInfo?.provinceCode} và NCC: ${supplierInfo.name}`)
            // if (warehouse.length == 0) throw new Error(`Không tìm thấy kho cho Vùng: ${orderInfo?.provinceCode} và NCC: ${supplierInfo.name}`)

            const priceInfo = price.find((p) => p.itemId == product.id)
            const totalPrice = Number(priceInfo?.price) || 0 * Number(item.quantity)

            arrayProduct.push({
              ...item,
              itemName: product.name,
              itemId: product.id,
              itemGroupName: group?.name,

              supplierId: product.supplierId, // 1 combo chỉ có 1 nhà cung cấp
              // distributorId: distributor[0], // NPP
              // warehouseId: warehouse[0], // Kho
              // distributorCode: supplierLst.find((s) => s.id == distributor[0])?.code,
              thirdPartyCode: '', // Từ SO không có code
              provinceId: city?.id, // Tỉnh/TP của KH

              itemGroupId: group?.id,
              packingSpecification: unit?.name,
              baseUnit: product.quantityUnit,
              purchaseUnit: poUnit?.name,
              purchaseBaseUnit: poUnit?.baseUnit,
              partnerId: orderInfo?.customerId,
              partnerCode: orderInfo?.customerPhone,
              partnerName: orderInfo?.customerName,
              provinceCode: orderInfo.provinceCode,
              sellPrice: +product.sellPrice || 0,
              inputPrice: +priceInfo?.price || 0,
              quantityBasicUnit: +item.quantity,
              quantityCombo: 0,
              totalAmount: totalPrice,
              totalAmountVat: totalPrice,
              addressType: item.addressType,
              soId: item.soId,
              isCombo: false,
            })
          }
        }

        // Clear key
        for (const element of arrayProduct) {
          delete element.__unit__
          delete element.__itemGroup__
          delete element.__pOUnit__
        }

        for (let i = 0; i < arrayProduct.length; i++) {
          const item = arrayProduct[i]
          if (!item.supplierId) {
            throw new Error(`${item.itemName} chưa cấu hình nhà cung cấp`)
          }

          const cityCode = item.provinceCode
          const region = await this.getRegionIdByProvinceCode(cityCode)
          // Lấy chuỗi cung ứng theo vùng
          const regionId = region[0].regionId

          // Kiểm tra ID vùng
          const regionCheck = await this.regionRepository.findOne({ where: { id: regionId } })
          if (!regionCheck) {
            throw new Error(`Không tìm thấy id của Vùng [${regionId}]`)
          }

          // Kiểm tra NCC
          const supplierCheck = await this.supplierRepo.findOne({ where: { id: item.supplierId } })
          if (!supplierCheck) {
            throw new Error(`Không tìm thấy id của Vùng [${item.supplierId}]`)
          }

          const sp = await this.getSupplyChain(regionId, item.supplierId)
          const regionName = sp?.regionName

          const { distributor, warehouse } = await this.getDistributorAndWarehouseByProvinceCode(
            item.provinceCode,
            districtCodeLst,
            item.supplierId,
            sp?.deliveryId,
          )
          const city = cities.find((c) => c.code == item?.provinceCode)

          if (distributor.length == 0) throw new Error(`Không tìm thấy nhà NPP ở [ ${city?.name} ] và NCC [ ${supplierCheck.name} ]`)
          if (warehouse.length == 0) throw new Error(`Không tìm thấy kho trung tâm trong: ${city?.name}`)

          if (!sp) throw new Error(`Không tìm thấy Supply Chain cho Vùng: ${regionCheck.name}, Nhà cung cấp: ${supplierCheck.name}`)
          itemDistribute.push({
            ...item,
            //distributorId: sp?.distributorId,
            deliveryId: sp?.deliveryId,
            distributorId: distributor[0], // Mặc định lấy NPP index 0
            warehouseId: warehouse[0], // Mặc định lấy Kho index 0
            distributorCode: supplierLst.find((s) => s.id == distributor[0])?.code,
            regionId,
            regionName,
          })
        }

        items = itemDistribute.map((val) => {
          const filterSoId = val.comboId
            ? products.filter((c) => c.productId == val.comboId && c.soId == val.soId)
            : products.filter((c) => c.productId == val.itemId && c.soId == val.soId)

          // Tạo array SO có type {id, quantity}
          const soIdQuantity = filterSoId.map((m) => ({
            id: m.soId,
            quantity: m.quantity,
            isCombo: val.comboId ? true : false,
          }))

          return {
            ...val,
            soIdQuantity,
          }
        })
      }

      return { combos, items }
    } catch (error) {
      throw new Error(error)
    }
  }

  //upload preorder po
  public async uploadManualPO(body: UploadPreOrderPODto, req: IRequest) {
    try {
      let { items, isCombo } = body
      if (!items) {
        throw new Error('Không có sản phẩm để tạo PO')
      }
      let lstItemId = items.map((val) => val.id)
      let comboLst = []
      if (isCombo) {
        comboLst = [...items]
        lstItemId = items.map((val) => `'${val.id}'`)
        const itemInComboLst = await this.getItemsInComboDetails(lstItemId)
        lstItemId = Array.from(new Set(itemInComboLst.map((val) => val.itemId)))
        items = itemInComboLst.map((val) => ({
          ...val,
          id: val.itemId,
        }))
      } else {
      }

      const dictItemDetails: Record<any, ItemEntity> = {}
      {
        const itemDetails = await this.itemRepository.find({
          where: { id: In(lstItemId), isDeleted: false },
          relations: { unit: true, pOUnit: true, itemGroup: true, supplier: true },
        })

        if (itemDetails.length != lstItemId.length) {
          throw new Error('Có sản phẩm không tồn tại hoặc đã bị ngưng hoạt động')
        }

        itemDetails.forEach((val) => (dictItemDetails[val.id] = val))
      }

      const dictPricing: Record<any, PricingEntity> = {}
      {
        const lstPricing = await this.pricingRepo.find({
          where: { itemId: In(lstItemId) },
        })
        lstPricing.forEach((val) => (dictPricing[val.itemId] = val))
      }

      for (let item of items) {
        item.quantityBasicUnit = 1

        //groupname
        item.groupNameId = (await dictItemDetails[item.id].itemGroup)?.id
        item.groupName = (await dictItemDetails[item.id].itemGroup)?.name

        //unitName
        item.unitId = (await dictItemDetails[item.id].unit)?.id
        item.unitName = (await dictItemDetails[item.id].unit)?.name

        //poUnitName
        item.poUnitId = (await dictItemDetails[item.id].pOUnit)?.id
        item.poUnitName = (await dictItemDetails[item.id].pOUnit)?.name

        //supplierName
        item.supplierId = (await dictItemDetails[item.id].supplier)?.id
        item.supplierName = (await dictItemDetails[item.id].supplier)?.name

        //quantityUnit
        item.quantityUnit = dictItemDetails[item.id]?.quantityUnit

        //inputPrice
        item.inputPrice = dictPricing[item.id]?.price || 0

        //vat
        item.vat = (await dictItemDetails[item.id]?.buyTax)?.percent || 0

        //clear relation
        {
          delete item.unit
          delete item.pOUnit
          delete item.itemGroup
          delete item.supplier
        }
      }

      let formattedData = [...items]

      return { message: 'Tạo PO thành công', data: formattedData, combo: comboLst }
    } catch (error) {
      throw new Error(error)
    }
  }

  /** Update thông tin PO */
  public async updatePO(body: UpdatePurchaseOrderDto, req: IRequest) {
    const { purchaseOrderId, updateBy, ...data } = body
    const check = await this.purchaseOrderRepo.findOne({ where: { id: purchaseOrderId } })
    if (!check) {
      throw new Error(`Không tìm thấy PO`)
    }

    // Kiểm tra xem có phiếu nhập kho đang gán với PO này không
    const checkInbound = await this.inboundRepository.findOne({ where: { poId: check.id } })

    if (checkInbound) {
      const currentFile = checkInbound?.files || []
      const newFile = data.files

      await this.inboundRepository.update({ id: checkInbound.id }, { files: [...newFile] })
    }

    const oldDataString = JSON.stringify(check)

    const user: any = await this.userRepository.findOne({ where: { id: updateBy } })
    let updateName = user?.fullName
    if (!updateName) {
      const supplier = await this.supplierRepo.findOne({ where: { id: updateBy } })
      if (supplier) {
        updateName = supplier.name
      }
    }
    await this.addHistory(check.id, check.purchaseOrderCode, updateName, 'UPDATE')

    return await this.purchaseOrderRepo.update({ id: purchaseOrderId }, data)
  }

  //#region Import Combo
  /** Import Combo Overview */
  public async importComboOverview(file: Express.Multer.File, req: IRequest) {
    try {
      if (!file) {
        throw new Error('File not provided')
      }

      const workbook = XLSX.read(file.buffer, { type: 'buffer' })
      const sheetName = workbook.SheetNames[0]
      const data: any[] = XLSX.utils.sheet_to_json(workbook.Sheets[sheetName])

      if (!data || data.length === 0) {
        throw new Error('No data found in file')
      }

      const transformedData = data.map((row: any) => ({
        comboCode: row['Mã Combo'],
        partnerCode: row['Mã bưu cục'],
        quantity: row['Số lượng phân bổ'],
        distributor: row['NPP'],
        thirdParty: row['Mã 3PL'],
      }))

      const comboCodes = transformedData.map((item) => item.comboCode)
      const partnerCodes = Array.from(new Set(transformedData.map((item) => String(item.partnerCode)))) // Danh sách code của partner
      const distributorCodes = transformedData.filter((item) => item.distributor).map((item) => item.distributor)
      const thirdPartyCodes = transformedData.filter((item) => item.thirdParty).map((item) => item.thirdParty)

      if (comboCodes.length == 0 || partnerCodes.length == 0) {
        throw new Error(`Vui lòng kiểm tra lại dữ liệu import`)
      }

      const partners = await omsApiHelper.lstPartnerByCodes(req, { codes: partnerCodes })
      if (partners.length == 0) {
        throw new Error(`Không tìm thấy Partner, vui lòng kiểm tra lại dữ liệu import`)
      }

      const combos = await this.itemRepository.find({
        where: {
          isCombo: true,
          code: In(comboCodes),
        },
        select: {
          code: true,
          name: true,
          id: true,
        },
      })

      if (combos.length == 0) {
        throw new Error(`Không tìm thấy Combo, vui lòng kiểm tra lại dữ liệu import`)
      }

      const dataImport = transformedData
      const mergedData = dataImport.reduce((acc, item) => {
        const existing = acc.find((val) => val.comboCode === item.comboCode)
        if (existing) {
          existing.quantity += item.quantity
        } else {
          acc.push({ ...item })
        }
        return acc
      }, [])

      const mappingCombos = combos.map((combo) => {
        const item = mergedData.find((val) => val.comboCode == combo.code)
        return {
          ...combo,
          quantity: item.quantity,
        }
      })

      // Phân bổ combo
      const distributeCombo = []
      const comboLst = await this.itemRepository.find({ where: { isCombo: true }, select: { id: true, name: true, code: true } })
      if (!comboLst || comboLst.length == 0) {
        throw new Error('Không tìm thấy danh sách Combo')
      }
      transformedData.map((item) => {
        const partner = partners.find((p) => String(p.code) === String(item.partnerCode))
        const existing = distributeCombo.find((val) => val.comboCode === item.comboCode && val.partnerCode === item.partnerCode)
        const combo = comboLst.find((cb) => String(cb.code) === String(item.comboCode))

        if (!combo) {
          throw new Error(`Không tìm thấy dữ liệu Combo: ${item.comboCode} `)
        }

        if (existing) {
          existing.quantity += item.quantity
        } else {
          distributeCombo.push({
            ...item,
            comboName: combo.name,
            partnerId: partner ? partner.id : null,
            partnerName: partner ? partner.name : null,
            provinceCode: partner.provinceCode,
          })
          return {
            ...item,
            partnerId: partner ? partner.id : null,
            partnerName: partner ? partner.name : null,
            provinceCode: partner.provinceCode,
          }
        }
      })

      // Phân bổ item
      const combosCode = combos.map((val) => `'${val.code}'`)
      const comboItems = await this.getItemsInComboDetails(combosCode, 'code')

      let distributeComboItems = []
      for (let i = 0; i < transformedData.length; i++) {
        const val = transformedData[i]
        for (let y = 0; y < comboItems.length; y++) {
          const item = comboItems[y] // Lấy ra thông tin của combo
          if (item.code == val.comboCode) {
            const partner = partners.find((p) => p.code == val.partnerCode)
            // Kiểm tra số lượng item trong combo
            const countItem = await this.itemComboRepo.findOne({ where: { itemId: item.comboId, itemInComboId: item.itemId } })
            if (countItem.quantity == 0) throw new Error(`Cấu hình số lượng item (${item.itemCode}) trong combo (${item.code}) chưa hợp lệ `)

            const existing = distributeComboItems.find((val) => val.code === item.code && val.itemId === item.itemId && val.partnerId === partner.id)
            const itemTotalAmount = Number(item.inputPrice) * val.quantity * Number(countItem.quantity)
            const itemTotalAmountVat = Math.round(Number(itemTotalAmount) + Number(+itemTotalAmount * (+item.itemVat / 100)))

            if (existing) {
              existing.quantityBasicUnit += val.quantity * Number(countItem.quantity)
              existing.quantityCombo += val.quantity
              existing.totalAmount += itemTotalAmount
              existing.totalAmountVat += itemTotalAmountVat
            } else {
              distributeComboItems.push({
                ...item,
                sellPrice: +item.sellPrice,
                inputPrice: +item.inputPrice,
                quantityBasicUnit: val.quantity * Number(countItem.quantity), // Nhân với số lượng item trong combo
                quantityCombo: val.quantity,
                partnerId: partner ? partner.id : null,
                distributorCode: val.distributor,
                thirdPartyCode: val.thirdParty,
                partnerCode: val.partnerCode,
                partnerName: partner.name,
                totalAmount: itemTotalAmount,
                totalAmountVat: itemTotalAmountVat,
              })
            }
          }
        }
      }

      let itemDistribute = []
      let supDistr = [],
        supThird = []
      if (distributorCodes.length == 0 && thirdPartyCodes.length == 0) {
        for (let i = 0; i < partners.length; i++) {
          const partner = partners[i] // Bưu cục
          const cityCode = partner?.provinceCode
          const region = await this.getRegionIdByProvinceCode(cityCode)
          const regionId = region[0].regionId
          const regionName = region[0].regionName

          for (let i = 0; i < distributeComboItems.length; i++) {
            const item = distributeComboItems[i]
            const sp = await this.getSupplyChain(regionId, item.supplierId)
            if (!sp) throw new Error(`Không tìm thấy Supply Chain cho Vùng: ${regionName} và NCC: ${item.supplierId}`)

            if (partner.id === item.partnerId) {
              itemDistribute.push({
                ...item,
                distributorId: sp?.distributorId,
                deliveryId: sp?.deliveryId,
                regionId,
                regionName,
              })
            }
          }
        }
      } else {
        supDistr = await this.supplierRepo.find({ where: { code: In(distributorCodes) } })
        supThird = await this.supplierRepo.find({ where: { code: In(thirdPartyCodes) } })
        for (let i = 0; i < partners.length; i++) {
          const partner = partners[i] // Bưu cục
          const cityCode = partner?.provinceCode
          const region = await this.getRegionIdByProvinceCode(cityCode)
          const regionId = region[0].regionId
          const regionName = region[0].regionName

          for (let i = 0; i < distributeComboItems.length; i++) {
            const item = distributeComboItems[i]
            const distr = supDistr.find((val) => val.code == item.distributorCode)
            const third = supThird.find((val) => val.code == item.thirdPartyCode)

            if (partner.id === item.partnerId) {
              itemDistribute.push({
                ...item,
                distributorId: distr?.id,
                deliveryId: third?.id,
                regionId,
                regionName,
              })
            }
          }
        }
      }
      return { combos: mappingCombos, distribute: distributeCombo, items: itemDistribute }
    } catch (error) {
      throw new Error(error.message || 'Error importing file')
    }
  }
  //#endregion

  //#region Import Item
  /** Import Item Overview */
  public async importItemOverview(file: Express.Multer.File, req: IRequest) {
    try {
      if (!file) {
        throw new Error('File not provided')
      }

      const workbook = XLSX.read(file.buffer, { type: 'buffer' })
      const sheetName = workbook.SheetNames[0]
      const data: any[] = XLSX.utils.sheet_to_json(workbook.Sheets[sheetName], {
        range: 1,
      })

      //#region Xử lý thông tin kho lastmile
      const sheet = workbook.Sheets[sheetName]
      const cellB1 = sheet['B1']
      const warehouseCode = cellB1 ? String(cellB1.w || cellB1.v).trim() : null
      if (!warehouseCode) {
        throw new Error('Vui lòng nhập mã Kho')
      }
      const wh = await this.warehouseRepository.findOne({ where: { code: warehouseCode } })
      if (!wh) {
        throw new Error('Không tìm thấy Kho')
      }

      const wh_provinceCode = await this.cityRepository.findOne({
        where: {
          id: wh.cityId,
        },
      })
      if (!wh_provinceCode) {
        throw new Error('Không tìm thấy Tỉnh/Thành phố')
      }
      const wh_districtCode = await this.districtRepository.findOne({
        where: {
          id: wh.districtId,
        },
      })
      if (!wh_districtCode) {
        throw new Error('Không tìm thấy Quận/Huyện')
      }
      //#endregion

      if (!data || data.length === 0) {
        throw new Error('Không tìm thấy dữ liệu trong file')
      }

      //check template
      const header = Object.keys(data[0])
      const requiredHeader = ['Vùng (*)', 'Mã sản phẩm (*)', 'Số lượng (*)']
      requiredHeader.forEach((val, index) => {
        if (header[index] != val) {
          throw new Error(`Template không đúng định dạng vui lòng kiểm tra lại !!!`)
        }
      })
      let errors = ''
      const transformedData = data.map((row: any, index: number) => {
        if (!row['Vùng (*)']) {
          errors += `Vui lòng nhập Vùng cho sản phẩm dòng thứ ${index}\n`
        }
        if (!row['Mã sản phẩm (*)']) {
          errors += `Vui lòng nhập Mã sản phẩm cho sản phẩm dòng thứ ${index}\n`
        }

        if (!row['Số lượng (*)']) {
          errors += `Vui lòng nhập Số lượng cho sản phẩm dòng thứ ${index}\n`
        }

        return {
          regionCode: row['Vùng (*)'],
          itemCode: row['Mã sản phẩm (*)'],
          quantity: row['Số lượng (*)'],
          price: row['Đơn Giá'],
          vat: row['Vat'],
        }
      })

      if (errors) {
        throw new Error(errors)
      }

      const itemCodes = transformedData.map((item) => item.itemCode)

      if (itemCodes.length == 0) {
        throw new Error(`Vui lòng kiểm tra lại dữ liệu import`)
      }

      const items = await this.itemRepository.find({
        where: { code: In(itemCodes) },
        select: { code: true, name: true, id: true, supplierId: true, quantityUnit: true },
        relations: { unit: true, itemGroup: true, pOUnit: true },
      })

      const dictItemPricing: any = {}
      {
        const itemPricings = await this.pricingRepo.find({ where: { itemId: In(items.map((val) => val.id)) } })
        itemPricings.forEach((val) => (dictItemPricing[val.itemId] = val))
      }

      if (items.length == 0) {
        throw new Error(`Không tìm thấy sản phẩm, vui lòng kiểm tra lại dữ liệu import`)
      }

      // Phân bổ item
      let distributeItems = []
      for (let i = 0; i < items.length; i++) {
        const val = items[i]
        const item = items.find((i) => i.code == val.code)

        // Lấy ra vùng từ regionCode
        const tf = transformedData.find((val) => val.itemCode == item.code)

        // Lấy thông tin vùng
        const region = await this.regionRepository.findOne({ where: { code: tf.regionCode } })

        // Lấy chuỗi cung ứng
        const sp = await this.getSupplyChain(region.id, item.supplierId)

        // Lấy ra kho của 3PL
        // 3PL có nhiều kho
        const { distributor, warehouse } = await this.getDistributorAndWarehouseByProvinceCode(
          wh_provinceCode.code,
          [wh_districtCode.code],
          item.supplierId,
          sp?.deliveryId,
        )

        // Lấy quy cách đóng gói
        const unit = await item.unit

        // Lấy quy cách giao hàng
        const poUnit = await item.pOUnit

        // Lấy nhóm sản phẩm
        const group = await item.itemGroup

        const totalPrice = Number(tf.price) * Number(tf.quantity)
        const totalPriceVat = totalPrice + totalPrice * (Number(tf.vat) / 100)

        distributeItems.push({
          ...item,
          itemName: item.name,
          itemId: item.id,
          groupName: group?.name || '--Không có dữ liệu--',
          itemGroupId: group?.id,
          packingSpecification: unit?.name,
          baseUnit: unit?.baseUnit,
          purchaseUnit: poUnit?.baseUnit,
          purchaseBaseUnit: poUnit?.name,
          poUnitId: poUnit?.id,
          poUnitName: poUnit?.name,
          unitName: unit?.name,
          quantityUnit: item.quantityUnit,
          provinceCode: tf.regionCode,
          regionName: region?.name,
          regionId: region?.id,
          sellPrice: +tf.price || dictItemPricing[item.id]?.price || 0,
          inputPrice: +tf.price || dictItemPricing[item.id]?.price || 0,
          quantityBasicUnit: tf.quantity,
          vat: tf.vat || (await item.buyTax)?.percent || 0,
          totalAmount: totalPrice,
          totalAmountVat: totalPriceVat,
          distributorId: sp.distributorId,
          deliveryId: sp.deliveryId,
          warehouseId: warehouse[0], // Kho 3PL
          partnerId: wh.storeId, // Kho trung tâm cho lastmile
        })
      }

      for (const item of distributeItems) {
        delete item.__unit__
        delete item.__itemGroup__
        delete item.__pOUnit__
      }

      return { items: distributeItems, warehouseCode }
    } catch (error) {
      throw new Error(error.message || 'Error importing file')
    }
  }
  //#endregion

  //#region Import Combo Manual
  public async importComboManual(file: Express.Multer.File, req: IRequest) {
    try {
      if (!file) {
        throw new Error('File not provided')
      }

      const workbook = XLSX.read(file.buffer, { type: 'buffer' })
      const sheetName = workbook.SheetNames[0]
      const data: any[] = XLSX.utils.sheet_to_json(workbook.Sheets[sheetName], {
        range: 1,
      })

      //#region Xử lý thông tin kho lastmile
      const sheet = workbook.Sheets[sheetName]
      const cellB1 = sheet['B1']
      const warehouseCode = cellB1 ? String(cellB1.w || cellB1.v).trim() : null
      if (!warehouseCode) {
        throw new Error('Vui lòng nhập mã Kho')
      }
      const wh = await this.warehouseRepository.findOne({ where: { code: warehouseCode } })
      if (!wh) {
        throw new Error('Không tìm thấy Kho')
      }

      const wh_provinceCode = await this.cityRepository.findOne({
        where: {
          id: wh.cityId,
        },
      })
      if (!wh_provinceCode) {
        throw new Error('Không tìm thấy Tỉnh/Thành phố')
      }
      const wh_districtCode = await this.districtRepository.findOne({
        where: {
          id: wh.districtId,
        },
      })
      if (!wh_districtCode) {
        throw new Error('Không tìm thấy Quận/Huyện')
      }
      //#endregion

      if (!data || data.length === 0) {
        throw new Error('No data found in file')
      }

      //check template

      const header = Object.keys(data[0])
      const requiredHeader = ['Vùng (*)', 'Mã combo (*)', 'Số lượng (*)']
      requiredHeader.forEach((val, index) => {
        if (header[index] != val) {
          throw new Error(`Template không đúng định dạng vui lòng kiểm tra lại !!!`)
        }
      })

      const transformedData = data.map((row: any) => ({
        region: row['Vùng (*)'],
        comboCode: row['Mã combo (*)'],
        quantity: row['Số lượng (*)'],
      }))

      const comboCodes = transformedData.map((item) => item.comboCode)
      const regionCodes = transformedData.map((item) => item.region)

      if (comboCodes.length == 0) {
        throw new Error(`Vui lòng kiểm tra lại dữ liệu import`)
      }

      const combos = await this.itemRepository.find({
        where: {
          isCombo: true,
          code: In(comboCodes),
        },
        select: {
          code: true,
          name: true,
          id: true,
        },
      })

      const regions = await this.regionRepository.find({
        where: {
          code: In(regionCodes),
        },
      })

      if (combos.length == 0 || combos.length !== comboCodes.length) {
        throw new Error(`Có Combo không hoạt động, vui lòng kiểm tra lại dữ liệu import`)
      }

      const comboLst = await this.itemRepository.find({ where: { isCombo: true }, select: { id: true, name: true, code: true } })
      if (!comboLst || comboLst.length == 0) {
        throw new Error('Không tìm thấy danh sách Combo')
      }

      // Phân bổ item
      const combosCode = comboCodes.map((val) => `'${val}'`)
      const comboItems = await this.getItemsInComboDetails(combosCode, 'code')

      const distributeComboItems = []
      const distributeCombo = []

      const baInfo = await this.supplierRepo.findOne({ where: { code: 'BA' } })
      const dictItemPricing: any = {}
      {
        const itemPricings = await this.pricingRepo.find({
          where: { itemId: In(comboItems.map((val) => val.itemId)), supplierId: In(comboItems.map((val) => val.supplierId)) },
        })
        itemPricings.forEach((val) => (dictItemPricing[val.itemId] = val))
      }

      // Duyệt toàn bộ danh sách sản phẩm trong combo
      for (let y = 0; y < comboItems.length; y++) {
        const item = comboItems[y] // Lấy ra thông tin của combo

        const combo = transformedData.find((t) => t.comboCode === item.code)
        const r = regions.find((r) => r.code == combo.region)

        // Kiểm tra số lượng item trong combo
        const countItem = await this.itemComboRepo.findOne({ where: { itemId: item.comboId, itemInComboId: item.itemId } })
        if (countItem.quantity == 0) throw new Error(`Cấu hình số lượng item (${item.itemCode}) trong combo (${item.code}) chưa hợp lệ `)

        // Lấy chuỗi cung ứng
        const sp = await this.getSupplyChain(r.id, item.supplierId)
        const itemTotalAmount = Number(item.inputPrice) * combo.quantity
        const itemTotalAmountVat = Math.round(Number(itemTotalAmount) + Number(+itemTotalAmount * (+item.itemVat / 100)))

        const { distributor, warehouse } = await this.getDistributorAndWarehouseByProvinceCode(
          wh_provinceCode.code,
          [wh_districtCode.code],
          item.supplierId,
          sp?.deliveryId,
        )

        distributeComboItems.push({
          ...item,
          sellPrice: dictItemPricing[item.id]?.price || 0, //lấy theo giá được thiết lập trong bảng giá nếu không có thì trả về 0
          inputPrice: dictItemPricing[item.id]?.price || 0, //lấy theo giá được thiết lập trong bảng giá nếu không có thì trả về 0
          poUnitId: item.poUnitId,
          poUnitName: item.poUnitName,
          vat: item.buyTaxPercent,
          groupName: item.itemGroupName,
          buyTaxPercent: item.buyTaxPercent,
          quantityUnit: item.quantityUnit,
          unitName: item.unitName,
          supplierName: item.supplierName,

          regionName: r?.name,
          regionId: r?.id,
          quantityBasicUnit: countItem.quantity, // Trên FE nhân số lượng thành phần combo, số lượng thành phần trong combo
          quantityCombo: combo.quantity,
          distributorId: sp.distributorId,
          deliveryId: sp.deliveryId,
          totalAmount: itemTotalAmount, // Trên FE tính lại tiền
          totalAmountVat: itemTotalAmountVat, // Trên FE tính lại tiền
          warehouseId: warehouse[0],
          partnerId: wh.storeId,
        })
      }

      for (const combo of transformedData) {
        const c = combos.find((c) => c.code === combo.comboCode)

        distributeCombo.push({
          comboId: c?.id,
          comboCode: combo.comboCode,
          comboName: c?.name,
          quantity: combo.quantity,
        })
      }

      return { combo: distributeCombo, items: distributeComboItems }
    } catch (error) {
      throw new Error(error.message || 'Error importing file')
    }
  }
  //#endregion

  /** Xem Chi tiết phân bổ PO khi upload combo */
  public async detailDistributePurchaseOrder(body: DistributeViewPODto) {
    const { distribute, poType } = body
    const result = []
    const resultMapping = []
    if (distribute && distribute.length > 0) {
      for (let d = 0; d < distribute.length; d++) {
        const elm = distribute[d]
        const cityCode = elm.provinceCode
        const region = await this.getRegionIdByProvinceCode(cityCode)

        result.push({
          ...elm,
          regionName: region[0].regionName,
          regionId: region[0].regionId,
        })
      }
      if (poType == NSPo.EPoType.WITHCOMBO) {
        for (let i = 0; i < result.length; i++) {
          const item = result[i]
          const combo = await this.itemRepository.findOne({ where: { id: item.productId, isCombo: true } })
          const comboPrice = await this.itemPriceRepo.findOne({ where: { itemId: item.productId, isFinal: true } })
          const itemInCombo = await this.itemComboRepo.find({ where: { itemId: item.productId } })
          const itemPrices = await this.itemPriceRepo.find({ where: { itemId: In(itemInCombo.map((val) => val.itemInComboId)), isFinal: true } })
          const itemLst = await this.itemRepository.find({ where: { id: In(itemInCombo.map((val) => val.itemInComboId)) } })

          const itemsMapping = itemInCombo.map((val) => {
            const itemPrice = itemPrices.find((i) => i.itemId == val.itemInComboId)
            if (!itemPrice) throw new Error(`Có sản phẩm/combo chưa cấu hình giá`)
            const _item = itemLst.find((i) => i.id == val.itemInComboId)

            return {
              id: _item.id,
              name: _item.name,
              code: _item.code,
              quantityItem: val.quantity, // Số lượng thành phần
              sellPrice: +itemPrice?.priceSell,
              quantity: +item.quantity, // Số lượng combo
              totalAmount: itemPrice.priceSell * item.quantity * val.quantity,
            }
          })
          const totalAmountItem = itemsMapping.reduce((sum, item) => sum + item.totalAmount, 0)
          const totalAmountCombo = +comboPrice.priceSell * +item.quantity

          resultMapping.push({
            ...item,
            comboName: combo.name,
            comboPrice: +comboPrice.priceSell,
            totalAmountCombo,
            totalAmountItem,
            itemsMapping,
          })
        }
        return resultMapping
      } else {
        const productIds = Array.from(new Set(result.map((val) => val.productId)))
        const itemPrices = await this.itemPriceRepo.find({ where: { itemId: In(productIds), isFinal: true } })

        const resultMapping = result.map((val) => {
          const itemPrice = itemPrices.find((i) => i.itemId == val.productId)
          if (!itemPrice) throw new Error(`Có sản phẩm/combo chưa cấu hình giá`)

          return {
            ...val,
            itemId: val.productId,
            sellPrice: +itemPrice?.priceSell,
            totalAmount: itemPrice.priceSell * val.quantity,
          }
        })
        return resultMapping
      }
    }
  }

  /** Gửi PO tới Supplier */
  public async sendPurchaseOrder(body: UpdateStatusPurchaseOrderDto, req: IRequest) {
    const { id, updateBy } = body
    const po = await this.purchaseOrderRepo.findOne({ where: { id } })
    if (!po) {
      throw new Error(`Po id ${id} is not found`)
    }
    const checkStatus = await this.purchaseOrderRepo.findOne({ where: { id, status: NSPo.EPoStatus.NEWLYCREATED } })
    if (!checkStatus) {
      throw new Error(`Trạng thái của PO ${po.purchaseOrderCode} phải là mới tạo`)
    }
    const supplyChainApprove = await this.supplyChainConfigApproveRepo.find({
      where: { supplyChainDetailId: po.supplyChainId },
      order: { approvalLevel: 'ASC' },
    })

    const user: any = await this.userRepository.findOne({ where: { id: updateBy } })
    if (!user) throw new Error('Không tìm thấy thông tin người gửi PO')

    if (!po.approverCurrentId) {
      await this.purchaseOrderRepo.update(
        { id },
        {
          approverCurrentId: supplyChainApprove[0].approverId,
          approvalLevelCurrent: supplyChainApprove[0].approvalLevel,
        },
      )
      await this.addHistory(po.id, po.purchaseOrderCode, user.username, 'SEND_PO', `Đã gửi cho ${supplyChainApprove[0].approverName}`)
    }

    // Update status PO
    await this.purchaseOrderRepo.update({ id }, { status: NSPo.EPoStatus.SENT, approveStatus: NSPo.EPoStatus.PENDING_APPROVE })

    return { message: UPDATE_SUCCESS }
  }

  //#region Duyệt PO
  /** Supplier duyệt PO */
  public async approvePurchaseOrder(query: UpdateStatusPurchaseOrderDto, req: IRequest, user: UserDto) {
    const { id } = query
    const po = await this.purchaseOrderRepo.findOne({ where: { id } })
    if (!po) {
      throw new Error(`Po id ${id} is not found`)
    }
    // Kiểm tra status của PO phải là TRANSFER
    const checkStatus = await this.purchaseOrderRepo.findOne({ where: { id, status: NSPo.EPoStatus.SENT } })
    if (po.approvalLevelCurrent == 1) {
      if (!checkStatus) {
        throw new Error(`Status's PO ${po.purchaseOrderCode} must be SENT`)
      }
    }
    if (po.status === NSPo.EPoStatus.PENDING_CONFIRM) {
      throw new Error(`${po.purchaseOrderCode} đã được duyệt xong`)
    }

    if (!po.supplyChainId) {
      // Duyệt PO không theo cấu hình chuỗi supplyChain
      await Promise.all([
        this.purchaseOrderRepo.update({ id }, { status: NSPo.EPoStatus.COMPLETE, approveStatus: NSPo.EPoStatus.APPROVED }),
        this.purchaseOrderApproveRepo.update({ purchaseOrderId: po.id }, { isApproved: true }),
        this.addHistory(po.id, po.purchaseOrderCode, user.username, 'APPROVED', `BA đã duyệt ${po.purchaseOrderCode}`),
      ])
      await this.syncPoPMS(po.id, req)
      return { message: UPDATE_SUCCESS }
    } else {
      const supplyChainDetail = await this.supplyChainConfigDetailRepo.findOne({ where: { id: po.supplyChainId } })
      if (!supplyChainDetail) throw new Error(`Không tìm thấy chi tiết cấp duyệt của chuỗi cung ứng`)

      const supplyChainApprove = await this.supplyChainConfigApproveRepo.find({
        where: { supplyChainDetailId: supplyChainDetail.id },
        order: { approvalLevel: 'ASC' },
      })

      const level = po.approvalLevelCurrent

      if (level == supplyChainApprove.length) {
        // Lấy danh sách soId hiện tại từ po
        const soIds = await this.purchaseOrderSORepo.find({ where: { poId: po.id }, select: { soId: true } }).then((r) => r.map((r) => r.soId))

        // Cập nhật tham chiếu SO
        await this.purchaseOrderSORepo.update({ poId: po.id, soId: In(soIds) }, { status: NSPo.EPoStatus.APPROVED })

        // Approve phiếu nhập kho
        const rsInbound = await this.approveInboundSupplierWarehouse(po.id, req)
        if (!rsInbound?.message) {
          throw new Error(`Không thể duyệt PO ${po.purchaseOrderCode}, vui lòng cập nhật chứng từ`)
        }
        await this.purchaseOrderRepo.update({ id }, { status: NSPo.EPoStatus.COMPLETE, approveStatus: NSPo.EPoStatus.APPROVED })

        // Tạo PO con cho distributor
        await this.createChildPO(po.id)

        // Update status SO là Chờ Giao
        if (soIds && soIds.length > 0) {
          // Lấy danh sách po đang tham chiếu tới soIds
          // ! Kiểm tra soIds có đang được tham chiếu ở PO khác hay không
          const poIds = await this.purchaseOrderSORepo.find({ where: { soId: In(soIds) } }).then((r) => Array.from(new Set(r.map((r) => r.poId))))

          // Lấy danh sách PO đã duyệt xong để kiểm tra
          const checkPoLst = await this.purchaseOrderRepo.find({ where: { id: In(poIds), approveStatus: NSPo.EPoStatus.APPROVED } })

          // Toàn bộ PO đang tham chiếu tới SO phải APPROVED hết thì mới cập nhật SO
          if (checkPoLst.length == poIds.length) {
            const soLst = await this.purchaseOrderSORepo
              .find({ where: { poId: In(poIds) }, select: { soId: true } })
              .then((r) => Array.from(new Set(r.map((r) => r.soId))))
            const rs = await omsApiHelper.updateSOStatus(req, { ids: soLst, status: NSRecurringOrder.EStatus.PENDING_DELIVERY })
            if (!rs?.message) {
              throw new Error(`Lỗi cập nhật danh sách SO`)
            }
          }
        }

        //find DN của PO
        const deliveryNote = await this.deliveryNoteRepo.findOne({
          where: {
            poId: Raw((alias) => `${alias} @> '["${po.id}"]'::jsonb`),
          },
        })

        const poLst = deliveryNote.poId

        const listApprovedPo = await this.purchaseOrderRepo.find({
          where: { id: In(poLst), status: NSPo.EPoStatus.COMPLETE, approveStatus: NSPo.EPoStatus.APPROVED },
        })

        //check nếu tất cả các PO đã được duyệt thì bắt đầu quá trình đồng bộ PO ---> PMS
        if (listApprovedPo.length == poLst.length) {
          await Promise.all(poLst.map((id) => this.syncPoPMS(id, req))).catch((err) => {
            throw new Error(err)
          })
        }
      } else {
        await this.purchaseOrderRepo.update(
          { id },
          {
            approverCurrentId: supplyChainApprove[level].approverId,
            approvalLevelCurrent: supplyChainApprove[level].approvalLevel,
          },
        )
        await this.purchaseOrderRepo.update({ id }, { approveStatus: NSPo.EPoStatus.PENDING_APPROVE })
      }
      await this.purchaseOrderApproveRepo.update(
        { purchaseOrderId: po.id, supplierId: supplyChainApprove[level - 1].approverId },
        { isApproved: true },
      )
      await this.addHistory(
        po.id,
        po.purchaseOrderCode,
        supplyChainApprove[level - 1].approverName,
        'APPROVED',
        `Nhà cung cấp ${supplyChainApprove[level - 1].approverName} đã duyệt ${po.purchaseOrderCode}`,
      )
      return { message: UPDATE_SUCCESS }
    }
  }
  //#endregion

  //#region History
  async addHistory(purchaseOrderId: string, purchaseOrderCode: string, username: string, action: string, note: string = '') {
    const description = `${new Date().toLocaleDateString()}, 
      Người tạo: ${username}, 
      Thao tác: ${action} Mã PO: ${purchaseOrderCode},
      Ghi chú: ${note}
    `

    const history = this.purchaseOrderHistory.create({
      purchaseOrderId,
      description: description,
      createdByName: username,
    })

    await this.purchaseOrderHistory.save(history)
  }
  //#endregion

  //#region Cancel PO
  public async cancelPurchaseOrder(body: UpdateStatusPurchaseOrderDto, req: IRequest) {
    const { id, updateBy } = body
    const po = await this.purchaseOrderRepo.findOne({ where: { id } })
    if (!po) {
      throw new Error(`Không tìm thấy thông tin PO`)
    }
    const checkStatus = await this.purchaseOrderRepo.findOne({ where: { id, status: NSPo.EPoStatus.NEWLYCREATED } })
    if (!checkStatus) {
      throw new Error(`Trạng thái của PO ${po.purchaseOrderCode} phải là mới tạo`)
    }

    let updateName = ''
    const supplier: any = await this.supplierRepo.findOne({ where: { id: updateBy } })
    if (!supplier) {
      const user: any = await this.userRepository.findOne({ where: { id: updateBy } })
      updateName = user.username
    } else {
      updateName = supplier.username
    }
    await this.addHistory(po.id, po.purchaseOrderCode, updateName, 'CANCEL_APPROVE')

    // Update status PO
    await this.purchaseOrderRepo.update({ id }, { status: NSPo.EPoStatus.CANCEL, approveStatus: NSPo.EPoStatus.CANCEL })

    // Lấy danh sách soId từ purchaseOrderSalesOrder
    const soIds = await this.purchaseOrderSORepo
      .find({
        where: { poId: po.id },
        select: { soId: true },
      })
      .then((r) => r.map((r) => r.soId))

    if (soIds && soIds.length > 0) {
      // Kiểm tra SO của PO này có đang được tham chiếu tới PO khác không
      const dataMapping = await this.getRelatedSoIds(po.id)
      if (dataMapping.soIds.length > 0) {
        // Rule 2: Trường hợp có thì Cancel toàn bộ PO liên quan
        const { poIds, soIds } = dataMapping
        await Promise.all([
          this.purchaseOrderRepo.update({ id: In(poIds) }, { status: NSPo.EPoStatus.CANCEL, approveStatus: NSPo.EPoStatus.REJECT }),
          this.purchaseOrderSORepo.update({ poId: In(poIds), soId: In(soIds) }, { status: NSPo.EPoStatus.CANCEL, isDeleted: true }),
        ])
        // Cập nhật lại deliveryNote
        const dlNote = await this.deliveryNoteRepo
          .createQueryBuilder('deliveryNote')
          .where('deliveryNote.poId @> :poId', { poId: JSON.stringify(poIds) })
          .getOne()
        // Cập nhật lại Delivery note tracking
        await this.deliveryTrackingRepository.update({ deliveryNoteId: dlNote.id, supplierId: po.supplierId }, { isDeleted: true })

        const rs = await omsApiHelper.updateSOStatus(req, { ids: soIds, status: NSRecurringOrder.EStatus.PENDING })
        if (!rs?.message) {
          // Back lại
          await Promise.all([
            this.purchaseOrderRepo.update({ id: In(poIds) }, { status: NSPo.EPoStatus.NEWLYCREATED, approveStatus: NSPo.EPoStatus.PENDING_APPROVE }),
            this.purchaseOrderSORepo.update({ poId: In(poIds), soId: In(soIds) }, { status: NSPo.EPoStatus.NEWLYCREATED }),
          ])
          throw new Error(`Lỗi cập nhật danh sách SO`)
        }
      } else {
        const rs = await omsApiHelper.updateSOStatus(req, { ids: soIds, status: NSRecurringOrder.EStatus.PENDING })
        if (!rs?.message) {
          // Back lại
          await Promise.all([
            this.purchaseOrderRepo.update({ id: po.id }, { status: NSPo.EPoStatus.NEWLYCREATED, approveStatus: NSPo.EPoStatus.PENDING_APPROVE }),
            this.purchaseOrderSORepo.update({ poId: po.id, soId: In(soIds) }, { status: NSPo.EPoStatus.NEWLYCREATED }),
          ])
          throw new Error(`Lỗi cập nhật danh sách SO`)
        }
      }
    }

    return { message: UPDATE_SUCCESS }
  }
  //#endregion
  //#region Hủy Duyệt Po
  public async rejectPurchaseOrder(body: UpdateStatusPurchaseOrderDto, req: IRequest) {
    const { id, updateBy } = body
    const po = await this.purchaseOrderRepo.findOne({ where: { id } })
    if (!po) {
      throw new Error(`Không tìm thấy thông tin PO`)
    }
    const checkStatus = await this.purchaseOrderRepo.findOne({ where: { id, status: NSPo.EPoStatus.SENT } })
    if (!checkStatus) {
      throw new Error(`Trạng thái của PO ${po.purchaseOrderCode} phải là đã gửi`)
    }

    let updateName = ''
    const supplier: any = await this.supplierRepo.findOne({ where: { id: updateBy } })
    if (!supplier) {
      const user: any = await this.userRepository.findOne({ where: { id: updateBy } })
      updateName = user.username
    } else {
      updateName = supplier.username
    }
    await this.addHistory(po.id, po.purchaseOrderCode, updateName, 'REJECT')

    // Update status PO
    await this.purchaseOrderRepo.update({ id }, { status: NSPo.EPoStatus.CANCEL, approveStatus: NSPo.EPoStatus.REJECT })

    // Lấy danh sách soId từ purchaseOrderSalesOrder
    const soIds = await this.purchaseOrderSORepo
      .find({
        where: { poId: po.id },
        select: { soId: true },
      })
      .then((r) => r.map((r) => r.soId))

    if (soIds && soIds.length > 0) {
      // Kiểm tra SO của PO này có đang được tham chiếu tới PO khác không
      const dataMapping = await this.getRelatedSoIds(po.id)

      if (dataMapping.soIds.length > 0) {
        const { poIds, soIds } = dataMapping

        await Promise.all([
          this.purchaseOrderRepo.update({ id: In(poIds) }, { status: NSPo.EPoStatus.CANCEL, approveStatus: NSPo.EPoStatus.REJECT }),
          this.purchaseOrderSORepo.update({ poId: In(poIds), soId: In(soIds) }, { status: NSPo.EPoStatus.CANCEL, isDeleted: true }),
        ])
        // Cập nhật lại deliveryNote
        const dlNote = await this.deliveryNoteRepo
          .createQueryBuilder('deliveryNote')
          .where('deliveryNote.poId @> :poId', { poId: JSON.stringify(poIds) })
          .getOne()
        // Cập nhật lại Delivery note tracking
        await this.deliveryTrackingRepository.update({ deliveryNoteId: dlNote.id, supplierId: po.supplierId }, { isDeleted: true })

        const rs = await omsApiHelper.updateSOStatus(req, { ids: soIds, status: NSRecurringOrder.EStatus.PENDING })
        if (!rs?.message) {
          throw new Error(`Lỗi cập nhật danh sách SO`)
        }
      } else {
        const rs = await omsApiHelper.updateSOStatus(req, { ids: soIds, status: NSRecurringOrder.EStatus.PENDING })
        if (!rs?.message) {
          throw new Error(`Lỗi cập nhật danh sách SO`)
        }
      }
    }
    return { message: UPDATE_SUCCESS }
  }
  //#endregion

  async getRelatedSoIds(poId: string): Promise<{ poIds: string[]; soIds: string[] }> {
    const visitedPoIds = new Set<string>()
    const visitedSoIds = new Set<string>()

    // Hàm đệ quy tìm kiếm quan hệ
    const findRelations = async (currentPoIds: string[]) => {
      if (currentPoIds.length === 0) return

      // Lấy tất cả các soId liên quan đến danh sách poId hiện tại
      const soRelations = await this.purchaseOrderSORepo.find({
        where: currentPoIds.map((poId) => ({ poId })),
      })

      const newSoIds = soRelations.map((r) => r.soId).filter((id) => !visitedSoIds.has(id))
      newSoIds.forEach((id) => visitedSoIds.add(id))

      if (newSoIds.length === 0) return

      // Lấy tất cả các poId liên quan đến các soId vừa tìm được
      const poRelations = await this.purchaseOrderSORepo.find({
        where: newSoIds.map((soId) => ({ soId })),
      })

      const newPoIds = poRelations.map((r) => r.poId).filter((id) => !visitedPoIds.has(id))
      newPoIds.forEach((id) => visitedPoIds.add(id))

      // Đệ quy tiếp tục tìm kiếm
      await findRelations(newPoIds)
    }

    visitedPoIds.add(poId)
    await findRelations([poId])

    return {
      poIds: Array.from(visitedPoIds),
      soIds: Array.from(visitedSoIds),
    }
  }

  //#region Danh sách SO tham thiếu
  async findSoByPo(body: ListPOwithSODto, req: IRequest) {
    // Kiểm tra id
    const { id, distributorId } = body
    const po = await this.purchaseOrderRepo.findOne({ where: { id } })
    if (!po) {
      throw new Error(`Không tìm thấy thông tin PO`)
    }

    const soIds = await this.purchaseOrderSORepo.find({ where: { poId: po.id }, select: { soId: true } }).then((r) => r.map((r) => r.soId))
    if (soIds.length == 0) {
      throw new Error(`PO ${po.purchaseOrderCode} này không có SO nào tham chiếu`)
    }

    // Gọi oms service lấy ra danh sách SO
    const soLst = await omsApiHelper.getOrderByIds(req, { orderListIds: soIds })

    for (let data of soLst.data) {
      for (let product of data.products) {
        if (product.isCombo == true) {
          product.items = await this.itemComboRepo.find({ where: { itemId: product.productId } })
          product.unitName = 'COMBO'
          for (let item of product.items) {
            let itemDetails = await this.itemRepository.findOne({ where: { id: item.itemInComboId } })
            let unitDetails = await this.unitRepository.findOne({ where: { id: itemDetails.unitId } })
            item.productName = itemDetails.name
            item.productCode = itemDetails.code
            item.unitId = unitDetails.id
            item.unitName = unitDetails.name
          }
        }
        // if (!product.isCombo) {
        //   let itemDetails = await this.itemRepository.findOne({ where: { id: product.productId } })
        //   let unitDetails = await this.unitRepository.findOne({ where: { id: itemDetails.unitId } })
        //   product.unitId = unitDetails.id
        //   product.unitName = unitDetails.name
        // } else {
        //   // let listItemCombos = await this.getItemsInComboDetails([product.productId], 'id')
        //   product.items = await this.itemComboRepo.find({ where: { itemId: product.productId } })
        //   for (let item of product.items) {
        //     let itemDetails = await this.itemRepository.findOne({ where: { id: item.itemInComboId } })
        //     let unitDetails = await this.unitRepository.findOne({ where: { id: itemDetails.unitId } })
        //     item.productName = itemDetails.name
        //     item.productCode = itemDetails.code
        //     item.unitId = unitDetails.id
        //     item.unitName = unitDetails.name
        //   }
        //   data.products = product.items
        // }
      }
    }
    return {
      po: po,
      ...soLst,
    }
  }
  //#endregion

  //#region Tạo PO con của distributor từ thông tin PO cha
  public async createChildPO(poId: string) {
    const po = await this.purchaseOrderRepo.findOne({ where: { id: poId } })
    if (!po) {
      throw new Error(`Không tìm thấy thông tin PO`)
    }

    const poItems = await this.purchaseOrderItemRepo.find({ where: { purchaseOrderId: po.id } })
    const mappingDistributor = Array.from(new Set(poItems.map((val) => val.distributorId)))

    // Tạo PO con từ danh sách Distributor
    for (const distributorId of mappingDistributor) {
      const countPo = await this.purchaseOrderChildRepo.count()
      const poCode = coreHelper.generatePOString(countPo, 'POC')
      const totalAmount = poItems.filter((val) => val.distributorId === distributorId).reduce((sum, item) => +sum + +item.totalAmount, 0)
      const totalAmountVat = poItems.filter((val) => val.distributorId === distributorId).reduce((sum, item) => +sum + +item.totalAmountVat, 0)

      await this.purchaseOrderChildRepo.save({
        purchaseOrderId: po.id,
        purchaseOrderCode: poCode,
        purchaseOrderType: po.purchaseOrderType,
        supplierId: po.supplierId,
        distributorId: distributorId,
        vat: totalAmountVat - totalAmount,
        totalAmount,
        totalAmountVat,
        supplyChainId: po.supplyChainId,
      })
    }
  }
  //#endregion

  //#region HELPER
  /** -------------------- Helper --------------------- */
  /** Lấy regionID bằng cityCode (province code) */
  public async getRegionIdByProvinceCode(provinceCode: string) {
    const query = `
      SELECT  c.id as "cityId", c.code, c.name, r."regionId", rg.name as "regionName"
      FROM public.city as c
      LEFT JOIN region_city as r 
	      ON r."cityId" = c.id
      LEFT JOIN region as rg 
	      ON rg.id = r."regionId"
      WHERE c.code = '${provinceCode}'`
    return await this.cityRepository.query(query)
  }

  /** Lấy supplyChainId bằng regionId và supplierId */
  public async getSupplyChain(regionId: string, supplierId: string) {
    const query = `
      SELECT 
        c.id as "supplyChainId", 
        c."supplyChainCode", 
        c."supplierId", 
        d."distributorId", 
        d."regionId", 
        d."deliveryId",
        r.name as "regionName"
      FROM supply_chain_config as c
      LEFT JOIN supply_chain_config_detail as d
	      ON c.id = d."supplyChainId"
      LEFT JOIN region as r
	      ON r.id = d."regionId"
      WHERE d."regionId" = '${regionId}' AND c."supplierId" = '${supplierId}' AND  c."status" = 'ACTIVE'
    `
    return await this.supplyChainConfigRepository.queryOne(query)
  }

  /** Lấy chuỗi cung ưng bằng distributorId và supplierId */
  public async getSupplyChainByDistributor(distributorId: string, supplierId: string) {
    const query = `
      SELECT 
        c.id as "supplyChainId", 
        c."supplyChainCode", 
        c."supplierId", 
        d."distributorId", 
        d."regionId", 
        d."deliveryId",
        d.id as "supplyChainDetailId",
        r.name as "regionName",
        r.id as "regionId"
      FROM supply_chain_config as c
      LEFT JOIN supply_chain_config_detail as d
	      ON c.id = d."supplyChainId"
      LEFT JOIN region as r
	      ON r.id = d."regionId"
      WHERE c."supplierId" = '${supplierId}' 
       AND c."partnerId" IS NULL
       AND  c."status" = 'ACTIVE'
    `
    return await this.supplyChainConfigRepository.queryOne(query)
  }

  public async getItemsInComboDetails(ids: string[], property: string = 'id') {
    let whereCon = ''
    if (property == 'id') {
      whereCon = `i."${property}" = ANY(ARRAY[${ids.join(',')}]::uuid[])`
    } else {
      whereCon = `i."${property}" = ANY(ARRAY[${ids.join(',')}])`
    }

    const query = `
    SELECT DISTINCT
      ig."name" AS "itemGroupName",
      ig.id AS "itemGroupId",
      i.code AS "code", 
      i.id AS "comboId", 
      i.name AS "comboName", 
      i.vat, 
      i2.code AS "itemCode",
      i2.id AS "itemId", 
      i2.name AS "itemName",
      i2.vat AS "itemVat",
      ic.quantity AS "quantityInCombo",
      ip."priceSell" as "sellPrice",
      ip."priceInput" as "inputPrice",
      i2."supplierId" AS "supplierId",
      i2."unitId" AS "unitId",
      i2."poUnitId" AS "poUnitId",
      i2."buyTaxId" AS "buyTaxId",
      u.name AS "packingSpecification",
      u."baseUnit" AS "baseUnit",
      u2.name AS "purchaseUnit",
      u2."baseUnit" AS "purchaseBaseUnit",
      s.name AS "supplierName",
      t.percent AS "buyTaxPercent",
      u.name AS "unitName",
      u2.name AS "poUnitName",
      i2."quantityUnit" AS "quantityUnit"
    FROM item i
      INNER JOIN item_combo ic ON i.id = ic."itemId"
      INNER JOIN item i2 ON ic."itemInComboId" = i2.id
      INNER JOIN item_price ip ON i2.id = ip."itemId"
      LEFT JOIN tax t ON t.id = i2."buyTaxId"
      LEFT JOIN unit u ON u.id = i2."unitId"
      LEFT JOIN unit u2 ON u2.id = i2."poUnitId"
      INNER JOIN item_group ig ON ig.id = i2."itemGroupId"
      LEFT JOIN supplier s ON s.id = i2."supplierId"
    WHERE i."isCombo" = TRUE 
      AND ${whereCon}
      AND ip."isFinal" = true
    ORDER BY i.name
    `
    return await this.itemRepository.query(query)
  }

  // Lấy chuỗi cung ứng áp dụng cho bán sỉ
  public async getSupplyChainForWholesale(partnerId: string, supplierId: string, regionId?: string) {
    const spl = await this.supplyChainConfigRepository.findOne({ where: { partnerId, supplierId } })
    if (!spl) throw new Error(`Không tìm thấy chuỗi cung ứng cho bán sỉ`)
    const splDetail = await this.supplyChainConfigDetailRepo.findOne({ where: { supplyChainId: spl.id, regionId } })
    if (!splDetail) throw new Error(`Không tìm thấy chi tiết chuỗi cung ứng cho bán sỉ`)
    const slpApprove = await this.supplyChainConfigApproveRepo.findOne({ where: { supplyChainId: spl.id, supplyChainDetailId: splDetail.id } })
    if (!slpApprove) throw new Error(`Không tìm thấy cấu hình duyệt chuỗi cung ứng cho bán sỉ`)
    return {
      supplyChainId: spl.id,
      supplyChainApprove: slpApprove,
      supplyChainDetail: splDetail,
    }
  }

  public checkComboMultipleSuppliers(items) {
    const comboSuppliers = {}
    const rs = []

    items.forEach((item) => {
      const comboId = item.comboId
      const supplierId = item.supplierId

      if (!comboSuppliers[comboId]) {
        comboSuppliers[comboId] = new Set()
      }

      comboSuppliers[comboId].add(supplierId)
    })

    for (const comboId in comboSuppliers) {
      if (comboSuppliers[comboId].size === 2) {
        rs.push(comboId)
      }
    }
    return rs
  }

  private async getSupplyChainDetails(supplierId: string, poItems: any[]) {
    const mappingMBC = poItems.map((val) => val.partnerId)
    const mappingRegion = poItems.map((val) => val.regionId)
    const supplyChain = await this.supplyChainConfigRepository.find({ where: { supplierId, partnerId: In(mappingMBC) } })
    const supChainIds = Array.from(new Set(supplyChain.map((val) => val.id)))
    return await this.supplyChainConfigDetailRepo.find({ where: { regionId: In(mappingRegion), supplyChainId: In(supChainIds) } })
  }

  /** Lấy số lượng PO có poGroup trong ngày hiện tại */
  public async countPoGroupToday(): Promise<number> {
    const startOfDay = new Date()
    startOfDay.setHours(0, 0, 0, 0)

    const endOfDay = new Date()
    endOfDay.setHours(23, 59, 59, 999)

    const query = `
      SELECT COUNT(*)
      FROM purchase_order
      WHERE "purchaseOrderGroup" IS NOT NULL
      AND "createdAt" BETWEEN '${startOfDay.toISOString()}' AND '${endOfDay.toISOString()}'
    `

    const result = await this.purchaseOrderRepo.query(query)
    return parseInt(result[0].count, 10)
  }

  /** Lấy số lượng ASNT trong ngày hiện tại */
  public async countASNToday(): Promise<number> {
    const startOfDay = new Date()
    startOfDay.setHours(0, 0, 0, 0)

    const endOfDay = new Date()
    endOfDay.setHours(23, 59, 59, 999)

    const query = `
      SELECT COUNT(*)
      FROM delivery_note
      WHERE "createdAt" BETWEEN '${startOfDay.toISOString()}' AND '${endOfDay.toISOString()}'
    `

    const result = await this.deliveryNoteRepo.query(query)
    return parseInt(result[0].count, 10)
  }

  /** Lấy số lượng ASN Tracking trong ngày hiện tại */
  public async countTrackingToday(): Promise<number> {
    const startOfDay = new Date()
    startOfDay.setHours(0, 0, 0, 0)

    const endOfDay = new Date()
    endOfDay.setHours(23, 59, 59, 999)

    const query = `
      SELECT COUNT(*)
      FROM delivery_note_tracking
      WHERE "createdAt" BETWEEN '${startOfDay.toISOString()}' AND '${endOfDay.toISOString()}'
    `

    const result = await this.deliveryTrackingRepository.query(query)
    return parseInt(result[0].count, 10)
  }

  /** Lấy số lượng ASN Packing trong ngày hiện tại */
  public async countPackingToday(): Promise<number> {
    const startOfDay = new Date()
    startOfDay.setHours(0, 0, 0, 0)

    const endOfDay = new Date()
    endOfDay.setHours(23, 59, 59, 999)

    const query = `
      SELECT COUNT(*)
      FROM delivery_note_tracking
      WHERE "isPacking" = 'true' AND "createdAt" BETWEEN '${startOfDay.toISOString()}' AND '${endOfDay.toISOString()}'
    `

    const result = await this.deliveryTrackingRepository.query(query)
    return parseInt(result[0].count, 10)
  }

  /** Lấy ra nhà NPP và Warehouse theo provinceCode từ Operational Area Entity*/
  public async getDistributorAndWarehouseByProvinceCode(provinceCode: string, districtCode: string[], supplierId: string, thirdPartyId: string) {
    // Lấy ra danh sách NPP của NCC
    const distributorLst = await this.supplierRepo.find({
      where: { isDistributor: true, parentId: supplierId },
      select: ['id'],
    })
    const distributorIds = distributorLst.map((val) => val.id)

    // Lấy ra NPP
    let distributor = await this.operationalAreaRepo.find({
      where: {
        areaCode: provinceCode,
        mappingRefId: In(distributorIds),
        type: NSOperational.EOperationalAreaType.PROVINCE,
        mappingRefType: NSOperational.EAppliedObjectType.SUPPLIER,
      },
      select: ['mappingRefId'],
    })

    // Nếu disitributor lớn hơn 1 thì lấy ra distributor theo districtCode
    if (distributor.length > 1) {
      distributor = await this.operationalAreaRepo.find({
        where: {
          areaCode: In(districtCode),
          mappingRefId: In(distributorIds),
          type: NSOperational.EOperationalAreaType.DISTRICT,
          mappingRefType: NSOperational.EAppliedObjectType.SUPPLIER,
        },
        select: ['mappingRefId'],
      })
    }

    const warehouseIds = await this.warehouseRepository.find({
      where: { storeId: thirdPartyId },
      select: ['id', 'name'],
    })

    // Lấy ra Warehouse
    const warehouse = await this.operationalAreaRepo.find({
      where: {
        areaCode: provinceCode,
        type: NSOperational.EOperationalAreaType.PROVINCE,
        mappingRefType: NSOperational.EAppliedObjectType.WAREHOUSE,
        mappingRefId: In(warehouseIds.map((val) => val.id)),
      },
      select: ['mappingRefId'],
    })

    return {
      distributor: distributor.map((val) => val.mappingRefId),
      warehouse: warehouse.map((val) => val.mappingRefId),
    }
  }

  public async sendPoNotification(id: string, user: UserDto) {
    const po = await this.purchaseOrderRepo.findOne({ where: { id } })
    const employee = await this.employeeRepo.findOne({ where: { userId: user.userId } })
    //supplier
    const supplier = await this.supplierRepo.findOne({ where: { id: po.supplierId } })
    const body_html = `
    <!DOCTYPE html>
    <html lang="vi">
      <head>
        <meta charset="UTF-8" />
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            font-size: 14px;
            color: #333;
          }
          .container {
            padding: 16px;
          }
          .signature {
            margin-top: 24px;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <p><strong>Kính gửi:</strong> ${supplier.name}</p>

          <p>
            Đây là email thông báo đặt hàng đính kèm đơn đặt hàng số
            <strong>${po.purchaseOrderCode}</strong> -
            <a href="https://supplier.balancelife.vn" target="_blank">supplier.balancelife.vn</a>
            từ <strong>Balance AgriService</strong>, vui lòng đảm bảo hàng hóa và thời gian giao hàng và xác nhận trên hệ thống bằng cách nhấp vào link trên.
          </p>

          <p>
            Nếu bạn có bất kỳ thắc mắc nào, đừng ngần ngại liên hệ với chúng tôi.
          </p>

          <div class="signature">
            <p>Trân trọng,</p>
            <p><strong>${employee.name}</strong><br />
            Phòng mua hàng<br /> SĐT liên hệ: ${employee.phone}</p>
          </div>
        </div>
      </body>
    </html>
`

    const emailOptions = {
      toAddresses: supplier.email,
      subject: `Đơn đặt hàng [${po.purchaseOrderCode}]: Tên nhà cung cấp [${supplier.code}]`,
      body_text: 'Thông báo đơn hàng',
      body_html: body_html,
      type: 'SendPoNotification',
    }
    await this.emailService.sendEmail(emailOptions)
    return { message: 'Gửi thông báo đơn hàng thành công', purchaseOrderCode: po.purchaseOrderCode }
  }

  public async exportPoSupplier(body: ExportPoSupplierDto, req: IRequest) {
    let { createdDateFrom, createdDateTo, approveStatus, supplierId } = body
    const wheres: any = {}

    if (createdDateFrom && createdDateTo) {
      wheres.createdAt = Between(createdDateFrom, createdDateTo)
    }
    if (approveStatus) {
      wheres.approveStatus = NSPo?.EPoStatus[approveStatus]
    }

    if (supplierId) {
      //find user
      const user = await this.userRepository.findOne({ where: { supplierId: supplierId } })
      if (!user) {
        throw new Error(`Không tìm thấy thông tin nhà cung cấp`)
      }
      wheres.createdBy = user.id
    }
    let po: any = await this.purchaseOrderRepo.find({ where: { ...wheres, purchaseOrderType: NSPo.EPoType.SUPPLIER }, order: { createdAt: 'ASC' } })
    if (!po || po?.length === 0) {
      throw new Error(`Không tìm thấy thông tin PO`)
    }

    const poIds = po.map((val) => val.id)
    const poWarehouse = Array.from(new Set(po.map((val) => val.warehouseId)))
    const poItems = await this.purchaseOrderItemRepo.find({ where: { purchaseOrderId: In(poIds) } })

    let dictItem: any = {}
    {
      const items = await this.itemRepository.find()
      items.forEach(async (val: any) => (dictItem[val.id] = { id: val.id, name: val.name, code: val.code }))
    }

    let dictWarehouse: any = {}
    {
      const warehouses: any = await this.warehouseRepository.find({ where: { id: In(poWarehouse) } })

      const warehouseWard = await this.wardRepository.find({ where: { id: In(Array.from(new Set(warehouses.map((val) => val.wardId)))) } })
      const warehouseDistrict = await this.districtRepository.find({
        where: {
          id: In(Array.from(new Set(warehouses.map((val) => val.districtId)))),
        },
      })
      const warehouseCity = await this.cityRepository.find({ where: { id: In(Array.from(new Set(warehouses.map((val) => val.cityId)))) } })

      warehouses.forEach((val) => {
        val.ward = warehouseWard.find((x) => x.id === val.wardId)?.name
        val.district = warehouseDistrict.find((x) => x.id === val.districtId)?.name
        val.city = warehouseCity.find((x) => x.id === val.cityId)?.name
        dictWarehouse[val.id] = `${val.address}, ${val.ward}, ${val.district}, ${val.city}`
      })
    }

    for (let child of po) {
      //find creator
      const user = await this.userRepository.findOne({ where: { id: child.createdBy } })
      //find supplier
      const customer = await this.supplierRepo.findOne({ where: { id: user.supplierId } })

      const childItems = poItems.filter((val) => val.purchaseOrderId === child.id)

      const mergedChildItem = childItems.reduce((acc, item) => {
        const existing = acc.find((val) => val.itemId == item.itemId)
        if (!existing) {
          acc.push({
            quantityBasicUnit: item.quantityBasicUnit,
            totalAmount: item.totalAmount,
            totalAmountVat: item.totalAmountVat,
            itemId: item.itemId,
            itemCode: dictItem[item.itemId].code,
            itemName: item.itemName,
            itemUnit: item.packingSpecification,
          })
        } else {
          existing.quantityBasicUnit = Number(existing.quantityBasicUnit) + Number(item.quantityBasicUnit)
          existing.totalAmount = Number(existing.totalAmount) + Number(item.totalAmount)
          existing.totalAmountVat = Number(existing.totalAmountVat) + Number(item.totalAmountVat)
        }
        return acc
      }, [])
      child.items = mergedChildItem
      child.customerName = customer.name
      child.supplierName = 'Công ty cổ phẩn Balance Agriservices' //tạm set cứng
      child.address = dictWarehouse[child.warehouseId]
    }

    //gộp các item trùng id
    const mergedItem = poItems.reduce((acc, item) => {
      const existing = acc.find((val) => val.itemId == item.itemId)
      if (!existing) {
        acc.push({ ...item, itemCode: dictItem[item.itemId].code, itemName: item.itemName, itemUnit: item.purchaseUnit })
      } else {
        existing.quantityBasicUnit = Number(existing.quantityBasicUnit) + Number(item.quantityBasicUnit)
        existing.totalAmount = Number(existing.totalAmount) + Number(item.totalAmount)
        existing.totalAmountVat = Number(existing.totalAmountVat) + Number(item.totalAmountVat)
      }
      return acc
    }, [])
    return { po, items: mergedItem }
  }

  //#endregion
  //#region
  /** Import PO Ref */
  public async importPoRef(data: ImportPoRefDto, req: IRequest) {
    return await this.purchaseOrderRepo.manager.transaction(async (trans) => {
      const { ids } = data
      const pos = await this.purchaseOrderRepo.find({ where: { id: In(ids) } })
      if (!pos) {
        throw new Error('PO not found')
      }
      const posItems = await this.purchaseOrderItemRepo.find({ where: { purchaseOrderId: In(ids) } })
      const mergedItem = posItems.reduce((acc, item) => {
        const existing = acc.find((val) => val.itemId == item.itemId)
        if (!existing) {
          acc.push({ ...item, itemCode: item.itemCode, itemName: item.itemName, itemUnit: item.purchaseUnit })
        } else {
          existing.quantityBasicUnit = Number(existing.quantityBasicUnit) + Number(item.quantityBasicUnit)
          existing.totalAmount = Number(existing.totalAmount) + Number(item.totalAmount)
          existing.totalAmountVat = Number(existing.totalAmountVat) + Number(item.totalAmountVat)
        }
        return acc
      }, [])

      return {
        items: mergedItem,
        list: pos,
      }
    })
  }
  //#endregion
}
