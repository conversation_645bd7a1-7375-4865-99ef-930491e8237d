import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { JwtAuthGuard } from '../../common/guards'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { CurrentUser } from '../../common/decorators'
import { PackingService } from './packing.service'
import { PackingCreateDto, PackingUpdateDto } from './dto'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'

@ApiBearerAuth()
@ApiTags('Packing')
@UseGuards(JwtAuthGuard)
@Controller('packing')
export class PackingController {
  constructor(private readonly service: PackingService) {}

  @Post('find')
  public async find(@Body() data: any) {
    return await this.service.find(data)
  }

  @Post('pagination')
  public async pagination(@Body() data: PaginationDto) {
    return await this.service.pagination(data)
  }

  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: PackingCreateDto) {
    return await this.service.createData(user, data)
  }

  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: PackingUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @Post('update_active')
  public async updateActive(@Body() data: any, @CurrentUser() user: UserDto) {
    return await this.service.updateIsDelete(data.id, user)
  }

  @Post('find_one')
  public async findOne(@Body() data: FilterOneDto) {
    return await this.service.findOne(data)
  }

  @Post('create_data_excel')
  public async createDataExcel(@Body() data: PackingCreateDto[], @CurrentUser() user: UserDto) {
    return await this.service.createDataExcel(data, user)
  }
}
