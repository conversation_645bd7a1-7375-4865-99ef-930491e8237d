import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsEnum, IsNotEmpty, IsOptional } from 'class-validator'
import { PaginationDto } from './pagination.dto'
import { NSItem } from '../constants/NSItem'

/** Interface Id */
export class FilterIdDto {
  @ApiProperty({ description: 'Id của đối tượng', example: 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx' })
  id?: string

  @ApiProperty({ description: 'List Id của đối tương', example: 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx' })
  lstId?: string[]

  @ApiProperty({ description: 'Id của đối tượng', example: 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx' })
  employeeId?: string

  lstParentId?: any

  isRelation?: any

  lstCode?: string[]

  @ApiProperty({ description: 'name c<PERSON><PERSON> đố<PERSON> t<PERSON>', example: 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx' })
  name?: string

  @ApiProperty({ description: 'code của đối tượng', example: 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx' })
  code?: string

  @ApiProperty({ description: 'Trạng thái của đối tượng', example: 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx' })
  status?: string

  @ApiProperty({ description: 'List trạng thái của đối tương', example: 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx' })
  lstStatus?: string[]

  lstProductId?: string[]

  productId?: string

  itemId?: string

  isDaLenDon?: boolean

  producDetailId?: string

  poId?: string

  warehouseId?: string

  expiryDate?: string

  isCombo?: string

  isDeleted?: boolean
}

export class SearchDto extends PaginationDto {
  @ApiProperty({ description: 'Giá trị tìm kiếm', example: 'Tên sản phẩm, nhóm sản phẩm,v.v...' })
  @IsNotEmpty()
  searchValue: string
}

export class ListProduct extends PaginationDto {
  @ApiProperty({ description: 'Giá trị tìm kiếm', example: 'Tên sản phẩm, nhóm sản phẩm,v.v...' })
  @IsOptional()
  searchValue?: string

  @ApiPropertyOptional({ description: 'Id của sản phẩm để lấy món liên quan' })
  @IsOptional()
  productId?: string

  @ApiPropertyOptional({ description: 'Là combo' })
  isCombo?: boolean

  @ApiPropertyOptional({ description: 'Id của nhãn hàng' })
  brandId?: string

  @ApiPropertyOptional({ description: 'Loại sản phẩm (cấp 1)' })
  typeId?: string

  @ApiPropertyOptional({ description: 'Danh mục sản phẩm (cấp 2)' })
  categoryId?: string

  @ApiPropertyOptional({ description: 'Nhóm sản phẩm (cấp 3)' })
  groupId?: string

  @ApiPropertyOptional({ description: 'Sắp xếp giá tang dần hoặc giảm dần', example: 'ASC hoặc DESC' })
  orderPrice?: string

  @ApiPropertyOptional({ description: 'Tên sản phẩm' })
  name?: string

  @ApiPropertyOptional({ description: 'Sản phẩm yêu thích' })
  @IsOptional()
  isFavoriteCombo?: boolean

  @ApiPropertyOptional({ description: 'Sản phẩm yêu thích' })
  @IsOptional()
  isPeriodSale?: boolean

  @ApiPropertyOptional({ description: 'Kênh bán hàng' })
  @IsOptional()
  channel?: NSItem.EOrderPlatformType

  //orderplatform
  @ApiPropertyOptional({ description: 'Các nền tảng được phép đặt hàng' })
  @IsOptional()
  orderPlatformType?: string = NSItem.EOrderPlatformType.MEDIAONE
}

export class ListProductByChannel extends PaginationDto {
  @ApiProperty({ description: 'Giá trị tìm kiếm', example: 'Tên sản phẩm, nhóm sản phẩm,v.v...' })
  @IsOptional()
  searchValue?: string

  @ApiPropertyOptional({ description: 'Id của sản phẩm để lấy món liên quan' })
  @IsOptional()
  productId?: string

  @ApiPropertyOptional({ description: 'Là combo' })
  isCombo?: boolean

  @ApiPropertyOptional({ description: 'Id của nhãn hàng' })
  brandId?: string

  @ApiPropertyOptional({ description: 'Loại sản phẩm (cấp 1)' })
  typeId?: string

  @ApiPropertyOptional({ description: 'Danh mục sản phẩm (cấp 2)' })
  categoryId?: string

  @ApiPropertyOptional({ description: 'Nhóm sản phẩm (cấp 3)' })
  groupId?: string

  @ApiPropertyOptional({ description: 'Sắp xếp giá tang dần hoặc giảm dần', example: 'ASC hoặc DESC' })
  orderPrice?: string

  @ApiPropertyOptional({ description: 'Tên sản phẩm' })
  name?: string

  @ApiPropertyOptional({ description: 'Sản phẩm yêu thích' })
  @IsOptional()
  isFavoriteCombo?: boolean

  @ApiPropertyOptional({ description: 'Sản phẩm yêu thích' })
  @IsOptional()
  isPeriodSale?: boolean

  @ApiPropertyOptional({ description: 'Kênh bán hàng' })
  @IsOptional()
  @IsEnum(NSItem.EOrderPlatformType)
  channel: NSItem.EOrderPlatformType

  //orderplatform
  @ApiPropertyOptional({ description: 'Các nền tảng được phép đặt hàng' })
  @IsOptional()
  orderPlatformType?: string = NSItem.EOrderPlatformType.MEDIAONE
}

export class FindItemsByIdsDto {
  @ApiProperty({ description: 'Giá trị tìm kiếm', example: 'Tên sản phẩm, nhóm sản phẩm,v.v...' })
  @IsNotEmpty()
  ids: string[]

  @IsOptional()
  isFavoriteCombo?: boolean = false

  @IsOptional()
  productName?: string

  @IsOptional()
  isDeleted?: boolean

  @ApiPropertyOptional({ description: 'Kênh bán hàng' })
  @IsOptional()
  channel?: NSItem.EOrderPlatformType
}
