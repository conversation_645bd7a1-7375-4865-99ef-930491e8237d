import { Controller, UseGuards, Post, Body, Req } from '@nestjs/common'
import { Request as IRequest } from 'express'
import { WarehouseTransferService } from './warehouseTransfer.service'
import { WarehouseTransferCreateByExcelDto, WarehouseTransferCreateDto, WarehouseTransferUpdateDto } from './dto'
import { CurrentUser } from '../../common/decorators'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { JwtAuthGuard } from '../../common/guards'

@ApiBearerAuth()
@ApiTags('WarehouseTransfer')
@UseGuards(JwtAuthGuard)
@Controller('warehouse_transfer')
export class WarehouseTransferController {
  constructor(private readonly service: WarehouseTransferService) { }

  @Post('find')
  async find(@Body() data: any) {
    return await this.service.find(data)
  }

  @Post('find_detail')
  async findDetail(@Body() data: FilterOneDto, @Req() req: IRequest) {
    return await this.service.findDetail(data, req)
  }

  @Post('pagination')
  async pagination(@Body() data: PaginationDto, @Req() req: IRequest, @CurrentUser() user: UserDto) {
    return await this.service.pagination(data, req, user)
  }

  @Post('get_auto_gen_code')
  async getCodeAutoGen() {
    return await this.service.getCodeAutoGen()
  }

  @Post('create_data')
  async createData(@Req() req: IRequest, @Body() data: WarehouseTransferCreateDto) {
    return await this.service.createData(data, req)
  }

  @Post('update_approve')
  async updateApprove(@Body() data: FilterOneDto, @Req() req: IRequest) {
    return await this.service.updateApprove(data, req)
  }

  @Post('update_cancel')
  async updateCancel(@Body() data: FilterOneDto, @Req() req: IRequest) {
    return await this.service.updateCancel(data, req)
  }

  @Post('send_shipper')
  async sendShipper(@Req() req: IRequest, @Body() data: FilterOneDto) {
    return await this.service.sendShipper(data, req)
  }

  @Post('shipper_confirm')
  async shipperConfirm(@Req() req: IRequest, @Body() data: FilterOneDto) {
    return await this.service.shipperConfirm(data, req)
  }

  @Post('shipper_delivering')
  async shipperDelivering(@Req() req: IRequest, @Body() data: FilterOneDto) {
    return await this.service.shipperDelivering(data, req)
  }

  @Post('shipper_finish')
  async shipperFinish(@Req() req: IRequest, @Body() data: FilterOneDto) {
    return await this.service.shipperFinish(data, req)
  }

  @Post('shipper_comfirm_transfer')
  async shipperComfirmTransfer(@Body() data: FilterOneDto, @Req() req: IRequest) {
    return await this.service.shipperComfirmTransfer(data, req)
  }

  @Post('update_data')
  async updateData(@Req() req: IRequest, @Body() data: WarehouseTransferUpdateDto) {
    return await this.service.updateData(data, req)
  }

  @Post('create_data_by_excel')
  async createDataByExcel(@Body() data: WarehouseTransferCreateByExcelDto[], @Body('createBy') createBy: any, @Req() req: IRequest) {
    return await this.service.createDataByExcel(data, createBy, req)
  }
}
