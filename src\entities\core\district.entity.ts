import { Entity, Column, OneToMany, ManyToOne, JoinColumn } from 'typeorm'
import { BaseEntity } from './base.entity'
import { CityEntity } from './city.entity'
import { WardEntity } from './ward.entity'
import { SupplierEntity } from './supplier.entity'

@Entity('district')
export class DistrictEntity extends BaseEntity {
  @Column({ type: 'varchar', length: 50, nullable: false })
  code: string

  @Column({ type: 'varchar', length: 250, nullable: false })
  name: string

  @Column({ type: 'varchar', nullable: false })
  cityId: string
  @ManyToOne(() => CityEntity, (p) => p.districts)
  @JoinColumn({ name: 'cityId', referencedColumnName: 'id' })
  city: Promise<CityEntity>

  @OneToMany((type) => WardEntity, (p) => p.district)
  wards: Promise<WardEntity[]>

  @OneToMany((type) => SupplierEntity, (p) => p.district)
  suppliers: Promise<SupplierEntity[]>
}
