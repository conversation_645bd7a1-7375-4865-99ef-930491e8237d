import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional, IsUUID } from "class-validator";
import { PaginationDto } from "../../../../dto";

export class CreateOrderConfigDto {
  @ApiProperty({ description: "Của NCC nào" })
  @IsNotEmpty()
  supplierId: string;

  @ApiProperty({ description: "Thời gian duyệt đơn", example: "1" })
  @IsNotEmpty()
  approvalTime: string;

  @ApiProperty({ description: "Số ngày NCC phải giao tới 3PL ", example: "1" })
  @IsNotEmpty()
  supplierDeliveryTime: string;

  @ApiProperty({ description: "Số ngày 3PL phải giao hàng tới MBC", example: "1" })
  @IsNotEmpty()
  thirdPartyDeliveryTime: string;

  @ApiProperty({ description: "Thời gian nhận hàng mong muốn tối thiểu", example: "7" })
  @IsNotEmpty()
  minReceivingTime: string;

  @ApiProperty({ description: "Thời gian nhận hàng mong muốn tối đa", example: "90" })
  @IsNotEmpty()
  maxReceivingTime: string;
}

export class UpdateOrderConfigDto extends CreateOrderConfigDto {
  @ApiProperty({ description: "ID của cấu hình" })
  @IsUUID()
  @IsNotEmpty()
  id: string
}

export class ListOrderConfigDto extends PaginationDto {
  @ApiPropertyOptional({ description: "ID NCC" })
  @IsOptional()
  supplierId?: string;
}