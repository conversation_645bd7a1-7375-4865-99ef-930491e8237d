import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsInt, IsDate, IsNotEmpty } from 'class-validator';

export class CreatePoDeliveryScheduleDto {
  @ApiProperty({ example: 'DS121225000001', description: 'Mã lịch giao hàng' })
  @IsString()
  @IsNotEmpty()
  code: string;

  @ApiProperty({ example: '12/11/2024', description: 'Ngày giao hàng' })
  @IsDate()
  @IsNotEmpty()
  deliveryDate: Date;

  @ApiProperty({ example: 'mbc_001', description: 'ID của MBC' })
  @IsString()
  @IsNotEmpty()
  mbcId: string;

  @ApiProperty({ example: 'htx_01', description: 'ID của 3PL (Nhà vận chuyển)' })
  @IsString()
  @IsNotEmpty()
  thirdPartyLogisticsId: string;

  @ApiProperty({ example: 'product_123', description: 'ID của sản phẩm' })
  @IsString()
  @IsNotEmpty()
  productId: string;

  @ApiProperty({ example: 'Cái', description: 'Đơn vị tính' })
  @IsString()
  @IsNotEmpty()
  unit: string;

  @ApiProperty({ example: 500, description: 'Nhu cầu đơn hàng' })
  @IsInt()
  @IsNotEmpty()
  orderDemand: number;

  @ApiProperty({ example: 'Ngày cụ thể', description: 'Loại thời gian cung cấp' })
  @IsString()
  @IsNotEmpty()
  supplyTimeType: string;

  @ApiProperty({ example: '12/12/2024', description: 'Thời gian cung cấp' })
  @IsDate()
  @IsNotEmpty()
  supplyTime: Date;
}
