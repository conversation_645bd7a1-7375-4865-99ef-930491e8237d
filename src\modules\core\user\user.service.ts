import { Injectable } from '@nestjs/common'
import { In, Like } from 'typeorm'
import { UserFilterDto } from './dto'
import { UserRepository } from '../../../repositories'
import { UserDto } from '../../../dto'
import { enumData, UPDATE_SUCCESS } from '../../../constants'

@Injectable()
export class UserService {
  constructor(private readonly repo: UserRepository) {}

  /** Lấy ds user theo đi<PERSON>u kiện */
  public async find(user: UserDto, data: UserFilterDto) {
    const whereCon: any = { companyId: user.companyId }
    if (data.isDeleted) whereCon.isDeleted = data.isDeleted
    if (data.lstRoleId?.length > 0) whereCon.roleId = In(data.lstRoleId)
    if (data.lstId?.length > 0) whereCon.id = In(data.lstId)
    if (data.type) whereCon.type = data.type
    const res = await this.repo.find({
      where: whereCon,
      select: { id: true, username: true, type: true, email: true },
    })

    return res
  }

  public async findSupplierAccount(user: UserDto, data: UserFilterDto) {
    const whereCon: any = {}
    //username
    if (data.username) whereCon.username = Like(`%${data.username}%`)
    //supplierId
    if (data.supplierId) whereCon.supplierId = data.supplierId
    if (data.isDeleted != undefined) whereCon.isDeleted = data.isDeleted
    if (data.lstRoleId?.length > 0) whereCon.roleId = In(data.lstRoleId)
    if (data.lstId?.length > 0) whereCon.id = In(data.lstId)
    if (data.type) whereCon.type = data.type
    const res = await this.repo.findAndCount({
      where: { ...whereCon, type: enumData.UserType.Supplier.code },
      skip: data.pageSize * (data.pageIndex - 1),
      take: data.pageSize,
      select: {
        id: true,
        username: true,
        fullName: true,
        createdAt: true,
        type: true,
        email: true,
        phone: true,
        isDeleted: true,
        supplierId: true,
        address: true,
        supplier: { name: true },
      },
      relations: { supplier: true },
      order: { createdAt: 'DESC' },
    })

    return { data: res[0], total: res[1] }
  }

  public async setActive(params: any) {
    const { userId, isDeleted } = params
    await this.repo.update({ id: userId }, { isDeleted })
    return { message: UPDATE_SUCCESS }
  }

  /** Tìm tên thành phố theo mã hoặc Id */
  public async findOne(user: UserDto, data: { id?: string; code?: string }) {
    const whereCon: any = { isDeleted: false }
    if (data.code) whereCon.code = Like(`%${data.code}%`)
    if (data.id) whereCon.id = data.id
    return await this.repo.findOne({ where: whereCon, order: { createdAt: 'DESC' } })
  }
}
