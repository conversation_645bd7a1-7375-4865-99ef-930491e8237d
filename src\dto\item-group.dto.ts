import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { NSItemGroup } from '../constants/NSItemGroup'
import { PageRequest } from './pagination.dto'
import { IsOptional, IsUUID } from 'class-validator'

export class ListItemGroupReq extends PageRequest {
  @ApiPropertyOptional({
    enum: NSItemGroup.EGroupType,
    default: NSItemGroup.EGroupType.PRODUCT,
  })
  groupType?: NSItemGroup.EGroupType
}

export class ListComboReq extends PageRequest {
  @ApiPropertyOptional()
  @IsOptional()
  @IsUUID('4')
  itemGroupId: string
}
