import { MigrationInterface, QueryRunner } from 'typeorm'

export class initBusinessOrder1758254578336 implements MigrationInterface {
  name = 'initBusinessOrder1758254578336'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "business_order" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "createdBy" character varying(36), "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT now(), "updatedBy" character varying(36), "isDeleted" boolean NOT NULL DEFAULT false, "businessPurchaseOrderId" uuid NOT NULL, "totalPrice" numeric NOT NULL, "approveStatus" character varying NOT NULL, "deliveryStatus" character varying NOT NULL, "warehouseId" uuid NOT NULL, CONSTRAINT "PK_a89e1933cca606bed0918278789" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `CREATE TABLE "business_order_item" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "createdBy" character varying(36), "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT now(), "updatedBy" character varying(36), "isDeleted" boolean NOT NULL DEFAULT false, "businessOrderId" uuid NOT NULL, "totalPrice" numeric NOT NULL, "totalPriceVat" numeric NOT NULL, "vat" numeric NOT NULL, "itemId" uuid NOT NULL, "unit" character varying NOT NULL, "quantity" numeric NOT NULL, CONSTRAINT "PK_934949e3c851e841c2c8da0d42c" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `ALTER TABLE "business_order" ADD CONSTRAINT "FK_1ca0ffbeb67fe6d79c10b4007ed" FOREIGN KEY ("warehouseId") REFERENCES "warehouse"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "business_order_item" ADD CONSTRAINT "FK_2f9adf91b71ed62e7c7d65e0856" FOREIGN KEY ("businessOrderId") REFERENCES "business_order"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "business_order_item" DROP CONSTRAINT "FK_2f9adf91b71ed62e7c7d65e0856"`)
    await queryRunner.query(`ALTER TABLE "business_order" DROP CONSTRAINT "FK_1ca0ffbeb67fe6d79c10b4007ed"`)
    await queryRunner.query(`DROP TABLE "business_order_item"`)
    await queryRunner.query(`DROP TABLE "business_order"`)
  }
}
