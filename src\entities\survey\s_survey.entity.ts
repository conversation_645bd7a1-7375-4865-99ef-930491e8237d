import { Column, Entity, OneToMany } from 'typeorm'
import { BaseEntity } from '../core/base.entity'
import { SurveyMemberEntity } from './s_surveyMember.entity'
import { SurveyQuestionEntity } from './s_surveyQuestion.entity'
import { NotifyEntity } from './s_notify.entity'
import { SurveyHistoryEntity } from './s_surveyHistory.entity'
import { SurveyQuestionListDetailEntity } from './s_surveyQuestionListDetail.entity'

/** Phiếu khảo sát */
@Entity('s_survey')
export class SurveyEntity extends BaseEntity {
  /** Tên phiếu khảo sát */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  name: string

  /** Mã phiếu khảo sát */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  /** <PERSON><PERSON> tả */
  @Column({
    type: 'text',
    nullable: true,
  })
  description: string

  /** Tổng điểm */
  @Column({
    type: 'float',
    nullable: true,
    default: 0,
  })
  totalPoint: number

  /** Thời gian bắt đầu */
  @Column({
    type: 'timestamptz',
    nullable: true,
  })
  timeStart: Date

  /** Thời gian kết thúc */
  @Column({
    type: 'timestamptz',
    nullable: true,
  })
  timeEnd: Date

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  status: string

  /** Danh sách câu trả lời */
  @OneToMany(() => SurveyQuestionEntity, (p) => p.survey)
  questions: Promise<SurveyQuestionEntity[]>

  /** Danh sách câu hỏi */
  @OneToMany(() => SurveyQuestionListDetailEntity, (p) => p.survey)
  questionInfos: Promise<SurveyQuestionListDetailEntity[]>

  /** Danh sách nhân viên */
  @OneToMany(() => SurveyMemberEntity, (p) => p.survey)
  members: Promise<SurveyMemberEntity[]>

  /** Danh sách câu hỏi */
  @OneToMany(() => NotifyEntity, (p) => p.survey)
  notifies: Promise<NotifyEntity[]>

  /** Lịch sử */
  @OneToMany(() => SurveyHistoryEntity, (p) => p.survey)
  histories: Promise<SurveyHistoryEntity[]>
}
