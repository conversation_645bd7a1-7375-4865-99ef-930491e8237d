import { MigrationInterface, QueryRunner } from "typeorm";

export class updateEntityDeliveryLastmileSo1744260807592 implements MigrationInterface {
    name = 'updateEntityDeliveryLastmileSo1744260807592'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "delivery_note_child_detail" ADD "soId" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "delivery_note_child_detail" DROP COLUMN "soId"`);
    }

}
