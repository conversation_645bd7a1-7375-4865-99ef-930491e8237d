import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity, DistrictEntity, RegionEntity } from '../core'
import { LuckyBillConfigEntity } from './lucky-bill-config.entity'

@Entity('lucky_bill_apply_region')
export class LuckyBillApplyRegionEntity extends BaseEntity {
  @Column({ type: 'uuid', nullable: false })
  luckyBillConfigId: string

  @Column({ type: 'uuid', nullable: false, array: true })
  cityIds: string[]

  //limit
  @Column({ type: 'int', nullable: true, default: 0 })
  limit: number

  @ManyToOne(() => LuckyBillConfigEntity, (p) => p.applyRegions, { onDelete: 'CASCADE' })
  luckyBillConfig: Promise<LuckyBillConfigEntity>
}
