import { Controller, UseGuards, Request, Post, Body, Param, Get } from '@nestjs/common'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { Request as IRequest } from 'express'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { JwtAuthGuard, CurrentUser } from '../../common'
import { CityCreateDto, CityUpdateDto } from './dto'
import { CityService } from './city.service'
import { ApeAuthGuard } from '../../survey/common/guards'

@ApiBearerAuth()
@ApiTags('Geo City')
@UseGuards(ApeAuthGuard)
@Controller('city_public')
export class CityPublicController {
  constructor(private readonly service: CityService) {}

  @Post('find')
  async find(@Body() data: any) {
    return await this.service.find(data)
  }

  @Get('load_city_by_regionId/:regionId')
  async loadCitiByRegionId(@Param('regionId') regionId: string) {
    return await this.service.loadCitiesByRegionId(regionId)
  }
}
