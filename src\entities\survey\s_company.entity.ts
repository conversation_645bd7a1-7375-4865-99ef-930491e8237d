import { Entity, Column, OneToMany } from 'typeorm'
// import { UserEntity } from './user.entity'
import { ApiProperty } from '@nestjs/swagger'
import { CompanyCategoriesEntity } from './s_companyCategories.entity'
import { BaseEntity } from '../core/base.entity'

/** <PERSON>u<PERSON>n lý các công ty là khách hàng của APE */
@Entity('s_company')
export class CompanyEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  code: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  /** Mã số thuế */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  taxCode: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  phone: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  email: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  bankName: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  bankBranch: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  bankAccount: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  contactName: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  contactNumberPhone: string

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  address: string

  @ApiProperty({ description: 'Tên miền trang admin Survey của đối tác' })
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  domain: string

  // @ApiProperty({ description: 'Danh sách các user của Đối tác' })
  // @OneToMany(() => UserEntity, (p) => p.company)
  // users: Promise<UserEntity[]>

  @ApiProperty({ description: 'Số lượng user giới hạn' })
  @Column({ default: 0, nullable: true })
  numUserLimit: number

  @ApiProperty({ description: 'Số lượng user hiện tại' })
  @Column({ default: 0, nullable: true })
  numUser: number

  /** Loại category */
  @OneToMany(() => CompanyCategoriesEntity, (p) => p.company)
  companyCategories: Promise<CompanyCategoriesEntity[]>
}
