import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import { PackingRepository } from '../../../repositories'
import { PackingController } from './packing.controller'
import { PackingService } from './packing.service'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([PackingRepository])],
  controllers: [PackingController],
  providers: [PackingService],
  exports: [PackingService],
})
export class PackingModule {}
