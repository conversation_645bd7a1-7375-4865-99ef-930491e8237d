import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { PaginationDto, UserDto } from '../../../dto';
import { DeliveryScheduleEntity } from '../../../entities';
import { CreatePoDeliveryScheduleDto, UpdatePoDeliveryScheduleDto } from './dto';
import { DeliveryScheduleRepository } from '../../../repositories';

@Injectable()
export class DeliveryScheduleService {
  constructor(
    @InjectRepository(DeliveryScheduleRepository)
    private readonly deliveryScheduleRepository: DeliveryScheduleRepository,
  ) { }

  async pagination(req: any, data: PaginationDto) {
    return await this.deliveryScheduleRepository.findAndCount({
      where: {},
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
    });
  }

  async createData(data: CreatePoDeliveryScheduleDto, req: any, user: UserDto) {
    const { mbcId, thirdPartyLogisticsId, productId, unit, orderDemand, supplyTimeType, supplyTime } = data;

    const today = new Date();
    const datePart = today.toISOString().slice(2, 10).replace(/-/g, "");
    const lastDelivery = await this.deliveryScheduleRepository
      .createQueryBuilder("ds")
      .orderBy("ds.code", "DESC")
      .getOne();

    let nextNumber = lastDelivery ? parseInt(lastDelivery.code.slice(-6), 10) + 1 : 1;
    const newCode = `DS${datePart}${String(nextNumber).padStart(6, "0")}`;

    const deliverySchedule = this.deliveryScheduleRepository.create({
      code: newCode,
      deliveryDate: today,
      mbcId,
      thirdPartyLogisticsId,
      productId,
      unit,
      orderDemand,
      supplyTimeType,
      supplyTime,
      createdAt: new Date(),
    });

    return await this.deliveryScheduleRepository.save(deliverySchedule);
  }

  async updateData(data: UpdatePoDeliveryScheduleDto, req: any, user: UserDto) {
    const deliverySchedule = await this.deliveryScheduleRepository.findOneBy({ id: data.id });
    if (!deliverySchedule) throw new Error('Không tìm thấy lịch giao hàng');

    Object.assign(deliverySchedule, data);
    await this.deliveryScheduleRepository.save(deliverySchedule);

    return deliverySchedule;
  }

  async findDetail(req: any, id: string) {
    return await this.deliveryScheduleRepository.findOneBy({ id });
  }

  async deleteData(id: string) {
    const deliverySchedule = await this.deliveryScheduleRepository.findOneBy({ id });
    if (!deliverySchedule) throw new Error('Không tìm thấy lịch giao hàng');

    return await this.deliveryScheduleRepository.remove(deliverySchedule);
  }

  async exportExcel(req: any, data: PaginationDto, user: UserDto) {
    return { message: 'Xuất file Excel thành công', data, user };
  }

  async print(data: string[], req: any) {
    return { message: 'In lịch giao hàng thành công', data };
  }
}
