import { ApiProperty } from '@nestjs/swagger'
import { Transform } from 'class-transformer'
import { IsNotEmpty, IsNumber, IsOptional, IsUUID, Min } from 'class-validator'

export class CreatePricingDto {
  @ApiProperty({ description: '<PERSON>i<PERSON>' })
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @Transform(({ value }) => Number(value))
  price: number

  @ApiProperty({ description: 'Id sản phẩm' })
  @IsNotEmpty()
  @IsUUID()
  itemId: string

  @ApiProperty({ description: 'Id nhà cung cấp' })
  @IsNotEmpty()
  @IsUUID()
  supplierId: string

  @ApiProperty({ description: 'Mô tả' })
  @IsOptional()
  description: string
}
