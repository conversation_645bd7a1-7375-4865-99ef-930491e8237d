import { BaseEntity } from '../core/base.entity'
import { Entity, Column, ManyToOne, JoinColumn, BeforeUpdate, BeforeInsert } from 'typeorm'
import { WarehouseProductEntity } from './warehouseProduct.entity'

/** Chi tiết sản phẩm trong kho vật lý */
@Entity('warehouse_product_detail')
export class WarehouseProductDetailEntity extends BaseEntity {
  /** Id sản phẩm trong kho vật lý */
  @Column({ type: 'varchar', nullable: true })
  warehouseProductId: string
  @ManyToOne(() => WarehouseProductEntity, (p) => p.details)
  @JoinColumn({ name: 'warehouseProductId', referencedColumnName: 'id' })
  warehouseProduct: Promise<WarehouseProductEntity>

  /** Id kho vật lý */
  @Column({ type: 'varchar', length: 36, nullable: true })
  warehouseId: string

  /** Id <PERSON>ản phẩm */
  @Column({ type: 'varchar', length: 36, nullable: true })
  productId: string

  /** Mã sp */
  @Column({ type: 'varchar', length: 50, nullable: true })
  productCode: string

  /** Tên sp */
  @Column({ type: 'varchar', length: 250, nullable: true })
  productName: string

  /** Id detail Sản phẩm */
  @Column({ type: 'varchar', length: 36, nullable: true })
  productDetailId: string

  /** Ngày sản xuất */
  @Column({ type: 'timestamptz', nullable: true })
  manufactureDate: Date

  /** Hạn sử dụng */
  @Column({ type: 'timestamptz', nullable: true })
  expiryDate: Date

  /** Số lượng tồn = quantityBegin + quantityImport - quantityExport */
  @Column({ nullable: true, default: 0 })
  quantity: number

  /** Số lượng nhập từ inbound */
  @Column({ nullable: true, default: 0 })
  quantityImport: number

  /** Số lượng xuất từ outbound */
  @Column({ nullable: true, default: 0 })
  quantityExport: number

  /** Số lượng tồn ban đầu */
  @Column({ nullable: true, default: 0 })
  quantityBegin: number

  /* stock */
  @Column({ nullable: true, default: 0 })
  stock: number

  /** Số lượng lock lên đơn */
  @Column({ nullable: true, default: 0 })
  quantityLock: number

  /** Số lô */
  @Column({ type: 'varchar', length: 100, nullable: true })
  lotNumber: string

  /** Cập nhật sl tồn trước khi thêm hoặc cập nhật */
  // @BeforeUpdate()
  // @BeforeInsert()
  // updateDates() {
  // this.quantity = (+this.quantityBegin || 0) + (+this.quantityImport || 0) - (+this.quantityExport || 0)
  // this.quantity = (+this.quantityBegin || 0) + (+this.quantity || 0)
  // }
}
