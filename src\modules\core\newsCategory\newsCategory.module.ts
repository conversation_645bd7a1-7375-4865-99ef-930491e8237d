import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import { NewsCategoryRepository } from '../../../repositories'
import { NewsCategoryController } from './newsCategory.controller'
import { NewsCategoryService } from './newsCategory.service'
import { NewsCategoryPublicController } from './newsCategoryPublic.controller'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([NewsCategoryRepository])],
  controllers: [NewsCategoryController, NewsCategoryPublicController],
  providers: [NewsCategoryService],
  exports: [NewsCategoryService],
})
export class NewsCategoryModule {}
