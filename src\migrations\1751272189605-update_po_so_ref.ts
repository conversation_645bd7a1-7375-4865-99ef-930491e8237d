import { MigrationInterface, QueryRunner } from "typeorm";

export class updatePoSoRef1751272189605 implements MigrationInterface {
    name = 'updatePoSoRef1751272189605'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "purchase_order_so" DROP COLUMN "productInCombo"`);
        await queryRunner.query(`ALTER TABLE "purchase_order_so" DROP COLUMN "isCombo"`);
        await queryRunner.query(`ALTER TABLE "purchase_order_so" DROP COLUMN "productId"`);
        await queryRunner.query(`ALTER TABLE "purchase_order_so" DROP COLUMN "quantity"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "purchase_order_so" ADD "quantity" integer`);
        await queryRunner.query(`ALTER TABLE "purchase_order_so" ADD "productId" uuid`);
        await queryRunner.query(`ALTER TABLE "purchase_order_so" ADD "isCombo" boolean`);
        await queryRunner.query(`ALTER TABLE "purchase_order_so" ADD "productInCombo" uuid`);
    }

}
