import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString } from 'class-validator'
export class SettingStringCreateDto {
  @ApiProperty({ description: 'Mã setting string' })
  @IsNotEmpty()
  code: string

  @ApiProperty({ description: 'Tên setting string' })
  @IsNotEmpty()
  @IsString()
  name: string

  @ApiProperty({ description: 'Giá trị cấu hình động' })
  value: number

  @ApiProperty({ description: 'Giá trị cấu hình động' })
  valueString: string

  @ApiProperty({ description: 'Banner' })
  banner: string[]

  @ApiProperty({ description: 'Loại dữ liệu' })
  @IsOptional()
  @IsString()
  type: string
}
