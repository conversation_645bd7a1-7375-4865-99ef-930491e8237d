import { BaseEntity } from '../core/base.entity'
import { Entity, Column, OneToMany } from 'typeorm'
import { CheckInventoryEntity, InboundEntity, OutboundEntity, WarehouseProductEntity, WarehouseTransferEntity } from '.'
import { NSWarehouse, transformer } from '../../constants'
import { WarehouseProductSafetyEntity } from './warehouseProductSafety.entity'

/** Kho vật lý */
@Entity('warehouse')
export class WarehouseEntity extends BaseEntity {
  @Column({ type: 'varchar', length: 50, nullable: true })
  code: string

  /** Tên ch<PERSON>h thức */
  @Column({ type: 'varchar', length: 250, nullable: true })
  name: string

  /** Phân loại kho 3PL/MBC */
  @Column({
    type: 'enum',
    enum: NSWarehouse.EWarehouseType,
    default: NSWarehouse.EWarehouseType.MBC,
    nullable: true
  })
  type: NSWarehouse.EWarehouseType

  @Column({ type: 'varchar', length: 50, nullable: true })
  phone: string

  @Column({ type: 'varchar', length: 250, nullable: true })
  status: string

  /** Địa chỉ kho */
  @Column({ type: 'varchar', length: 250, nullable: true })
  address: string

  @Column({ type: 'text', nullable: true })
  description: string

  @Column({ type: 'varchar', length: 36, nullable: true })
  wardId: string

  @Column({ type: 'varchar', length: 36, nullable: true })
  districtId: string

  @Column({ type: 'varchar', length: 36, nullable: true })
  cityId: string

  /* Id store */
  @Column({ type: 'varchar', length: 36, nullable: true })
  storeId: string

  /** Là kho mặc định ? */
  @Column({ default: false, nullable: true })
  isDefault: boolean

  /** Thể tích (m3 - Sức chứa kho) */
  @Column({ nullable: true, type: 'decimal', precision: 12, scale: 3, default: 0, transformer })
  cbm: number

  /** Dài */
  @Column({ nullable: true, type: 'decimal', precision: 12, scale: 3, default: 0, transformer })
  length: number

  /** Rộng */
  @Column({ nullable: true, type: 'decimal', precision: 12, scale: 3, default: 0, transformer })
  width: number

  /** Cao */
  @Column({ nullable: true, type: 'decimal', precision: 12, scale: 3, default: 0, transformer })
  height: number

  /** Các PNK của kho vật lý */
  @OneToMany(() => InboundEntity, (p) => p.warehouse)
  inbounds: Promise<InboundEntity[]>

  /** Định mức tồn kho an toàn */
  @OneToMany(() => WarehouseProductSafetyEntity, (p) => p.warehouse)
  productSafeties: Promise<WarehouseProductSafetyEntity[]>

  /** Các sản phẩm trong kho vật lý */
  @OneToMany(() => WarehouseProductEntity, (p) => p.warehouse)
  products: Promise<WarehouseProductEntity[]>

  /** Các phiếu xuất kho của kho này */
  @OneToMany(() => OutboundEntity, (p) => p.warehouse)
  outbounds: Promise<OutboundEntity[]>

  /** Phiếu chuyển kho từ kho này */
  @OneToMany(() => WarehouseTransferEntity, (p) => p.fromWarehouse)
  fromWarehouseTransfers: Promise<WarehouseTransferEntity[]>

  /** Phiếu chuyển kho đến kho này */
  @OneToMany(() => WarehouseTransferEntity, (p) => p.toWarehouse)
  toWarehouseTransfers: Promise<WarehouseTransferEntity[]>

  /** Phiếu kiểm kho của kho này */
  @OneToMany(() => CheckInventoryEntity, (p) => p.warehouse)
  checkInventories: Promise<CheckInventoryEntity[]>

  /** Phiếu điều chỉnh của kho này */
  // @OneToMany(() => CombineLotEntity, (p) => p.warehouse)
  // combineLots: Promise<CombineLotEntity[]>
}
