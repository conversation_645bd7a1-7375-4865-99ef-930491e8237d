import { Controller, UseGuards, Body, Req, Get, Post, Param } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { AuthGuard } from '../../common/guards/auth.guard'
import { PublicSurveyService } from './publicSurvey.service'
import { CurrentUser } from '../../common'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { SurveyUpdateDto } from '../survey/dto/surveyUpdate.dto'
import { SurveyCreateDto } from '../survey/dto/surveyCreate.dto'
import { Request as IRequest } from 'express'
import { TopicCreateDto, TopicCreateExcelDto, TopicUpdateDto } from '../topic/dto'
import {
  QuestionCreateDto,
  QuestionCreateMasterDataDto,
  QuestionListDetailCreateDto,
  QuestionListDetailUpdateDto,
  QuestionUpdateDto,
} from '../question/dto'
import { SurveyAnswerQuestionDto, SurveyFilterOneDto  } from '../mobileApp/dto'
import { ApeAuthGuard } from '../common/guards'


@ApiBearerAuth()
@ApiTags('Public API')
@UseGuards(ApeAuthGuard)
@Controller('public-survey')
export class PubliSurveyController {
  constructor(private readonly service: PublicSurveyService) {}

  @ApiOperation({ summary: 'chi tiết' })
  @Post('survey/find_detail')
  public async surveyFindDetail(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.surveyFindDetail(user, data)
  }

  @ApiOperation({ summary: 'Danh sách phân trang phiếu khảo sát' })
  @Post('survey/pagination')
  public async surveyPagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.surveyPagination(user, data)
  }

  @ApiOperation({ summary: 'Load all phiếu khảo sát' })
  @Post('survey/find_all')
  public async surveyFindAll(@Body() data: any) {
    return await this.service.surveyFindAll(data)
  }

  @ApiOperation({ summary: 'Tạo mới mẫu khảo sát' })
  @Post('survey/create_data')
  public async surveyCreateData(@CurrentUser() user: UserDto, @Body() data: SurveyCreateDto, @Req() req: IRequest) {
    return await this.service.surveyCreateData(user, data, req)
  }

  @ApiOperation({ summary: 'Chỉnh sửa mới mẫu khảo sát' })
  @Post('survey/update_data')
  public async surveyUpdateData(@CurrentUser() user: UserDto, @Body() data: SurveyUpdateDto) {
    return await this.service.surveyUpdateData(user, data)
  }

  @ApiOperation({ summary: 'Chỉnh sửa trạng thái hoạt động phiếu khảo sát' })
  @Post('survey/update_active')
  public async surveyUpdateActive(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.surveyUpdateActive(user, data)
  }

  @ApiOperation({ summary: 'Danh sách phân trang phiếu trả lời' })
  @Post('survey_question/pagination')
  public async surveyQuestionPagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.surveyQuestionPagination(user, data)
  }

  @ApiOperation({ summary: 'Tìm toàn bộ survey question' })
  @Post('survey_question/find_all')
  public async surveyQuestionFindAll(@Body() data: any) {
    return await this.service.surveyQuestionFindAll(data)
  }

  @ApiOperation({ summary: 'Thống kê số lượt làm khảo sát từ ngày đến ngày' })
  @Post('get_survey_stats')
  public async ggetSurveyStats(@Body() data: { from: any; to: any }) {
    return await this.service.getSurveyStatsByMonth(data.from, data.to)
  }

  @ApiOperation({ summary: 'Tìm câu hỏi survey question trong survey' })
  @Post('survey_question/find_question')
  public async surveyQuestionFindQuestion(@Body() data: any) {
    return await this.service.surveyQuestionFindQuestion(data)
  }

  @ApiOperation({ summary: 'Hàm tìm kiếm' })
  @Post('topic/find')
  public async find(@Body() data: { isDeleted?: boolean }) {
    return await this.service.topicFind(data)
  }

  @ApiOperation({ summary: 'Hàm tìm kiếm kèm khảo sát' })
  @Post('topic/find_with_survey')
  public async topicFindWithSurvey(@Body() data: { isDeleted?: boolean }) {
    return await this.service.topicFindWithSurvey(data)
  }

  @ApiOperation({ summary: 'Hàm phân trang' })
  @Post('topic/pagination')
  public async topicPagination(@Body() data: PaginationDto) {
    return await this.service.topicPagination(data)
  }

  @ApiOperation({ summary: 'Hàm tạo mới' })
  @Post('topic/create_data')
  public async topicCreateData(@CurrentUser() user: UserDto, @Body() data: TopicCreateDto) {
    return await this.service.topicCreateData(user, data)
  }

  @ApiOperation({ summary: 'Hàm cập nhật' })
  @Post('topic/update_data')
  public async topicUpdateData(@CurrentUser() user: UserDto, @Body() data: TopicUpdateDto) {
    return await this.service.topicUpdateData(user, data)
  }

  @ApiOperation({ summary: 'Hàm cập nhật trạng thái hoạt động' })
  @Post('topic/update_active')
  public async topicUpdateActive(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.topicUpdateActive(user, data.id)
  }

  @ApiOperation({ summary: 'Hàm tạo mới bằng excel' })
  @Post('topic/create_data_by_excel')
  public async topicCreateDataByExcel(@CurrentUser() user: UserDto, @Body() data: TopicCreateExcelDto[]) {
    return await this.service.topicCreateDataByExcel(user, data)
  }

  @ApiOperation({ summary: 'Tìm câu hỏi theo id' })
  @Post('question/find_one')
  public async questionFindOne(@CurrentUser() user: UserDto, @Body() data: any) {
    return await this.service.questionFindOne(user, data)
  }

  @ApiOperation({ summary: 'Tạo mới câu hỏi' })
  @Post('question/create_data')
  public async questionCreateData(@CurrentUser() user: UserDto, @Body() data: QuestionCreateDto) {
    return await this.service.questionCreateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật câu hỏi' })
  @Post('question/update_data')
  public async questionUpdateData(@CurrentUser() user: UserDto, @Body() data: QuestionUpdateDto) {
    return await this.service.questionUpdateData(user, data)
  }

  @ApiOperation({ summary: 'Danh sách phân trang' })
  @Post('question/pagination')
  public async questionPagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.questionPagination(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động' })
  @Post('question/update_active')
  public async questionUpdateActive(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.questionUpdateActive(user, data.id)
  }

  @ApiOperation({ summary: 'Danh sách các lựa chọn của câu hỏi kiểu List' })
  @Post('question_list_detail/pagination')
  public async questionlistdetail_pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.questionlistdetail_pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo mới lựa chọn cho câu hỏi kiểu List' })
  @Post('question_list_detail/create_data')
  public async questionlistdetail_create_data(@CurrentUser() user: UserDto, @Body() data: QuestionListDetailCreateDto) {
    return await this.service.questionlistdetail_create_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật lựa chọn cho câu hỏi kiểu List' })
  @Post('question_list_detail/update_data')
  public async questionlistdetail_update_data(@CurrentUser() user: UserDto, @Body() data: QuestionListDetailUpdateDto) {
    return await this.service.questionlistdetail_update_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động lựa chọn cho câu hỏi kiểu List' })
  @Post('question_list_detail/update_active')
  public async questionlistdetail_update_active(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.questionlistdetail_update_active(user, data.id)
  }

  @ApiOperation({ summary: 'Xóa tất cả câu hỏi' })
  @Post('question/delete_all')
  public async delete_all(@CurrentUser() user: UserDto, @Body() data: { topicId: string }) {
    return await this.service.questionDeleteAll(user, data.topicId)
  }

  @ApiOperation({ summary: 'Danh sách danh mục từ master data' })
  @Post('question/create_master_data_question')
  public async questionCreateMasterData(@Body() data: QuestionCreateMasterDataDto[]) {
    return await this.service.questionCreateMasterData(data)
  }

  @ApiOperation({ summary: 'Import danh sách câu hỏi theo chủ đề' })
  @Post('question/import/:topicId')
  public async import(@Param('topicId') topicId: string, @CurrentUser() user: UserDto, @Body() data: { lstDataTable1: any[]; lstDataTable2: any[] }) {
    return await this.service.questionImport(topicId, user, data)
  }

  @ApiOperation({ summary: 'khách hàng trả lời câu hỏi' })
  @Post('survey_answer_question')
  public async surveyAnswerQuestion(
    @CurrentUser() user: UserDto,
    @Req() req: IRequest,
    @Body()
    data: SurveyAnswerQuestionDto,
  ) {
    return await this.service.surveyAnswerQuestion(data)
  }

  @ApiOperation({ summary: 'Lấy ds các câu hỏi' })
  @Post('survey_load_question')
  public async mobileAppsurveyLoadQuestion(@CurrentUser() user: UserDto, @Req() req: IRequest, @Body() data: SurveyFilterOneDto) {
    return await this.service.mobileAppSurveyQuestion(user, data)
  }
}
