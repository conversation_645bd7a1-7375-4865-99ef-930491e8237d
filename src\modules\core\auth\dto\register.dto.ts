import { Expose } from 'class-transformer'
import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator'

export class UserRegisterDto {
  @IsString()
  @Expose()
  @IsNotEmpty()
  address: string

  @Expose()
  @IsString()
  phone: string

  @Expose()
  @IsString()
  @IsOptional()
  company?: string

  @Expose()
  @IsString()
  @IsOptional()
  email?: string

  // Mã số thuế
  @Expose()
  @IsString()
  @IsOptional()
  tax?: string

  // CCCD
  @IsString()
  @Expose()
  @IsOptional()
  identityCard?: string

  @IsString()
  @Expose()
  @IsNotEmpty()
  username: string

  //fullname
  @IsString()
  @Expose()
  @IsNotEmpty()
  fullName: string

  @Expose()
  @IsString()
  @IsOptional()
  password?: string

  @Expose()
  @IsString()
  @IsOptional()
  type?: string

  //supplierId
  @Expose()
  @IsString()
  @IsOptional()
  supplierId: string
}

export class UserUpdateDto extends UserRegisterDto {
  @IsUUID()
  @Expose()
  @IsNotEmpty()
  id: string
}

export class UserUpdatePasswordDto {
  @Expose()
  @IsUUID()
  @IsNotEmpty()
  id: string

  @Expose()
  @IsString()
  @IsNotEmpty()
  currentPassword: string

  @Expose()
  @IsString()
  @IsNotEmpty()
  newPassword: string
}
