import { Module } from '@nestjs/common'
import { SettingMediaRepository } from '../../../repositories'
import { SettingMediaController } from './settingMedia.controller'
import { TypeOrmExModule } from '../../../typeorm'
import { SettingMediaService } from './settingMedia.service'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([SettingMediaRepository])],
  controllers: [SettingMediaController],
  providers: [SettingMediaService],
  exports: [SettingMediaService],
})
export class SettingMediaModule {}
