import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'

export class NotifyCreateDto {
  @ApiProperty({ description: 'tiêu đề gửi' })
  @IsNotEmpty()
  @IsString()
  title?: string

  @ApiProperty({ description: 'nội dung' })
  @IsNotEmpty()
  @IsString()
  content?: string

  @ApiProperty({ description: 'Id User' })
  @IsNotEmpty()
  @IsString()
  userId?: string

  @ApiProperty({ description: 'Id Survey' })
  @IsNotEmpty()
  @IsString()
  surveyId: string
}
