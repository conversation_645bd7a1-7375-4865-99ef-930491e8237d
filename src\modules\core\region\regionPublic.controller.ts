import { Controller, UseGuards, Request, Post, Body } from '@nestjs/common'
import { Request as IRequest } from 'express'
import { PaginationDto, UserDto } from '../../../dto'
import { RegionService } from './region.service'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { ApeAuthGuard } from '../../survey/common/guards'

@ApiBearerAuth()
@ApiTags('Region Public')
@UseGuards(ApeAuthGuard)
@Controller('region_public')
export class RegionPublicController {
  constructor(private readonly service: RegionService) {}

  @Post('find')
  public async find(@Request() req: IRequest, @Body() data: any) {
    return await this.service.find(data)
  }

  @Post('pagination')
  public async pagination(@Request() req: IRequest, @Body() data: PaginationDto) {
    return await this.service.pagination(data)
  }

  @Post('find_region_city')
  public async findRegionCity(@Request() req: IRequest) {
    return await this.service.findRegionCity()
  }
}
