import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'

/** Interface đăng nhập trang admin Survey */
export class LoginAdminDto {
  @ApiProperty({ description: '<PERSON><PERSON><PERSON> kho<PERSON>n' })
  @IsString()
  @IsNotEmpty()
  username: string

  @ApiProperty({ description: '<PERSON>ậ<PERSON> khẩu' })
  @IsString()
  @IsNotEmpty()
  password: string
}
