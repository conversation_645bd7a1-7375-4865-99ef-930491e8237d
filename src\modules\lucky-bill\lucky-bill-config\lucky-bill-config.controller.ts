import { Controller, Post, Body, UseGuards, Param, Get } from '@nestjs/common'
import { LuckyBillConfigService } from './lucky-bill-config.service'
import { CreateLuckyBillConfigDto } from './dto/create-lucky-bill-config.dto'
import { CurrentUser, JwtAuthGuard } from '../../common'
import { UserDto } from '../../../dto'
import { UpdateLuckyBillConfigDto } from './dto/update-lucky-bill-config.dto'
import { ListLuckyBillConfigDto } from './dto/list-lucky-bill.dto'

@Controller('lucky-bill-config')
@UseGuards(JwtAuthGuard)
export class LuckyBillConfigController {
  constructor(private readonly luckyBillConfigService: LuckyBillConfigService) {}
  //Create
  @Post('create')
  create(@Body() createLuckyBillConfigDto: CreateLuckyBillConfigDto, @CurrentUser() user: UserDto) {
    return this.luckyBillConfigService.create(createLuckyBillConfigDto, user)
  }
  //Read
  @Post('list')
  list(@Body() params: ListLuckyBillConfigDto) {
    return this.luckyBillConfigService.list(params)
  }
  //Update
  @Post('update')
  update(@Body() updateLuckyBillConfigDto: UpdateLuckyBillConfigDto, @CurrentUser() user: UserDto) {
    return this.luckyBillConfigService.updateLuckyBillConfig(updateLuckyBillConfigDto, user)
  }

  //Delete
  @Post('setActive/:id')
  setActive(@Param('id') id: string, @CurrentUser() user: UserDto) {
    return this.luckyBillConfigService.setActive(id, user)
  }
}
