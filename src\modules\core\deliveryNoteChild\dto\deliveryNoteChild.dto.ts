import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { PageRequest } from '../../../../dto'
import { IsArray, IsDateString, IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID, ValidateNested } from 'class-validator'
import { Type } from 'class-transformer'

export class FilterDeliverNoteChildDto extends PageRequest {
  @ApiProperty({ description: 'Code của phiếu giao nhận' })
  @IsOptional()
  code?: string

  @ApiProperty({ description: 'Ngày tạo' })
  @IsOptional()
  dateFrom?: string

  @ApiProperty({ description: 'Ngày tạo' })
  @IsOptional()
  dateTo?: string

  @ApiProperty({ description: 'ID đơn vị vận chuyển' })
  @IsOptional()
  thirdPartyId?: string
}

export class FilterDeliverNoteChildDetailDto extends PageRequest {
  @ApiProperty({ description: 'Code của phiếu giao nhận' })
  @IsArray()
  @IsNotEmpty()
  partnerIds: string[]

  @ApiProperty({ description: 'Mã code phiếu giao nhận' })
  @IsOptional()
  code?: string

  @ApiProperty({ description: 'Ngày tạo' })
  @IsOptional()
  dateFrom?: string

  @ApiProperty({ description: 'Ngày tạo' })
  @IsOptional()
  dateTo?: string

  @IsOptional()
  status?: string
}

export class DetailDeliverNoteChildDto {
  @ApiProperty({ description: 'ID của phiếu giao nhận' })
  @IsUUID()
  @IsNotEmpty()
  id: string
}

export class ItemDto {
  @IsNotEmpty()
  productCode: string

  @IsNotEmpty()
  productName: string

  @IsNotEmpty()
  productId: string

  @IsNotEmpty()
  quantity: number

  @IsNotEmpty()
  priceSell: number

  @IsOptional()
  priceBuy?: number

  @IsOptional()
  poDetailId?: string

  @IsString()
  @IsNotEmpty()
  purchaseOrderId: string

  @IsNotEmpty()
  @IsDateString()
  manufactureDate: Date

  @IsNotEmpty()
  @IsDateString()
  expiryDate: Date
}

export class CreateInboundFromDeliveryNoteChildDto {
  @ApiProperty({ description: 'ID chi tiết phiếu giao nhận con' })
  @IsUUID()
  @IsNotEmpty()
  id: string

  @ApiProperty({ description: 'ID của partner để tim WH' })
  @IsString()
  @IsNotEmpty()
  partnerId: string

  // @ApiProperty({ description: "Danh sách combo nhập kho" })
  // @IsArray()
  // @IsOptional()
  // @Type(() => ItemDto)
  // combo: ItemDto[] = [];

  // @ApiProperty({ description: "Danh sách sản phẩm nhập kho" })
  // @IsOptional()
  // @IsArray()
  // @Type(() => ItemDto)
  // item: ItemDto[] = [];

  @ApiProperty({ description: 'Danh sách sản phẩm nhập kho' })
  @IsOptional()
  @IsArray()
  @Type(() => ItemDto)
  products?: ItemDto[]

  @ApiProperty({ description: 'ID chi tiết phiếu giao nhận con' })
  @IsString()
  @IsNotEmpty()
  createBy: string
}

export class UUIDDto {
  @ApiProperty({ description: 'ID chi tiết phiếu giao nhận con' })
  @IsUUID()
  @IsNotEmpty()
  id: string
}

export class SendPartnerDto extends UUIDDto {
  @ApiPropertyOptional({ description: 'ID chi tiết phiếu giao nhận con' })
  @IsOptional()
  combo?: [any]

  @ApiPropertyOptional({ description: 'ID chi tiết phiếu giao nhận con' })
  @IsOptional()
  item?: [any]

  //warehouseId?: string
  @IsOptional()
  partnerId?: string
}
