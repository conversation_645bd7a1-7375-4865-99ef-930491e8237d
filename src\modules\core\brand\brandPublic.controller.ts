import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { BrandService } from './brand.service'
import { JwtAuthGuard } from '../../common/guards'
import { BrandCreateDto } from './dto/brandCreate.dto'
import { BrandUpdateDto } from './dto/brandUpdate.dto'
import { BrandCreateExcelDto, BrandUpdateIsActiveDto } from './dto'
import { CurrentUser } from '../../common/decorators'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { AuthGuard } from '../../common/guards/auth.guard'

@ApiTags('Brand')
@Controller('brand_public')
export class BrandPublicController {
  constructor(private readonly service: BrandService) { }

  @Post('find')
  public async find(@Body() data: any) {
    return await this.service.find(data)
  }

  @Post('pagination')
  public async pagination(@Body() data: PaginationDto) {
    return await this.service.pagination(data)
  }

  @Post('details')
  public async getListParent(@Body() data: any) {
    return await this.service.getListParent(data)
  }

  @Post('find_one')
  public async findOne(@Body() data: FilterOneDto) {
    return await this.service.findOne(data)
  }
}
