import { Injectable } from '@nestjs/common'
import { CreateLuckyBillConfigDto } from './dto/create-lucky-bill-config.dto'
import { LuckyBillConfigRepository } from '../../../repositories/lucky-bill/lucky-bill-config.repository'
import { LuckyBillApplyRegionRepository } from '../../../repositories/lucky-bill/lucky-bill-apply-region.repository'
import { UserDto } from '../../../dto'
import { coreHelper } from '../../../helpers'
import { UpdateLuckyBillConfigDto } from './dto/update-lucky-bill-config.dto'
import { ListLuckyBillConfigDto } from './dto/list-lucky-bill.dto'
import { Between, ILike, In, LessThanOrEqual, MoreThanOrEqual } from 'typeorm'
import { LuckyBillApplyRegionService } from '../lucky-bill-apply-region/lucky-bill-apply-region.service'
import { LuckyBillConfigEntity } from '../../../entities/lucky-bill/lucky-bill-config.entity'
import { LuckyBillApplyRegionEntity } from '../../../entities/lucky-bill/lucky-bill-apply-region.entity'

@Injectable()
export class LuckyBillConfigService {
  constructor(
    private readonly luckyBillConfigRepo: LuckyBillConfigRepository,
    private readonly luckyBillApplyRegionRepo: LuckyBillApplyRegionRepository,
  ) {}
  async create(createLuckyBillConfigDto: CreateLuckyBillConfigDto, user: UserDto) {
    const { applyRegions, ...rest } = createLuckyBillConfigDto
    const data = this.luckyBillConfigRepo.create(rest)
    if (data.applyDateFrom > data.applyDateTo) throw new Error('Ngày bắt đầu áp dụng không được lớn hơn ngày kết thúc áp dụng')
    //count
    const count = await this.luckyBillConfigRepo.count()

    data.code = coreHelper.generatePOString(count, 'LB')

    data.createdBy = user.id
    //check applyRegions
    if (applyRegions && applyRegions.length > 0) {
      const listUpdateRegionIds: any[] = []
      for (let item of applyRegions) {
        if (item.id) {
          listUpdateRegionIds.push(item.id)
        }
      }
      //check
      if (listUpdateRegionIds.length > 0) {
        const check = await this.luckyBillApplyRegionRepo.find({ where: { id: In(listUpdateRegionIds) } })
        if (check.length !== listUpdateRegionIds.length) throw new Error('Danh sách áp dụng không hợp lệ')
      }
    }
    //trasaction
    await this.luckyBillConfigRepo.manager.transaction(async (trans) => {
      const luckyBillConfigRepo = trans.getRepository(LuckyBillConfigEntity)
      const luckyBillApplyRegionRepo = trans.getRepository(LuckyBillApplyRegionEntity)
      //create luckyBillConfig
      const luckyBillConfig = await luckyBillConfigRepo.save(data)

      //create applyRegions
      const lstApplyRegion = []
      for (let item of applyRegions) {
        lstApplyRegion.push({ ...luckyBillApplyRegionRepo.create(item), luckyBillConfigId: luckyBillConfig.id })
      }
      await luckyBillApplyRegionRepo.insert(lstApplyRegion)
    })

    return {
      message: 'Tạo mới cấu hình Lucky Bill thành công',
      data,
    }
  }

  async updateLuckyBillConfig(updateLuckyBillConfigDto: UpdateLuckyBillConfigDto, user: UserDto) {
    const { applyRegions, ...rest } = updateLuckyBillConfigDto
    const data = await this.luckyBillConfigRepo.findOne({ where: { id: rest.id } })
    if (!data) throw new Error('Không tìm thấy cấu hình Lucky Bill')
    if (rest.applyDateFrom > rest.applyDateTo) throw new Error('Ngày bắt đầu áp dụng không được lớn hơn ngày kết thúc áp dụng')
    //check applyRegions
    if (applyRegions && applyRegions.length > 0) {
      const listUpdateRegionIds: any[] = []
      for (let item of applyRegions) {
        if (item.id) {
          listUpdateRegionIds.push(item.id)
        }
      }
      //check
      if (listUpdateRegionIds.length > 0) {
        const check = await this.luckyBillApplyRegionRepo.find({ where: { id: In(listUpdateRegionIds) } })
        if (check.length !== listUpdateRegionIds.length) throw new Error('Danh sách áp dụng không hợp lệ')
      }
    }

    Object.assign(data, rest)
    data.updatedBy = user.id
    await this.luckyBillConfigRepo.manager.transaction(async (trans) => {
      const luckyBillConfigRepo = trans.getRepository(LuckyBillConfigEntity)
      const luckyBillApplyRegionRepo = trans.getRepository(LuckyBillApplyRegionEntity)
      //create luckyBillConfig
      const luckyBillConfig = await luckyBillConfigRepo.save(data)

      //create applyRegions
      const lstNewApplyRegion = []
      for (let item of applyRegions) {
        if (item.id) {
          await luckyBillApplyRegionRepo.update(item.id, { cityIds: item.cityIds, limit: item.limit, updatedBy: user.id })
        } else {
          lstNewApplyRegion.push({ ...luckyBillApplyRegionRepo.create(item), luckyBillConfigId: luckyBillConfig.id })
        }
      }
      await luckyBillApplyRegionRepo.insert(lstNewApplyRegion)
    })

    return {
      message: 'Cập nhật cấu hình Lucky Bill thành công',
      data,
    }
  }

  async setActive(id: any, user: UserDto) {
    const config = await this.luckyBillConfigRepo.findOne({ where: { id } })
    if (!config) throw new Error('Không tìm thấy cấu hình Lucky Bill')

    config.isDeleted = !config.isDeleted
    config.updatedBy = user.id
    await this.luckyBillConfigRepo.update(config.id, config)
    return {
      message: config.isDeleted ? 'Vô hiệu hóa cấu hình Lucky Bill thành công' : 'Kích hoạt cấu hình Lucky Bill thành công',
      data: config,
    }
  }

  async list(params: ListLuckyBillConfigDto) {
    const { name, code, isDeleted, value, applyDateFrom, applyDateTo, createdAtFrom, createdAtTo, pageSize = 10, pageIndex = 1 } = params
    const whereCon: any = {}
    if (name) whereCon.name = ILike(`%${name}%`)
    if (code) whereCon.code = ILike(`%${code}%`)
    if (isDeleted != undefined) whereCon.isDeleted = isDeleted
    if (value) whereCon.value = value
    if (applyDateFrom) whereCon.applyDateFrom = MoreThanOrEqual(applyDateFrom)
    if (applyDateTo) whereCon.applyDateTo = LessThanOrEqual(applyDateTo)
    if (createdAtFrom && createdAtTo) whereCon.createdAt = Between(createdAtFrom, createdAtTo)
    const [data, total] = await this.luckyBillConfigRepo.findAndCount({
      where: whereCon,
      skip: (pageIndex - 1) * pageSize,
      take: pageSize,
      order: { createdAt: 'DESC' },
    })
    return {
      data,
      total,
    }
  }
}
