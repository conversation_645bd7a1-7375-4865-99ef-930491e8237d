import { ApiProperty } from '@nestjs/swagger'
import { IsOptional } from 'class-validator'
import { PageRequest } from '../../../../dto'

export class ListLuckyBillConfigDto extends PageRequest {
  @ApiProperty({ description: '<PERSON>ê<PERSON> cấu hình' })
  @IsOptional()
  name?: string

  @ApiProperty({ description: 'Mã cấu hình' })
  @IsOptional()
  code?: string

  @ApiProperty({ description: 'Trạng thái' })
  @IsOptional()
  isDeleted?: boolean

  @ApiProperty({ description: 'Giá trị cấu hình' })
  @IsOptional()
  value?: number

  @ApiProperty({ description: '<PERSON><PERSON>y bắt đầu áp dụng' })
  @IsOptional()
  applyDateFrom?: Date

  @ApiProperty({ description: 'Ngày kết thúc áp dụng' })
  @IsOptional()
  applyDateTo?: Date

  //createdAt
  @ApiProperty({ description: '<PERSON><PERSON><PERSON> tạo' })
  @IsOptional()
  createdAtFrom?: Date

  @ApiProperty({ description: 'Ngày tạo đến' })
  @IsOptional()
  createdAtTo?: Date
}
