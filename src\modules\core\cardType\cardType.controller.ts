import { Controller, UseGuards, Post, Body, Req, Query, Get } from '@nestjs/common'
import { JwtAuthGuard } from '../../common/guards'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { Request as IRequest } from 'express'
import { omsApiHelper } from '../../../helpers/omsApiHelper'
import { DeclareCardDuration, DeclareCardPeriod, DeclareCardType } from '../../../helpers/dto/apiCaller.dto'

@ApiBearerAuth()
@ApiTags('CardType')
@UseGuards(JwtAuthGuard)
@Controller('card_type')
export class CardTypeController {
  constructor() {}

  @Get('list')
  public async pagination(@Query() data: { pageSize: any; pageIndex: any }, @Req() req: IRequest) {
    return await omsApiHelper.getCardType(req, data)
  }

  @Post('import_card_type')
  public async importCardTypeExcel(@Body() data: DeclareCardType[], @Req() req: IRequest) {
    return await omsApiHelper.importCardTypeExcel(req, data)
  }
}
