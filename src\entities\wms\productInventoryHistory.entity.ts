import { BaseEntity } from '../core/base.entity'
import { Entity, Column } from 'typeorm'

/** <PERSON><PERSON><PERSON> sử tăng/giảm tồn kho của kho vật lý & kho tổng theo sản phẩm */
@Entity('product_inventory_history')
export class ProductInventoryHistoryEntity extends BaseEntity {
  /** Id kho vật lý */
  @Column({ type: 'varchar', length: 36, nullable: true })
  warehouseId: string

  /** Id sản phẩm trong kho vật lý */
  @Column({ type: 'varchar', length: 36, nullable: true })
  warehouseProductId: string

  /** Id chi tiết sản phẩm trong kho vật lý */
  @Column({ type: 'varchar', length: 36, nullable: true })
  warehouseProductDetailId: string

  /** Id Sản phẩm */
  @Column({ type: 'varchar', length: 36, nullable: true })
  productId: string

  /** Mã sp */
  @Column({ type: 'varchar', length: 50, nullable: true })
  productCode: string

  /** Tên sp */
  @Column({ type: 'varchar', length: 250, nullable: true })
  productName: string

  /** Id PNK */
  @Column({ type: 'varchar', length: 36, nullable: true })
  inboundId?: string

  /** Id chi tiết PNK */
  @Column({ type: 'varchar', length: 36, nullable: true })
  inboundDetailId?: string

  /** Id PXK */
  @Column({ type: 'varchar', length: 36, nullable: true })
  outboundId?: string

  /** Id chi tiết PXK */
  @Column({ type: 'varchar', length: 36, nullable: true })
  outboundDetailId?: string

  /** Số lượng tồn hiện tại (tính theo totalQuantity của inboundDetail) */
  @Column({ nullable: true })
  quantity: number

  /** Số lượng tồn mới (tính theo totalQuantity của inboundDetail) */
  @Column({ nullable: true })
  quantityNew: number

  /** Ghi chú, ví dụ `Duyệt PNK [${detail.code}]<br>Nhập thêm: ${detail.totalQuantity}<br>HSD ${dateStr}` */
  @Column({ type: 'text', nullable: true })
  description: string
}
