import { Controller, UseGuards, Post, Body, Req, Query, Get } from '@nestjs/common'
import { JwtAuthGuard } from '../../common/guards'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { Request as IRequest } from 'express'
import { omsApiHelper } from '../../../helpers/omsApiHelper'
import { DeclareCardDuration, DeclareCardPeriod } from '../../../helpers/dto/apiCaller.dto'

@ApiBearerAuth()
@ApiTags('CardDuration')
@UseGuards(JwtAuthGuard)
@Controller('card_duration')
export class CardDurationController {
  constructor() {}

  @Get('list')
  public async pagination(@Query() data: { pageSize: any; pageIndex: any }, @Req() req: IRequest) {
    return await omsApiHelper.getCardDuration(req, data)
  }

  @Post('import_card_duration')
  public async importCardDurationExcel(@Body() data: DeclareCardDuration[], @Req() req: IRequest) {
    return await omsApiHelper.importCardDurationExcel(req, data)
  }
}
