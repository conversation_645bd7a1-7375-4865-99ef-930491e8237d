import { Column, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from '../core/base.entity'
import { SurveyEntity } from './s_survey.entity'
import { SurveyMemberEntity } from './s_surveyMember.entity'

/** Phiếu khảo sát - câu hỏi */
@Entity('s_survey_question')
export class SurveyQuestionEntity extends BaseEntity {
  /** Id phiếu khảo sát */
  @Column({
    type: 'varchar',
    nullable: false,
  })
  surveyId: string
  @ManyToOne(() => SurveyEntity, (p) => p.questions)
  @JoinColumn({ name: 'surveyId', referencedColumnName: 'id' })
  survey: Promise<SurveyEntity>

  /** Id câu hỏi */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  questionId: string

  /** Id danh mục */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  categoryId: string

  /** Id chủ đề */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  topicId: string

  @Column({
    type: 'float',
    nullable: true,
    default: 0,
  })
  score: number

  @Column({
    type: 'varchar',
    array: true,
    nullable: true,
  })
  value: string[]

  /** Id phiếu khảo sát */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  surveyMemberId: string
}
