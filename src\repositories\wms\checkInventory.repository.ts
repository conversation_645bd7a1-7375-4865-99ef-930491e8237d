import { CheckInventoryDetailByEmployeeEntity, CheckInventoryDetailEntity, CheckInventoryEntity, CheckInventoryHistoryEntity } from '../../entities'
import { Repository } from 'typeorm'
import { CustomRepository } from '../../typeorm'

@CustomRepository(CheckInventoryEntity)
export class CheckInventoryRepository extends Repository<CheckInventoryEntity> {}

@CustomRepository(CheckInventoryDetailEntity)
export class CheckInventoryDetailRepository extends Repository<CheckInventoryDetailEntity> {}

@CustomRepository(CheckInventoryDetailByEmployeeEntity)
export class CheckInventoryDetailByEmployeeRepository extends Repository<CheckInventoryDetailByEmployeeEntity> {}

@CustomRepository(CheckInventoryHistoryEntity)
export class CheckInventoryHistoryRepository extends Repository<CheckInventoryHistoryEntity> {}
