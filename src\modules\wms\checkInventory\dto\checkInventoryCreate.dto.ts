import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator'

export class CheckInventoryCreateDto {
  @ApiProperty({ description: '<PERSON><PERSON> kiểm' })
  @IsNotEmpty()
  @IsString()
  warehouseId: string

  @ApiProperty({ description: 'Ngày tạo phiếu' })
  @IsNotEmpty()
  @IsString()
  createdAt: Date

  @ApiProperty({ description: 'Ghi chú' })
  @IsOptional()
  @IsString()
  description?: string

  @ApiProperty({ description: 'Id người tạo phiếu kiểm kho' })
  @IsOptional()
  @IsUUID()
  createBy?: string

  @ApiProperty({ description: 'Danh sách sản phẩm trong kho vật lý' })
  lstCheckInventoryDetail: CheckInventoryDetailCreateDto[]
}

export class CheckInventoryDetailCreateDto {
  @ApiProperty({ description: 'Sản phẩm' })
  @IsNotEmpty()
  @IsString()
  productId: string

  @ApiProperty({ description: 'Sản phẩm thực' })
  @IsNotEmpty()
  @IsString()
  productDetailId: string

  @ApiProperty({ description: 'Hạn sử dụng' })
  @IsOptional()
  expiryDate: Date

  @ApiProperty({ description: 'Ngày sản xuất' })
  @IsOptional()
  manufactureDate: Date

  @ApiProperty({ description: 'Số lô' })
  @IsOptional()
  @IsString()
  lotNumber: string

  @ApiProperty({ description: 'Số lượng xuất' })
  @IsOptional()
  quantity: number

  @ApiProperty({ description: 'Tồn kho' })
  @IsOptional()
  inventory: number
}
