import { MigrationInterface, QueryRunner } from 'typeorm'

export class generateLuckyBill1756366132256 implements MigrationInterface {
  name = 'generateLuckyBill1756366132256'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "lucky_bill_apply_region" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "createdBy" character varying(36), "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT now(), "updatedBy" character varying(36), "isDeleted" boolean NOT NULL DEFAULT false, "luckyBillConfigId" uuid NOT NULL, "regionIds" text array NOT NULL, "districtIds" text array NOT NULL, CONSTRAINT "PK_5fb0c0e68313815bff3285a8db1" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `CREATE TABLE "lucky_bill_config" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "createdBy" character varying(36), "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT now(), "updatedBy" character varying(36), "isDeleted" boolean NOT NULL DEFAULT false, "name" character varying(255) NOT NULL, "code" character varying(50) NOT NULL, "value" integer NOT NULL, "applyDateFrom" TIMESTAMP WITH TIME ZONE NOT NULL, "applyDateTo" TIMESTAMP WITH TIME ZONE NOT NULL, "luckyDrawMechanism" character varying(50) DEFAULT 'MANUAL', "frequency" TIMESTAMP WITH TIME ZONE, "description" text, CONSTRAINT "PK_19d04a0993c4e8dd560720fbc01" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `ALTER TABLE "lucky_bill_apply_region" ADD CONSTRAINT "FK_44618739ba5cab859793759a44e" FOREIGN KEY ("luckyBillConfigId") REFERENCES "lucky_bill_config"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "lucky_bill_apply_region" DROP CONSTRAINT "FK_44618739ba5cab859793759a44e"`)
    await queryRunner.query(`DROP TABLE "lucky_bill_config"`)
    await queryRunner.query(`DROP TABLE "lucky_bill_apply_region"`)
  }
}
