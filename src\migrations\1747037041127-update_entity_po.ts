import { MigrationInterface, QueryRunner } from "typeorm";

export class updateEntityPo1747037041127 implements MigrationInterface {
    name = 'updateEntityPo1747037041127'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "purchase_order" ADD "approveDate" TIMESTAMP WITH TIME ZONE`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "purchase_order" DROP COLUMN "approveDate"`);
    }

}
