import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString } from 'class-validator'

export class ItemCategoryCreateDto {
  @IsNotEmpty()
  @IsString()
  name: string

  @IsNotEmpty()
  @IsString()
  code: string

  @ApiPropertyOptional()
  itemTypeId: string

  @IsOptional()
  description: string

  lstMediaItemType?: MediaDto[]
}

export class MediaDto {
  @ApiProperty({ description: 'Id sản phẩm' })
  @IsString()
  productId?: string
  @ApiProperty({ description: 'Đường dẫn của file' })
  @IsNotEmpty({ message: 'Đường dẩn của file không được để trống' })
  @IsString()
  url: string

  content: string
  table: string

  @ApiProperty({ description: 'Tên file' })
  @IsNotEmpty({ message: 'Tên file không được để trống' })
  @IsString()
  name: string
}
