import { MigrationInterface, QueryRunner } from 'typeorm'

export class initPricing1757562603559 implements MigrationInterface {
  name = 'initPricing1757562603559'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "pricing" ADD CONSTRAINT "FK_22aa7ee9e019f4f8bf61920da8c" FOREIGN KEY ("itemId") REFERENCES "item"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "pricing" ADD CONSTRAINT "FK_76fcd175dbfa0d52513023ae94f" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "pricing" DROP CONSTRAINT "FK_76fcd175dbfa0d52513023ae94f"`)
    await queryRunner.query(`ALTER TABLE "pricing" DROP CONSTRAINT "FK_22aa7ee9e019f4f8bf61920da8c"`)
  }
}
