import { Injectable, ConflictException, NotFoundException } from '@nestjs/common'
import { NewsCategoryRepository, NewsRepository } from '../../../repositories'
import { ERROR_NOT_FOUND_DATA, ERROR_CODE_TAKEN, UPDATE_ACTIVE_SUCCESS, CREATE_SUCCESS, UPDATE_SUCCESS, IMPORT_SUCCESS } from '../../../constants'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { ILike, In, Like } from 'typeorm'
import { NewsCategoryEntity, NewsEntity } from '../../../entities'
import { NewsCreateDto, NewsUpdateDto } from './dto'
import { error } from 'console'

@Injectable()
export class NewsService {
  constructor(private readonly repo: NewsRepository, private readonly repoNewsCategory: NewsCategoryRepository) {}

  public async find(data: any) {
    const whereCon: any = { isDeleted: false }
    if (data.title) whereCon.title = Like(`%${data.title}%`)
    if (data.code) whereCon.code = Like(`%${data.code}%`)
    if (data.lstId?.length > 0) whereCon.id = In(data.lstId)
    if (data.lstCode?.length > 0) whereCon.code = In(data.lstCode)
    if (data.type) whereCon.newsCategory = { type: data.type }
    return await this.repo.find({ where: whereCon, order: { title: 'ASC' } })
  }

  public async createData(user: UserDto, data: NewsCreateDto) {
    const checkCodeExist = await this.repo.findOne({ where: { code: data.code } })
    if (checkCodeExist) throw new ConflictException(ERROR_CODE_TAKEN)
    const newEntity = this.repo.create({ ...data })
    newEntity.newsCategoryId = data.newsCategoryId
    newEntity.createdBy = user.id
    newEntity.createdAt = new Date()
    await this.repo.insert(newEntity)
    return { message: CREATE_SUCCESS }
  }

  public async updateData(user: UserDto, data: NewsUpdateDto) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    if (entity.code != data.code) {
      const checkCodeExist = await this.repo.findOne({ where: { code: data.code } })
      if (checkCodeExist) throw new ConflictException(ERROR_CODE_TAKEN)
    }
    entity.title = data.title
    entity.code = data.code
    entity.newsCategoryId = data.newsCategoryId
    entity.image = data.image
    entity.content = data.content
    entity.updatedAt = new Date()
    entity.updatedBy = user.id
    await this.repo.update(entity.id, entity)
    return { message: UPDATE_SUCCESS }
  }

  public async pagination(data: PaginationDto) {
    let whereCon: any = {}
    if (data.where.title) whereCon.title = Like(`%${data.where.title}%`)
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    if (data.where.type) whereCon.newsCategory = { type: data.where.type }
    const result: any = await this.repo.findAndCount({
      where: whereCon,
      order: { createdAt: 'DESC', code: 'DESC' },
      skip: data.skip,
      take: data.take,
      relations: { newsCategory: true },
    })
    for (let item of result[0]) {
      item.category = await item.newsCategory
      delete item.__newsCategory__
    }
    return result
  }

  public async updateIsDelete(id: string, user: UserDto) {
    const entity = await this.repo.findOne({ where: { id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    await this.repo.update({ id: entity.id }, { isDeleted: !entity.isDeleted })
    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  async findOne(data: FilterOneDto) {
    const whereCon: any = { id: data.id, isDeleted: false }
    let res: any = await this.repo.findOne({ where: whereCon, relations: { newsCategory: true } })
    res.category = await res.newsCategory
    delete res.__newsCategory__
    return res
  }

  async listNew(data: { title?: string; categoryType?: string; take?: number; index?: number }) {
    const whereCon: any = { isDeleted: false }
    if (data.title !== undefined) whereCon.title = ILike(`%${data.title}%`)
    if (data.categoryType !== undefined) whereCon.newsCategory = { type: data.categoryType }
    if (!data.take || data.take < 0) data.take = 10
    if (!data.index || data.index < 0) {
      data.index = 0
    } else data.index -= 1
    let res: any = await this.repo.findAndCount({
      where: whereCon,
      skip: data.index * data.take,
      take: data.take,
      select: { id: true, title: true, image: true, newsCategory: { name: true, type: true }, createdAt: true },
      order: { createdAt: 'DESC' },
      relations: { newsCategory: true },
    })
    for (let item of res[0]) {
      item.category = await item.newsCategory
      delete item.__newsCategory__
    }
    return {
      data: res[0],
      total: res[1],
    }
  }

  async listNewAppSale() {
    const newsCategory = await this.repoNewsCategory.findOne({
      where: { code: 'CTUD' },
      select: ['id'],
    })

    if (!newsCategory) {
      throw new Error('Không tìm thấy loại danh mục tin tức')
    }

    const newsList = await this.repo.find({
      where: {
        newsCategoryId: newsCategory.id,
        isDeleted: false,
      },
      select: ['id', 'title', 'image', 'content'],
    })

    return newsList
  }
}
