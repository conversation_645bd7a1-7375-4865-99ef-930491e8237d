import { Injectable, NotFoundException } from '@nestjs/common'
import { Request as IRequest } from 'express'
import * as path from 'path'
import { In, IsNull, Like, Not } from 'typeorm'
import { enumData, ERROR_NOT_FOUND_DATA, UPDATE_ACTIVE_SUCCESS, UPDATE_SUCCESS } from '../../../constants'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { omsApiHelper } from '../../../helpers/omsApiHelper'
import { ContractRepository } from '../../../repositories/core/contract.repository'
import { UploadFileService } from '../uploadFile/uploadFile.service'
import { ContractCreateDto, ContractFilterDto, ContractGenerateDto, ContractMediaTypeFilterDto, ContractMemberCardFilterDto } from './dto'
import { CreateContractDto } from './dto/createContract.dto'
import { v4 as uuidv4 } from 'uuid'
import * as fs from 'fs'
import puppeteer from 'puppeteer'
import { ContractUpdateDto } from './dto/contractUpdate.dto'
import { coreHelper } from '../../../helpers'
import { BankRepository, ItemComboRepository, ItemRepository } from '../../../repositories'
import { callApiHelper } from '../../../helpers/callApiHelper'

@Injectable()
export class ContractService {
  constructor(
    private repo: ContractRepository,
    private readonly uploadService: UploadFileService,
    private itemRepo: ItemRepository,
    private itemComboRepo: ItemComboRepository,
    private bankRepo: BankRepository,
  ) { }

  /** Tạo template hợp đồng */
  async createData(user: UserDto, data: ContractCreateDto, req: IRequest) {
    const infoA = JSON.stringify(data.infoA)
    delete data.infoA
    const contractCode = await this.codeTemplateDefault()
    const newEntity = this.repo.create({ ...data, createdBy: user.id, infoA: infoA, contractCode: contractCode })
    await this.repo.insert(newEntity)

    return { message: 'Tạo mới thành công' }
  }

  async updateData(user: UserDto, data: ContractUpdateDto) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    entity.infoA = JSON.stringify(data.infoA)
    entity.isCTV = data.isCTV
    entity.partnerId = data.partnerId
    entity.partnerName = data.partnerName
    entity.mediaCode = data.mediaCode
    entity.contractName = data.contractName
    entity.effectiveDate = data.effectiveDate
    entity.expirationDate = data.expirationDate
    entity.templateApplied = data.templateApplied
    if (data.term) entity.term = data.term
    entity.templateApplied = data.templateApplied
    entity.eContractType = data.eContractType
    entity.fileUrl = data.fileUrl
    entity.updatedBy = user?.id
    entity.updatedAt = new Date()
    const updatedEntity = await this.repo.update(entity.id, entity)

    return { message: UPDATE_SUCCESS }
  }

  async createContract(data: CreateContractDto[]) {
    for (const item of data) {
      const entity = await this.repo.findOne({ where: { id: item.id } })
      if (!entity) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
      if (item.sign && item.sign !== null) {
        // Upload sign (base64) to S3
        const base64Data = item.sign.split(';base64,').pop() // Lấy phần base64 từ chuỗi đầy đủ
        const buffer = Buffer.from(base64Data, 'base64') // Chuyển đổi base64 thành Buffer
        const file: Express.Multer.File = {
          buffer,
          originalname: `signature-${item.id}.png`, // Tên file giả lập
          fieldname: '',
          encoding: '',
          mimetype: 'image/png',
          size: buffer.length,
          stream: null,
          destination: '',
          filename: '',
          path: '',
        }

        const uploaded = await this.uploadService.uploadSingle(file)

        entity.sign = uploaded.fileUrl
        entity.term = entity.term.split('${sign}').join(`<img src="${uploaded.fileUrl}" width = "100px" height = "100px" />`)
        entity.isDeleted = false

        const browser = await puppeteer.launch({
          executablePath: '/usr/bin/chromium-browser',
          args: [
            '--no-sandbox', // Tắt sandbox (bắt buộc khi chạy trong Docker dưới quyền root)
            '--disable-setuid-sandbox', // Thêm tham số này để tắt setuid sandbox
          ],
        })
        const page = await browser.newPage()

        await page.setContent(entity.term, { waitUntil: 'networkidle0' })

        const tempFilePath = path.join(__dirname, `${uuidv4()}.pdf`)

        await page.pdf({
          path: tempFilePath,
          format: 'A4',
          printBackground: true,
        })

        await browser.close()

        const pdfBuffer = fs.readFileSync(tempFilePath)

        entity.eContractUrl = await this.uploadService.uploadSinglePDF(pdfBuffer)

        // Delete the local PDF file after upload
        fs.unlinkSync(tempFilePath)
      }
      entity.memberId = item.memberId
      entity.isDeleted = false
      entity.updatedAt = new Date()
      const updatedEntity = await this.repo.update(entity.id, entity)
    }

    return { message: UPDATE_SUCCESS }
  }

  async pagination(data: PaginationDto) {
    const whereCon: any = {}
    if (data.where.isTemplate == true) {
      whereCon.memberId = IsNull()
      whereCon.isDeleted = false
    }
    if (data.where.isTemplate == false) whereCon.memberId = Not(IsNull())
    if (data.where.contractCode) whereCon.contractCode = data.where.contractCode
    if (data.where.code) whereCon.contractCode = Like(`%${data.where.code}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    let result: any = await this.repo.findAndCount({
      where: whereCon,
      order: { createdAt: 'DESC' },
      skip: data.skip,
      take: data.take,
    })
    if (result[0].length === 0) return []
    for (let item of result[0]) {
      item.mediaName = enumData.SettingMedia[item.mediaCode]?.typeName
      item.eContractTypeName = enumData.EContractType[item.eContractType]?.name
      item.infoA = JSON.parse(item.infoA)
    }
    return result
  }

  public async updateIsDelete(id: string, user: UserDto) {
    const entity = await this.repo.findOne({ where: { id } })
    if (!entity) {
      throw new Error(ERROR_NOT_FOUND_DATA)
    }

    await this.repo.update({ id: entity.id }, { isDeleted: !entity.isDeleted, updatedBy: user.id })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  /** Tạo hợp đồng trạng thái chưa xử lí và trả về mẫu hợp đồng vừa tạo (isDeleted: false) */
  async generateTemplate(data: ContractGenerateDto, req: IRequest) {
    const whereCon: any = { isDeleted: false, eContractType: data.eContractType, isCTV: data.isCTV, memberId: IsNull() }
    req.user = {}
    whereCon.partnerId = IsNull()
    if (data.partnerId) whereCon.partnerId = data.partnerId

    const lstNewContract = []
    const STT = await this.repo.count({ where: { isCTV: data.isCTV, memberId: Not(IsNull()) } })
    for (const media of data.mediaCodes) {
      whereCon.mediaCode = media
      const templateContract: any = await this.repo.findOne({
        where: { ...whereCon },
      })
      const mediaItem = enumData.SettingMedia[media]

      const today = new Date()
      let bank = null
      if (data.party.bankCode) bank = await this.bankRepo.findOne({ where: { code: data.party?.bankCode, isDeleted: false } })
      const geo: any = {}
      if (data.party?.provinceCode) geo.province = await callApiHelper.processGeoApiRequest(`api/p/${data.party.provinceCode}`)
      const listWard = await callApiHelper.processGeoApiRequest(`api/w`)
      if (listWard !== null && listWard.length > 0) {
        if (data.party?.wardCode && data.party.wardCode !== null) {
          const wardCodeNumber = Number(data.party?.wardCode) // Convert wardCode to a number
          geo.ward = listWard.find((x) => x.code == wardCodeNumber) // Compare using the number
        }
      }

      if (data.party?.districtCode) geo.district = await callApiHelper.processGeoApiRequest(`api/d/${data.party.districtCode}`)

      if (!templateContract) throw new Error(ERROR_NOT_FOUND_DATA)
      const newCode = await this.codeDefault(templateContract.contractCode)
      templateContract.term = templateContract.term.split('${day}').join(today.getDate())
      templateContract.term = templateContract.term.split('${month}').join(today.getMonth() + 1)
      templateContract.term = templateContract.term.split('${year}').join(today.getFullYear())
      if (!data.party.address || !geo.ward?.name || !geo.district?.name || !geo.province?.name)
        templateContract.term = templateContract.term.split('${address}').join('')
      if (data.party.address && geo.ward?.name && geo.district?.name && geo.province?.name)
        templateContract.term = templateContract.term
          .split('${address}')
          .join(data.party.address + ' ' + geo.ward?.name + ' ' + geo.district?.name + ' ' + geo.province?.name)

      templateContract.term = templateContract.term.split('${media}').join(mediaItem?.typeName || '')
      templateContract.term = templateContract.term.split('${name}').join(data.party.fullName || '')
      templateContract.term = templateContract.term.split('${phone}').join(data.party.phone || '')
      templateContract.term = templateContract.term.split('${representative}').join(data.party.representative || '')
      templateContract.term = templateContract.term.split('${position}').join(data.party.position || '')
      templateContract.term = templateContract.term.split('${fax}').join(data.party.fax || '')
      templateContract.term = templateContract.term.split('${bankAccount}').join(data.party.bankAccountName || '')
      templateContract.term = templateContract.term.split('${bank}').join(bank?.name || '')
      templateContract.term = templateContract.term.split('${bankNumber}').join(data.party.bankAccountNumber || '')
      templateContract.term = templateContract.term.split('${personalId}').join(data.party.personalId || '')
      templateContract.term = templateContract.term.split('${taxCode}').join(data.party.taxCode || '')
      templateContract.term = templateContract.term.split('${email}').join(data.party.email || '')
      templateContract.term = templateContract.term.split('${part}').join(data.party.part || '')
      templateContract.term = templateContract.term.split('${invitePhone}').join(data.invitePhone || '')
      templateContract.term = templateContract.term.split('${code}').join(newCode)
      if (!templateContract.effectiveDate) templateContract.term = templateContract.term.split('${effectiveDate}').join('')
      templateContract.term = templateContract.term
        .split('${effectiveDate}')
        .join(
          `${new Date(templateContract.effectiveDate).getDate()}/${new Date(templateContract.effectiveDate).getMonth() + 1}/${new Date(
            templateContract.effectiveDate,
          ).getFullYear()}`,
        )
      if (!templateContract.expirationDate) templateContract.term = templateContract.term.split('${expirationDate}').join('Vô thời hạn')
      templateContract.term = templateContract.term
        .split('${expirationDate}')
        .join(
          `${new Date(templateContract.expirationDate).getDate()}/${new Date(templateContract.expirationDate).getMonth() + 1}/${new Date(
            templateContract.expirationDate,
          ).getFullYear()}` || '',
        )

      templateContract.term = templateContract.term.split('${deliveryDate}').join('24')

      templateContract.term = templateContract.term.split('${STT}').join(STT + 1 || '')

      if (data.card) {
        if (data.card && data.card.lstCombo) {
          const lstProduct = await this.itemComboRepo.find({
            where: { itemId: In(data.card.lstCombo.map((x) => x.comboId)) },
            relations: { itemInCombo: { supplier: true, unit: true } },
          })
          const lstItem = lstProduct.mapAndDistinct((x: any) => x.__itemInCombo__)
          let dataProduct = ''
          lstItem.forEach((item, index) => {
            const filteredProducts = lstProduct.filter((x) => x.itemInComboId === item.id)
            item.quantity = filteredProducts.reduce((sum, x) => sum + x.quantity, 0)

            // Sử dụng `index`
            dataProduct += `<tr><td>${index}</td><td>${item.name}</td><td>${item.__supplier__?.name}</td><td>${item.quantity}</td><td>${item.__unit__?.name}</td><td></td></tr>`
          })

          templateContract.term = templateContract.term.split('${product}').join(`<table border="1" style="border-collapse: collapse; width: 100%;">
              <tr>
                <th>STT</th>
                <th>Tên hàng hóa</th>
                <th>Xuất xứ</th>
                <th>Quy cách</th>
                <th>Đơn vị tính</th>
                <th>Chất lượng</th>
              </tr>
                ${dataProduct}
            </table>`)
        }

        const cardType: any = await omsApiHelper.getCardType(req, { pageIndex: 1, pageSize: 9999999 })
        const cardPeriod: any = await omsApiHelper.getCardPeriod(req, { pageIndex: 1, pageSize: 9999999 })
        const card: any = await omsApiHelper.getCard(req, { pageIndex: 1, pageSize: 999999 })

        let dataCard: any = ''
        for (const item of card.data) {
          let checked = false
          const type = cardType.data.find((x) => x.id === item.typeId)
          const period = cardPeriod.data.find((x) => x.id === item.periodId)
          if (item.id === data.card.id) {
            checked = true
          }
          let number = 1
          switch (period.unit) {
            case 'MONTH':
              number = 1
              break
            case 'WEEK':
              number = 4
              break
          }

          dataCard =
            dataCard +
            `<tr><td>Thẻ tiêu dùng "${enumData.EBusinessType[item.businessType]?.name}"</td><td>${item.name}</td><td>${new Intl.NumberFormat(
              'vi-VN',
              {
                style: 'currency',
                currency: 'VND',
              },
            ).format(item.parValue)}</td><td>Không giới hạn</td><td>${number} lần/ 1 tháng</td><td><input type="checkbox" id="${item.id}" name="${item.id
            }" ${checked ? 'checked' : ''}></td></tr>`
        }
        templateContract.term = templateContract.term.split('${card}').join(`<table border="1" style="border-collapse: collapse; width: 100%;">
            <tr>
              <th>Loại thẻ</th>
              <th>Tên thẻ</th>
              <th>Giá trị thẻ (VND) </th>
              <th>Thời gian phát hành</th>
              <th>Số lần giao hàng trong tháng</th>
              <th>Sản phẩm Bên Mua lựa chọn</th>
            </tr>
              ${dataCard}
          </table>`)
      }

      // Bên A
      const infoA = JSON.parse(templateContract.infoA)
      if (infoA !== null) {
        templateContract.term = templateContract.term.split('${nameA}').join(infoA.name || '')
        templateContract.term = templateContract.term.split('${personalIdA}').join(infoA.personalId || '')
        templateContract.term = templateContract.term.split('${taxCodeA}').join(infoA.taxCode || '')
        templateContract.term = templateContract.term.split('${phoneA}').join(infoA.phone || '')
        templateContract.term = templateContract.term.split('${addressA}').join(infoA.address || '')
        templateContract.term = templateContract.term.split('${representativeA}').join(infoA.representative || '')
        templateContract.term = templateContract.term.split('${positionA}').join(infoA.position || '')
        templateContract.term = templateContract.term.split('${bankA}').join(infoA.bank || '')
        templateContract.term = templateContract.term.split('${bankNumberA}').join(infoA.bankNumber || '')
        templateContract.term = templateContract.term.split('${bankAccountA}').join(infoA.bankAccount || '')
        templateContract.term = templateContract.term.split('${faxA}').join(infoA.fax || '')
        templateContract.term = templateContract.term.split('${emailA}').join(infoA.email || '')
        templateContract.term = templateContract.term.split('${partA}').join(infoA.part || '')
      }

      const browser = await puppeteer.launch({
        executablePath: '/usr/bin/chromium-browser',
        args: [
          '--no-sandbox', // Tắt sandbox (bắt buộc khi chạy trong Docker dưới quyền root)
          '--disable-setuid-sandbox', // Thêm tham số này để tắt setuid sandbox
        ],
      })
      const page = await browser.newPage()
      let term = templateContract.term
      if (!data.party.personalId || data.party.personalId === null) term = term.split('${personalId}').join('')
      if (!data.party.address || !geo.ward?.name || !geo.district?.name || !geo.province?.name) term = term.split('${address}').join('')
      await page.setContent(templateContract.term, { waitUntil: 'networkidle0' })

      const tempFilePath = path.join(__dirname, `${uuidv4()}.pdf`)

      await page.pdf({
        path: tempFilePath,
        format: 'A4',
        printBackground: true,
      })

      await browser.close()

      const pdfBuffer = fs.readFileSync(tempFilePath)

      templateContract.termURL = await this.uploadService.uploadSinglePDF(pdfBuffer)

      fs.unlinkSync(tempFilePath)

      const newContract = { ...templateContract }
      newContract.id = uuidv4()
      newContract.infoA
      newContract.bank = bank?.name
      newContract.bankAccount = data.party.bankAccountName
      newContract.bankNumber = data.party.bankAccountNumber
      newContract.personalId = data.party.personalId
      newContract.taxCode = data.party.taxCode
      newContract.representative = data.party.representative
      newContract.position = data.party.position
      newContract.fax = data.party.fax
      newContract.phone = data.party.phone
      newContract.address = data.party.address
      newContract.partyName = data.party.fullName
      if (data.referralCode && data.referralCode !== null) newContract.referralCode = data.referralCode
      newContract.memberId = data.party.id || data.party.memberId
      newContract.isDeleted = true // Vì hợp đồng chưa xử lí
      newContract.contractCode = newCode
      newContract.invitePhone = data.invitePhone || null
      newContract.createdAt = new Date()
      if (data.card?.id) newContract.cardId = data.card.id
      await this.repo.insert(newContract)
      lstNewContract.push(newContract)
    }

    const tempLstReturnContract = lstNewContract
    for (let item of tempLstReturnContract) {
      item.term = item.term.split('${sign}').join('')
    }

    return { data: tempLstReturnContract, total: data.mediaCodes.length }
  }

  async findByMember(data: FilterOneDto) {
    const res: any = await this.repo.findAndCount({
      where: { memberId: data.id, isDeleted: false, sign: Not(IsNull()) },
      skip: data.skip,
      take: data.take,
      select: {
        id: true,
        contractCode: true,
        contractName: true,
        partnerName: true,
        eContractType: true,
        mediaCode: true,
        partyName: true,
        createdAt: true,
        updatedAt: true,
        createdBy: true,
        eContractUrl: true,
      },
    })
    const lstEContractType = coreHelper.convertObjToArray(enumData.EContractType)

    for (const item of res[0]) {
      item.eContractTypeName = lstEContractType.find((x) => x.code == item.eContractType).name
      item.mediaName = enumData.SettingMedia[item.mediaCode]?.typeName
      item.createdBy = item.partyName

      if (item.eContractUrl) {
        const parsedUrl = JSON.parse(item.eContractUrl)
        item.fileUrl = parsedUrl.fileUrl
      } else {
        item.fileUrl = null
      }
    }
    return {
      data: res[0],
      total: res[1]
    }
  }

  private async codeTemplateDefault() {
    const today = new Date()
    const ddMMYY = `${today.getDate().toString().padStart(2, '0')}${(today.getMonth() + 1).toString().padStart(2, '0')}${today
      .getFullYear()
      .toString()
      .slice(-2)}`

    const objData = await this.repo.findOne({
      where: { contractCode: Like(`HD/${ddMMYY}/%`) },
      order: { contractCode: 'DESC' },
    })
    let sortString = '0'
    if (objData) {
      /** code.length + 1 = ABC + /
       */
      sortString = objData.contractCode.substring(objData.contractCode.length - 3, objData.contractCode.length)
    }
    const lastSort = parseInt(sortString)
    sortString = ('00' + (lastSort + 1)).slice(-3)

    return `HD/${ddMMYY}/` + sortString
  }

  private async codeDefault(code: string) {
    const today = new Date()
    const ddMMYY = `${today.getDate().toString().padStart(2, '0')}${(today.getMonth() + 1).toString().padStart(2, '0')}${today
      .getFullYear()
      .toString()
      .slice(-2)}`
    code = code.replace(/\/.*/, `/${ddMMYY}`)

    const objData = await this.repo.findOne({
      where: { contractCode: Like(`%${code}/%`), memberId: Not(IsNull()) },
      order: { contractCode: 'DESC' },
    })
    let sortString = '0'
    if (objData) {
      sortString = objData.contractCode.substring(code.length + 1, code.length + 5)
    }
    const lastSort = parseInt(sortString)
    sortString = ('000' + (lastSort + 1)).slice(-4)

    return code + '/' + sortString
  }

  async findDetail(data: FilterOneDto) {
    const res: any = await this.repo.findOne({ where: { id: data.id, isDeleted: false } })
    res.infoA = JSON.parse(res.infoA)
    res.mediaName = enumData.SettingMedia[res.mediaCode]?.typeName
    return res
  }

  async findByMemberCard(data: ContractMemberCardFilterDto) {
    return await this.repo.find({
      where: { memberId: data.memberId, cardId: data.cardId, isDeleted: false },
    })
  }
  async findByMediaType(data: ContractMediaTypeFilterDto) {
    return await this.repo.find({
      where: { memberId: data.memberId, isCTV: true, isDeleted: false, sign: Not(IsNull()) },
    })
  }

  async findContract(data: ContractFilterDto) {
    let where: any = {}
    if (data.memberId && data.memberId.length > 0) where.memberId = In(data.memberId)
    if (data.isCTV && data.isCTV.length > 0) where.isCTV = In(data.isCTV)
    if (data.eContractType && data.eContractType.length > 0) where.eContractType = In(data.eContractType)
    if (data.serviceType && data.serviceType.length > 0) where.serviceType = In(data.serviceType)
    if (data.partnerId && data.partnerId.length > 0) where.partnerId = In(data.partnerId)
    if (data.mediaCode && data.mediaCode.length > 0) where.mediaCode = In(data.mediaCode)
    const result = await this.repo.find({
      where: { ...where, isDeleted: false },
      order: { createdAt: 'DESC' },
    })

    return result
  }
}
