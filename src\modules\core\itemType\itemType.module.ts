import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import { ItemTypeRepository, MediaRepository } from '../../../repositories'
import { ItemTypeController } from './itemType.controller'
import { ItemTypeService } from './itemType.service'
import { ItemTypePublicController } from './itemTypePublic.controller'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([ItemTypeRepository, MediaRepository])],
  controllers: [ItemTypeController, ItemTypePublicController],
  providers: [ItemTypeService],
  exports: [ItemTypeService],
})
export class ItemTypeModule { }
