import { Module } from '@nestjs/common'
import { EmployeeController } from './employee.controller'
import { EmployeeService } from './employee.service'
import { AuthModule } from '../auth/auth.module'
import { UserModule } from '../user/user.module'
import { TypeOrmExModule } from '../../../typeorm'
import { DepartmentRepository, EmployeeRepository, UserRepository } from '../../../repositories'
import { EmployeePublicController } from './employeePublic.controller'
import { ActionLogModule } from '../actionLog/actionLog.module'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([EmployeeRepository, DepartmentRepository, UserRepository]), AuthModule, UserModule, ActionLogModule],
  controllers: [EmployeeController, EmployeePublicController],
  providers: [EmployeeService],
  exports: [EmployeeService],
})
export class EmployeeModule { }
