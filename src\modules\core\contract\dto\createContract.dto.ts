import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString } from 'class-validator'

export class CreateContractDto {
  @ApiProperty({ description: 'Id hợp đồng' })
  @IsNotEmpty()
  @IsString()
  id: string

  @ApiProperty({ description: 'Chữ ký dạng base64' })
  @IsOptional()
  sign: string

  @ApiProperty({ description: 'Id member' })
  @IsOptional()
  memberId: string
}
