import { Body, Controller, Post, Req, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { Request as IRequest } from 'express'
import { EmployeeImportDto } from './dto'
import { EmployeeCreateDto } from './dto/employeeCreate.dto'
import { EmployeeUpdateDto } from './dto/employeeUpdate.dto'
import { EmployeeService } from './employee.service'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { UpdatePasswordDto } from '../auth/dto'
import { ApeAuthGuard } from '../../survey/common/guards'
import { CurrentUser } from '../../common/decorators'
@UseGuards(ApeAuthGuard)
@ApiBearerAuth()
@ApiTags('Employee')
@Controller('employee_public')
export class EmployeePublicController {
  constructor(private readonly service: EmployeeService) {}

  @ApiOperation({ summary: 'Lấy ds nhân viên' })
  @Post('find')
  public async find() {
    return await this.service.find()
  }

  @ApiOperation({ summary: 'Lấy một dữ liệu chi tiết' })
  @Post('find_detail')
  public async findDetail(@Body() data: FilterOneDto) {
    return await this.service.findDetail(data)
  }

  @ApiOperation({ summary: 'Lấy danh sách theo trang để hiển thị trên giao diện.' })
  @Post('pagination')
  public async pagination(@Body() data: PaginationDto, @Req() req: IRequest) {
    return await this.service.pagination(data)
  }

  @ApiOperation({ summary: 'Tạo mới một dữ liệu' })
  @Post('create_data')
  public async createData(@Body() data: EmployeeCreateDto, @Req() req: IRequest) {
    return await this.service.createData(data)
  }
  @ApiOperation({ summary: 'Cập nhật thông tin của dữ liệu' })
  @Post('update_data')
  public async updateData(@Body() data: EmployeeUpdateDto, @Req() req: IRequest) {
    return await this.service.updateData(data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái kích hoạt' })
  @Post('update_active')
  public async updateActive(@Body() data: FilterOneDto, @Req() req: IRequest) {
    return await this.service.updateActive(data, req)
  }

  @ApiOperation({ summary: 'Danh sách nhân viên' })
  @Post('load_data')
  public async loadData() {
    return await this.service.loadData()
  }

  @ApiOperation({ summary: 'Danh sách nhân viên từ phòng ban' })
  @Post('load_data_by_department')
  public async loadDataByDepartment(@Body() data: FilterOneDto) {
    return await this.service.loadDataByDepartment(data)
  }

  @ApiOperation({ summary: 'Cập nhật mật khẩu' })
  @Post('update_password')
  public async updatePassword(@Body() data: UpdatePasswordDto, @Req() req: IRequest) {
    return await this.service.updatePassword(data, req)
  }

  @ApiOperation({ summary: 'Tạo mới bằng import excel' })
  @Post('create_data_by_excel')
  public async importData(@Body() data: EmployeeImportDto[], @Req() req: IRequest, @CurrentUser() user: UserDto) {
    return await this.service.createDataByExcel(data, user)
  }
}
