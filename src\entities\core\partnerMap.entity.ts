import { Entity, Column } from 'typeorm'
import { ApiProperty } from '@nestjs/swagger'
import { BaseEntity } from './base.entity'

// Quy cách đóng gói
@Entity('partner_map')
export class PartnerMapEntity extends BaseEntity {
  @Column({ type: 'varchar', nullable: false })
  @ApiProperty()
  parentId: string

  @Column({ type: 'varchar', nullable: false })
  @ApiProperty()
  childId: string

  @Column({ type: 'text', nullable: true })
  @ApiProperty()
  role: string
}
