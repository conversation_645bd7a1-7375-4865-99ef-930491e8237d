import { MigrationInterface, QueryRunner } from 'typeorm'

export class updateLuckyBill1756387390394 implements MigrationInterface {
  name = 'updateLuckyBill1756387390394'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "lucky_bill_apply_region" DROP CONSTRAINT "FK_44618739ba5cab859793759a44e"`)
    await queryRunner.query(`ALTER TABLE "lucky_bill_apply_region" RENAME COLUMN "districtId" TO "cityIds"`)
    await queryRunner.query(
      `ALTER TABLE "lucky_bill_apply_region" ADD CONSTRAINT "FK_44618739ba5cab859793759a44e" FOREIGN KEY ("luckyBillConfigId") REFERENCES "lucky_bill_config"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "lucky_bill_apply_region" DROP CONSTRAINT "FK_44618739ba5cab859793759a44e"`)
    await queryRunner.query(`ALTER TABLE "lucky_bill_apply_region" RENAME COLUMN "cityIds" TO "districtId"`)
    await queryRunner.query(
      `ALTER TABLE "lucky_bill_apply_region" ADD CONSTRAINT "FK_44618739ba5cab859793759a44e" FOREIGN KEY ("luckyBillConfigId") REFERENCES "lucky_bill_config"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }
}
