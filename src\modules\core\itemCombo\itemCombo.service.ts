import { Injectable } from '@nestjs/common'
import { ItemComboRepository, ItemPriceRepository, ItemRepository, OutboundDetailRepository, OutboundRepository } from '../../../repositories'
import { FilterOneDto, UserDto } from '../../../dto'
import { In, Like, Raw } from 'typeorm'
import { CancelComboDto } from './dto'
import {
  InboundDetailEntity,
  InboundEntity,
  InboundHistoryEntity,
  OutboundDetailEntity,
  OutboundEntity,
  OutboundHistoryEntity,
  ItemDetailEntity,
  ItemEntity,
  ProductInventoryHistoryEntity,
  UnitEntity,
  WarehouseProductDetailEntity,
  WarehouseProductEntity,
} from '../../../entities'
import { coreHelper } from '../../../helpers'
import { v4 as uuidv4 } from 'uuid'
import * as moment from 'moment'
import { ERROR_CODE_GEN_TAKEN, enumData } from '../../../constants'
import { Request as IRequest } from 'express'
import { ActionLogCreateDto } from '../actionLog/dto'
import { ActionLogService } from '../actionLog/actionLog.service'

@Injectable()
export class ItemComboService {
  constructor(
    private readonly repo: ItemComboRepository,
    private readonly productRepo: ItemRepository,
    private readonly productPriceRepo: ItemPriceRepository,
    private readonly outboundDetailRepo: OutboundDetailRepository,
    private readonly outboundRepo: OutboundRepository,
    private actionLogService: ActionLogService,
  ) {}

  async loadProductInCombo(data: FilterOneDto) {
    // Tìm sản phẩm
    const checkProd: any = await this.productRepo.findOne({
      where: { id: data.id },
      relations: { itemCombo: true },
    })
    if (!checkProd) throw new Error(`Không tìm thấy sản phẩm combo!`)

    const lstProductCombinationId: string[] = checkProd.__itemCombo__?.map((e) => e.itemInComboId)
    const dicProductInCombo = coreHelper.arrayToObject(checkProd.__itemCombo__, 'itemInComboId')
    const rs: any = await this.productRepo.find({
      where: { id: In(lstProductCombinationId), isDeleted: false },
      relations: { unit: true, itemGroup: true, itemType: true, itemCategory: true },
    })
    for (let prod of rs) {
      prod.unitCode = prod.__unit__?.code
      prod.unitName = prod.__unit__?.name

      prod.itemGroupCode = prod.__itemGroup__?.code
      prod.itemGroupName = prod.__itemGroup__?.name

      prod.itemTypeCode = prod.__itemType__?.code
      prod.itemTypeName = prod.__itemType__?.name

      prod.itemCategoryCode = prod.__itemCategory__?.code
      prod.itemCategoryName = prod.__itemCategory__?.name
      prod.quantityInCombo = dicProductInCombo[prod.id].quantity

      delete prod.__unit__
      delete prod.__itemGroup__
      delete prod.__itemType__
      delete prod.__itemCategory__
    }

    return rs
  }

  /** Huỷ combo */
  async cancelCombo(data: CancelComboDto, user: UserDto, req: IRequest) {
    // Tìm sản phẩm
    const checkProductCombo: any = await this.productRepo.findOne({
      where: { id: data.id },
      relations: { itemCombo: true },
    })
    if (!checkProductCombo) throw new Error(`Không tìm thấy sản phẩm combo!`)

    // #region kiểm tra điều kiện
    const dicProductInCombo: Record<string, any> = {}
    const dicInboundProduct: Record<string, any> = {}
    const dicProductPrice: Record<string, any> = {}
    {
      // Dic sản phẩm trong combo
      {
        const lstProductCombinationId: string[] = checkProductCombo.__itemCombo__?.map((e) => e.itemInComboId)
        const lstProductInCombo: ItemEntity[] = await this.productRepo.find({
          where: { id: In(lstProductCombinationId), isDeleted: false },
          select: { id: true, code: true, name: true },
        })
        for (let prod of lstProductInCombo) dicProductInCombo[prod.id] = prod

        const lstProdPrice: any[] = await this.productPriceRepo.find({ where: { itemId: In(lstProductCombinationId), isFinal: true } })
        for (let prodPrice of lstProdPrice) {
          dicProductPrice[prodPrice.itemId] = prodPrice
        }
      }
      // Dic sản phẩm nhập kho
      {
        const lstProductId: string[] = data.lstProductInbound?.map((e) => e.productId)
        const lstProductInCombo: ItemEntity[] = await this.productRepo.find({
          where: { id: In(lstProductId), isDeleted: false },
          select: { id: true, code: true, name: true, unitId: true },
        })
        for (let prod of lstProductInCombo) dicInboundProduct[prod.id] = prod
      }
      for (let prod of data.lstProductInbound) {
        if (!dicProductInCombo[prod.productId]) {
          throw new Error(`Sản phẩm [ ${dicInboundProduct[prod.productId]} ] không có trong combo [ ${checkProductCombo.name} ]!`)
        }
      }
    }
    // #endregion

    // Lấy tất cả chi tiết phiếu xuất kho sản phẩm combo
    const lstOutboundDetail = await this.outboundDetailRepo.find({ where: { productId: data.id } })

    // Id của phiếu xuất kho sản phẩm combo
    const lstOutboundId = lstOutboundDetail.mapAndDistinct((e) => e.outboundId)

    let dicOutbound: Record<string, any> = {}
    // Danh sách phiếu xuất kho sản phẩm combo
    const lstOutbound: any = await this.outboundRepo.find({
      where: { id: In(lstOutboundId) },
      select: { id: true, warehouseId: true, employeeId: true },
    })
    dicOutbound = coreHelper.arrayToObject(lstOutbound)

    // Sinh code theo quy tắc ddmmyyy_xxxx (xxxx tăng dần) VD: 01102023_0001
    const curDate = moment(new Date()).format('DDMMYYYY')
    let genCode: string
    // Đếm số lượng phiếu xuất kho trong ngày
    let count = await this.outboundRepo.count({ where: { code: Like(`${curDate}_%`) }, select: { id: true } })
    if (!count) count = 0

    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(OutboundEntity)
      const outboundDetailRepo = trans.getRepository(OutboundDetailEntity)
      const outboundHistoryRepo = trans.getRepository(OutboundHistoryEntity)
      const productRepo = trans.getRepository(ItemEntity)
      const productDetailRepo = trans.getRepository(ItemDetailEntity)
      const warehouseProductRepo = trans.getRepository(WarehouseProductEntity)
      const warehouseProductDetailRepo = trans.getRepository(WarehouseProductDetailEntity)

      // #region Trừ tồn kho của sản phẩm combo
      await productRepo.update({ id: data.id }, { quantity: 0 })
      await productDetailRepo.update({ itemId: data.id }, { quantity: 0 })
      await warehouseProductRepo.update({ productId: data.id }, { quantity: 0 })
      await warehouseProductDetailRepo.update({ productId: data.id }, { quantity: 0 })
      // #endregion

      // #region tạo phiếu xuất kho cho sản phẩm combo
      for (let obd of lstOutboundDetail) {
        genCode = this.genCode(count)

        const outbound = new OutboundEntity()
        outbound.id = uuidv4()
        outbound.code = genCode
        outbound.type = enumData.OutboundType.INTERNAL_WAREHOUSE.code
        outbound.warehouseId = dicOutbound[obd.outboundId].warehouseId
        outbound.employeeId = dicOutbound[obd.outboundId].employeeId
        outbound.createdAt = new Date()
        outbound.status = enumData.OutboundStatus.APPROVED.code
        outbound.approvedBy = user.id
        outbound.approvedDate = new Date()
        outbound.createdBy = user.id
        await repo.insert(outbound)

        const outboundDetail = new OutboundDetailEntity()
        outboundDetail.outboundId = outbound.id
        outboundDetail.productId = obd.productId
        outboundDetail.isCombo = true
        outboundDetail.quantity = obd.quantity
        outboundDetail.createdBy = user.id
        await outboundDetailRepo.insert(outboundDetail)

        const outboundHistory = new OutboundHistoryEntity()
        outboundHistory.outboundId = outbound.id
        outboundHistory.description = `Nhân viên tạo mới phiếu xuất kho đã duyệt khi huỷ combo`
        await outboundHistoryRepo.insert(outboundHistory)

        count++
      }
      // #endregion

      // #region Nhập kho cho sản phẩm của combo
      const inboundRepo = trans.getRepository(InboundEntity)
      const inboundDetailRepo = trans.getRepository(InboundDetailEntity)
      const inboundHistoryRepo = trans.getRepository(InboundHistoryEntity)
      const unitRepo = trans.getRepository(UnitEntity)
      const productInventoryHistoryRepo = trans.getRepository(ProductInventoryHistoryEntity)

      const quantityNew = coreHelper.selectSum(data.lstProductInbound, 'quantity')
      const lstProductWH = data.lstProductInbound.mapAndDistinct((e) => e.productId)
      let quantityOld: any = 0
      let lstOldWh = await warehouseProductRepo.find({ where: { warehouseId: data.warehouseId, productId: In(lstProductWH) } })
      if (lstOldWh.length > 0) {
        quantityOld = coreHelper.selectSum(lstOldWh, 'quantity')
      }

      let code = 'NK' + '_' + moment(new Date()).format('DDMMYYYY') + '_'
      const objData = await inboundRepo.findOne({
        where: { code: Like(`%${code}%`) },
        order: { code: 'DESC' },
      })
      let sortString = '0'
      if (objData) {
        sortString = objData.code.substring(code.length, code.length + 4)
      }
      const lastSort = parseInt(sortString)
      sortString = ('000' + (lastSort + 1)).slice(-4)

      // Kiểm tra code đã tồn tại chưa
      const checkCodeExist = await inboundRepo.findOne({ where: { code }, select: { id: true } })
      if (checkCodeExist) throw new Error(ERROR_CODE_GEN_TAKEN)

      const inboundNew = new InboundEntity()
      inboundNew.warehouseId = data.warehouseId
      inboundNew.type = enumData.InboundType.ENTER_INTERNAL_WAREHOUSE.code
      inboundNew.code = code + sortString
      inboundNew.totalPrice = 0
      inboundNew.totalPriceVND = 0
      inboundNew.status = enumData.InboundStatus.Approved.code
      inboundNew.createdBy = user.id
      inboundNew.createdAt = new Date()
      inboundNew.approvedBy = user.id
      inboundNew.approvedDate = new Date()
      inboundNew.id = uuidv4()
      await inboundRepo.insert(inboundNew)

      // Lưu lại lịch sử inbound
      let arr = new Array()

      // Lọc qua danh sách detail và tạo detail
      for (let item of data.lstProductInbound) {
        const unitObj: any = await unitRepo.findOne({ where: { id: dicInboundProduct[item.productId].unitId, isDeleted: false } })
        if (!unitObj) throw new Error(`Đơn vị tính không còn tồn tại!`)

        const inboundDetailNew = new InboundDetailEntity()
        inboundDetailNew.inboundId = inboundNew.id
        inboundDetailNew.productId = item.productId
        inboundDetailNew.productCode = dicInboundProduct[item.productId].code
        inboundDetailNew.productName = dicInboundProduct[item.productId].name
        inboundDetailNew.unitId = dicInboundProduct[item.productId].unitId
        inboundDetailNew.baseUnit = unitObj.baseUnit || 0
        inboundDetailNew.code = code + sortString
        inboundDetailNew.quantity = +item.quantity || 0
        inboundDetailNew.totalQuantity = 0
        inboundDetailNew.price = 0
        inboundDetailNew.costPrice = +dicProductPrice[item.productId]?.priceCapital || 0
        inboundDetailNew.priceVND = 0
        inboundDetailNew.expiryDate = item?.expiryDate ? new Date(item.expiryDate) : null
        inboundDetailNew.manufactureDate = item?.manufactureDate ? new Date(item.manufactureDate) : null
        inboundDetailNew.totalPrice = +inboundDetailNew.price * +inboundDetailNew.quantity
        inboundDetailNew.totalPriceVND = 0
        inboundDetailNew.lotNumber = item.lotNumber
        inboundDetailNew.buyPrice = 0
        inboundDetailNew.buyPriceVND = 0
        inboundDetailNew.createdBy = user.id
        await inboundDetailRepo.insert(inboundDetailNew)

        arr.push({
          'Mã sản phẩm': inboundDetailNew.productCode,
          'Tên sản phẩm': inboundDetailNew.productName,
          'Giá mua': inboundDetailNew.buyPriceVND.toLocaleString('vi', { style: 'currency', currency: 'VND' }),
          'Giá nhập': inboundDetailNew.priceVND.toLocaleString('vi', { style: 'currency', currency: 'VND' }),
          'Số lượng nhập': inboundDetailNew.quantity,
          'Số lượng nhập theo đvcs': inboundDetailNew.totalQuantity,
          'Hạn sử dụng': inboundDetailNew.expiryDate ? moment(inboundDetailNew.expiryDate).format('YYYY-MM-DD') : '',
          'Ngày sản xuất': inboundDetailNew.manufactureDate ? moment(inboundDetailNew.manufactureDate).format('YYYY-MM-DD') : '',
        })

        let warehouseProduct: any = await warehouseProductRepo.findOne({ where: { warehouseId: data.warehouseId, productId: item.productId } })
        if (!warehouseProduct) {
          const warehouseProductNew = new WarehouseProductEntity()
          warehouseProductNew.warehouseId = data.warehouseId
          warehouseProductNew.productId = item.productId
          warehouseProductNew.quantity = item.quantity
          warehouseProductNew.createdAt = new Date()
          warehouseProductNew.createdBy = user.id
          warehouseProduct = await warehouseProductRepo.save(warehouseProductNew)
        } else {
          const quantityOld = +warehouseProduct.quantity
          const quantityNew = quantityOld + +item.quantity
          warehouseProduct.quantity = quantityNew
          warehouseProduct = await warehouseProductRepo.save(warehouseProduct)
        }

        const dateStr = moment(item.expiryDate).format('YYYY-MM-DD')
        const ds = new Date(new Date(item.expiryDate).setHours(0, 0, 0, 0))
        const de = new Date(new Date(item.expiryDate).setHours(23, 59, 59, 99))
        let warehouseProductDetail: any = await warehouseProductDetailRepo.findOne({
          where: {
            warehouseId: data.warehouseId,
            productId: item.productId,
            expiryDate: Raw(
              (alias) => `(${alias}) BETWEEN ("${moment(ds).format('YYYY-MM-DD HH:mm:ss')}") AND ("${moment(de).format('YYYY-MM-DD HH:mm:ss')}")`,
            ),
          },
        })
        if (!warehouseProductDetail) {
          const warehouseProductDetailNew = new WarehouseProductDetailEntity()
          warehouseProductDetailNew.warehouseId = data.warehouseId
          warehouseProductDetailNew.productId = item.productId
          warehouseProductDetailNew.warehouseProductId = warehouseProduct.id
          warehouseProductDetailNew.quantity = item.quantity
          warehouseProductDetailNew.manufactureDate = item.manufactureDate
          warehouseProductDetailNew.expiryDate = item.expiryDate
          warehouseProductDetailNew.createdAt = new Date()
          warehouseProductDetailNew.createdBy = user.id
          warehouseProductDetail = await warehouseProductDetailRepo.save(warehouseProductDetailNew)
        } else {
          const quantityDetailNew = +warehouseProductDetail.quantity + +item.quantity
          warehouseProductDetail.quantity = quantityDetailNew
          warehouseProductDetail = await warehouseProductDetailRepo.save(warehouseProductDetail)
        }

        // #region Lưu lịch sử
        const his = new ProductInventoryHistoryEntity()
        his.warehouseId = data.warehouseId
        his.warehouseProductId = warehouseProduct.id
        his.warehouseProductDetailId = warehouseProductDetail.id
        his.productId = item.productId
        his.inboundId = inboundNew.id
        // his.inboundDetailId = item.id
        his.quantity = quantityOld
        his.quantityNew = quantityNew
        his.description = `Duyệt PNK [${inboundNew.code}]<br>Nhập thêm: ${item.quantity}<br>HSD ${dateStr}`
        his.createdAt = new Date()
        his.createdBy = user.id
        await productInventoryHistoryRepo.insert(his)
        // #endregion

        let product = await productRepo.findOne({ where: { id: item.productId, isDeleted: false } })
        if (!product) throw new Error(`Sản phẩm ${dicInboundProduct[item.productId].name} không còn tồn tại!`)
        const quantityPdOld = +product.quantity
        const quantityPdNew = quantityPdOld + +item.quantity
        const quantityPdNewLockEmp = product.quantityLockEmp + +item.quantity
        await productRepo.update(product.id, {
          quantity: quantityPdNew,
          quantityLockEmp: quantityPdNewLockEmp,
          expiryDate: item.expiryDate,
          createdAt: new Date(),
          createdBy: user.id,
        })

        let productDetail = await productDetailRepo.findOne({
          where: {
            itemId: item.productId,
            expiryDate: Raw(
              (alias) => `(${alias}) BETWEEN ("${moment(ds).format('YYYY-MM-DD HH:mm:ss')}") AND ("${moment(de).format('YYYY-MM-DD HH:mm:ss')}")`,
            ),
            lotNumber: item.lotNumber,
          },
        })

        if (!productDetail) {
          const productDetailNew = new ItemDetailEntity()
          productDetailNew.itemId = item.productId
          productDetailNew.quantity = item.quantity
          productDetailNew.manufactureDate = item.manufactureDate
          productDetailNew.expiryDate = item.expiryDate
          productDetailNew.costPrice = +dicProductPrice[item.productId]?.priceCapital || 0
          productDetailNew.createdAt = new Date()
          productDetailNew.createdBy = user.id
          productDetailNew.lotNumber = item.lotNumber
          productDetailNew.quantityLockEmp = item.quantity
          productDetail = await productDetailRepo.save(productDetailNew)
        } else {
          const quantityPdDetailNew = +productDetail.quantity + +item.quantity
          // cập nhật cache
          productDetail.quantity = quantityPdDetailNew
          productDetail.lotNumber = item.lotNumber
          productDetail.quantityLockEmp = +productDetail.quantityLockEmp + +item.quantity
          await productDetailRepo.update(productDetail.id, { quantity: quantityPdDetailNew, quantityLockEmp: quantityPdDetailNew })
        }

        await warehouseProductDetailRepo.update(warehouseProductDetail.id, { productDetailId: productDetail.id })
      }

      // #region Lưu lịch sử nhập kho
      const history = new InboundHistoryEntity()
      history.inboundId = inboundNew.id
      history.createdBy = user.id
      history.createdByName = user.username

      let description = {
        'Thêm mới phiếu nhập kho đã duyệt. Thao tác chi tiết': {
          'Mã phiếu nhập kho': `${inboundNew.code}`,
          'Thời gian tạo': `${moment(inboundNew.createdAt).format('YYYY-MM-DD HH:mm:ss')}`,
          'Người tạo': `${user.username}`,
          'Thông tin sản phẩm': arr,
          'IP Request': req.headers['x-forwarded-for'] || req.socket.remoteAddress || null,
        },
      }

      history.description = JSON.stringify(description)
      await inboundHistoryRepo.insert(history)
      // #endregion

      // #endregion
    })
    const actionLogCreateDto: ActionLogCreateDto = {
      idHistory: checkProductCombo.id,
      tableName: '',
      oldJson: JSON.stringify({ ...checkProductCombo}),
      newJson: null,
      type: enumData.ActionLogType.CANCEL.code,
      description: `Nhân viên [ ${user.username} ] đã hủy combo sản phẩm`,
    }
    await this.actionLogService.createData(user, actionLogCreateDto)

    return { message: `Huỷ combo thành công!` }
  }

  private genCode(count: number, plusString: string = ''): string {
    const curDate = moment(new Date()).format('DDMMYYYY')
    let numberString: string = '0001'
    let genCode: string
    const incrementedNumber = Number(numberString) + count
    const newLastPart = String(incrementedNumber).padStart(numberString.length, '0')
    genCode = `${curDate}_${newLastPart}`
    if (plusString.length > 0) genCode = `${plusString}_${curDate}_${newLastPart}`
    return genCode
  }
}
