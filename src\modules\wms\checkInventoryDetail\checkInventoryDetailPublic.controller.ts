import { Controller, UseGuards, Post, Body, Req } from '@nestjs/common'
import { PaginationDto, UserDto } from '../../../dto'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { Request as IRequest } from 'express'
import { CheckInventoryDetailService } from './checkInventoryDetail.service'
import { JwtAuthGuard } from '../../common/guards'
import { CurrentUser } from '../../common'
import { AuthGuard } from '../../common/guards/auth.guard'

@ApiBearerAuth()
@ApiTags('CheckInventory')
@UseGuards(AuthGuard)
@Controller('check_inventory_detail_public')
export class CheckInventoryDetailPublicController {
  constructor(private readonly service: CheckInventoryDetailService) { }
  @Post('pagination')
  async pagination(@Body() data: PaginationDto, @Req() req: IRequest) {
    return await this.service.pagination(data, req)
  }
}
