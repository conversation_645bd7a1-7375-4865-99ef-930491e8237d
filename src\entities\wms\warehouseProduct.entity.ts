import { BaseEntity } from '../core/base.entity'
import { Entity, Column, ManyToOne, JoinColumn, OneToMany, BeforeInsert, BeforeUpdate } from 'typeorm'
import { WarehouseEntity } from './warehouse.entity'
import { WarehouseProductDetailEntity } from './warehouseProductDetail.entity'
// import { ProductEntity } from './product.entity'

/** Sản phẩm trong kho vật lý */
@Entity('warehouse_product')
export class WarehouseProductEntity extends BaseEntity {
  /** Id PNK */
  @Column({ type: 'varchar', nullable: true })
  warehouseId: string
  @ManyToOne(() => WarehouseEntity, (p) => p.products)
  @JoinColumn({ name: 'warehouseId', referencedColumnName: 'id' })
  warehouse: Promise<WarehouseEntity>

  /** Id Sản phẩm */
  @Column({ type: 'varchar', length: 36, nullable: true })
  productId: string

  /** Mã sp */
  @Column({ type: 'varchar', length: 50, nullable: true })
  productCode: string

  /** Tên sp */
  @Column({ type: 'varchar', length: 250, nullable: true })
  productName: string

  /** Số lượng đã bán  */
  @Column({ nullable: true, default: 0 })
  quantityOrder: number

  /** Số lượng tồn = quantityBegin + quantityImport - quantityExport  */
  @Column({ nullable: true, default: 0 })
  quantity: number

  /** Số lượng nhập từ inbound */
  @Column({ nullable: true, default: 0 })
  quantityImport: number

  /** Số lượng xuất từ outbound */
  @Column({ nullable: true, default: 0 })
  quantityExport: number

  /** Số lượng tồn ban đầu */
  @Column({ nullable: true, default: 0 })
  quantityBegin: number

  /** Số lượng lock lên đơn */
  @Column({ nullable: true, default: 0 })
  quantityLock: number

  /** Chi tiết sản phẩm trong kho vật lý */
  @OneToMany(() => WarehouseProductDetailEntity, (p) => p.warehouseProduct)
  details: Promise<WarehouseProductDetailEntity[]>

  /** Cập nhật sl tồn trước khi thêm hoặc cập nhật */
  // @BeforeUpdate()
  // @BeforeInsert()
  // updateQuantity() {
  // this.quantity = (+this.quantityBegin || 0) + (+this.quantityImport || 0) - (+this.quantityExport || 0)
  // this.quantity = (+this.quantityBegin || 0) + (+this.quantity || 0)
  // }
}
