import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator'

export class InboundtApproveDto {
  @ApiProperty({ description: "ID của PNX" })
  @IsNotEmpty()
  @IsString()
  id: string

  @ApiPropertyOptional({ description: "Ghi chú" })
  @IsOptional()
  note?: string

  @ApiPropertyOptional({ description: "Giá" })
  @IsOptional()
  price?: number

  @ApiPropertyOptional({ description: "" })
  @IsOptional()
  type?: string

  @ApiPropertyOptional({ description: "" })
  @IsOptional()
  totalPrice?: number

  @ApiProperty({ description: "Id người duyệt" })
  @IsNotEmpty()
  @IsUUID()
  approveBy: string
}
