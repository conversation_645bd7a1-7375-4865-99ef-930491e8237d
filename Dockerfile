# Version: 0.3
# syntax=docker/dockerfile:1.4
# Bật BuildKit
FROM node:20.9.0-alpine as deps
WORKDIR /tmp/
COPY package.json yarn.lock ./
RUN --mount=type=cache,target=/root/.cache/yarn \
    yarn install --frozen-lockfile

FROM node:20.9.0-alpine as builder
WORKDIR /tmp/
COPY --from=deps /tmp/node_modules ./node_modules
COPY package.json yarn.lock tsconfig.json nest-cli.json tsconfig.build.json ./
COPY src/ src/
ENV NODE_OPTIONS=--max_old_space_size=4096
RUN yarn build

FROM node:20.9.0-alpine
WORKDIR /usr/local/nub-api

COPY --from=builder /tmp/node_modules ./node_modules
COPY --from=builder /tmp/dist ./dist

RUN mkdir /uploads

RUN apk update && \
    apk upgrade && \
    apk add --no-cache \
        chromium \
        nss \
        freetype \
        harfbuzz \
        ca-certificates \
        ttf-freefont \
        fontconfig && \
    rm -rf /var/cache/apk/*

CMD exec node --max_old_space_size=4096 dist/main.js