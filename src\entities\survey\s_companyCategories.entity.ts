import { <PERSON><PERSON><PERSON>, Column, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm'
import { BaseEntity } from '../core/base.entity'
import { CompanyEntity } from './s_company.entity'
import { CategoriesEntity } from './s_categories.entity'

@Entity('s_company_categories')
export class CompanyCategoriesEntity extends BaseEntity {
  /** Danh mục */
  @Column({ type: 'varchar', nullable: true })
  categoriesId: string

  /** Công ty */
  @Column({ type: 'varchar', nullable: true })
  companyId: string

  /** đã đồng bộ dữ liệu */
  @Column({
    nullable: true,
    default: false,
  })
  isSync: boolean

  /** danh mục */
  @ManyToOne(() => CompanyEntity, (p) => p.companyCategories)
  @JoinColumn({ name: 'companyId', referencedColumnName: 'id' })
  company: Promise<CompanyEntity>
}
