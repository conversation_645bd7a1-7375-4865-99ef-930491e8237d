import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import { RegionRepository } from '../../../repositories'
import { RegionController } from './region.controller'
import { RegionService } from './region.service'
import { RegionPublicController } from './regionPublic.controller'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([RegionRepository])],
  controllers: [RegionController, RegionPublicController],
  providers: [RegionService],
  exports: [RegionService],
})
export class RegionModule {}
