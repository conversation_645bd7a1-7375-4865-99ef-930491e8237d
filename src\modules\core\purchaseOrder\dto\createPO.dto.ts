import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString, IsUUID, IsNumber, IsDateString, IsEnum, MaxLength, Min, IsDecimal } from 'class-validator'
import { NSPo, NSSupplier } from '../../../../constants'
import { ItemDto } from '../../deliveryNoteChild/dto/deliveryNoteChild.dto'

export class CreateItemPurchaseOrderDto {
  @ApiProperty({ description: 'PO ID' })
  @IsNotEmpty()
  @IsUUID()
  purchaseOrderId: string

  @ApiProperty({ description: 'ID Combo' })
  @IsNotEmpty()
  @IsUUID()
  comboId?: string

  @ApiProperty({ description: 'ID Vùng' })
  @IsNotEmpty()
  @IsUUID()
  regionId: string

  @ApiProperty({ description: 'ID Nhóm hàng hóa' })
  @IsNotEmpty()
  @IsUUID()
  itemGroupId: string

  @ApiProperty({ description: 'ID Hàng hóa' })
  @IsNotEmpty()
  @IsUUID()
  itemId: string

  @ApiProperty({ description: 'Tên Hàng hóa' })
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  itemName: string

  @ApiProperty({ description: 'Mã Hàng hóa' })
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  itemCode: string

  @ApiProperty({ description: 'Số lượng đơn vị cơ sở' })
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  quantityBasicUnit: number

  @ApiProperty({ description: 'Số lượng combo' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  quantityCombo?: number

  @ApiProperty({ description: 'Quy cách đóng gói' })
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  packingSpecification: string

  @ApiProperty({ description: 'Đơn vị tính đặt hàng' })
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  purchaseUnit: string

  @ApiProperty({ description: 'Đơn giá', type: 'number', example: 0 })
  @IsNotEmpty()
  @IsDecimal()
  @Min(0)
  sellPrice: number

  @ApiProperty({ description: 'Đơn giá', type: 'number', example: 0 })
  @IsNotEmpty()
  @IsDecimal()
  @Min(0)
  inputPrice: number

  @ApiProperty({ description: 'VAT', type: 'number', example: 0, required: false })
  @IsOptional()
  @IsNumber()
  @Min(0)
  vat?: number

  @ApiProperty({ description: 'VAT item', type: 'number', example: 0, required: false })
  @IsOptional()
  @IsNumber()
  @Min(0)
  itemVat?: number

  @ApiProperty({ description: 'Thành tiền', type: 'number', example: 0 })
  @IsNotEmpty()
  @IsDecimal()
  @Min(0)
  totalAmount: number

  @ApiProperty({ description: 'Thành tiền VAT', type: 'number', example: 0 })
  @IsNotEmpty()
  @IsDecimal()
  @Min(0)
  totalAmountVat: number

  @ApiProperty({ description: 'Mã Bưu cục', required: false })
  @IsOptional()
  @IsUUID()
  partnerId?: string | null

  @ApiProperty({ description: 'Nhà cc', required: false })
  @IsOptional()
  @IsUUID()
  supplierId?: string | null

  @ApiProperty({ description: 'Nhà phân phối', required: false })
  @IsOptional()
  @IsUUID()
  distributorId?: string | null

  @ApiProperty({ description: 'Đơn vị vận chuyển 3PL', required: false })
  @IsOptional()
  @IsUUID()
  deliveryId?: string | null

  @ApiProperty({ description: 'Kho trung tâm nhận hàng', required: false })
  @IsOptional()
  @IsUUID()
  warehouseId?: string | null

  @ApiProperty({ description: 'ID Tỉnh/TP', required: false })
  @IsOptional()
  @IsUUID()
  provinceId?: string | null

  @ApiProperty({ description: 'Danh sách SO ID', required: false })
  @IsOptional()
  soIds?: string[]

  @IsOptional()
  addressType?: string

  @IsOptional()
  soIdQuantity?: { id: string; quantity: number; isCombo: boolean }[]

  @IsOptional()
  isCombo?: boolean
}

export class ComboDistributePurchaseOrder {
  @ApiProperty({ description: 'Mã PO' })
  purchaseOrderId: string

  @ApiProperty({ description: 'ID combo' })
  comboId: string

  @ApiProperty({ description: 'Số lượng phân bổ' })
  quantity: number

  @ApiProperty({ description: 'ID Bưu cục' })
  partnerId: string

  @ApiProperty({ description: 'Province Code' })
  provinceCode: string
}

export class DistributeViewPODto {
  @ApiPropertyOptional({ description: 'Danh sách phân bổ' })
  distribute: [ComboDistributePurchaseOrder]

  @ApiProperty({ description: 'Flag po type' })
  @IsNotEmpty()
  poType: NSPo.EPoType
}

export class CreatePurchaseOrderDto {
  @ApiProperty({
    description: `Loại PO ${Object.values(NSPo.EPoType).join(' | ')}`,
    enum: NSPo.EPoType,
    default: NSPo.EPoType.WITHCOMBO,
  })
  @IsNotEmpty()
  @IsEnum(NSPo.EPoType)
  purchaseOrderType: NSPo.EPoType

  @ApiProperty({ description: 'VAT' })
  @IsNotEmpty()
  @IsNumber()
  vat: number

  @ApiPropertyOptional({ description: 'Người tạo PO' })
  @IsUUID()
  creatorId?: string

  @ApiPropertyOptional({
    description: 'Ngày nhận hàng',
    example: '2024-11-30T15:40:00.000Z',
  })
  @IsOptional()
  @IsDateString()
  deliveryDate?: string

  @ApiPropertyOptional({
    description: `Trạng thái ${Object.values(NSPo.EPoStatus).join(' | ')}`,
    enum: NSPo.EPoStatus,
    default: NSPo.EPoStatus.NEWLYCREATED,
  })
  @IsOptional()
  @IsEnum(NSPo.EPoStatus)
  status?: NSPo.EPoStatus

  @ApiPropertyOptional({
    description: `Trạng thái thanh toán ${Object.values(NSPo.EPoStatusPayment).join(' | ')}`,
    enum: NSPo.EPoStatusPayment,
    default: NSPo.EPoStatusPayment.UNPAID,
  })
  @IsOptional()
  @IsEnum(NSPo.EPoStatusPayment)
  paymentStatus?: NSPo.EPoStatusPayment

  @ApiPropertyOptional({ description: 'Danh sách Supplier' })
  @IsOptional()
  items?: [CreateItemPurchaseOrderDto]

  warehouseCode?: string
  warehouseId?: string
}

export class CreatePurchaseOrderFromSODto {
  @ApiPropertyOptional({ description: 'Danh sách phân bổ' })
  @IsOptional()
  combos?: [ComboSoDto]
}
class ComboSoDto {
  @ApiPropertyOptional({ description: 'ID combo' })
  comboId: string

  @ApiPropertyOptional({ description: 'ID của Partner' })
  partnerId: string

  @ApiPropertyOptional({ description: 'Số lượng' })
  quantity: number

  @ApiPropertyOptional({ description: 'Số lượng' })
  @IsOptional()
  @IsUUID()
  soId?: string
}

export class CreatePOFromSODto {
  @ApiPropertyOptional({ description: 'Danh sách phân bổ' })
  @IsOptional()
  products?: [ProductSODto]
}

class ProductSODto {
  @ApiPropertyOptional({ description: 'ID Product' })
  @IsNotEmpty()
  productId: string

  @ApiPropertyOptional({ description: 'ID MBC' })
  @IsNotEmpty()
  partnerId: string

  @ApiPropertyOptional({ description: 'Số lượng' })
  @IsNotEmpty()
  quantity: string

  @ApiPropertyOptional({ description: 'Số lượng' })
  @IsNotEmpty()
  @IsUUID()
  soId: string

  @ApiPropertyOptional({ description: 'Loại địa chỉ nhận của SO' })
  addressType: string
}

export class UpdatePurchaseOrderFilesDto {
  @ApiProperty({
    description: 'Danh sách tệp đính kèm',
    example: { name: 'default', src: 'aws.com/dada' },
  })
  @IsOptional()
  files?: string
}

export class UpdatePurchaseOrderDto extends UpdatePurchaseOrderFilesDto {
  @ApiProperty({ description: 'ID của PO' })
  @IsNotEmpty()
  @IsUUID()
  purchaseOrderId: string

  @ApiProperty({
    description: `Loại thanh toán ${Object.values(NSPo.EPoTypePayment).join(' | ')}`,
    enum: NSPo.EPoTypePayment,
  })
  @IsOptional()
  @IsEnum(NSPo.EPoTypePayment)
  paymentType: NSPo.EPoTypePayment

  @ApiProperty({ description: 'Tiền cọc' })
  @IsOptional()
  deposit: number

  @ApiProperty({ description: 'Điều kiện giao hàng' })
  @IsOptional()
  deliveryTerms: string

  @ApiProperty({ description: 'ID người cập nhật' })
  @IsOptional()
  updateBy?: string
}

export class SendPurchaseOrderDto {
  @ApiProperty({ description: 'ID của PO' })
  @IsNotEmpty()
  @IsUUID()
  purchaseOrderId: string
}

export class UploadPreOrderPODto {
  @ApiProperty({ description: 'Danh sách sản phẩm' })
  @IsNotEmpty()
  items: any[]

  //iscombo
  @ApiProperty({ description: 'Có phải là combo không' })
  @IsNotEmpty()
  isCombo: boolean
}
