import { Controller, UseGuards, Request, Post, Body } from '@nestjs/common'
import { PermissionService } from './permission.service'
import { PermissionCreateDto, PermissionUpdateDto } from './dto'
import { JwtAuthGuard } from '../../common/guards'
import { CurrentUser } from '../../common/decorators'
import { UserDto } from '../../../dto'

@Controller('permission')
export class PermissionController {
  constructor(private readonly service: PermissionService) { }

  @UseGuards(JwtAuthGuard)
  @Post('pagination')
  public async pagination(@Body() data: any) {
    return await this.service.pagination(data)
  }

  @UseGuards(JwtAuthGuard)
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: PermissionCreateDto) {
    const permission = await this.service.createData(user, data)
    return permission
  }

  @UseGuards(JwtAuthGuard)
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: PermissionUpdateDto) {
    const permission = await this.service.createData(user, data)
    return permission
  }
}
