import { Body, Controller, Get, Post, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'

import { CurrentUser } from '../../common/decorators'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
// import { JwtAuthGuard } from '../../common/guards'
import { SettingStringCreateDto } from './dto/settingStringCreate.dto'
import { SettingStringUpdateDto } from './dto/settingStringUpdate.dto'
import { SettingStringService } from './settingString.service'
import { SettingStringUpdateActiveStatusDto } from './dto/settingStringUpdateActiveStatus.dto'
import { JwtAuthGuard } from '../../common/guards'
@ApiBearerAuth()
@ApiTags('SETTING_STRING')
@Controller('setting_string')
export class SettingStringController {
  constructor(private readonly service: SettingStringService) {}

  @ApiOperation({ summary: '<PERSON>àm tìm kiếm tất cả setting string' })
  // @UseGuards(JwtAuthGuard)
  @Get('find_all')
  public async findAll() {
    return await this.service.findAll()
  }

  @ApiOperation({ summary: 'Hàm lấy danh sách Banner cho Customer' })
  @Get('find_banner_customer')
  public async findBannerCustomer() {
    return await this.service.findBannerCustomer()
  }

  @ApiOperation({ summary: 'Hàm load data setting string' })
  // @UseGuards(JwtAuthGuard)
  @Post('load_data')
  public async loadData() {
    return await this.service.loadData()
  }

  @ApiOperation({ summary: 'Hàm tìm kiếm tất cả setting string' })
  // @UseGuards(JwtAuthGuard)
  @Post('load_data_select')
  public async loadDataSelectBox(user: UserDto) {
    return await this.service.loadDataSelectBox(user)
  }

  @ApiOperation({ summary: 'Hàm tìm kiếm chi tiết setting string theo mã' })
  // @UseGuards(JwtAuthGuard)
  @Post('find_one_by_code')
  public async findOneByCode(@CurrentUser() user: UserDto, @Body() data: any) {
    return this.service.findOneByCode(data)
  }

  @ApiOperation({ summary: 'Hàm tìm kiếm chi tiết setting string theo danh sách mã' })
  // @UseGuards(JwtAuthGuard)
  @Post('find_list_by_code')
  public async findListByCode(@CurrentUser() user: UserDto, @Body() data: any) {
    return this.service.findListByCode(data)
  }

  @ApiOperation({ summary: 'Hàm cập nhật setting string' })
  @UseGuards(JwtAuthGuard)
  @Post('update')
  public async update(@CurrentUser() user: UserDto, @Body() data: SettingStringUpdateDto) {
    return this.service.update(user, data)
  }

  @ApiOperation({ summary: 'Hàm cập nhật trạng thái setting string' })
  // @UseGuards(JwtAuthGuard)
  @Post('update_active_status')
  public async updateActiveStatus(@CurrentUser() user: UserDto, @Body() data: SettingStringUpdateActiveStatusDto) {
    return this.service.updateActiveStatus(user, data)
  }

  @ApiOperation({ summary: 'Hàm phân trang' })
  // @UseGuards(JwtAuthGuard)
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return this.service.pagination(user, data)
  }
}
