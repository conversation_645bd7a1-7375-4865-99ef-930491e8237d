import { Controller, Post, Body, UseGuards, Req } from '@nestjs/common'
import { AuthService } from './auth.service'
import { UserDto } from '../../../dto'
import { CurrentUser } from '../../common/decorators'
import { JwtAuthGuard } from '../../common/guards'
import { Request as IRequest } from 'express'
import {
  UserLoginDto,
  UpdatePasswordDto,
  CreateUserSurveyDto,
  UpdateEmailAdminto,
  UpdatePasswordAdminto,
  UpdateRoleAdminto,
  UpdateStatusUserSurveyDto,
  UpdateUsernameDto,
} from './dto'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { UserRegisterDto, UserUpdateDto, UserUpdatePasswordDto } from './dto/register.dto'

@ApiBearerAuth()
@ApiTags('AuthCore')
@Controller('auth')
export class AuthController {
  constructor(private readonly service: AuthService) {}

  @Post('login')
  public async login(@Body() data: UserLoginDto) {
    return await this.service.login(data)
  }

  @Post('register')
  public async register(@Body() user: UserRegisterDto) {
    return await this.service.register(user)
  }

  @ApiOperation({ summary: 'Đổi mật khẩu user trang Auth admin' })
  @UseGuards(JwtAuthGuard)
  @Post('update_password')
  public async updatePassword(@Body() data: UpdatePasswordDto, @CurrentUser() user: UserDto) {
    return await this.service.updatePassword(user, data)
  }

  @ApiOperation({ summary: 'Reset mật khẩu user trang Auth admin' })
  @UseGuards(JwtAuthGuard)
  @Post('reset_password')
  public async resetPassword(@CurrentUser() user: UserDto, @Body() data: { userName: string; newPassword: string }) {
    return await this.service.resetPassword(user, data)
  }

  @ApiTags('Auth')
  @ApiOperation({ summary: 'Kiểm tra để xác thực token Auth gọi' })
  @UseGuards(JwtAuthGuard)
  @Post('validate_token_auth')
  public async validateTokenAuth(@CurrentUser() user: UserDto) {
    return await this.service.validateTokenAuth(user)
  }

  // @ApiTags('Auth', 'AuthSurvey')
  // @ApiOperation({ summary: 'Quên mật khẩu trang admin Survey' })
  // @Post('forget_survey_admin')
  // public async forgetSurveyAdmin(@Req() req: IRequest, @Body() data: ForgetAdminDto) {
  //   return await this.service.forgetSurveyAdmin(req, data)
  // }

  @ApiTags('Auth', 'AuthSurvey')
  @ApiOperation({ summary: 'Kiểm tra để xác thực token Survey gọi' })
  @UseGuards(JwtAuthGuard)
  @Post('validate_token')
  public async validateToken(@CurrentUser() user: UserDto, @Body() data: { domain?: string }) {
    return await this.service.validateToken(user)
  }

  @ApiTags('Auth', 'AuthSurvey')
  @ApiOperation({ summary: 'Lấy companyId từ domain' })
  @Post('get_company_id')
  public async getcompanyId(@Body() data: { domain: string }) {
    return await this.service.getcompanyId(data)
  }

  @ApiTags('Auth', 'AuthSurvey')
  @ApiOperation({ summary: 'Tạo user Survey' })
  @UseGuards(JwtAuthGuard)
  @Post('create_user_survey')
  public async createUserSurvey(@CurrentUser() user: UserDto, @Body() data: CreateUserSurveyDto) {
    return await this.service.createUserSurvey(user, data)
  }

  @ApiTags('Auth', 'AuthSurvey')
  @ApiOperation({ summary: 'User tự đổi mật khẩu Survey' })
  @UseGuards(JwtAuthGuard)
  @Post('update_password_user_survey')
  public async updatePasswordSurvey(@CurrentUser() user: UserDto, @Body() data: UpdatePasswordDto) {
    return await this.service.updatePasswordSurvey(user, data)
  }

  @ApiTags('Auth', 'AuthSurvey')
  @ApiOperation({ summary: 'Admin đổi mật khẩu user Survey' })
  @UseGuards(JwtAuthGuard)
  @Post('update_password_user_survey_admin')
  public async updatePasswordSurvey_Admin(@CurrentUser() user: UserDto, @Body() data: UpdatePasswordAdminto) {
    return await this.service.updatePasswordSurvey_Admin(user, data)
  }

  @ApiTags('Auth', 'AuthSurvey')
  @ApiOperation({ summary: 'Admin đổi email user Survey' })
  @UseGuards(JwtAuthGuard)
  @Post('update_email_user_survey_admin')
  public async updateEmailSurvey_Admin(@CurrentUser() user: UserDto, @Body() data: UpdateEmailAdminto) {
    return await this.service.updateEmailSurvey_Admin(user, data)
  }

  @ApiTags('Auth', 'AuthSurvey')
  @ApiOperation({ summary: 'Admin đổi vai trò user Survey' })
  @UseGuards(JwtAuthGuard)
  @Post('update_role_user_survey_admin')
  public async updateRoleSurvey_Admin(@CurrentUser() user: UserDto, @Body() data: UpdateRoleAdminto) {
    return await this.service.updateRoleSurvey_Admin(user, data)
  }

  @ApiTags('Auth', 'AuthSurvey')
  @ApiOperation({ summary: 'Active/Inactive user Survey' })
  @UseGuards(JwtAuthGuard)
  @Post('update_status_user_survey')
  public async updateStatusUserSurvey(@CurrentUser() user: UserDto, @Body() data: UpdateStatusUserSurveyDto) {
    return await this.service.updateStatusUserSurvey(user, data)
  }

  @ApiTags('Portal')
  @ApiOperation({ summary: 'API Portal Auth' })
  @Post('portal/login')
  public async loginPortal(@Body() data: UserLoginDto) {
    return await this.service.loginPortal(data)
  }

  @ApiOperation({ summary: 'Đổi mật khẩu  Portal' })
  @UseGuards(JwtAuthGuard)
  @Post('update-password-portal')
  public async updatePasswordPortal(@Body() data: UpdatePasswordDto, @CurrentUser() user: UserDto, @Req() req: IRequest) {
    return await this.service.updatePasswordPortal(data, user, req)
  }

  @ApiOperation({ summary: 'Đổi tên tài khoản cho ncc' })
  @UseGuards(JwtAuthGuard)
  @Post('update-username')
  public async updateUsername(@Body() data: UpdateUsernameDto, @CurrentUser() user: UserDto, @Req() req: IRequest) {
    return await this.service.updateUsername(data, user, req)
  }

  @ApiTags('Supplier Auth')
  @ApiOperation({ summary: 'Đăng nhập cho nhà cung cấp' })
  @Post('supplier/login')
  public async loginSupplier(@Body() data: UserLoginDto) {
    return await this.service.loginSupplier(data)
  }
}
