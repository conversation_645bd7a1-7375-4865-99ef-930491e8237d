import { Entity, Column, PrimaryGeneratedColumn, ManyToOne, Join<PERSON>olumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from './base.entity';
import { PoScheduleEntity } from './poSchedule.entity';


@Entity('po_schedule_history')
export class PoScheduleHistoryEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  poScheduleId: string

  @ManyToOne(() => PoScheduleEntity, (p) => p.histories)
  @JoinColumn({ name: 'poScheduleId', referencedColumnName: 'id' })
  poSchedule: Promise<PoScheduleEntity>

  @ApiProperty({ example: 'create', description: 'Loại hành động (create, update, approve)' })
  @Column({ type: 'varchar', length: 50, nullable: false })
  actionType: string;

  @ApiProperty({ example: 'Người dùng tạo mới lịch hàng về.', description: '<PERSON><PERSON> tả chi tiết hành động' })
  @Column({ type: 'text', nullable: true })
  description: string;

  @ApiProperty({ example: 'admin', description: 'Người thực hiện hành động' })
  @Column({ type: 'varchar', length: 255, nullable: false })
  performedBy: string;

  @ApiProperty({ example: '2024-02-27 14:30:00', description: 'Thời gian thực hiện hành động' })
  @Column({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  performedAt: Date;
}
