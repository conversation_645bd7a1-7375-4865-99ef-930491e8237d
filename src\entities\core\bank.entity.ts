import { Entity, Column, Index, Unique } from "typeorm";
import { BaseEntity } from './base.entity'

@Entity('bank')
export class BankEntity extends BaseEntity {
  /** Code từ file excel vd: ********, ******** */
  @Index()
  @Column({ type: "varchar", nullable: false, unique: true })
  code: string

  /** Bank Code, vd: VCB, ICB,...*/
  @Column({ type: "varchar", nullable: false, unique: true })
  @Index()
  bankCode: string

  /** Bank Name */
  @Column({ type: "varchar", nullable: false, unique: true })
  name: string

  /** Bank Name */
  @Column({ type: "varchar", nullable: true })
  shortName: string

  /** Bank Name */
  @Column({ type: "varchar", nullable: true })
  bin: string

  /** Bank Name */
  @Column({ type: "varchar", nullable: true })
  key: string

  /** Bank Name */
  @Column({ type: "varchar", nullable: true })
  logo: string
}

@Entity('bank_branch')
export class BankBrandEntity extends BaseEntity {
  /** Bank Code */
  @Column({ type: "varchar", nullable: false })
  bankCode: string

  /** Bank Branch Code */
  @Index()
  @Column({ type: "varchar", nullable: false, unique: true })
  code: string

  /** Bank Branch Name */
  @Column({ type: "varchar", nullable: false })
  name: string
}