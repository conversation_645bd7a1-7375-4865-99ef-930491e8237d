'use strict'
import { Injectable, ConflictException, NotFoundException } from '@nestjs/common'
import { In, Like, Raw } from 'typeorm'
import { arrayHelper, coreHelper } from '../../../helpers'
import { ERROR_CODE_TAKEN, ERROR_NOT_FOUND_DATA, IMPORT_SUCCESS, UPDATE_ACTIVE_SUCCESS, UPDATE_SUCCESS, enumData } from '../../../constants'
import { FilterOneDto, UserDto } from '../../../dto'
import { WardEntity, CityEntity, DistrictEntity } from '../../../entities'
import { CityRepository, DistrictRepository, WardRepository } from '../../../repositories'
import { WardCreateDto, WardCreateExcelDto, WardFilterDto, WardUpdateDto } from './dto'
import * as dataWard from './dataward.json'

@Injectable()
export class WardService {
  constructor(private readonly repo: WardRepository, private districtRepo: DistrictRepository) {}

  async find(data: any) {
    const whereCon: any = { isDeleted: false }
    if (data.name) whereCon.name = Like(`%${data.name}%`)
    if (data.code) whereCon.code = Like(`%${data.code}%`)
    if (data.lstId?.length > 0) whereCon.id = In(data.lstId)
    if (data.lstCode?.length > 0) whereCon.code = In(data.lstCode)
    if (data.districtId) whereCon.districtId = data.districtId
    return await this.repo.find({ where: whereCon })
  }
  async loadWardByDistrictId(districtId: string) {
    return await this.repo.find({
      where: { districtId, isDeleted: false },
      order: { code: 'ASC', name: 'ASC' },
    })
  }

  async loadData(data: { id?: string }) {
    const whereCon: any = { isDeleted: false }
    if (data.id) whereCon.districtId = data.id
    return await this.repo.find({ where: whereCon, select: { id: true, code: true, name: true } })
  }

  async createData(user: UserDto, data: WardCreateDto): Promise<any> {
    const checkCodeExist = await this.repo.findOne({ where: { code: data.code } })
    if (checkCodeExist) throw new ConflictException(ERROR_CODE_TAKEN)

    const newEntity = this.repo.create({ ...data })
    newEntity.createdBy = user.id
    const createdEntity = await this.repo.save(newEntity)

    // const type = enumData.Type.ThemMoi.code
    // const functionType = enumData.ActionLog.Setting_Ward.code
    // const res = { type: type, functionType: functionType, code: createdEntity.code }
    // await this.actionService.createdata(res, user)

    return { message: 'Tạo mới thành công' }
  }

  async updateData(user: UserDto, data: WardUpdateDto) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) {
      throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    }
    if (entity.code != data.code) {
      const checkCodeExist = await this.repo.findOne({ where: { code: data.code } })
      if (checkCodeExist) throw new ConflictException(ERROR_CODE_TAKEN)
    }

    entity.updatedBy = user.id
    entity.name = data.name
    entity.code = data.code
    entity.districtId = data.districtId
    const updatedEntity = await this.repo.save(entity)

    // const type = enumData.Type.ChinhSua.code
    // const functionType = enumData.ActionLog.Setting_Ward.code
    // const res = { type: type, functionType: functionType, code: updatedEntity.code }
    // await this.actionService.createdata(res, user)

    return { message: UPDATE_SUCCESS }
  }

  async pagination(data: any) {
    let whereCon: any = {}
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.cityId) {
      const lstDistrict = await this.districtRepo.find({ where: { cityId: data.where.cityId, isDeleted: false } })
      if (lstDistrict.length == 0) return []
      const lstId = lstDistrict.map((x) => x.id)
      whereCon.districtId = In(lstId)
    }
    if (data.where.districtId) whereCon.districtId = data.where.districtId
    return await this.repo.findAndCount({
      relations: { district: { city: true } },
      where: whereCon,
      order: { createdAt: 'DESC' },
      skip: data.skip,
      take: data.take,
    })
  }

  async updateIsDelete(id: string, user: UserDto) {
    const entity = await this.repo.findOne({ where: { id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    entity.isDeleted = !entity.isDeleted
    await this.repo.save(entity)

    // const type = enumData.Type.ChinhSua.code
    // const functionType = enumData.ActionLog.Setting_Ward.code
    // const res = { type: type, functionType: functionType, code: entity.code }
    // await this.actionService.createdata(res, user)

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  async createMasterData() {
    const check = await this.repo.findOne({})
    if (!check) {
      const newData = dataWard.Sheet1
      const group = await arrayHelper.groupByArray(newData, 'Tinh')
      const wardRepo = this.repo.manager.getRepository(WardEntity)
      const districtRepo = this.repo.manager.getRepository(DistrictEntity)
      const cityRepo = this.repo.manager.getRepository(CityEntity)
      let i = 0
      for (let itemGroup of group) {
        const cityName = itemGroup.heading
        const findCity = await cityRepo.findOne({
          where: { name: Raw((alias) => `${alias} Like '%${cityName}%'`) },
        })
        if (findCity) {
          for (let item of itemGroup.list) {
            const districtName = item['Huyen']
            const findD = await districtRepo.findOne({
              where: { name: districtName },
            })
            if (findD) {
              let ward = new WardEntity()
              ward.name = item['Xa']
              ward.districtId = findD.id

              const created = await wardRepo.create({ ...ward })
              wardRepo.save(created)
              // console.log('save success: ' + i)
              i++
            }
          }
        }
      }
      return 'Cập nhật thành công'
    }
  }

  async createDataExcel(user: UserDto, data: WardCreateExcelDto[]): Promise<any> {
    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(WardEntity)
      const districtRepo = trans.getRepository(DistrictEntity)

      const lstDistrictCode = coreHelper.selectDistinct(data, 'districtCode')
      const districts: any = await districtRepo.find({ where: { code: In(lstDistrictCode), isDeleted: false } })

      const dicCode: any = {}
      {
        const listBrand: any[] = await repo.find({
          where: { isDeleted: false },
          select: { id: true, code: true },
        })
        listBrand.forEach((c) => (dicCode[c.code] = c))
      }

      const districtMap = new Map(districts.map((d) => [d.code, d]))

      const lsBrandNew: WardEntity[] = []
      const dicCodeFile: any = {}
      for (const [idx, item] of data.entries()) {
        if (dicCodeFile[item.code]) throw new Error(`[ Dòng ${idx + 1} - Mã Phường / Xã [${item.code}] trùng với dòng ${dicCodeFile[item.code]} ]`)
        if (dicCode[item.code]) throw new Error(`[ Dòng ${idx + 1} - Mã Phường / Xã [${item.code}] đã được sử dụng ]`)

        let districtId = null
        if (item.districtCode) {
          const district: any = districtMap.get(item.districtCode)
          if (!district) throw new Error(`[ Dòng ${idx + 1} - Mã Quận / Huyện [${item.districtCode}] không tồn tại ]`)
          districtId = district.id
        }

        const newLocation = repo.create({
          ...item,
          districtId: districtId,
          createdBy: user.id,
        })
        lsBrandNew.push(newLocation)
        dicCodeFile[item.code] = idx + 1

        // const type = enumData.Type.ThemMoi.code
        // const functionType = enumData.ActionLog.Setting_Ward.code
        // const res = { type: type, functionType: functionType, code: item.code }
        // await this.actionService.createdata(res, user)
      }
      await repo.insert(lsBrandNew)
    })

    return { message: IMPORT_SUCCESS }
  }

  async findOne(data: FilterOneDto) {
    const whereCon: any = { id: data.id, isDeleted: false }
    return await this.repo.findOneBy(whereCon)
  }

  async loadWardByDistrict(data: WardFilterDto) {
    return await this.repo.find({
      where: { districtId: data.districtId, isDeleted: false },
      order: { code: 'ASC', name: 'ASC' },
    })
  }
}
