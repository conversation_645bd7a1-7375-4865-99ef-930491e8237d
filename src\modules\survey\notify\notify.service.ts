import { Injectable } from '@nestjs/common'
import { Like } from 'typeorm'
import { NotifyCreateDto } from './dto'
import { Request as IRequest } from 'express'
import { NotifyRepository } from '../../../repositories/survey'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { coreHelper } from '../../../helpers'
import { CREATE_SUCCESS, enumData, ERROR_NOT_FOUND_DATA, UPDATE_ACTIVE_SUCCESS } from '../../../constants'
import { NotifyEntity } from '../../../entities/survey'
const IP = require('ip')
const requestIP = require('request-ip')
@Injectable()
export class NotifyService {
  constructor(private readonly repo: NotifyRepository) {}

  public async find(user: UserDto, data: any) {
    let res: any = await this.repo.find({
      where: { ...data },
      relations: { survey: true },
    })
    const lstStatus = coreHelper.convertObjToArray(enumData.NotifyStatus)
    for (let item of res) {
      let itemSurvey = await item.survey
      item.surveyName = itemSurvey?.name
      item.surveyCode = itemSurvey?.code
      const statusItem = lstStatus.find((x) => x.code === item.status)
      if (statusItem) {
        item.statusName = statusItem.name
      }
      delete item.__survey__
    }
    return res
  }

  /** Phân trang */
  async pagination(userLogin: UserDto, data: PaginationDto) {
    const condition: any = { companyId: userLogin.companyId, userId: userLogin.userId }
    if (data.where.status) {
      condition.status = Like(`%${data.where.status}%`)
    }
    if (data.where.isDeleted != undefined) condition.isDeleted = data.where.isDeleted
    let res: any = await this.repo.findAndCount({
      where: condition,
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
      relations: { survey: true },
    })
    const lstStatus = coreHelper.convertObjToArray(enumData.NotifyStatus)
    for (let item of res[0]) {
      let itemSurvey = await item.survey
      item.surveyName = itemSurvey?.name
      item.surveyCode = itemSurvey?.code
      const statusItem = lstStatus.find((x) => x.code === item.status)
      if (statusItem) {
        item.statusName = statusItem.name
      }
      delete item.__survey__
    }
    return res
  }

  /** Tạo mới */
  async createData(userLogin: UserDto, data: NotifyCreateDto[], req: IRequest) {
    let lstNotify = []
    for (let item of data) {
      const notify = new NotifyEntity()
      // notify.companyId = userLogin.companyId
      notify.surveyId = item.surveyId
      notify.userId = item.userId
      notify.title = item.title
      notify.content = item.content
      notify.createdAt = new Date()
      notify.createdBy = userLogin.id
      notify.status = enumData.NotifyStatus.New.code
      await this.repo.save(notify)
      lstNotify.push(notify)
    }
    // await surveyAuthApiHelper.sendFirebase(req, lstNotify)
    return { message: CREATE_SUCCESS }
  }

  /** Cập nhật trạng thái */
  async updateStatus(userLogin: UserDto, data: FilterOneDto) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) {
      throw new Error(ERROR_NOT_FOUND_DATA)
    }
    entity.status = enumData.NotifyStatus.Read.code
    entity.updatedAt = new Date()
    entity.updatedBy = userLogin.id
    await this.repo.save(entity)
    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  /** Cập nhật trạng thái */
  async updateStatusAll(userLogin: UserDto) {
    const lstNotify = await this.repo.find({
      where: { userId: userLogin.userId, status: enumData.NotifyStatus.New.code },
    })
    for (let item of lstNotify) {
      await this.repo.update(item.id, {
        updatedBy: userLogin.id,
        updatedAt: new Date(),
        status: enumData.NotifyStatus.Read.code,
      })
    }

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  public async checkIp(req: IRequest) {
    console.log(req.headers['x-forwarded-for'] || req.socket.remoteAddress, 'x-forwarded-for gọi từ setting qua survey')
    console.log(req.headers['x-real-ip'] || req.socket.remoteAddress, 'x-real-ip gọi từ setting qua survey')
    console.log(req.headers['x-client-ip'] || req.socket.remoteAddress, 'x-client-ip gọi từ setting qua survey')
    const clientIp = req.ip
    console.log(clientIp, '-------------clientIp gọi từ setting qua survey--------')

    const serverIp = req.socket.localAddress
    console.log(serverIp, '-------------serverIp gọi từ setting qua survey--------')

    const localPort = req.socket.localPort
    console.log(localPort, '-------------localPort gọi từ setting qua core--------')

    const serverAddress = req.headers.host
    // Sử dụng serverAddress để làm gì đó
    console.log(`Server Address: ${serverAddress}`)

    const ipAddress = IP.address()
    console.log(ipAddress, '-------------ipAddress sử dụng thư viện ip--------')

    const ipAddress1 = requestIP.getClientIp(req)
    console.log(ipAddress1, '-------------ipAddress sử dụng thư viện request-ip--------')
  }
}
