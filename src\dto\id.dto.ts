import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsUUID } from 'class-validator'
import { Expose } from 'class-transformer'

/** Interface Id đối tượng */

export class IdDto {
  @ApiProperty({ description: 'Id của đối tượng', example: 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx' })
  @IsUUID()
  @IsNotEmpty()
  @Expose()
  id: string
}

export class UUIDReq {
  @ApiProperty()
  @IsUUID('4')
  id: string
}
