import { BaseEntity } from './base.entity'
import { Entity, Column, OneToMany } from 'typeorm'
// Quy cách đóng gói
// TODO: Đổi cách packing mới
@Entity('packing')
export class PackingEntity extends BaseEntity {
  @Column({ type: 'varchar', length: 50, nullable: false })
  code: string

  @Column({ type: 'varchar', length: 250, nullable: false })
  name: string

  @Column({ type: 'text', nullable: true })
  description: string
}
