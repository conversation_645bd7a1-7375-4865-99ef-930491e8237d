import { Controller, UseGuards, Request, Post, Body, Param, Get } from '@nestjs/common'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { Request as IRequest } from 'express'
import { CurrentUser, JwtAuthGuard } from '../../common'
import { FilterOneDto, ListProvinceCodeDto, PaginationDto, UserDto } from '../../../dto'
import { DistrictCreateDto, DistrictCreateExcelDto, DistrictOneDto, DistrictUpdateDto } from './dto'
import { DistrictService } from './district.service'
import { ApeAuthGuard } from '../../survey/common/guards'

@ApiBearerAuth()
@ApiTags('Geo District')
@UseGuards(ApeAuthGuard)
@Controller('district_public')
export class DistrictPublicController {
  constructor(private readonly service: DistrictService) { }

  @Post('find')
  async find(@Body() data: any) {
    return await this.service.find(data)
  }

  @Post('find-by-province-code')
  async findDistrictByProvinceCodes(@Body() data: ListProvinceCodeDto) {
    const { provinceCodes } = data;
    return await this.service.findListByProvinceCodes(provinceCodes)
  }

  @Post('load_data')
  async loadData(@Body() data: { id?: string }) {
    return await this.service.loadData(data)
  }

  @Get('load_district_by_cityId/:cityId')
  async loadDistrictByCityId(@Param('cityId') cityId: string) {
    return await this.service.loadDistrictByCityId(cityId)
  }

  @Post('pagination')
  async pagination(@Body() data: PaginationDto) {
    return await this.service.pagination(data)
  }
}
