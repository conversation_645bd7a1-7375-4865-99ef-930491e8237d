import { ApiProperty } from '@nestjs/swagger'
import { Transform } from 'class-transformer'
import { IsEnum, IsNotEmpty, IsNumber, Min } from 'class-validator'
import { NSBusinessOrder } from '../../../../constants'

export class CreateBusinessOrderDto {
  @ApiProperty({ description: 'ID của đơn mua hàng' })
  @IsNotEmpty({ message: 'ID của đơn mua hàng không được để trống' })
  businessPurchaseOrderId: string

  @ApiProperty({ description: 'Tổng giá trị đơn hàng' })
  @IsNotEmpty({ message: 'Tổng giá trị đơn hàng không được để trống' })
  @IsNumber()
  @Min(0)
  @Transform(({ value }) => Number(value))
  totalPrice: number

  @ApiProperty({ description: 'Trạng thái đơn hàng' })
  @IsEnum(NSBusinessOrder.EApproveStatus)
  approveStatus?: NSBusinessOrder.EApproveStatus = NSBusinessOrder.EApproveStatus.PENDING_APPROVE

  @ApiProperty({ description: 'Trạng thái giao hàng' })
  @IsNotEmpty()
  @IsEnum(NSBusinessOrder.EDeliveryStatus)
  deliveryStatus: NSBusinessOrder.EDeliveryStatus = NSBusinessOrder.EDeliveryStatus.PENDING_DELIVERY

  @ApiProperty({ description: 'ID của kho' })
  @IsNotEmpty({ message: 'ID của kho không được để trống' })
  warehouseId: string
}
