import { Column, Entity, OneToMany } from 'typeorm'
import { QuestionEntity } from './s_question.entity'
import { TopicEntity } from './s_topic.entity'
import { CompanyCategoriesEntity } from './s_companyCategories.entity'
import { BaseEntity } from '../core/base.entity'
import { SurveyHistoryEntity } from './s_surveyHistory.entity'

@Entity('s_categories')
export class CategoriesEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'text',
    nullable: true,
  })
  description: string

  /** Danh sách câu hỏi */
  @OneToMany(() => TopicEntity, (p) => p.category)
  topics: Promise<TopicEntity[]>

  /** Lịch sử */
  @OneToMany(() => SurveyHistoryEntity, (p) => p.category)
  histories: Promise<SurveyHistoryEntity[]>
}
