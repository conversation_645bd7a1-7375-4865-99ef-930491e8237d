import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import { PartnerRepository } from '../../../repositories'
import { PartnerController } from './partner.controller'
import { PartnerService } from './partner.service'
import { UserRepository } from '../../../repositories/core/user.repository'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([PartnerRepository, UserRepository])],
  controllers: [PartnerController],
  providers: [PartnerService],
  exports: [PartnerService],
})
export class PartnerModule {}
