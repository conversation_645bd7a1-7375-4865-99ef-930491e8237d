import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DeliveryScheduleEntity } from '../../../entities';

import { DeliveryScheduleController } from './poDeliverySchedule.controller';
import { DeliveryScheduleService } from './poDeliverySchedule.service';
import { DeliveryScheduleRepository } from '../../../repositories';


@Module({
  imports: [TypeOrmModule.forFeature([DeliveryScheduleEntity, DeliveryScheduleRepository])],
  controllers: [DeliveryScheduleController],
  providers: [DeliveryScheduleService],
  exports: [DeliveryScheduleService],
})
export class DeliveryScheduleModule { }
