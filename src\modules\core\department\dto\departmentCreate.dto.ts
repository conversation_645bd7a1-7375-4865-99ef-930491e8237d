import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'

export class DepartmentCreateDto {
  @ApiProperty({ description: 'Tên phòng ban' })
  @IsNotEmpty({ message: 'Tên phòng ban không được để trống' })
  @IsString()
  name: string

  @ApiProperty({ description: 'Mã phòng ban' })
  @IsString()
  @IsNotEmpty({ message: 'Mã phòng ban không được để trống' })
  code: string

  @ApiProperty({ description: 'Mô tả' })
  description: string
}
