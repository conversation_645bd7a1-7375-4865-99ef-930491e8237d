import { Column, Entity, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from './base.entity'

@Entity('purchase_order_approve')
export class PurchaseOrderApproveEntity extends BaseEntity {
    @ApiProperty({ description: 'ID PO' })
    @Column("uuid")
    @Index()
    purchaseOrderId: string;

    @ApiProperty({ description: 'ID Nhà cung cấp / xuất xứ' })
    @Column("uuid")
    supplierId: string

    @ApiProperty({ description: 'Approval status' })
    @Column({ type: 'boolean', default: false })
    isApproved: boolean;
}

