import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import {
  BrandRepository,
  ItemComboRepository,
  ItemDetailRepository,
  ItemPriceRepository,
  ItemRepository,
  PackingRepository,
  UnitRepository,
  InboundDetailRepository,
  InboundRepository,
  OutboundDetailRepository,
  WarehouseProductRepository,
  ItemGroupRepository,
  ItemCategoryRepository,
  ItemTypeRepository,
  SupplierRepository,
  MediaRepository,
  WarehouseProductDetailRepository,
  TaxRepository,
  WarehouseRepository,
} from './../../../repositories'
import { ItemController } from './item.controller'
import { ItemService } from './item.service'
import { ItemPublicController } from './itemPublic.controller'
import { ActionLogModule } from '../actionLog/actionLog.module'
import { PricingRepository } from '../../../repositories/core/pricing.repository'

@Module({
  imports: [
    ActionLogModule,
    TypeOrmExModule.forCustomRepository([
      BrandRepository,
      ItemComboRepository,
      ItemDetailRepository,
      ItemPriceRepository,
      ItemRepository,
      PackingRepository,
      ItemGroupRepository,
      ItemCategoryRepository,
      ItemTypeRepository,
      UnitRepository,
      InboundDetailRepository,
      InboundRepository,
      OutboundDetailRepository,
      WarehouseProductRepository,
      SupplierRepository,
      MediaRepository,
      WarehouseProductDetailRepository,
      TaxRepository,
      WarehouseRepository,
      PricingRepository,
    ]),
  ],
  controllers: [ItemController, ItemPublicController],
  providers: [ItemService],
  exports: [ItemService],
})
export class ItemModule {}
