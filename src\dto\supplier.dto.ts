import { ApiProperty } from '@nestjs/swagger'
import { IsOptional, IsString, IsUUID } from 'class-validator'
import { StoreDto } from './user.dto'

/** Interface supplier */
export class SupplierDto {
  @ApiProperty({ description: 'Id user', example: 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx' })
  @IsUUID()
  id: string

  @ApiProperty({ description: 'Tài khoản', example: 'userX' })
  @IsString()
  username: string

  @ApiProperty({ description: 'Loại tài khoản' })
  @IsString()
  type?: string

  @ApiProperty({ description: 'Id công ty', example: 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx' })
  companyId?: string

  @ApiProperty({ description: 'Là admin?', example: 'false' })
  isAdmin: boolean

  userId?: string

  lstStore?: StoreDto[]

  @IsString()
  employeeId?: string

  is3PL?: boolean
  isSupplier?: boolean
  isDistributor?: boolean
}
