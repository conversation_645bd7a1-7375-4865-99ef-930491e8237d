import { SupplierRepository } from './../../../repositories/core/supplier.repository'
import { InboundService } from './../../wms/inbound/inbound.service'
import { Injectable } from '@nestjs/common'
import {
  CityRepository,
  DeliveryNoteChildDetailRepository,
  DeliveryNoteChildRepository,
  DeliveryNoteLastMileProductRepository,
  DeliveryNoteRepository,
  DistrictRepository,
  EmployeeRepository,
  InboundDetailRepository,
  InboundRepository,
  ItemComboRepository,
  ItemDetailRepository,
  ItemPriceRepository,
  ItemRepository,
  PurchaseOrderItemRepository,
  PurchaseOrderRepository,
  PurchaseOrderSaleOrderRepository,
  UserRepository,
  WardRepository,
  WarehouseProductDetailRepository,
  WarehouseRepository,
} from '../../../repositories'
import {
  CreateInboundFromDeliveryNoteChildDto,
  FilterDeliverNoteChildDetailDto,
  FilterDeliverNoteChildDto,
  SendPartnerDto,
  UUIDDto,
} from './dto/deliveryNoteChild.dto'
import { omsApiHelper } from '../../../helpers/omsApiHelper'
import { Request as IRequest } from 'express'
import { Between, In, IsNull } from 'typeorm'
import { CREATE_SUCCESS, enumData, NSPo, NSRecurringOrder, UPDATE_SUCCESS } from '../../../constants'
import { OutboundEntity } from '../../../entities'
import { OutboundService } from '../../wms/outbound/outbound.service'
import { coreHelper } from '../../../helpers'
import { ImportSendPartnerDto } from './dto/ImportSendPartnerDto'
import axios from 'axios'
import { PrintTemplateUrl } from '../../../constants/PrintTemplateUrl'
import * as moment from 'moment'

@Injectable()
export class DeliveryNoteChildService {
  constructor(
    private readonly deliveryNoteRepo: DeliveryNoteRepository,
    private readonly deliveryNoteChildRepo: DeliveryNoteChildRepository,
    private readonly deliveryNoteChildDetailRepo: DeliveryNoteChildDetailRepository,
    private readonly deliveryNoteLastMileProductRepo: DeliveryNoteLastMileProductRepository,
    private readonly purchaseOrderRepository: PurchaseOrderRepository,
    private readonly purchaseOrderItemRepository: PurchaseOrderItemRepository,
    private readonly warehouseRepository: WarehouseRepository,
    private readonly inboundRepository: InboundRepository,
    private readonly inboundDetailRepository: InboundDetailRepository,
    private readonly itemPriceRepository: ItemPriceRepository,
    private readonly itemRepository: ItemRepository,
    private readonly itemComboRepo: ItemComboRepository,
    private readonly supplierRepository: SupplierRepository,
    private readonly warehouseProductDetail: WarehouseProductDetailRepository,
    private readonly outboundService: OutboundService,
    private readonly userRepository: UserRepository,
    private readonly employeeRepository: EmployeeRepository,
    private readonly cityRepository: CityRepository,
    private readonly wardRepository: WardRepository,
    private readonly districtRepository: DistrictRepository,
    private readonly purchaseOrderSoRepo: PurchaseOrderSaleOrderRepository,
    private readonly inboundService: InboundService,
  ) {}
  async paginationDeliveryNoteChild(params: FilterDeliverNoteChildDto) {
    const { code, dateFrom, dateTo, pageIndex, pageSize, thirdPartyId } = params
    let wheres: any = []

    if (code) wheres.push(`dnc."code" = '${code}'`)
    if (thirdPartyId) wheres.push(`dnc."thirdPartyId" = '${thirdPartyId}'`)
    if (dateFrom) wheres.push(`dnc."createdAt" >= '${new Date(dateFrom).toISOString()}'`)
    if (dateTo) wheres.push(`dnc."createdAt" <= '${new Date(dateTo).toISOString()}'`)
    const whereClause = wheres.length > 0 ? `WHERE ${wheres.join(' AND ')}` : ''

    try {
      const query = `
        SELECT 
          dnc.*, 
          dn."poId",
          dn."code" as deliveryNoteCode, 
          dn.id as "deliveryNoteId", 
          dn."itemTotalAmount"
        FROM delivery_note_child as dnc
        LEFT JOIN delivery_note as dn ON dnc."deliveryNoteId" = dn.id
        ${whereClause}
        ORDER BY dnc."createdAt" DESC
      `
      const { data, total } = await this.deliveryNoteChildRepo.queryPagination(query, { pageIndex, pageSize })
      for (let lastMile of data) {
      }
      const mappingResult = []
      for (const item of data) {
        item.poItems = []
        const lastMileDetail = await this.deliveryNoteChildDetailRepo.find({ where: { deliveryNoteChildId: item.id } })

        for (let poId of item.poId) {
          const items = await this.purchaseOrderItemRepository.find({
            where: { purchaseOrderId: poId, partnerId: In(lastMileDetail.map((val) => val.partnerId)) },
          })
          item.poItems.push(...items)
        }

        const poLst = await this.purchaseOrderRepository.find({ where: { id: In(item.poId) } })
        const totalAmount = item.poItems.reduce((acc, po) => acc + Number(po.totalAmount), 0)
        const totalAmountVat = item.poItems.reduce((acc, po) => acc + Number(po.totalAmountVat), 0)

        mappingResult.push({
          ...item,
          poLst,
          totalAmount,
          totalAmountVat,
          vat: totalAmountVat - totalAmount,
        })
      }

      return { data: mappingResult, total }
    } catch (error) {
      throw new Error(error)
    }
  }

  async paginationDeliveryNoteChildSupplier(params: FilterDeliverNoteChildDto) {
    const { code, dateFrom, dateTo, pageIndex, pageSize, thirdPartyId } = params
    let wheres: any = []

    if (code) wheres.push(`dnc."code" = '${code}'`)
    if (thirdPartyId) wheres.push(`dnc."thirdPartyId" = '${thirdPartyId}'`)
    if (dateFrom) wheres.push(`dnc."createdAt" >= '${new Date(dateFrom).toISOString()}'`)
    if (dateTo) wheres.push(`dnc."createdAt" <= '${new Date(dateTo).toISOString()}'`)
    const whereClause = wheres.length > 0 ? `WHERE ${wheres.join(' AND ')}` : ''

    try {
      const query = `
        SELECT 
          dnc.*, 
          dn."poId",
          dn."code" as deliveryNoteCode, 
          dn.id as "deliveryNoteId", 
          dn."itemTotalAmount"
        FROM delivery_note_child as dnc
        LEFT JOIN delivery_note as dn ON dnc."deliveryNoteId" = dn.id
        ${whereClause}
        ORDER BY dnc."createdAt" DESC
      `
      const { data, total } = await this.deliveryNoteChildRepo.queryPagination(query, { pageIndex, pageSize })
      for (let lastMile of data) {
      }
      const mappingResult = []
      for (const item of data) {
        item.poItems = []
        const lastMileDetail = await this.deliveryNoteChildDetailRepo.find({ where: { deliveryNoteChildId: item.id } })

        for (let poId of item.poId) {
          const items = await this.purchaseOrderItemRepository.find({
            where: { purchaseOrderId: poId, partnerId: In(lastMileDetail.map((val) => val.partnerId)) },
          })
          item.poItems.push(...items)
        }

        const poLst = await this.purchaseOrderRepository.find({ where: { id: In(item.poId) } })
        const totalAmount = item.poItems.reduce((acc, po) => acc + Number(po.totalAmount), 0)
        const totalAmountVat = item.poItems.reduce((acc, po) => acc + Number(po.totalAmountVat), 0)
        if (poLst.find((val) => val.purchaseOrderType == NSPo.EPoType.SUPPLIER)) {
          mappingResult.push({
            ...item,
            poLst,
            totalAmount,
            totalAmountVat,
            vat: totalAmountVat - totalAmount,
          })
        }
      }

      return { data: mappingResult, total: mappingResult.length }
    } catch (error) {
      throw new Error(error)
    }
  }

  async paginationDeliveryNoteChildDetail(body: FilterDeliverNoteChildDetailDto) {
    try {
      const { partnerIds, dateFrom, dateTo, status, code, ...param } = body
      const whereCon: any = { isDeleted: false }
      if (status) whereCon.status = status
      if (code) whereCon.code = code
      if (dateFrom && dateTo) whereCon.createdAt = Between(new Date(dateFrom), new Date(dateTo))

      const { data, total } = await this.deliveryNoteChildDetailRepo.findPagination(
        {
          where: {
            ...(partnerIds && partnerIds.length > 0 && { partnerId: In(partnerIds) }),
            sendStatus: In([NSPo.EDeliveryNoteSendInboundStatus.SENT_MBC, NSPo.EDeliveryNoteSendInboundStatus.SENT_CUSTOMER]),
            ...whereCon,
          },
          order: { createdAt: 'DESC' },
        },
        param,
      )

      const resultMapping = await Promise.all(
        data.map(async (dld) => {
          const dlc = await this.deliveryNoteChildRepo.findOne({
            where: { id: dld.deliveryNoteChildId },
            select: { id: true, deliveryNoteId: true, thirdPartyId: true },
          })
          const dln = await this.deliveryNoteRepo.findOne({ where: { id: dlc.deliveryNoteId }, select: { id: true, poId: true } })

          const poItems = await this.purchaseOrderItemRepository.find({ where: { purchaseOrderId: In(dln.poId), partnerId: In(partnerIds) } })
          const sup = await this.supplierRepository.findOne({ where: { id: dlc.thirdPartyId } })

          const quantityProducts = await this.deliveryNoteLastMileProductRepo.find({
            where: { deliveryNoteChildDetailId: dld.id },
            select: {
              deliveryNoteChildDetailId: true,
              productId: true,
              quantityBegin: true,
              quantityExport: true,
              poId: true,
            },
          })

          const comboIds = Array.from(
            new Set(
              poItems
                .map((p) => {
                  if (p.comboId && p.partnerId == dld.partnerId) return p.comboId
                })
                .filter((i) => i),
            ),
          )
          const itemIds = Array.from(
            new Set(
              poItems
                .map((p) => {
                  if (!p.comboId && p.partnerId == dld.partnerId) return p.itemId
                })
                .filter((i) => i),
            ),
          )

          const lstCombo = await this.itemRepository.find({
            where: { id: In([...comboIds, ...itemIds]) },
            select: { id: true, code: true, name: true, isCombo: true },
          })

          const lstPriceProducts = await this.itemPriceRepository.find({
            where: { itemId: In([...comboIds, ...itemIds]), isFinal: true },
            select: { id: true, itemId: true, priceSell: true, priceCapital: true, priceInput: true },
          })

          const mappingPoCombo = poItems
            .filter((po) => po.comboId)
            .map((po) => {
              const combo = lstCombo.find((c) => c.id === po.comboId)
              return {
                ...po,
                poDetailId: po.id,
                comboName: combo?.name,
                comboCode: combo?.code,
              }
            })

          const mappingPoItems = poItems
            .filter((po) => !po.comboId)
            .map((po) => {
              const item = lstCombo.find((c) => c.id === po.itemId)
              return {
                ...po,
                poDetailId: po.id,
                itemName: item?.name,
                itemCode: item?.code,
              }
            })

          const inbound = await this.inboundRepository.find({ where: { deliveryNoteLmCode: dld.code } })
          const inboundDetails = await this.inboundDetailRepository.find({ where: { inboundId: In(inbound.map((val) => val.id)) } })
          const mappingQuantityProduct = []
          for (let i = 0; i < quantityProducts.length; i++) {
            const qp = quantityProducts[i]
            const product = lstCombo.find((c) => c.id === qp.productId)
            const price = lstPriceProducts.find((p) => p.itemId === qp.productId)
            const mapInbound = inboundDetails.filter((i) => i.productId === qp.productId)
            const totalQuantityInbound = mapInbound.reduce((acc, cur) => acc + cur.quantity, 0)
            const poItem = mappingPoItems.find((p) => p.itemId === qp.productId)

            // Tính lại priceInput theo giá từ PO items
            const _products = await this.purchaseOrderItemRepository.find({
              where: [
                { purchaseOrderId: qp.poId, itemId: qp.productId },
                { purchaseOrderId: qp.poId, comboId: qp.productId },
              ],
            })
            const _inputPrice = _products.reduce((acc, cur) => acc + Number(cur.totalAmount), 0)
            const quantityTotalPo = _products.reduce((acc, cur) => acc + Number(cur.quantityBasicUnit), 0)

            // Nếu product.isCombo, đếm thành phần trong combo
            let count = 1
            if (product?.isCombo) {
              const itemInCombo = await this.itemComboRepo.find({ where: { itemId: qp.productId } })
              count = itemInCombo.reduce((acc, cur) => acc + cur.quantity, 0)
            }
            const _quantityProduct = +quantityTotalPo / +count

            mappingQuantityProduct.push({
              ...qp,
              purchaseOrderId: qp.poId,
              productName: product?.name,
              productCode: product?.code,
              isCombo: product?.isCombo,
              priceSell: +price?.priceSell,
              priceCapital: +price?.priceCapital,
              priceInput: +_inputPrice / +_quantityProduct, // Đơn giá
              buyPrice: +_inputPrice / +_quantityProduct, // Đơn giá
              totalQuantity: +_quantityProduct, // Tổng số lượng trong PO
              numberInbound: mapInbound.length,
              numberProductInbound: totalQuantityInbound,
            })
          }

          return {
            ...dld,
            deliveryName: sup?.name,
            deliveryCode: sup?.code,
            deliveryPhone: sup?.phone,
            deliveryAddress: sup?.address,
            deliveryId: sup?.id,
            combo: mappingPoCombo,
            item: mappingPoItems,
            quantityDetail: mappingQuantityProduct,
          }
        }),
      )

      return { data: resultMapping, total }
    } catch (error) {
      console.log(error)
      throw new Error(error)
    }
  }

  async detailDeliveryNoteChild(id: string, req: IRequest) {
    try {
      const check = await this.deliveryNoteChildRepo.findOne({ where: { id } })
      if (!check) {
        throw new Error(`Không tìm thấy phiếu giao nhận với ID ${id}`)
      }
      const dlChildLst = await this.deliveryNoteChildDetailRepo.find({ where: { deliveryNoteChildId: check.id } })
      const partnerIdLst = dlChildLst.map((val) => val.partnerId)

      const deliveryNote = await this.deliveryNoteRepo.findOne({ where: { id: check.deliveryNoteId } }) // Lấy thông tin phiếu cha
      const purchaseOrderId = deliveryNote.poId // poId là kiểu mảng vì có trường hợp  DeliveryNote lưu nhiều PO

      const purchaseOrder = await this.purchaseOrderRepository.findOne({ where: { id: purchaseOrderId[0] }, select: { createdBy: true } })
      const poItems = await this.purchaseOrderItemRepository.find({ where: { purchaseOrderId: In(purchaseOrderId) } })

      const itemIds = Array.from(new Set(poItems.filter((val) => !val.comboId).map((val) => val.itemId)))
      const comboIds = Array.from(new Set(poItems.map((val) => val.comboId)))
      const items = await this.itemRepository.find({ select: { id: true, code: true, name: true } })
      const combos = await this.itemRepository.find({ where: { id: In(comboIds), isCombo: true }, select: { id: true, code: true, name: true } })

      const partnerLst = []
      for (let i = 0; i < dlChildLst.length; i++) {
        const partner = dlChildLst[i]
        const partnerId = partner.partnerId
        const combo = [],
          item = []
        let totalAmountVat = 0
        let totalAmount = 0

        const filterPoItemWithPartner = poItems.filter((val) => val.partnerId === partnerId)
        for (let y = 0; y < filterPoItemWithPartner.length; y++) {
          const poItem = filterPoItemWithPartner[y]
          if (poItem.partnerId === partnerId) {
            if (poItem.comboId) {
              const c = combos.find((val) => val.id === poItem.comboId)
              const i = items.find((val) => val.id === poItem.itemId)
              combo.push({
                ...poItem,
                itemCode: i.code,
                itemName: i.name,
                itemId: i.id,
                comboName: c.name,
                comboId: c.id,
                comboCode: c.code,
                quantity: poItem.quantityCombo,
                poDetailId: poItem.id,
              })
            } else {
              const i = items.find((val) => val.id === poItem.itemId)
              item.push({
                ...poItem,
                itemName: i.name,
                itemId: i.id,
                itemCode: i.code,
                quantity: poItem.quantityBasicUnit,
                poDetailId: poItem.id,
              })
            }
            totalAmountVat += +poItem.totalAmountVat
            totalAmount += +poItem.totalAmount
          }
        }

        partnerLst.push({
          ...partner,
          totalAmount: Number(totalAmount),
          totalAmountVat: Number(totalAmountVat),
          vat: Math.round(Number(totalAmountVat) - Number(totalAmount)),
          combo,
          item,
        })
      }

      // const poItemsNotPartner = await this.purchaseOrderItemRepository.find({ where: { purchaseOrderId: In(purchaseOrderId), partnerId: IsNull() } });
      // for (let y = 0; y < poItemsNotPartner.length; y++) {
      //   const poItem = poItemsNotPartner[y];
      //   let totalAmountVat = 0;
      //   let totalAmount = 0;
      //   const combo = [], item = []
      //   // Không có partnerId
      //   if (poItem.comboId) {
      //     const c = combos.find(val => val.id === poItem.comboId)
      //     combo.push({
      //       ...poItem,
      //       comboName: c.name,
      //       comboId: c.id,
      //       comboCode: c.code,
      //       quantity: poItem.quantityCombo,
      //       poDetailId: poItem.id
      //     })
      //   } else {
      //     const i = items.find(val => val.id === poItem.itemId)
      //     item.push({
      //       ...poItem,
      //       itemName: i.name,
      //       itemId: i.id,
      //       itemCode: i.code,
      //       quantity: poItem.quantityBasicUnit,
      //       poDetailId: poItem.id
      //     })
      //   }
      //   totalAmountVat += +poItem.totalAmountVat;
      //   totalAmount += +poItem.totalAmount;
      // }

      const deliveryIds = Array.from(new Set(poItems.map((val) => val.deliveryId)))
      /** Lấy thông tin của Balance và 3PL */
      const deliveryLst = await this.supplierRepository.find({
        where: { id: In(deliveryIds) },
        relations: { ward: true, district: true, city: true },
      })
      const delivery = await Promise.all(
        deliveryLst.map(async (val) => {
          const ward = await val.ward
          const district = await val.district
          const city = await val.city
          return {
            name: val.name,
            phone: val.phone,
            email: val.email,
            address: val.address,
            ward: ward.name,
            district: district.name,
            city: city.name,
          }
        }),
      )

      const user = await this.userRepository.findOne({ where: { id: purchaseOrder.createdBy } })
      const employee = await this.employeeRepository.findOne({ where: { id: user.employeeId } })

      const city = await this.cityRepository.findOne({ where: { id: employee.cityId }, select: { name: true } })
      const district = await this.districtRepository.findOne({ where: { id: employee.districtId }, select: { name: true } })
      const ward = await this.wardRepository.findOne({ where: { id: employee.wardId }, select: { name: true } })

      const blf = {
        name: employee.name,
        phone: employee.phone,
        email: employee.email,
        address: employee.address,
        city: city.name,
        district: district.name,
        ward: ward.name,
      }

      const result = {
        partner: partnerLst,
        delivery: delivery[0],
        balanceLife: blf,
      }
      return result
    } catch (error) {
      throw new Error(error)
    }
  }

  async createInboundWithCombo(body: CreateInboundFromDeliveryNoteChildDto, req: IRequest) {
    return await this.inboundRepository.manager.transaction(async (trans) => {
      try {
        const { id, partnerId, createBy, products } = body
        const checkDLNDetail = await this.deliveryNoteChildDetailRepo.findOne({ where: { id: id } })
        if (!checkDLNDetail) {
          throw new Error(`Không tìm thấy chi tiết phiếu giao nhận con`)
        }
        if (checkDLNDetail && checkDLNDetail.sendStatus == NSPo.EDeliveryNoteSendInboundStatus.SEND_PENDING) {
          throw new Error(`Phiếu này chưa được gửi tới MBC`)
        }
        if (checkDLNDetail && checkDLNDetail.status == NSPo.EDeliveryNoteInboundStatus.INSTOCK) {
          throw new Error(`Phiếu này đã đươc nhập kho`)
        }
        const lstLastMileProduct = await this.deliveryNoteLastMileProductRepo.find({ where: { deliveryNoteChildDetailId: id } })

        const wh = await this.warehouseRepository.findOne({ where: { storeId: partnerId } })
        if (!wh) {
          throw new Error(`Không tìm thấy kho với partnerId ${partnerId}`)
        }

        const poIds = Array.from(new Set(products.map((item) => item.purchaseOrderId)))
        if (poIds.length == 0) throw new Error(`Không có dữ liệu nhập kho`)

        for (let i = 0; i < poIds.length; i++) {
          const poId = poIds[i]
          const poType = await this.purchaseOrderRepository.findOne({
            where: { id: poId, isDeleted: false, approveStatus: NSPo.EPoStatus.APPROVED, status: NSPo.EPoStatus.COMPLETE },
          })
          const lstDetail = []
          const lstUpdateLMP = []
          let inbound: any = {}
          // Lấy danh sách combo sản phẩm combo trong PO
          const lstProducts = products.filter((item) => item.purchaseOrderId == poId)
          if (poType) {
            // PO combo theo kỳ
            if (poType.purchaseOrderType == 'WITHCOMBO') {
              const lstComboIds = lstProducts.map((item) => item.productId)
              const lstComboPrice = await this.itemPriceRepository.find({ where: { itemId: In(lstComboIds), isFinal: true } }) // Lấy ra giá combos
              const mappingCombo = lstProducts.map((val) => {
                const comboPrice = lstComboPrice.find((price) => price.itemId === val.productId)
                return {
                  ...val,
                  sellPrice: comboPrice.priceSell,
                }
              })

              // Nhóm combo lại theo comboId, cộng dồn sellPrice
              const groupByCombo = mappingCombo.reduce((acc, cb) => {
                const existingCombo = acc.find((item) => item.comboId === cb.productId)
                if (existingCombo) {
                  existingCombo.sellPrice += Number(cb.sellPrice)
                  existingCombo.quantity += Number(cb.quantity)
                } else {
                  acc.push({ ...cb })
                }
                return acc
              }, [])

              for (let gc = 0; gc < groupByCombo.length; gc++) {
                const combo = groupByCombo[gc]
                const { productId, quantity, productName, productCode, poDetailId, purchaseOrderId } = combo
                const itemPrice = lstComboPrice.find((price) => price.itemId === productId)
                const lmProduct = lstLastMileProduct.find((i) => i.productId == productId)

                if (lmProduct) {
                  lstUpdateLMP.push({
                    ...lmProduct,
                    quantityExport: Number(quantity) + Number(lmProduct.quantityExport),
                  })
                }

                const _comboInPo = await this.purchaseOrderItemRepository.find({ where: { purchaseOrderId: poId, comboId: productId } })
                const _comboPriceInPo = _comboInPo.reduce((acc, item) => acc + Number(item.totalAmount), 0)

                if (purchaseOrderId == poId) {
                  lstDetail.push({
                    poDetailId: poDetailId,
                    productCode: productCode,
                    productName: productName,
                    productId: productId,
                    quantity: +combo.quantity,
                    totalQuantity: +combo.quantity,
                    price: +itemPrice?.priceSell, // Giá bán chung
                    priceVND: +itemPrice?.priceSell,
                    totalPrice: +combo.quantity * +itemPrice?.priceSell, // Giá bán chung * số lượng
                    totalPriceVND: +combo.quantity * +itemPrice?.priceSell,
                    buyPrice: +_comboPriceInPo, // priceSell là giá từ PO
                    buyPriceVND: +_comboPriceInPo, // priceSell là giá từ PO
                    manufactureDate: new Date('2000-01-01'),
                    expiryDate: new Date('2500-01-01'),
                  })
                }
              }

              inbound = {
                isFromDeliveryNote: true,
                poId: poId,
                poCode: poType.purchaseOrderCode,
                exchangeRate: 1,
                warehouseId: wh.id,
                currencyCode: 'VND',
                createBy,
                lstDetail: lstDetail,
              }
            } else {
              // PO bán lẻ
              const lstProductId = lstProducts.map((item) => item.productId)
              const lstProductInfo = await this.itemRepository.find({ where: { id: In(lstProductId) }, relations: { prices: true } })
              const mappingProducts = await Promise.all(
                lstProducts.map(async (val) => {
                  const item = lstProductInfo.find((i) => i.id == val.productId)
                  const itemPrice = (await item.prices).find((i) => i.isFinal == true)
                  const poItem: any = lstProducts.find((i) => i.productId == item.id && i.purchaseOrderId == poId)
                  return {
                    ...val,
                    isCombo: item.isCombo,
                    priceSell: itemPrice?.priceSell,
                    buyPrice: poItem?.buyPrice,
                    productName: item?.name,
                    productCode: item?.code,
                  }
                }),
              )

              // Xử lý Hàng lẻ thành phần
              const items = mappingProducts.filter((item) => item.isCombo == false)
              if (items) {
                for (let item of items) {
                  if (!item.expiryDate || !item.manufactureDate) {
                    throw new Error(`Sản phẩm [${item.productName}] chưa nhập NSX và HSD`)
                  }
                  const lmProduct = lstLastMileProduct.find((i) => i.productId == item.productId)
                  if (lmProduct) {
                    lstUpdateLMP.push({
                      ...lmProduct,
                      quantityExport: +item.quantity + lmProduct.quantityExport,
                    })
                  }
                  lstDetail.push({
                    poDetailId: item.poDetailId,
                    productCode: item.productCode,
                    productName: item.productName,
                    productId: item.productId,
                    quantity: +item.quantity,
                    totalQuantity: +item.quantity,
                    price: +item.priceSell,
                    priceVND: +item.priceSell,
                    totalPrice: +item.quantity * +item.priceSell,
                    totalPriceVND: +item.quantity * +item.priceSell,
                    buyPrice: +item.buyPrice,
                    buyPriceVND: +item.buyPrice * +item.quantity,
                    manufactureDate: item.manufactureDate,
                    expiryDate: item.expiryDate,
                  })
                }
              }

              // Xử lý hàng combo
              const combo = mappingProducts.filter((item) => item.isCombo == true)
              if (combo) {
                // Nhóm combo lại theo comboId, cộng dồn sellPrice
                const groupByCombo = combo.reduce((acc, cb) => {
                  const existingCombo = acc.find((item) => item.productId === cb.productId)
                  if (existingCombo) {
                    existingCombo.sellPrice += Number(cb.priceSell)
                  } else {
                    acc.push({ ...cb })
                  }
                  return acc
                }, [])

                for (let gc = 0; gc < groupByCombo.length; gc++) {
                  const combo = groupByCombo[gc]
                  console.log(combo.quantity)
                  const lmProduct = lstLastMileProduct.find((i) => i.productId == combo.comboId)
                  if (lmProduct) {
                    lstUpdateLMP.push({
                      ...lmProduct,
                      quantityExport: combo.quantity + lmProduct.quantityExport,
                    })
                  }

                  const _comboInPo = await this.purchaseOrderItemRepository.find({ where: { purchaseOrderId: poId, comboId: combo.comboId } })
                  const _comboPriceInPo = _comboInPo.reduce((acc, item) => acc + Number(item.totalAmount), 0)

                  lstDetail.push({
                    poDetailId: combo.poDetailId,
                    productCode: combo.productCode,
                    productName: combo.productName,
                    productId: combo.productId,
                    quantity: combo.quantity,
                    totalQuantity: combo.quantity,
                    price: +combo.priceSell,
                    priceVND: +combo.priceSell,
                    totalPrice: +combo.quantity * +combo.priceSell,
                    totalPriceVND: +combo.quantity * +combo.priceSell,
                    buyPrice: +_comboPriceInPo, // Tính giá mua từ thành phần của PO
                    buyPriceVND: +_comboPriceInPo, // Tính giá mua từ thành phần của PO
                    manufactureDate: new Date('2000-01-01'),
                    expiryDate: new Date('2500-01-01'),
                  })
                }
              }

              inbound = {
                isFromDeliveryNote: true,
                poId: poId,
                poCode: poType.purchaseOrderCode,
                exchangeRate: 1,
                warehouseId: wh.id,
                currencyCode: 'VND',
                createBy,
                lstDetail,
              }
            }
            if (lstDetail.length == 0) throw new Error(`Không tìm thấy danh sách sản phẩm nhập kho`)

            for (let i = 0; i < lstUpdateLMP.length; i++) {
              const lmp = lstUpdateLMP[i]
              if (+lmp.quantityBegin < +lmp.quantityExport) {
                throw new Error(
                  `Sản phẩm ${lmp.productName} không đủ số lượng để nhập kho (${lmp.quantityBegin - lmp.quantityExport}/ ${lmp.quantity})`,
                )
              }
            }
            await this.inboundService.createData({ ...inbound, deliveryNoteLmCode: checkDLNDetail.code }, req)
          }
        }
        return { message: CREATE_SUCCESS }
      } catch (error) {
        throw new Error(error)
      }
    })
  }

  // Giao hàng cho đối tạc hoặc khách hàng
  async sendPartner(body: SendPartnerDto, req?: IRequest) {
    const { id, combo, item, partnerId } = body
    //const wareHouse = await this.warehouseRepository.findOne({ where: { storeId: partnerId } })
    //if (!wareHouse) throw new Error(`Không tìm thấy kho của đối tác`)
    const dl = await this.deliveryNoteChildDetailRepo.findOne({ where: { id } })
    if (!dl) throw new Error(`Không tìm thấy phiếu Lastmile Delivery`)
    if (dl && dl.sendStatus == NSPo.EDeliveryNoteSendInboundStatus.SENT_CUSTOMER) throw new Error(`Phiếu này đã gửi cho khách hàng`)
    if (dl && dl.sendStatus == NSPo.EDeliveryNoteSendInboundStatus.SENT_MBC) throw new Error(`Phiếu này đã gửi cho MBC`)

    const orderIds = JSON.parse(dl.soId)
    const orderLst = await omsApiHelper.getOrderByIds(req, { orderListIds: orderIds })
    const orderLstData = orderLst.data
    const lstOutboundDetail = []

    /** Tạo phiếu xuất kho 3PL */
    if (orderLstData.length === 0) {
      const productIds = Array.from(new Set([combo.map((c): any => c.itemId), item.map((i): any => i.itemId)])).flat()
      const uniqueProductIds = Array.from(new Set(productIds))
      const warehouseIds = Array.from(new Set([combo.map((c): any => c.warehouseId), item.map((i): any => i.warehouseId)])).flat()
      const uniqueWarehouseIds = Array.from(new Set(warehouseIds))
      const wh = await this.warehouseRepository.findOne({ where: { id: In(uniqueWarehouseIds) } })
      if (!wh) throw new Error(`Không tìm thấy kho của 3PL`)
      const whp = await this.warehouseProductDetail.find({
        where: {
          productId: In(uniqueProductIds),
          warehouseId: In(uniqueWarehouseIds),
        },
      })
      if (whp.length > 0) {
        for (const i of item) {
          const detail = whp.find((w) => w.productId == i.itemId)
          if (detail) {
            lstOutboundDetail.push({
              productId: i.itemId,
              productName: i.itemName,
              productCode: i.itemCode,
              expiryDate: detail.expiryDate,
              manufactureDate: detail.manufactureDate,
              quantity: +i.quantity,
              inventory: detail.quantity,
              productDetailId: detail.productDetailId,
              lotNumber: 1,
            })
          }
        }
        for (const c of combo) {
          const detail = whp.find((w) => w.productId == c.itemId)
          if (detail) {
            lstOutboundDetail.push({
              productId: c.itemId,
              productName: c.itemName,
              productCode: c.itemCode,
              expiryDate: detail.expiryDate,
              manufactureDate: detail.manufactureDate,
              quantity: +c.quantityCombo,
              inventory: detail.quantity,
              productDetailId: detail.productDetailId,
              lotNumber: 1,
            })
          }
        }
        const outbound = {
          warehouseId: wh.id,
          createdAt: new Date(),
          createBy: wh.storeId,
          lstOutboundDetail: [],
          type: enumData.OutboundType.INTERNAL_WAREHOUSE.code,
          description: dl.type === NSPo.DeliveryLastMileType.MBC ? 'Xuất kho giao MBC' : 'Xuất kho giao khách hàng',
        }
        outbound['lstOutboundDetail'] = lstOutboundDetail
        await this.outboundService.createDataApproved(outbound, req)
      }
    } else {
      const productIds = Array.from(new Set([combo.map((c): any => c.comboId), item.map((i): any => i.itemId)])).flat()
      const uniqueProductIds = Array.from(new Set(productIds))
      const warehouseIds = Array.from(new Set([combo.map((c): any => c.warehouseId), item.map((i): any => i.warehouseId)])).flat()
      const uniqueWarehouseIds = Array.from(new Set(warehouseIds))
      for (const warehouseId of uniqueWarehouseIds) {
        const wh = await this.warehouseRepository.findOne({ where: { id: warehouseId } })
        if (!wh) throw new Error(`Không tìm thấy kho của 3PL`)
        const whp = await this.warehouseProductDetail.find({ where: { productId: In(uniqueProductIds), warehouseId: wh.id } })
        if (whp.length > 0) {
          const outbound = {
            warehouseId: wh.id,
            createdAt: new Date(),
            createBy: wh.storeId,
            lstOutboundDetail: [],
            type: enumData.OutboundType.INTERNAL_WAREHOUSE.code,
            description: dl.type === NSPo.DeliveryLastMileType.MBC ? 'Xuất kho giao MBC' : 'Xuất kho giao khách hàng',
          }
          if (orderLstData.length == 0) {
          } else {
            for (const order of orderLstData) {
              const products = order.products
              for (const product of products) {
                const detail = whp.find((i) => i.productId == product.productId)

                // Nếu trung productId thì cộng dồn quantity
                const existingDetail = lstOutboundDetail.find((i) => i.productId == product.productId)
                if (existingDetail) {
                  existingDetail.quantity += +product.quantity
                  continue
                }
                lstOutboundDetail.push({
                  productId: product.productId,
                  productName: product.productName,
                  productCode: product.productCode,
                  expiryDate: detail.expiryDate,
                  manufactureDate: detail.manufactureDate,
                  quantity: +product.quantity,
                  inventory: detail.quantity,
                  productDetailId: detail.productDetailId,
                  lotNumber: 1,
                })
              }
            }
          }
          outbound['lstOutboundDetail'] = lstOutboundDetail
          await this.outboundService.createDataApproved(outbound, req)
        }
      }
    }
    /** End */

    //Cập nhật trang thái của SO liên quan và tạo phiếu theo dõi nhập kho
    const newProductLM = []
    const orderId = JSON.parse(dl.soId)
    if (orderId && orderId.length > 0) {
      if (dl.type == NSPo.DeliveryLastMileType.CUSTOMER) {
        await this.deliveryNoteChildDetailRepo.update({ id }, { sendStatus: NSPo.EDeliveryNoteSendInboundStatus.SENT_CUSTOMER })
        const rs = await omsApiHelper.updateSOStatus(req, { ids: orderId, status: NSRecurringOrder.EStatus.DELIVERED })
        if (!rs?.message) {
          throw new Error(`Lỗi cập nhật đơn hàng`)
        }
      } else {
        const lmCount = await this.deliveryNoteLastMileProductRepo.count({
          where: {
            createdAt: Between(new Date(new Date().setHours(0, 0, 0, 0)), new Date(new Date().setHours(23, 59, 59, 999))),
          },
        })
        // Lấy ra danh sách order được gửi cho MBC
        const orderIds = Array.from(orderId)
        // Lấy thông tin order từ oms
        const orderLst = await omsApiHelper.getOrderByIds(req, { orderListIds: orderIds })
        const poIds = await this.purchaseOrderSoRepo.find({ where: { soId: In(orderIds) }, select: { poId: true } })
        const poCheck = await this.purchaseOrderRepository.find({ where: { id: In(poIds.map((val) => val.poId)), status: NSPo.EPoStatus.COMPLETE } })
        const poItems = await this.purchaseOrderItemRepository.find({ where: { purchaseOrderId: In(poCheck.map((val) => val.id)) } })

        const orderLstData = orderLst.data
        for (const order of orderLstData) {
          const products = order.products
          for (const product of products) {
            // Nếu đã có productId thì tăng quantityBegin lên
            const check = newProductLM.find((i) => i.productId == product.productId)
            if (check) {
              check.quantityBegin = +check.quantityBegin + +product.quantity
              continue
            }
            const itemInPo = poItems.find((i) => i.itemId == product.productId || i.comboId == product.productId)
            // Nếu chưa có productId thì tạo mới
            const newLmCode = coreHelper.generateDeliveryNoteCode(lmCount + newProductLM.length + 1, 'GR')
            newProductLM.push({
              code: newLmCode,
              deliveryNoteChildDetailId: id,
              productId: product.productId,
              poId: itemInPo.purchaseOrderId,
              quantityBegin: product.quantity,
              quantityExport: 0,
            })
          }
        }
        await this.deliveryNoteLastMileProductRepo.save(newProductLM)
        await this.deliveryNoteChildDetailRepo.update({ id }, { sendStatus: NSPo.EDeliveryNoteSendInboundStatus.SENT_MBC })
      }
    } else {
      const lmCount = await this.deliveryNoteLastMileProductRepo.count({
        where: {
          createdAt: Between(new Date(new Date().setHours(0, 0, 0, 0)), new Date(new Date().setHours(23, 59, 59, 999))),
        },
      })
      // Tạo LM product để nhập kho MBC
      for (const i of item) {
        const newLmCode = coreHelper.generateDeliveryNoteCode(lmCount + newProductLM.length + 1, 'GR')
        newProductLM.push({
          code: newLmCode,
          deliveryNoteChildDetailId: id,
          productId: i.itemId,
          poId: i.purchaseOrderId,
          quantityBegin: i.quantity,
          quantityExport: 0,
        })
      }
      for (const i of combo) {
        const newLmCode = coreHelper.generateDeliveryNoteCode(lmCount + newProductLM.length + 1, 'GR')
        newProductLM.push({
          code: newLmCode,
          deliveryNoteChildDetailId: id,
          productId: i.comboId,
          poId: i.poId,
          quantityBegin: i.quantityCombo,
          quantityExport: 0,
        })
      }

      await this.deliveryNoteLastMileProductRepo.save(newProductLM)
      await this.deliveryNoteChildDetailRepo.update({ id }, { sendStatus: NSPo.EDeliveryNoteSendInboundStatus.SENT_MBC })
    }

    //Kiểm tra toàn bộ phiếu thuộc deliveryNoteChildId đã hoàn thành
    const check = await this.deliveryNoteChildDetailRepo.find({ where: { deliveryNoteChildId: dl.deliveryNoteChildId } })
    if (
      check.length ==
      check.filter(
        (i) => i.sendStatus == NSPo.EDeliveryNoteSendInboundStatus.SENT_CUSTOMER || i.sendStatus == NSPo.EDeliveryNoteSendInboundStatus.SENT_MBC,
      ).length
    ) {
      await this.deliveryNoteChildRepo.update({ id: dl.deliveryNoteChildId }, { status: NSPo.EDeliveryNoteInboundStatus.COMPLETED })
    } else {
      await this.deliveryNoteChildRepo.update({ id: dl.deliveryNoteChildId }, { status: NSPo.EDeliveryNoteInboundStatus.DELIVERING })
    }
    return { message: UPDATE_SUCCESS }
  }

  async importExcelLastMile(data: ImportSendPartnerDto, req: IRequest) {
    data.poCodes = data.poCodes.map((code) => code.trim())

    await Promise.all(
      data.codes.map(async (code) => {
        let deliveryNote = await this.deliveryNoteChildDetailRepo.findOne({
          where: { code, sendStatus: NSPo.EDeliveryNoteSendInboundStatus.DELIVERING },
        })
        if (!deliveryNote) return
        let comboList: any = []
        let itemList: any = []
        let lstPoId: any[] = await this.purchaseOrderRepository.find({ where: { purchaseOrderCode: In(data.poCodes) } })
        lstPoId = lstPoId.map((val) => val.id)

        for (let soId of JSON.parse(deliveryNote.soId)) {
          let detail: any = await omsApiHelper.getDetailOrder(req, { id: soId })
          for (let product of detail.products) {
            if (product.isCombo) {
              let combo = await this.purchaseOrderItemRepository.findOne({ where: { comboId: product.productId, purchaseOrderId: In(lstPoId) } })
              comboList.push(combo)
            } else {
              let item = await this.purchaseOrderItemRepository.findOne({ where: { itemId: product.productId, purchaseOrderId: In(lstPoId) } })
              itemList.push(item)
            }
          }
        }
        if (deliveryNote != null) {
          return this.sendPartner({ id: deliveryNote.id, combo: comboList, item: itemList }, req)
        }
      }),
    ).catch((err) => {
      throw new Error(err)
    })
    return { message: UPDATE_SUCCESS }
  }

  async printLastMile({ data }: any) {
    try {
      //Lấy template có sẵn từ AWS
      const response = await axios.get(
        PrintTemplateUrl.LastMileTemplateUrl,
        { responseType: 'text' }, // Đảm bảo lấy text chứ không phải JSON
      )
      let html = response.data

      //partnerCode
      html = html.replace('${partnerCode}', data.partnerCode)
      //mã đơn hàng
      html = html.replace('${code}', data.code)
      //ngày đặt hàng
      html = html.replace('${shippedDate}', moment(data?.shippedDate || data?.estimatedShippedDate).format('DD/MM/YYYY'))

      let products = data.products.map((product: any) => {
        return `
      <tr>
        <td>
            <p>[${product.productCode}] ${product.productName}</p>
        </td>
        
        <td>
            <p>${product.quantity}</p>
        </td>

        <td>
            <p>${data.status == NSRecurringOrder.EStatus.DELIVERED ? product.quantity : 0}</p>
        </td>
      </tr>
      `
      })
      html = html.replace('${products}', products.join(''))
      //shippingAddress
      html = html.replace('${shippingAddress}', data.shippingAddress)
      //customerName
      html = html.replace('${customerPhone}', data.customerPhone)

      // const browser = await puppeteer.launch()
      // const page = await browser.newPage()

      // await page.setContent(html, { waitUntil: 'networkidle0' })

      // const tempFilePath = `${uuidv4()}.pdf`

      // const pdf = await page.pdf({
      //   path: tempFilePath,
      //   format: 'A4',
      //   printBackground: true,
      // })

      // await browser.close()

      // const pdfBuffer = fs.readFileSync(tempFilePath)
      // const file = await this.uploadService.uploadSinglePDF(pdfBuffer)
      // // Delete the local PDF file after upload
      // fs.unlinkSync(tempFilePath)
      return { content: html }
    } catch (error) {
      console.error('❌ Error converting file:', error)
    }
  }
}
