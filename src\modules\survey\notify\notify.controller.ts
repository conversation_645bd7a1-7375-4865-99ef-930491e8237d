import { Body, Controller, Ip, Post, Req, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { Request as IRequest } from 'express'
import { ApeAuthGuard } from '../common/guards'
import { NotifyService } from './notify.service'
// import { surveyAuthApiHelper } from '../../helpers'
import { NotifyCreateDto } from './dto'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { CurrentUser } from '../../common/decorators'

@ApiBearerAuth()
@ApiTags('Notify')
@Controller('notify')
export class NotifyController {
  constructor(private service: NotifyService) {}
  @UseGuards(ApeAuthGuard)
  @ApiOperation({ summary: 'Danh sách phân trang' })
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Req() req: IRequest, @Body() data: PaginationDto) {
    // const user = await surveyAuthApiHelper.validateRequestCompany(req)
    return await this.service.pagination(user, data)
  }
  @UseGuards(ApeAuthGuard)
  @ApiOperation({ summary: 'Tạo data thông báo' })
  @Post('create_data')
  public async createData(@Body() data: NotifyCreateDto[], @CurrentUser() user: UserDto, @Req() req: IRequest) {
    // const user = await surveyAuthApiHelper.validateRequestCompany(req)
    return await this.service.createData(user, data, req)
  }
  @UseGuards(ApeAuthGuard)
  @ApiOperation({ summary: 'Lấy ds thông báo' })
  @Post('find')
  public async find(@CurrentUser() user: UserDto, @Req() req: IRequest, @Body() data: any) {
    // const user = await surveyAuthApiHelper.validateRequestCompany(req)
    return await this.service.find(user, data)
  }
  @UseGuards(ApeAuthGuard)
  @ApiOperation({ summary: 'Cập nhật trạng thái kích hoạt' })
  @Post('update_status')
  public async updateStatus(@CurrentUser() user: UserDto, @Req() req: IRequest, @Body() data: FilterOneDto) {
    // const user = await surveyAuthApiHelper.validateRequestCompany(req)
    return await this.service.updateStatus(user, data)
  }
  @UseGuards(ApeAuthGuard)
  @ApiOperation({ summary: 'Cập nhật trạng thái kích hoạt' })
  @Post('update_status_all')
  public async updateStatusAll(@CurrentUser() user: UserDto, @Req() req: IRequest) {
    // const user = await surveyAuthApiHelper.validateRequestCompany(req)
    return await this.service.updateStatusAll(user)
  }

  @ApiOperation({ summary: 'check ip call' })
  @Post('check_ip')
  public async checkIp(@CurrentUser() user: UserDto, @Req() req: IRequest, @Ip() ip) {
    console.log(ip, '-------------HuyHuyHuyHuyHuyHuyHuyHuyHuyHuyHuyHuy--------')
    return await this.service.checkIp(req)
  }
}
