import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, Join<PERSON><PERSON>umn } from 'typeorm'
import { ItemEntity } from './item.entity'

/** <PERSON>hi<PERSON><PERSON> lập gi<PERSON> sản phẩm  */
@Entity('item_price')
export class ItemPriceEntity extends BaseEntity {
  /** Sản phẩm */
  @Column({ type: 'varchar', nullable: true })
  itemId: string
  @ManyToOne(() => ItemEntity, (p) => p.prices)
  @JoinColumn({ name: 'itemId', referencedColumnName: 'id' })
  item: Promise<ItemEntity>

  @Column({ type: 'varchar', length: 36, nullable: true })
  brandId: string

  /** Giá vốn */
  @Column({ type: 'decimal', precision: 20, scale: 2, nullable: true, default: 0 })
  priceCapital: number

  /** Gi<PERSON> nhập kho mới nhất  */
  @Column({ type: 'decimal', precision: 20, scale: 2, nullable: true, default: 0 })
  priceInput: number

  /** <PERSON><PERSON><PERSON> b<PERSON> chung  */
  @Column({ type: 'decimal', precision: 20, scale: 2, nullable: true, default: 0 })
  priceSell: number

  /** Giá gốc */
  @Column({ type: 'decimal', precision: 20, scale: 2, nullable: true, default: 0 })
  priceOriginal: number

  @Column({ type: 'text', nullable: true })
  description: string

  @Column({ nullable: true, default: true })
  isFinal: boolean
}
