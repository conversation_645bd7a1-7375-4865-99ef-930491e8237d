import { ApiProperty } from '@nestjs/swagger'
import { BaseEntity as Base, Column, CreateDateColumn, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm'

/** <PERSON><PERSON><PERSON> trường thông tin chung */
export abstract class BaseEntity extends Base {
  @ApiProperty({ description: 'Id khóa chính' })
  @PrimaryGeneratedColumn('uuid')
  id: string

  @ApiProperty({ description: 'Ngày tạo' })
  @CreateDateColumn({ type: "timestamptz", nullable: false })
  createdAt: Date

  @ApiProperty({ description: '<PERSON><PERSON><PERSON><PERSON> tạo, lưu user.id' })
  @Column({ type: 'varchar', length: 36, nullable: true })
  createdBy: string

  @ApiProperty({ description: 'Ngày sửa cuối' })
  @UpdateDateColumn({ type: "timestamptz", nullable: true })
  updatedAt: Date

  @ApiProperty({ description: 'Người sửa cuối, lưu user.id' })
  @Column({ type: 'varchar', length: 36, nullable: true })
  updatedBy: string

  @ApiProperty({ description: '<PERSON><PERSON>a mềm?' })
  @Column({ name: 'isDeleted', default: false })
  isDeleted: boolean


  // @Column({ type: 'varchar', length: 36, nullable: false })
  // companyId: string
}
