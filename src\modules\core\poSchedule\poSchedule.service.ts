import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { FilterIdDto, PaginationDto, UserDto } from '../../../dto';
import { PoScheduleEntity, PoScheduleHistoryEntity } from '../../../entities';
import { PoScheduleRepository } from '../../../repositories/core/poSchedule.repository';
import { PoScheduleHistoryRepository } from '../../../repositories/core/poScheduleHistory.repository';
import { ApprovePoScheduleDto, CreatePoScheduleDto, UpdatePoScheduleDto } from './dto';

@Injectable()
export class PoScheduleService {
  constructor(
    @InjectRepository(PoScheduleRepository)
    private readonly poScheduleRepository: PoScheduleRepository,

    @InjectRepository(PoScheduleHistoryRepository)
    private readonly poScheduleHistoryRepository: PoScheduleHistoryRepository,
  ) { }

  async pagination(req: any, data: PaginationDto) {
    return await this.poScheduleRepository.findAndCount({
      where: {},
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
    });
  }

  async createData(data: CreatePoScheduleDto, req: any, user: UserDto) {
    const { supplier, supplierId, products } = data;
    const poSchedules: PoScheduleEntity[] = [];

    const today = new Date();
    const datePart = today.toISOString().slice(2, 10).replace(/-/g, "");
    const lastPo = await this.poScheduleRepository
      .createQueryBuilder("po")
      .orderBy("po.code", "DESC")
      .getOne();

    let nextNumber = lastPo ? parseInt(lastPo.code.slice(-7), 10) + 1 : 1;
    for (const product of products) {
      const newCode = `DS${datePart}${String(nextNumber++).padStart(7, "0")}`;
      const poSchedule = this.poScheduleRepository.create({
        code: newCode,
        supplier: supplier,
        supplierId: supplierId || null,
        productName: product.productName,
        productId: product.productId || null,
        estimatedQuantity: product.estimatedQuantity,
        distributedQuantity: product.distributedQuantity || 0,
        distributionTime: product.distributionTime || null,
        createdAt: new Date(),
        status: "NEW",
      });

      poSchedules.push(await this.poScheduleRepository.save(poSchedule));
      await this.createHistoryEntry(
        poSchedule,
        'create',
        `Người dùng ${user.username} đã tạo mới ${products.length} sản phẩm trong lịch hàng về.`,
        user.id,
      );
    }
    return poSchedules;
  }

  async updateData(data: UpdatePoScheduleDto, req: any, user: UserDto) {
    const poSchedule = await this.poScheduleRepository.findOneBy({ id: data.id });
    if (!poSchedule) throw new Error('Không tìm thấy lịch hàng về');

    Object.assign(poSchedule, data);
    await this.poScheduleRepository.save(poSchedule);

    await this.createHistoryEntry(
      poSchedule,
      'update',
      `Người dùng ${user.username} đã cập nhật lịch hàng về ${poSchedule.id}.`,
      user.id,
    );

    return poSchedule;
  }

  async approveData(data: ApprovePoScheduleDto, req: any, user: UserDto) {
    const poSchedule = await this.poScheduleRepository.findOneBy({ id: data.id });
    if (!poSchedule) throw new Error('Không tìm thấy lịch hàng về');

    poSchedule.status = 'Đã duyệt';
    await this.poScheduleRepository.save(poSchedule);

    await this.createHistoryEntry(
      poSchedule,
      'approve',
      `Người dùng ${user.username} đã duyệt lịch hàng về ${poSchedule.id}.`,
      user.id,
    );

    return poSchedule;
  }

  async cancelData(data: ApprovePoScheduleDto, req: any, user: UserDto) {
    const poSchedule = await this.poScheduleRepository.findOneBy({ id: data.id });
    if (!poSchedule) throw new Error('Không tìm thấy lịch hàng về');

    poSchedule.status = 'Đã hủy';
    await this.poScheduleRepository.save(poSchedule);

    await this.createHistoryEntry(
      poSchedule,
      'cancel',
      `Người dùng ${user.username} đã hủy lịch hàng về ${poSchedule.id}.`,
      user.id,
    );

    return poSchedule;
  }

  async findDetail(req: any, id: string) {
    return await this.poScheduleRepository.findOneBy({ id });
  }

  async paginationHistory(req: any, data: PaginationDto) {
    return await this.poScheduleHistoryRepository.find({
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
    });
  }

  async exportExcel(req: any, data: PaginationDto, user: UserDto) {
    return { message: 'Xuất file Excel thành công', data, user };
  }

  async print(data: string[], req: any) {
    return { message: 'In lịch hàng về thành công', data };
  }

  private async createHistoryEntry(
    poSchedule: PoScheduleEntity,
    actionType: string,
    description: string,
    performedBy: string,
  ) {
    const historyEntry = this.poScheduleHistoryRepository.create({
      poScheduleId: poSchedule.id,
      actionType,
      description,
      performedBy,
      performedAt: new Date(),
    });

    await this.poScheduleHistoryRepository.save(historyEntry);
  }
}
