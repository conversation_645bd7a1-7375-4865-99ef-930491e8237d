import { ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsString, IsEmail, IsOptional } from 'class-validator'

export class SupplierCreateDto {
  @IsNotEmpty()
  @IsString()
  code: string

  @IsNotEmpty()
  @IsString()
  name: string

  email: string

  description: string

  address: string

  wardId: string

  districtId: string

  cityId: string

  phone: string

  @ApiPropertyOptional({ description: 'Id của nhà cung cấp cha, áp dung cho type NPP' })
  @IsOptional()
  parentId?: string

  @ApiPropertyOptional()
  username?: string

  @ApiPropertyOptional()
  password?: string

  @ApiPropertyOptional()
  isDistributor: boolean

  @ApiPropertyOptional()
  isSupplier: boolean

  @ApiPropertyOptional()
  is3PL: boolean

  configProvince?: string[]

  configDistrict?: string[]
}
