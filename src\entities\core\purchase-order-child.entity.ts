import { Column, Entity, Index } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { BaseEntity } from './base.entity'
import { NSPo } from '../../constants';

@Entity('purchase_order_child')
export class PurchaseOrderChildEntity extends BaseEntity {
    @ApiProperty({ description: 'PO ID' })
    @Column()
    @Index()
    purchaseOrderId: string;

    @ApiProperty({ description: 'Mã PO' })
    @Column({ unique: true })
    @Index()
    purchaseOrderCode: string;

    @ApiProperty({
        description: `Loại PO: ${Object.values(NSPo.EPoType).join(' | ')}`,
        enum: NSPo.EPoType,
        default: NSPo.EPoType.WITHCOMBO,
    })
    @Column({ default: NSPo.EPoType.WITHCOMBO })
    purchaseOrderType: NSPo.EPoType;

    @ApiProperty({ description: 'ID Nhà cung cấp / xuất xứ' })
    @Column("uuid")
    @Index()
    supplierId: string

    @ApiProperty({ description: 'ID Nhà phân phối' })
    @Column("uuid", { nullable: true })
    @Index()
    distributorId: string

    @ApiProperty({ description: "VAT" })
    @Column("numeric", { default: 0 })
    vat: number;

    @ApiProperty({ description: "Tổng giá trị" })
    @Column('numeric', { precision: 20, scale: 0 })
    totalAmount: number;

    @ApiProperty({ description: "Tống giá trị với VAT" })
    @Column('numeric', { precision: 20, scale: 0 })
    totalAmountVat: number;

    @ApiPropertyOptional({
        description: `Danh sách tệp đính kèm`
    })
    @Column("jsonb", { nullable: true })
    files: string;

    @ApiProperty({ description: "Tiện cọc" })
    @Column('numeric', { precision: 20, scale: 0, nullable: true },)
    deposit: number;

    @ApiProperty({ description: 'Điều kiện giao hàng' })
    @Column('varchar', { nullable: true })
    deliveryTerms: string;

    @ApiPropertyOptional({
        description: "Chuỗi cung ứng"
    })
    @Column("uuid", { nullable: true })
    supplyChainId: string
}

