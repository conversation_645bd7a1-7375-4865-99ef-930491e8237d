import { Injectable, ConflictException, NotFoundException } from '@nestjs/common'
import {
  ERROR_NOT_FOUND_DATA,
  ERROR_CODE_TAKEN,
  UPDATE_ACTIVE_SUCCESS,
  enumData,
  CREATE_SUCCESS,
  UPDATE_SUCCESS,
  IMPORT_SUCCESS,
} from '../../../constants'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { ILike, In, Like } from 'typeorm'
import { TaxRepository } from '../../../repositories'
import { TaxEntity, UnitEntity } from '../../../entities'
import { Request as IRequest } from 'express'
import { TaxCreateDto, TaxUpdateDto } from './dto'

@Injectable()
export class TaxService {
  constructor(private readonly repo: TaxRepository) {}

  async find(data: any) {
    const whereCon: any = { isDeleted: false }
    if (data.name) whereCon.name = Like(`%${data.name}%`)
    if (data.code) whereCon.code = Like(`%${data.code}%`)
    if (data.type) whereCon.type = data.type
    return await this.repo.find({ where: whereCon })
  }

  async loadData() {
    return await this.repo.find({
      where: { isDeleted: false },
      select: { id: true, name: true, percent: true, type: true, isDeleted: true, code: true },
    })
  }

  async pagination(data: PaginationDto) {
    const whereCon: any = {}
    if (data.where.name) whereCon.name = ILike(`%${data.where.name}%`)
    if (data.where.type) whereCon.type = ILike(`%${data.where.type}%`)
    if (data.where.code) whereCon.code = ILike(`%${data.where.code}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    return await this.repo.findAndCount({
      where: whereCon,
      order: { createdAt: 'DESC' },
      skip: data.skip,
      take: data.take,
    })
  }

  async createData(user: UserDto, data: TaxCreateDto) {
    const tax = new TaxEntity()
    tax.code = await this.codeDefault()
    tax.name = data.name
    tax.type = data.type
    tax.percent = data.percent
    tax.createdBy = user.id
    await this.repo.insert(tax)

    return { message: CREATE_SUCCESS }
  }

  private async codeDefault(currentCode?: string): Promise<string> {
    let sortString = '0'

    if (currentCode) {
      sortString = currentCode.substring(4) // Lấy phần số sau "TAX_"
    } else {
      const objData = await this.repo.findOne({
        where: { code: Like(`TAX_%`) },
        order: { code: 'DESC' },
      })

      if (objData) {
        sortString = objData.code.substring(4)
      }
    }

    const lastSort = parseInt(sortString)
    sortString = ('000' + (lastSort + 1)).slice(-4) // Định dạng lại thành 4 chữ số

    return 'TAX_' + sortString
  }

  async updateData(user: UserDto, data: TaxUpdateDto) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    entity.name = data.name
    entity.type = data.type
    entity.percent = data.percent
    entity.updatedBy = user.id

    await this.repo.update(entity.id, entity)

    return { message: UPDATE_SUCCESS }
  }

  async updateIsDelete(data: FilterOneDto, user: UserDto) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    entity.isDeleted = !entity.isDeleted
    entity.updatedBy = user.id
    await this.repo.update(entity.id, entity)

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  async findOne(data: FilterOneDto) {
    return await this.repo.findOneBy({ id: data.id })
  }

  public async createDataExcel(data: TaxCreateDto[], user: UserDto) {
    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(TaxEntity)
      const lstTax = []
      const tax = await this.repo.findOne({
        where: { code: Like(`TAX_%`) },
        order: { code: 'DESC' },
      })
      let code = tax ? tax.code : null
      for (const [idx, item] of data.entries() as any) {
        if (!item.name) throw new Error(`Dòng [${idx + 1}] - Tên thuế không được để trống`)
        if (!item.type) throw new Error(`Dòng [${idx + 1}] - Loại thuế không được để trống`)
        if (!item.percent) throw new Error(`Dòng [${idx + 1}] - Phần trăm thuế không được để trống`)
        if (code !== null) {
          item.code = await this.codeDefault(code)
        } else {
          item.code = await this.codeDefault()
        }
        code = item.code
        const newTax = repo.create({
          ...item,
          createdAt: new Date(),
          createdBy: user.id,
        })
        lstTax.push(newTax)
      }
      await repo.insert(lstTax)
    })
    return { message: IMPORT_SUCCESS }
  }
}
