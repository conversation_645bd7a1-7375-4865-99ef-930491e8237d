import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'

/** Interface tạo user Survey */
export class CreateUserSurveyDto {
  @ApiProperty({ description: '<PERSON><PERSON><PERSON> khoản' })
  @IsString()
  @IsNotEmpty()
  username: string

  @ApiProperty({ description: 'Mật khẩu' })
  @IsString()
  @IsNotEmpty()
  password: string

  @ApiProperty({ description: 'Email đăng ký tài khoản' })
  email: string

  @IsString()
  employeeId: string
}
