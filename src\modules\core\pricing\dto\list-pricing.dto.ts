import { IsOptional } from 'class-validator'
import { PageRequest } from '../../../../dto'

export class ListPricingDto extends PageRequest {
  @IsOptional()
  itemId?: string

  @IsOptional()
  itemCode?: string

  @IsOptional()
  itemName?: string

  @IsOptional()
  supplierId?: string

  @IsOptional()
  price?: number

  @IsOptional()
  code?: string

  @IsOptional()
  isDeleted?: boolean

  @IsOptional()
  updatedAtFrom?: Date

  @IsOptional()
  updatedAtTo?: Date
}
