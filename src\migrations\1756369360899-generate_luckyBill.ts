import { MigrationInterface, QueryRunner } from 'typeorm'

export class generateLuckyBill1756369360899 implements MigrationInterface {
  name = 'generateLuckyBill1756369360899'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "lucky_bill_config" ADD CONSTRAINT "UQ_78c5eaff5bd18cb95ab5a1b1b70" UNIQUE ("code")`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "lucky_bill_config" DROP CONSTRAINT "UQ_78c5eaff5bd18cb95ab5a1b1b70"`)
  }
}
