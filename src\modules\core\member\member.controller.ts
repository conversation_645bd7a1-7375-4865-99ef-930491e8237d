import { Controller, UseGuards, Post, Body, Req } from '@nestjs/common'
import { JwtAuthGuard } from '../../common/guards'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { Request as IRequest } from 'express'
import { MemberPaginationDto } from './dto/memberPagination.dto'
import { omsApiHelper } from '../../../helpers/omsApiHelper'

@ApiBearerAuth()
@ApiTags('Member')
@UseGuards(JwtAuthGuard)
@Controller('member')
export class MemberController {
  constructor() { }

  @Post('pagination')
  public async pagination(@Body() data: MemberPaginationDto, @Req() req: IRequest) {
    return await omsApiHelper.getMember(req, data)
  }
  @Post('pagination-customer')
  public async paginationCustomer(@Body() data: MemberPaginationDto, @Req() req: IRequest) {
    return await omsApiHelper.getCustomer(req, data)
  }

  @Post('statistical/overview')
  public async getStatisticalOverview(@Body() data: MemberPaginationDto, @Req() req: IRequest) {
    return await omsApiHelper.getStatisticalOverview(req, data)
  }

  ///api/members/statistical/details
  @Post('statistical/details')
  public async getStatisticalDetails(@Body() data: MemberPaginationDto, @Req() req: IRequest) {
    return await omsApiHelper.getStatisticalDetails(req, data)
  }

  ///api/members/list-businesstype-by-member
  @Post('list-businesstype-by-member')
  public async listBusinesstypeByMember(@Body() data: MemberPaginationDto, @Req() req: IRequest) {
    return await omsApiHelper.listBusinesstypeByMember(req, data)
  }

  ///api/members/ape/item/find_list_product_combo
  @Post('find-list-product-combo')
  public async findListProductCombo(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.findListProductCombo(req, data)
  }

  ///api/members/list-cards-by-member
  @Post('list-cards-by-member')
  public async listCardsByMember(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.listCardsByMember(req, data)
  }
  //get /api/members/find-detail
  @Post('find-detail')
  public async getMemberDetail(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getMemberDetail(req, data)
  }
  ///api/members/product/list-item-group get
  @Post('product/list-item-group')
  public async listItemGroup(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.listItemGroup(req, data)
  }
}
