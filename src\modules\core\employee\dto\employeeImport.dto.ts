import { IsNotEmpty, IsOptional, IsString } from 'class-validator'
import { EmployeeCreateDto } from './employeeCreate.dto'
import { ApiProperty } from '@nestjs/swagger'

/** Interface thêm nhân viên bằng excel. */
export class EmployeeImportDto extends EmployeeCreateDto {
  @IsNotEmpty()
  @IsString()
  name: string

  @IsNotEmpty()
  @IsString()
  code: string

  @IsOptional()
  @IsString()
  username: string

  @IsOptional()
  @IsString()
  password: string

  @IsOptional()
  email: string

  @IsNotEmpty()
  @IsString()
  departmentCode: string

  description: string

  @IsOptional()
  phone: string

  managerCode: string

  lstBrandCode: string

  isMobile: boolean
}
