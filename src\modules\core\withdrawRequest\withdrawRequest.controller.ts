import { Controller, UseGuards, Post, Body, Req, Get, Param, Query, Put } from '@nestjs/common'
import { JwtAuthGuard } from '../../common/guards'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { Request as IRequest } from 'express'
import { omsApiHelper } from '../../../helpers/omsApiHelper'
import { WithdrawRequestReq } from './dto/withdrawRequestReq.dto'
import { UpdateWithdrawRequestReq } from './dto/updateWithdrawRequestReq.dto'
import { UUIDReq } from '../../../dto/id.dto'

@ApiBearerAuth()
@ApiTags('WithdrawRequest')
@UseGuards(JwtAuthGuard)
@Controller('withdraw-request')
export class WithdrawRequestController {
  constructor() {}

  @Get('list')
  public async pagination(@Query() data: WithdrawRequestReq, @Req() req: IRequest) {
    return await omsApiHelper.listWithdraw(req, data)
  }

  @Put('update')
  public async update(@Body() data: UpdateWithdrawRequestReq, @Req() req: IRequest) {
    return await omsApiHelper.updateWithdraw(req, data)
  }

  @Get('detail')
  public async detail(@Query() data: UUIDReq, @Req() req: IRequest) {
    return await omsApiHelper.detailWithdraw(req, data)
  }
}
