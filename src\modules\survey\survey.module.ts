import { Module } from '@nestjs/common'
import { TopicModule } from './topic/topic.module'
import { QuestionModule } from './question/question.module'
import { MobileAppModule } from './mobileApp/mobileApp.module'
import { SurveyModule } from './survey/survey.module'
import { SurveyMemberModule } from './surveyMember/surveyMember.module'
import { SurveyQuestionModule } from './surveyQuestion/surveyQuestion.module'
import { TasksModule } from './common/tasks/tasks.module'
import { CategoriesModule } from './categories/categories.module'
import { CompanyModule } from './company/company.module'
import { PublicSurveyModule } from './public/publicSurvey.module'

@Module({
  imports: [
    TopicModule,
    QuestionModule,
    MobileAppModule,
    SurveyModule,
    SurveyMemberModule,
    SurveyQuestionModule,
    TasksModule,
    CategoriesModule,
    CompanyModule,
    PublicSurveyModule,
  ],
})
export class surveyModule {}
