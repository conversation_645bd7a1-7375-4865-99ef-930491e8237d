import { Module } from '@nestjs/common'
import { PricingService } from './pricing.service'
import { PricingController } from './pricing.controller'
import { TypeOrmExModule } from '../../../typeorm'
import { PricingRepository } from '../../../repositories/core/pricing.repository'
import { ItemRepository, SupplierRepository } from '../../../repositories'

@Module({
  controllers: [PricingController],
  providers: [PricingService],
  imports: [TypeOrmExModule.forCustomRepository([PricingRepository, ItemRepository, SupplierRepository])],
})
export class PricingModule {}
