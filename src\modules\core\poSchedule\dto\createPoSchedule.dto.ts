import { IsArray, IsDateString, IsInt, IsNotEmpty, IsOptional, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

class ProductDto {
  @IsString()
  @IsNotEmpty()
  productName: string;

  @IsString()
  @IsOptional()
  productId?: string;
  @IsInt()
  estimatedQuantity: number;

  @IsOptional()
  @IsInt()
  distributedQuantity?: number;

  @IsOptional()
  @IsString()
  distributionTime?: string;
}

export class CreatePoScheduleDto {

  @IsString()
  @IsNotEmpty()
  supplier: string;

  @IsString()
  @IsOptional()
  supplierId?: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ProductDto)
  products: ProductDto[];
}
