import { Injectable } from '@nestjs/common'
import { CreateLuckyBillApplyRegionDto } from './dto/create-lucky-bill-apply-region.dto'
import { LuckyBillApplyRegionRepository } from '../../../repositories/lucky-bill/lucky-bill-apply-region.repository'
import { UserDto } from '../../../dto'
import { UpdateLuckyBillApplyRegionDto } from './dto/update-lucky-bill-apply-region.dto'
import { In, Not } from 'typeorm'
import { RegionCityRepository, RegionRepository } from '../../../repositories'

@Injectable()
export class LuckyBillApplyRegionService {
  constructor(
    private readonly luckyBillApplyRegionRepo: LuckyBillApplyRegionRepository,
    private readonly regionRepo: RegionRepository,
    private readonly regionCityRepo: RegionCityRepository,
  ) {}

  async create(createLuckyBillApplyRegionDto: CreateLuckyBillApplyRegionDto, user: UserDto) {
    const { luckyBillConfigId, cityIds } = createLuckyBillApplyRegionDto
    //check cityIds
    const checkCity = await this.regionCityRepo.find({ where: { cityId: In(cityIds) } })
    if (checkCity.length !== cityIds.length) throw new Error('Danh sách thành phố không hợp lệ')
    await this.luckyBillApplyRegionRepo.insert({
      ...createLuckyBillApplyRegionDto,
      createdBy: user.id,
    })
    return {
      message: 'Tạo mới khu vực áp dụng thành công',
      data: { luckyBillConfigId, cityIds },
    }
  }

  //list
  async list(luckyBillConfigId: string) {
    let data: any[] = await this.luckyBillApplyRegionRepo.find({ where: { luckyBillConfigId, isDeleted: false }, order: { createdAt: 'DESC' } })
    for (let item of data) {
      //find region of district
      item.regionIds = []
      const listCity = await this.regionCityRepo.find({ where: { cityId: In(item.cityIds) }, relations: { region: true, city: true } })
      item.regionIds = [...new Set(listCity.map((city) => city.regionId))]
      item.regionCodes = [...new Set(listCity.map((city: any) => city.__region__.code))]
      item.provinceCodes = [...new Set(listCity.map((city: any) => city.__city__.code))]
    }
    return data
  }

  async update(updateLuckyBillApplyRegionDto: UpdateLuckyBillApplyRegionDto, user: UserDto) {
    const { id, cityIds } = updateLuckyBillApplyRegionDto
    const check = await this.luckyBillApplyRegionRepo.findOne({ where: { id, isDeleted: false } })
    if (!check) throw new Error('Không tìm thấy khu vực áp dụng')

    await this.luckyBillApplyRegionRepo.update({ id }, { cityIds, updatedBy: user.id })

    return {
      message: 'Cập nhật khu vực áp dụng thành công',
      data: cityIds,
    }
  }

  async delete(id: string, user: UserDto) {
    //check id
    const check = await this.luckyBillApplyRegionRepo.findOne({ where: { id, isDeleted: false } })
    if (!check) throw new Error('Không tìm thấy khu vực áp dụng')

    await this.luckyBillApplyRegionRepo.delete({ id })
    return {
      message: 'Xóa khu vực áp dụng thành công',
    }
  }
}
