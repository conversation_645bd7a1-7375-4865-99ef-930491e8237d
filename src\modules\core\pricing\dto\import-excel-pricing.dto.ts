import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsNumber, IsString } from 'class-validator'

export class ImportExcelPricingDto {
  @ApiProperty({ description: '<PERSON>i<PERSON>' })
  @IsNotEmpty()
  @IsNumber()
  price: number

  @ApiProperty({ description: 'Mã sản phẩm' })
  @IsNotEmpty()
  @IsString()
  itemCode: string

  @ApiProperty({ description: 'Mã nhà cung cấp' })
  @IsNotEmpty()
  @IsString()
  supplierCode: string
}
