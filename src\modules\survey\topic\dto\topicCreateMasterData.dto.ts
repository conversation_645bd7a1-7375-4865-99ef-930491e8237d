import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'

export class TopicCreateMasterDataDto {
  @ApiProperty({ description: 'Tên chủ đề' })
  @IsNotEmpty({ message: 'Tên không được trống' })
  @IsString()
  name: string

  @ApiProperty({ description: 'Mã chủ đề' })
  @IsNotEmpty({ message: 'Mã không được trống' })
  @IsString()
  code: string

  @ApiProperty({ description: 'Danh mục' })
  @IsNotEmpty({ message: '<PERSON><PERSON> mục không được trống' })
  @IsString()
  categoriesId: string

  @ApiProperty({ description: 'Ghi chú' })
  description: string

  createdBy: string

  companyId: string
}
