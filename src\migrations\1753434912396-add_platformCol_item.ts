import { MigrationInterface, QueryRunner } from 'typeorm'

export class addPlatformColItem1753434912396 implements MigrationInterface {
  name = 'addPlatformColItem1753434912396'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "item" ADD "orderPlatformType" text array`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "item" DROP COLUMN "orderPlatformType"`)
  }
}
