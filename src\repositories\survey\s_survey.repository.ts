import { Repository } from 'typeorm'
import { CustomRepository } from '../../typeorm'
import { SurveyEntity, SurveyHistoryEntity, SurveyMemberEntity, SurveyQuestionEntity, SurveyQuestionListDetailEntity } from '../../entities/survey'

@CustomRepository(SurveyEntity)
export class SurveyRepository extends Repository<SurveyEntity> {}

@CustomRepository(SurveyMemberEntity)
export class SurveyMemberRepository extends Repository<SurveyMemberEntity> {}

@CustomRepository(SurveyQuestionEntity)
export class SurveyQuestionRepository extends Repository<SurveyQuestionEntity> {}

@CustomRepository(SurveyQuestionListDetailEntity)
export class SurveyQuestionListDetailRepository extends Repository<SurveyQuestionListDetailEntity> {}

@CustomRepository(SurveyHistoryEntity)
export class SurveyHistoryRepository extends Repository<SurveyHistoryEntity> {}
