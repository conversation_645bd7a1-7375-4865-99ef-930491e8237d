import { Module } from '@nestjs/common'
import { NotifyController } from './notify.controller'
import { NotifyService } from './notify.service'
import { TypeOrmExModule } from '../../../typeorm'
import { NotifyRepository } from '../../../repositories/survey'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([NotifyRepository])],
  controllers: [NotifyController],
  providers: [NotifyService],
  exports: [NotifyService],
})
export class NotifyModule {}
