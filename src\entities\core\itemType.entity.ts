import { Column, Entity, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { ItemEntity } from './item.entity'
import { ItemCategoryEntity } from './itemCategory.entity'

/** Loại sản phẩm */
@Entity('item_type')
export class ItemTypeEntity extends BaseEntity {
  /** Mã */
  @Column({ type: 'varchar', length: 50, nullable: false })
  code: string

  /** Tên */
  @Column({ type: 'varchar', length: 250, nullable: false })
  name: string

  /** <PERSON><PERSON> chú */
  @Column({ type: 'text', nullable: true })
  description: string

  /** Danh sách các sản phẩm thuộc loại này */
  @OneToMany(() => ItemCategoryEntity, (p) => p.itemType)
  itemCategories: Promise<ItemCategoryEntity[]>

  /** <PERSON><PERSON> sách các sản phẩm thuộc loại này */
  @OneToMany(() => ItemEntity, (p) => p.itemType)
  items: Promise<ItemEntity[]>
}
