import { Controller, UseGuards, Post, Body, Req } from '@nestjs/common'
import { CurrentUser } from '../../common/decorators'
import { UserDto } from '../../../dto'
import { JwtAuthGuard } from '../../common/guards'
import { BannerUpdateIsActiveDto } from './dto'
import { Request as IRequest } from 'express'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { SettingMediaService } from './settingMedia.service'

@ApiBearerAuth()
@ApiTags('Public API')
// @UseGuards(JwtAuthGuard)
@Controller('setting-media')
export class SettingMediaController {
  constructor(private readonly service: SettingMediaService) {}

  // @UseGuards(JwtAuthGuard)

  @Post('find')
  public async find(@Req() req: IRequest, @Body() body: any) {
    return await this.service.find(body)
  }
  // @Post('findType')
  // public async findType(@Body() body: any) {
  //   return await this.service.findType(body)
  // }

  @Post('pagination')
  public async pagination(@Body() data: any) {
    return await this.service.pagination(data)
  }
  @UseGuards(JwtAuthGuard)
  @Post('create_data')
  public async createData(@Body() data: any, @CurrentUser() user: UserDto) {
    return await this.service.createData(data, user)
  }

  @UseGuards(JwtAuthGuard)
  @Post('update_data')
  public async updateData(@Body() data: any, @CurrentUser() user: UserDto) {
    return await this.service.updateData(data, user)
  }

  @UseGuards(JwtAuthGuard)
  @Post('update_active')
  public async updateActive(@Body() data: BannerUpdateIsActiveDto) {
    return await this.service.updateIsDelete(data.id)
  }
}
