import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsArray, IsOptional, IsString } from 'class-validator'

export class PoEmployeeDto {
  @ApiPropertyOptional()
  @IsOptional()
  createdBy?: string

  @ApiPropertyOptional()
  @IsOptional()
  name?: string

  @ApiPropertyOptional()
  @IsOptional()
  code?: string

  @ApiPropertyOptional()
  @IsOptional()
  email?: string

  @ApiPropertyOptional()
  @IsOptional()
  phone?: string
}
export class POCreatePlatformDto {
  @ApiPropertyOptional()
  @IsOptional()
  platformId?: string

  @ApiPropertyOptional()
  @IsOptional()
  createdBy?: string

  @ApiPropertyOptional()
  @IsOptional()
  companyId?: string

  @ApiPropertyOptional()
  @IsOptional()
  serviceLevel1?: string

  @ApiPropertyOptional()
  @IsOptional()
  title?: string

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  anotherRoleIds?: string[]

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  confirmId?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  cancelId?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  poPaymentId?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  editPOId?: string

  @ApiPropertyOptional()
  @IsOptional()
  deliveryDate?: Date

  //   @ApiPropertyOptional()
  //  @IsOptional()
  // @IsString()
  // paymentPlanType?: string

  @ApiPropertyOptional()
  @IsOptional()
  contractId?: string

  @ApiPropertyOptional()
  @IsOptional()
  contractPaymentPlanId?: string

  @ApiPropertyOptional()
  @IsOptional()
  supplierId?: string

  @ApiPropertyOptional()
  @IsOptional()
  bidId?: string

  @ApiPropertyOptional()
  @IsOptional()
  prId?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  company?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  currency?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  email?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  phone?: string

  @ApiPropertyOptional()
  @IsOptional()
  description?: string

  @ApiPropertyOptional()
  @IsOptional()
  operator?: string

  @ApiPropertyOptional()
  @IsOptional()
  type?: string

  @ApiPropertyOptional()
  @IsOptional()
  region?: string

  @ApiPropertyOptional()
  @IsArray()
  lstProduct?: PoProductPlatformDto[]

  @ApiPropertyOptional()
  @IsArray()
  supplier: PoSupplierDto[] = []

  @ApiPropertyOptional()
  @IsArray()
  platform: PoPlatformDto[] = []

  @ApiPropertyOptional()
  @IsArray()
  owner: PoEmployeeDto[] = []
}

export class PaymentProgressDto {
  @ApiProperty() id?: string
  @ApiProperty() createdAt?: string
  @ApiProperty() createdBy?: string
  @ApiProperty() updatedAt?: string
  @ApiPropertyOptional() updatedBy?: string | null
  @ApiProperty() isDeleted?: boolean
  @ApiPropertyOptional() companyId?: string | null
  @ApiProperty() poId?: string
  @ApiPropertyOptional() contractId?: string | null
  @ApiProperty() name?: string
  @ApiProperty() percent?: number
  @ApiProperty() time?: string
  @ApiProperty() description?: string
  @ApiProperty() money?: string
  @ApiProperty() suggestPaid?: string
  @ApiProperty() paymentStatus?: string
  @ApiProperty() historyNote?: string
}

export class PoSupplierDto {
  @ApiProperty()
  @ApiPropertyOptional()
  supplierId?: string
  @ApiPropertyOptional()
  @IsOptional()
  status?: string
  @ApiPropertyOptional()
  @IsOptional()
  description?: string

  /** Mã số doanh nghiệp */
  @ApiPropertyOptional()
  @IsOptional()
  code?: string

  // Tên chính thức
  @ApiPropertyOptional()
  @IsOptional()
  name?: string

  // Tên giao dịch
  @ApiPropertyOptional()
  @IsOptional()
  dealName?: string

  // Địa chỉ trụ sở
  @ApiPropertyOptional()
  @IsOptional()
  address?: string

  // Địa chỉ giao dịch
  @ApiPropertyOptional()
  @IsOptional()
  dealAddress?: string

  // Giấy phép đăng ký kinh doanh/Mã số thuế --> URL
  @ApiPropertyOptional()
  @IsOptional()
  fileMST?: string

  // Người đại diện pháp luật
  @ApiPropertyOptional()
  @IsOptional()
  represen?: string

  // Tên giám đốc
  @ApiPropertyOptional()
  @IsOptional()
  chief?: string

  // Số tài khoản ngân hàng
  @ApiPropertyOptional()
  @IsOptional()
  bankNumber?: string

  // Tên ngân hàng
  @ApiPropertyOptional()
  @IsOptional()
  bankname?: string

  // Tên chi nhánh ngân hàng
  @ApiPropertyOptional()
  @IsOptional()
  bankBrand?: string

  // File đính kèm thông báo mở tài khoản/mẫu 08 -->URL
  @ApiPropertyOptional()
  @IsOptional()
  fileAccount?: string

  // Người liên hệ
  @ApiPropertyOptional()
  @IsOptional()
  contactName?: string

  // Email
  @ApiPropertyOptional()
  @IsOptional()
  email?: string

  // Điện thoại
  @ApiPropertyOptional()
  @IsOptional()
  phone?: string

  // Năm thành lập công ty
  @ApiPropertyOptional()
  @IsOptional()
  createYear?: string

  // Vốn điều lệ (tỷ đồng)
  @ApiPropertyOptional()
  @IsOptional()
  capital?: number

  // Tài sản cố định (tỷ đồng)
  @ApiPropertyOptional()
  @IsOptional()
  assets?: number

  // File đính kèm hóa đơn mẫu/phiếu thu/biên lai --> URL
  @ApiPropertyOptional()
  @IsOptional()
  fileBill?: string

  // File đính kèm thông tin phát hành hóa đơn --> URL
  @ApiPropertyOptional()
  @IsOptional()
  fileInfoBill?: string
}

export class PoProductPlatformDto {
  @ApiProperty()
  @IsString()
  poId?: string

  @ApiPropertyOptional()
  @IsOptional()
  description?: string

  @ApiPropertyOptional()
  @IsOptional()
  type?: string

  @ApiPropertyOptional()
  @IsOptional()
  group?: string

  @ApiProperty()
  name?: string

  @ApiProperty()
  serviceId?: string

  @ApiPropertyOptional()
  @IsOptional()
  money?: number

  @IsOptional()
  itemId?: string

  @ApiPropertyOptional()
  @IsOptional()
  itemCode?: string

  @ApiPropertyOptional()
  @IsOptional()
  price?: number

  @ApiPropertyOptional()
  @IsOptional()
  quantity?: number

  @ApiPropertyOptional()
  @IsOptional()
  unit?: string

  @ApiPropertyOptional()
  @IsOptional()
  note?: string
}

export class PoPlatformDto {
  @ApiProperty()
  @IsString()
  platformId?: string

  @ApiPropertyOptional()
  @IsOptional()
  name?: string

  @ApiPropertyOptional()
  @IsOptional()
  code?: string
}
