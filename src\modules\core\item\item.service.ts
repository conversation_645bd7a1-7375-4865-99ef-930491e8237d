import { Injectable } from '@nestjs/common'
import { Request as IRequest } from 'express'
import * as moment from 'moment'
import { Brackets, Equal, ILike, In, Like, MoreThan, Not, Raw } from 'typeorm'
import { v4 as uuidv4 } from 'uuid'
import { CREATE_SUCCESS, ERROR_NOT_FOUND_DATA, UPDATE_ACTIVE_SUCCESS, UPDATE_SUCCESS, enumData } from '../../../constants'
import { FilterIdDto, FilterOneDto, FindItemsByIdsDto, ListProduct, PaginationDto, SearchDto, UserDto } from '../../../dto'
import {
  InboundDetailEntity,
  ItemCategoryEntity,
  ItemComboEntity,
  ItemDetailEntity,
  ItemEntity,
  ItemGroupEntity,
  ItemTypeEntity,
  MediaEntity,
  OutboundDetailEntity,
  UnitEntity,
} from '../../../entities'
import { coreHelper } from '../../../helpers'
import { ActionLogService } from '../actionLog/actionLog.service'
import { ActionLogCreateDto } from '../actionLog/dto'
import {
  BrandRepository,
  InboundDetailRepository,
  InboundRepository,
  ItemCategoryRepository,
  ItemComboRepository,
  ItemDetailRepository,
  ItemGroupRepository,
  ItemPriceRepository,
  ItemRepository,
  ItemTypeRepository,
  MediaRepository,
  OutboundDetailRepository,
  PackingRepository,
  SupplierRepository,
  TaxRepository,
  UnitRepository,
  WarehouseProductDetailRepository,
  WarehouseProductRepository,
  WarehouseRepository,
} from './../../../repositories'
import { ProductCreateDto, ProductCreateExcelDto, ProductExpiryDateDto, ProductUpdateDto, ProductUpdateIsActiveListDto } from './dto'
import { ProductDto } from './dto/product.dto'
import { omsApiHelper } from '../../../helpers/omsApiHelper'
import { NSItem } from '../../../constants/NSItem'
@Injectable()
export class ItemService {
  constructor(
    private repo: ItemRepository,
    private unitRepo: UnitRepository,
    private productDetailRepo: ItemDetailRepository,
    private productPriceRepo: ItemPriceRepository,
    private brandRepo: BrandRepository,
    private packingRepo: PackingRepository,
    private inboundDetailRepo: InboundDetailRepository,
    private mediaRepo: MediaRepository,
    private itemGroupRepository: ItemGroupRepository,
    private itemCategoryRepository: ItemCategoryRepository,
    private itemTypeRepository: ItemTypeRepository,
    private warehouseProductRepo: WarehouseProductRepository,
    private outboundDetailRepo: OutboundDetailRepository,
    private inboundRepo: InboundRepository,
    private productComboRepo: ItemComboRepository,
    private supplierRepo: SupplierRepository,
    private actionLogService: ActionLogService,
    private warehouseProductDetailRepository: WarehouseProductDetailRepository,
    private warehouseRepo: WarehouseRepository,
    private taxRepo: TaxRepository,
  ) {}

  async find(data: any, req: IRequest) {
    let whereCon: any = {}
    let lstId = []
    if (data?.isDeleted != undefined) whereCon.isDeleted = data.isDeleted
    if (data?.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data?.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data?.lstId?.length > 0) {
      lstId = data.lstId
      whereCon.id = In(data.lstId)
    }
    if (data?.lstCode?.length > 0) whereCon.code = In(data.lstCode)
    if (data?.id) whereCon.id = data.id
    if (data?.brandId) whereCon.brandId = data.brandId
    if (data?.isCombo != undefined) whereCon.isCombo = data.isCombo
    let relations: any = ['prices']
    if (data?.isRelation) relations = { details: true }
    if (data.warehouseId) {
      const wereHouseProduct = await this.warehouseProductRepo.find({ where: { warehouseId: data.warehouseId } })
      if (wereHouseProduct) for (const item of wereHouseProduct) lstId.push(item.productId)
      whereCon.id = In(lstId)
    }

    let res: any = await this.repo.find({ where: whereCon, relations: relations })
    if (res.length == 0) return []
    const dictBrand: any = {}
    {
      const lstBrandId = res.mapAndDistinct((e) => e.brandId)
      const lstBrand: any = await this.brandRepo.find({ where: { isDeleted: false, id: In(lstBrandId) } })
      lstBrand.forEach((c) => (dictBrand[c.id] = c))
    }

    const dictMedia: any = {}
    {
      const lstProductId = res.mapAndDistinct((e) => e.id)
      const lstMedia: any = await this.mediaRepo.find({ where: { productId: In(lstProductId), table: enumData.MediaTable.Item.code } })
      lstMedia.forEach((c) => {
        if (Array.isArray(dictMedia[c.productId])) {
          dictMedia[c.productId] = [...dictMedia[c.productId], c]
        } else {
          dictMedia[c.productId] = [c]
        }
      })
    }

    for (let item of res) {
      if (item.brandId) {
        item.brandName = dictBrand[item.brandId]?.name
        item.brandCode = dictBrand[item.brandId]?.code
        item.brandParentId = dictBrand[item.brandId]?.parentId
      }
      item.images = dictMedia[item.id] || []
    }
    return res
  }

  async details(body: FilterIdDto) {
    try {
      const { productId } = body

      const product = await this.repo.findOne({
        where: { id: productId },
        relations: { prices: true, itemGroup: true, itemType: true, itemCategory: true, supplier: true, buyTax: true, sellTax: true },
      })
      if (!product) throw new Error(`Không tìm thấy sản phẩm`)
      const lstMedia = await this.mediaRepo.find({
        where: { productId, table: enumData.MediaTable.Item.code },
        select: { productId: true, url: true },
      })
      if (lstMedia && lstMedia.length > 0) product['images'] = lstMedia
      if (product.isCombo == true) {
        const lstProductIdInCombo = await this.productComboRepo.find({
          where: { itemId: productId },
          select: { itemId: true, itemInComboId: true, quantity: true },
        })
        const lstProduct = await this.repo.find({
          where: { id: In(lstProductIdInCombo.map((val) => val.itemInComboId)) },
          relations: { itemGroup: true },
          select: { id: true, code: true, name: true, unitId: true },
        })

        const productIds = lstProduct.map((val) => val.id)
        const productUnitIds = lstProduct.map((val) => val.unitId)
        const prices = await this.productPriceRepo.find({ where: { itemId: In(productIds), isFinal: true } })
        const units = await this.unitRepo.find({ where: { id: In(productUnitIds), isDeleted: false } })

        const lstMedia = await this.mediaRepo.find({ select: { productId: true, url: true } })
        const lstProductCombo = lstProductIdInCombo.map((combo) => {
          const product = lstProduct.find((p) => p.id === combo.itemInComboId)
          const media = lstMedia.find((m) => m.productId === product.id)
          const price = prices.find((p) => p.itemId === combo.itemInComboId)
          const unit = units.find((p) => p.id === product.unitId)

          return {
            ...product,
            image: media?.url,
            quantity: combo.quantity,
            prices: price,
            unit: unit.name,
          }
        })

        product['lstProductCombo'] = lstProductCombo
        const pricesRl = (await product.prices).find((i) => i.isFinal === true)
        product['priceSell'] = +pricesRl?.priceSell
        product['priceOriginal'] = +pricesRl?.priceOriginal
        delete product.prices
        delete product['__prices__']
      } else {
        const unit = await this.unitRepo.findOne({ where: { id: product.unitId } })
        const poUnit = await this.unitRepo.findOne({ where: { id: product.poUnitId } })
        const prices = (await product.prices).find((i) => i.isFinal === true)

        product['priceSell'] = +prices?.priceSell
        product['priceOriginal'] = +prices?.priceOriginal
        product['supplierName'] = (await product.supplier)?.name
        product['groupName'] = (await product.itemGroup)?.name
        product['typeName'] = (await product.itemType)?.name
        product['cateGoryName'] = (await product.itemCategory)?.name
        product['unitName'] = unit?.name ?? ''
        product['poUnitName'] = poUnit?.name ?? ''
        const buyTax = (await product.buyTax)?.type
        const sellTax = (await product.sellTax)?.type
        product['buyTaxName'] = enumData.TaxType[buyTax]?.name
        product['sellTaxName'] = enumData.TaxType[sellTax]?.name
      }

      return product
    } catch (err) {
      throw new Error()
    }
  }

  async searchItems(body: SearchDto, req?: IRequest) {
    const { searchValue, take, skip } = body
    if (!searchValue) {
      await this.findAll({ where: {}, skip: 0, take: 10 })
    }

    // Kiểm tra nếu searchValue có tồn tại chữ Kỳ
    let listId = []
    let content = ''
    if (searchValue.toLocaleLowerCase().includes('kỳ')) {
      const input = searchValue.trim()
      let number: number | null = null

      const regex1 = /^(\d+)\s*(kỳ)(?:\s*(.*))?$/i
      const regex2 = /^(kỳ)\s*(\d+)(?:\s*(.*))?$/i

      let match = input.match(regex1)
      if (match) {
        number = Number(match[1])
        content = match[3]?.trim().toLowerCase() || ''
      } else {
        match = input.match(regex2)
        if (match) {
          number = Number(match[2])
          content = match[3]?.trim().toLowerCase() || ''
        }
      }

      const listComboConfig = await omsApiHelper.listProductPeriodBonus(req)

      // Lọc theo số kỳ và nội dung name.includes
      const listComboConfigFilter = listComboConfig.filter((item) => {
        const matchesNumber = number ? +item.numberOfPeriods === +number : true
        return matchesNumber
      })

      const listId = Array.from(new Set(listComboConfigFilter.map((item) => item.productId)))

      const productsInfo = await this.repo.find({
        where: {
          id: In(listId),
          name: ILike(`%${content}%`),
          isDeleted: false,
        },
        relations: { prices: true },
      })

      const data = []
      for (let combo of listComboConfigFilter) {
        const product = productsInfo?.find((item) => item.id === combo.productId)
        if (product) {
          const price = (await product?.prices)?.find((p) => p.isFinal === true)
          data.push({
            ...combo,
            name: `${product?.name ?? ''} ${combo.name}`,
            code: product?.code,
            priceOriginal: +price?.priceOriginal,
            priceSell: +price?.priceSell,
            isRecurring: true,
            periodId: combo.id,
            totalPriceWithoutDiscount: +combo.totalPayment + +combo.totalDiscountPrice,
          })
        }
      }

      const dictMedia: any = {}
      {
        const lstProductId = data.mapAndDistinct((e) => e.productId)
        const lstMedia: any = await this.mediaRepo.find({ where: { productId: In(lstProductId), table: enumData.MediaTable.Item.code } })
        lstMedia.forEach((c) => {
          if (Array.isArray(dictMedia[c.productId])) {
            dictMedia[c.productId] = [...dictMedia[c.productId], c]
          } else {
            dictMedia[c.productId] = [c]
          }
        })
      }
      for (let item of data) {
        item['images'] = dictMedia[item.productId] || []
        delete item['__prices__']
      }

      return {
        data,
        total: productsInfo.length,
      }
    }

    const [data, total] = await this.repo
      .createQueryBuilder('item')
      .leftJoinAndSelect('item.itemGroup', 'itemGroup')
      .leftJoinAndSelect('item.itemCategory', 'itemCategory')
      .leftJoinAndSelect('item.itemType', 'itemType')
      .leftJoinAndSelect('item.prices', 'prices')
      .select([
        'item.id',
        'item.code',
        'item.name',
        'item.brandId',
        'itemType.name',
        'itemType.code',
        'itemType.id',
        'itemGroup.name',
        'itemCategory.name',
      ])
      .where('item.isDeleted = :isDeleted', { isDeleted: false })
      .andWhere(
        new Brackets((qb) => {
          qb.where('item.name ILIKE :searchValue', { searchValue: `%${searchValue}%` })
            .orWhere('itemGroup.name ILIKE :searchValue', { searchValue: `%${searchValue}%` })
            .orWhere('itemCategory.name ILIKE :searchValue', { searchValue: `%${searchValue}%` })
            .orWhere('itemType.name ILIKE :searchValue', { searchValue: `%${searchValue}%` })
            .orWhere('item.code ILIKE :searchValue', { searchValue: `%${searchValue}%` })
        }),
      )
      .skip(skip)
      .take(take)
      .getManyAndCount()

    const dictMedia: any = {}
    {
      const lstProductId = data.mapAndDistinct((e) => e.id)
      const lstMedia: any = await this.mediaRepo.find({ where: { productId: In(lstProductId), table: enumData.MediaTable.Item.code } })
      lstMedia.forEach((c) => {
        if (Array.isArray(dictMedia[c.productId])) {
          dictMedia[c.productId] = [...dictMedia[c.productId], c]
        } else {
          dictMedia[c.productId] = [c]
        }
      })
    }

    const dictBrand: any = {}
    {
      const lstBrandId = data.mapAndDistinct((e) => e.brandId)
      const lstBrand: any = await this.brandRepo.find({ where: { isDeleted: false, id: In(lstBrandId) } })
      lstBrand.forEach((c) => (dictBrand[c.id] = c))
    }

    for (let item of data) {
      item['productId'] = item.id
      item['images'] = dictMedia[item.id] || []
      item['priceSell'] = +(await item.prices).find((p) => p.isFinal)?.priceSell
      item['priceOriginal'] = +(await item.prices).find((p) => p.isFinal)?.priceOriginal
      item['itemTypeName'] = (await item.itemType)?.name
      item['itemTypeCode'] = (await item.itemType)?.code
      item['itemTypeId'] = (await item.itemType)?.id
      if (item.brandId) {
        item['brandName'] = dictBrand[item.brandId]?.name
        item['brandCode'] = dictBrand[item.brandId]?.code
        item['brandParentId'] = dictBrand[item.brandId]?.parentId
      }
      delete item['__prices__']
      delete item['__itemType__']
      delete item['__itemGroup__']
      delete item['__itemCategory__']
      delete item['__has_prices__']
    }
    return { data, total }
  }

  async searchItemsMobile(body: SearchDto, req?: IRequest) {
    const { searchValue, take, skip } = body
    if (!searchValue) {
      await this.findAll({ where: { isDeleted: false }, skip: 0, take: 10 })
    }

    // Kiểm tra nếu searchValue có tồn tại chữ Kỳ
    let content = ''
    if (searchValue.toLocaleLowerCase().includes('kỳ')) {
      const input = searchValue.trim()
      let number: number | null = null

      const regex1 = /^(\d+)\s*(kỳ)(?:\s*(.*))?$/i
      const regex2 = /^(kỳ)\s*(\d+)(?:\s*(.*))?$/i

      let match = input.match(regex1)
      if (match) {
        number = Number(match[1])
        content = match[3]?.trim().toLowerCase() || ''
      } else {
        match = input.match(regex2)
        if (match) {
          number = Number(match[2])
          content = match[3]?.trim().toLowerCase() || ''
        }
      }

      const listComboConfig = await omsApiHelper.listProductPeriodBonus(req)

      // Lọc theo số kỳ và nội dung name.includes
      const listComboConfigFilter = listComboConfig.filter((item) => {
        const matchesNumber = number ? +item.numberOfPeriods === +number : true
        return matchesNumber
      })

      const listId = Array.from(new Set(listComboConfigFilter.map((item) => item.productId)))

      const productsInfo = await this.repo.find({
        where: {
          id: In(listId),
          name: ILike(`%${content}%`),
          isDeleted: false,
        },
        relations: { prices: true },
      })

      const data = []
      for (let combo of listComboConfigFilter) {
        const product = productsInfo?.find((item) => item.id === combo.productId)
        if (product) {
          const price = (await product?.prices)?.find((p) => p.isFinal === true)
          data.push({
            ...combo,
            name: `${product?.name ?? ''} ${combo.name}`,
            code: product?.code,
            priceOriginal: +price?.priceOriginal,
            priceSell: +price?.priceSell,
            isRecurring: true,
            periodId: combo.id,
            totalPriceWithoutDiscount: +combo.totalPayment + +combo.totalDiscountPrice,
          })
        }
      }

      const dictMedia: any = {}
      {
        const lstProductId = data.mapAndDistinct((e) => e.productId)
        const lstMedia: any = await this.mediaRepo.find({ where: { productId: In(lstProductId), table: enumData.MediaTable.Item.code } })
        lstMedia.forEach((c) => {
          if (Array.isArray(dictMedia[c.productId])) {
            dictMedia[c.productId] = [...dictMedia[c.productId], c]
          } else {
            dictMedia[c.productId] = [c]
          }
        })
      }
      for (let item of data) {
        item['images'] = dictMedia[item.productId] || []
        delete item['__prices__']
      }

      return {
        data,
        total: productsInfo.length,
      }
    }

    const [data, total] = await this.repo
      .createQueryBuilder('item')
      .leftJoinAndSelect('item.itemGroup', 'itemGroup')
      .leftJoinAndSelect('item.itemCategory', 'itemCategory')
      .leftJoinAndSelect('item.itemType', 'itemType')
      .leftJoinAndSelect('item.prices', 'prices')
      .select([
        'item.id',
        'item.code',
        'item.name',
        'item.brandId',
        'itemType.name',
        'itemType.code',
        'itemType.id',
        'itemGroup.name',
        'itemCategory.name',
      ])
      .where('item.isDeleted = :isDeleted', { isDeleted: false })
      .andWhere('item.orderPlatformType LIKE :orderPlatformType', { orderPlatformType: `%${NSItem.EOrderPlatformType.APP_BALACOM}%` })
      .andWhere(
        new Brackets((qb) => {
          qb.where('item.name ILIKE :searchValue', { searchValue: `%${searchValue}%` })
            .orWhere('itemGroup.name ILIKE :searchValue', { searchValue: `%${searchValue}%` })
            .orWhere('itemCategory.name ILIKE :searchValue', { searchValue: `%${searchValue}%` })
            .orWhere('itemType.name ILIKE :searchValue', { searchValue: `%${searchValue}%` })
            .orWhere('item.code ILIKE :searchValue', { searchValue: `%${searchValue}%` })
        }),
      )
      .skip(skip)
      .take(take)
      .getManyAndCount()

    const dictMedia: any = {}
    {
      const lstProductId = data.mapAndDistinct((e) => e.id)
      const lstMedia: any = await this.mediaRepo.find({ where: { productId: In(lstProductId), table: enumData.MediaTable.Item.code } })
      lstMedia.forEach((c) => {
        if (Array.isArray(dictMedia[c.productId])) {
          dictMedia[c.productId] = [...dictMedia[c.productId], c]
        } else {
          dictMedia[c.productId] = [c]
        }
      })
    }

    const dictBrand: any = {}
    {
      const lstBrandId = data.mapAndDistinct((e) => e.brandId)
      const lstBrand: any = await this.brandRepo.find({ where: { isDeleted: false, id: In(lstBrandId) } })
      lstBrand.forEach((c) => (dictBrand[c.id] = c))
    }

    for (let item of data) {
      item['productId'] = item.id
      item['images'] = dictMedia[item.id] || []
      item['priceSell'] = +(await item.prices).find((p) => p.isFinal)?.priceSell
      item['priceOriginal'] = +(await item.prices).find((p) => p.isFinal)?.priceOriginal
      item['itemTypeName'] = (await item.itemType)?.name
      item['itemTypeCode'] = (await item.itemType)?.code
      item['itemTypeId'] = (await item.itemType)?.id
      if (item.brandId) {
        item['brandName'] = dictBrand[item.brandId]?.name
        item['brandCode'] = dictBrand[item.brandId]?.code
        item['brandParentId'] = dictBrand[item.brandId]?.parentId
      }
      delete item['__prices__']
      delete item['__itemType__']
      delete item['__itemGroup__']
      delete item['__itemCategory__']
      delete item['__has_prices__']
    }
    return { data, total }
  }

  async getItem(body: FilterIdDto) {
    const queryBuilder = this.repo.createQueryBuilder('item')
    if (body.isDeleted !== undefined) {
      queryBuilder.andWhere('item.isDeleted = :isDeleted', { isDeleted: body.isDeleted })
    }
    if (body?.name) {
      queryBuilder.andWhere('item.name LIKE :name', { name: `%${body.name}%` })
    }
    if (body?.code) {
      queryBuilder.andWhere('item.code LIKE :code', { code: `%${body.code}%` })
    }
    if (body?.lstId?.length > 0) {
      queryBuilder.andWhere('item.id IN (:...lstId)', { lstId: body.lstId })
    }
    if (body?.lstCode?.length > 0) {
      queryBuilder.andWhere('item.code IN (:...lstCode)', { lstCode: body.lstCode })
    }
    if (body?.id) {
      queryBuilder.andWhere('item.id = :id', { id: body.id })
    }
    if (body?.isCombo !== undefined) {
      queryBuilder.andWhere('item.isCombo = :isCombo', { isCombo: body.isCombo })
    }

    if (body?.warehouseId) {
      const warehouseProductIds = await this.warehouseProductRepo
        .createQueryBuilder('wp')
        .select('wp.productId')
        .where('wp.warehouseId = :warehouseId', { warehouseId: body.warehouseId })
        .getRawMany()

      if (warehouseProductIds.length > 0) {
        const ids = warehouseProductIds.map((item) => item.product_id)
        queryBuilder.andWhere('item.id IN (:...warehouseProductIds)', { warehouseProductIds: ids })
      }
    }

    queryBuilder.leftJoinAndSelect('item.unit', 'unit')
    queryBuilder.leftJoinAndSelect('item.prices', 'ip')

    const [data, total] = await queryBuilder
      .select([
        'item.id',
        'item.name',
        'item.code',
        'item.isCombo',
        'unit.name',
        'unit.code',
        'ip.priceSell',
        'ip.priceCapital',
        'ip.priceInput',
        'item.isDeleted',
      ])
      .getManyAndCount()
    return {
      data,
      total,
    }
  }

  /** Dành cho mobile kèm lấy theo danh mục sản phẩm */
  async findAll(body: ListProduct, req?: IRequest) {
    const { isCombo, brandId, orderPrice, typeId, categoryId, groupId, name, productId, skip, take, channel } = body
    const whereCon: any = { isDeleted: false }
    if (isCombo === true) whereCon.isCombo = true
    if (isCombo === false) whereCon.isCombo = false
    if (brandId) whereCon.brandId = brandId
    if (typeId) whereCon.itemTypeId = typeId
    if (categoryId) whereCon.itemCategoryId = categoryId
    if (groupId) whereCon.itemGroupId = groupId
    if (name) whereCon.name = ILike(`%${name}%`)
    if (body.isFavoriteCombo === true) whereCon.isFavoriteCombo = true
    if (body.isFavoriteCombo === false) whereCon.isFavoriteCombo = false
    if (body.isPeriodSale === true) whereCon.isPeriodSale = true
    if (body.isPeriodSale === false) whereCon.isPeriodSale = false
    if (channel) whereCon.orderPlatformType = ILike(`%${channel}%`)

    let includeComboIds = []
    if (groupId) {
      const items = await this.repo.find({
        where: {
          itemGroupId: groupId,
          isDeleted: false,
        },
      })
      const ids = items.map((i) => i.id)
      const productCombos = await this.productComboRepo.find({
        where: {
          itemInComboId: In(ids),
        },
      })
      // Danh sách combo
      includeComboIds = Array.from(new Set(productCombos.map((i) => i.itemId)))
    }

    let relatedCombo = []
    if (productId) {
      const lstComboWithProduct = await this.productComboRepo.find({ where: { itemInComboId: productId }, select: ['itemId'] })
      const lstComboIds = lstComboWithProduct.map((c) => c.itemId)
      relatedCombo = Array.from(new Set(lstComboIds))
      whereCon.id = In(relatedCombo)
    }

    let [data, total] = await this.repo.findAndCount({
      where: [{ ...whereCon }, includeComboIds.length > 0 ? { id: In(includeComboIds), isDeleted: false } : {}],
      select: { id: true, code: true, name: true, isCombo: true, canPreOrder: true, supplierId: true, description: true },
      order: {
        ...(orderPrice && { prices: orderPrice == 'ASC' ? { priceSell: 'ASC' } : { priceSell: 'DESC' } }),
        isCombo: 'ASC',
      },
      relations: ['prices', 'itemType', 'itemGroup', 'itemCategory'],
      skip,
      take,
    })

    if (data.length == 0) return []

    const dictMedia: any = {}
    {
      const lstProductId = data.mapAndDistinct((e) => e.id)
      const lstMedia: any = await this.mediaRepo.find({ where: { productId: In(lstProductId), table: enumData.MediaTable.Item.code } })
      lstMedia.forEach((c) => {
        if (Array.isArray(dictMedia[c.productId])) {
          dictMedia[c.productId] = [...dictMedia[c.productId], c]
        } else {
          dictMedia[c.productId] = [c]
        }
      })
    }

    const supplierIds = data.map((e) => e.supplierId)
    const whs = await this.warehouseRepo.find({ where: { isDeleted: false, storeId: In(supplierIds) } })

    const whProductDetails = await this.warehouseProductRepo.find({
      where: {
        productId: In(data.map((e) => e.id)),
        warehouseId: In(whs.map((e) => e.id)),
      },
    })

    for (let item of data) {
      const prices = (await item.prices).find((p) => p.isFinal)
      const productDetail = whProductDetails.find((p) => p.productId === item.id)
      // Tính tổng số lượng trong detail
      const quantityTotal = productDetail?.quantity - productDetail?.quantityOrder
      item['quantity'] = quantityTotal > 0 ? quantityTotal : 0

      item['images'] = dictMedia[item.id] || []
      item['priceSell'] = +prices?.priceSell // Giá bán chung
      item['priceOriginal'] = +prices?.priceOriginal // Giá gốc
      item['priceSaleOff'] = +prices?.priceOriginal - +prices?.priceSell // Giá khuyến mãi
      item['itemTypeName'] = (await item.itemType)?.name
      item['itemTypeCode'] = (await item.itemType)?.code
      item['itemTypeId'] = (await item.itemType)?.id

      item['itemCategoryName'] = (await item.itemCategory)?.name
      item['itemCategoryCode'] = (await item.itemCategory)?.code
      item['itemCategoryId'] = (await item.itemCategory)?.id

      item['itemGroupName'] = (await item.itemGroup)?.name
      item['itemGroupCode'] = (await item.itemGroup)?.code
      item['itemGroupId'] = (await item.itemGroup)?.id

      delete item['__prices__']
      delete item['__itemType__']
      delete item['__itemCategory__']
      delete item['__itemGroup__']
    }
    return { data, total }
  }

  async findAllMobile(body: ListProduct, req?: IRequest) {
    const { isCombo, brandId, orderPrice, typeId, categoryId, groupId, name, productId, skip, take } = body
    const whereCon: any = { isDeleted: false }
    if (isCombo === true) whereCon.isCombo = true
    if (isCombo === false) whereCon.isCombo = false
    if (brandId) whereCon.brandId = brandId
    if (typeId) whereCon.itemTypeId = typeId
    if (categoryId) whereCon.itemCategoryId = categoryId
    if (groupId) whereCon.itemGroupId = groupId
    if (name) whereCon.name = ILike(`%${name}%`)
    if (body.isFavoriteCombo === true) whereCon.isFavoriteCombo = true
    if (body.isFavoriteCombo === false) whereCon.isFavoriteCombo = false
    if (body.isPeriodSale === true) whereCon.isPeriodSale = true
    if (body.isPeriodSale === false) whereCon.isPeriodSale = false

    let includeComboIds = []
    if (groupId) {
      const items = await this.repo.find({
        where: {
          itemGroupId: groupId,
          isDeleted: false,
        },
      })
      const ids = items.map((i) => i.id)
      const productCombos = await this.productComboRepo.find({
        where: {
          itemInComboId: In(ids),
        },
      })
      // Danh sách combo
      includeComboIds = Array.from(new Set(productCombos.map((i) => i.itemId)))
    }

    let relatedCombo = []
    if (productId) {
      const lstComboWithProduct = await this.productComboRepo.find({ where: { itemInComboId: productId }, select: ['itemId'] })
      const lstComboIds = lstComboWithProduct.map((c) => c.itemId)
      relatedCombo = Array.from(new Set(lstComboIds))
      whereCon.id = In(relatedCombo)
    }

    let [data, total] = await this.repo.findAndCount({
      where: [
        { ...whereCon, orderPlatformType: ILike(`%${NSItem.EOrderPlatformType.APP_BALACOM}%`) },
        includeComboIds.length > 0 ? { id: In(includeComboIds), isDeleted: false } : {},
      ],
      select: { id: true, code: true, name: true, isCombo: true, canPreOrder: true, supplierId: true, description: true },
      order: {
        ...(orderPrice && { prices: orderPrice == 'ASC' ? { priceSell: 'ASC' } : { priceSell: 'DESC' } }),
        isCombo: 'ASC',
      },
      relations: ['prices', 'itemType', 'itemGroup', 'itemCategory'],
      skip,
      take,
    })

    if (data.length == 0) return []

    const dictMedia: any = {}
    {
      const lstProductId = data.mapAndDistinct((e) => e.id)
      const lstMedia: any = await this.mediaRepo.find({ where: { productId: In(lstProductId), table: enumData.MediaTable.Item.code } })
      lstMedia.forEach((c) => {
        if (Array.isArray(dictMedia[c.productId])) {
          dictMedia[c.productId] = [...dictMedia[c.productId], c]
        } else {
          dictMedia[c.productId] = [c]
        }
      })
    }

    const supplierIds = data.map((e) => e.supplierId)
    const whs = await this.warehouseRepo.find({ where: { isDeleted: false, storeId: In(supplierIds) } })

    const whProductDetails = await this.warehouseProductRepo.find({
      where: {
        productId: In(data.map((e) => e.id)),
        warehouseId: In(whs.map((e) => e.id)),
      },
    })

    for (let item of data) {
      const prices = (await item.prices).find((p) => p.isFinal)
      const productDetail = whProductDetails.find((p) => p.productId === item.id)
      // Tính tổng số lượng trong detail
      const quantityTotal = productDetail?.quantity - productDetail?.quantityOrder
      item['quantity'] = quantityTotal > 0 ? quantityTotal : 0

      item['images'] = dictMedia[item.id] || []
      item['priceSell'] = +prices?.priceSell // Giá bán chung
      item['priceOriginal'] = +prices?.priceOriginal // Giá gốc
      item['priceSaleOff'] = +prices?.priceOriginal - +prices?.priceSell // Giá khuyến mãi
      item['itemTypeName'] = (await item.itemType)?.name
      item['itemTypeCode'] = (await item.itemType)?.code
      item['itemTypeId'] = (await item.itemType)?.id

      item['itemCategoryName'] = (await item.itemCategory)?.name
      item['itemCategoryCode'] = (await item.itemCategory)?.code
      item['itemCategoryId'] = (await item.itemCategory)?.id

      item['itemGroupName'] = (await item.itemGroup)?.name
      item['itemGroupCode'] = (await item.itemGroup)?.code
      item['itemGroupId'] = (await item.itemGroup)?.id

      delete item['__prices__']
      delete item['__itemType__']
      delete item['__itemCategory__']
      delete item['__itemGroup__']
    }
    return { data, total }
  }

  /** Danh sách sản phẩm liên quan */
  async getRelatedProducts(body: ProductDto) {
    const { productId, take, skip } = body
    const product = await this.repo.findOne({ where: { id: productId, isDeleted: false } })
    if (!product) throw new Error(`Không tìm thấy sản phẩm`)

    let combos = { data: [], total: 0 }
    let items = { data: [], total: 0 }

    if (product.isCombo) {
      const lstProductInCombo = await this.productComboRepo.find({ where: { itemId: product.id }, select: ['itemInComboId'] })
      const lstProductIds = lstProductInCombo.map((i) => i.itemInComboId)

      const otherComboLst = await this.productComboRepo.find({ where: { itemInComboId: In(lstProductIds) }, select: ['itemId'] })
      const lstComboIds = otherComboLst.filter((o) => o.itemId != product.id).map((c) => c.itemId)

      const [data, total] = await this.repo.findAndCount({
        where: { id: In(lstComboIds), isDeleted: false },
        select: ['id', 'code', 'name'],
        relations: ['prices', 'itemType'],
        skip,
        take,
      })
      combos = { data, total }
    } else {
      const lstComboWithProduct = await this.productComboRepo.find({ where: { itemInComboId: product.id }, select: ['itemId'] })
      const lstComboIds = lstComboWithProduct.map((c) => c.itemId)

      const [lstItemRelated, totalItem] = await this.repo.findAndCount({
        where: { itemGroupId: product.itemGroupId, isDeleted: false, id: Not(productId) },
        select: ['id', 'code', 'name'],
        relations: ['prices', 'itemType'],
        skip,
        take,
      })

      const [lstComboRelated, totalCombo] = await this.repo.findAndCount({
        where: { id: In(lstComboIds), isCombo: true, isDeleted: false },
        select: ['id', 'code', 'name'],
        relations: ['prices', 'itemType'],
        skip,
        take,
      })

      combos = { data: lstComboRelated, total: totalCombo }
      items = { data: lstItemRelated, total: totalItem }
    }

    const allProductIds = [...combos.data.map((e) => e.id), ...items.data.map((e) => e.id)]
    const lstMedia = await this.mediaRepo.find({ where: { productId: In(allProductIds), table: enumData.MediaTable.Item.code } })
    const dictMedia = lstMedia.reduce((acc, media) => {
      acc[media.productId] = acc[media.productId] ? [...acc[media.productId], media] : [media]
      return acc
    }, {})

    const supplierIds = [...combos.data.map((e) => e.supplierId), ...items.data.map((e) => e.supplierId)]
    const whs = await this.warehouseRepo.find({ where: { isDeleted: false, storeId: In(supplierIds) } })

    const whProductDetails = await this.warehouseProductRepo.find({
      where: {
        productId: In(allProductIds),
        warehouseId: In(whs.map((e) => e.id)),
      },
    })

    const enrichProductData = async (products) => {
      for (let item of products) {
        const prices = (await item.prices).find((p) => p.isFinal)
        const type = await item.itemType
        const productDetail = whProductDetails.find((p) => p.productId === item.id)

        item['quantity'] = productDetail?.quantity - productDetail?.quantityOrder || 0
        item['itemTypeCode'] = type?.code
        item['itemTypeId'] = type?.id
        item['itemTypeName'] = type?.name
        item['images'] = dictMedia[item.id] || []
        item['priceSell'] = +prices?.priceSell || 0
        item['priceOriginal'] = +prices?.priceOriginal || 0
        item['priceSaleOff'] = +prices?.priceOriginal - +prices?.priceSell || 0

        delete item['__prices__']
      }
    }

    await enrichProductData(combos.data)
    await enrichProductData(items.data)

    return { combos, items }
  }

  /** Kiểm tra sản phẩm có combo liên quan không */
  async checkRelatedComboInProduct(body: ProductDto) {
    const { productId } = body
    const product = await this.repo.findOne({ where: { id: productId, isDeleted: false } })
    if (!product) throw new Error(`Không tìm thấy sản phẩm`)

    const countComboWithProduct = await this.productComboRepo.count({ where: { itemInComboId: product.id } })
    if (countComboWithProduct > 0) return true

    return false
  }

  /** Full chi tiet combo */
  async findByIds(data: FindItemsByIdsDto) {
    const { ids, productName, isFavoriteCombo, isDeleted, channel } = data
    const wheres: any = {}
    if (productName) wheres.name = ILike(`%${productName}%`)
    if (isFavoriteCombo) wheres.isFavoriteCombo = true
    if (isDeleted !== undefined) wheres.isDeleted = isDeleted
    if (channel) wheres.channel = channel
    let res = []
    const listCombo = await this.repo.find({
      where: {
        id: In(ids),
        ...wheres,
      },
      relations: {
        itemType: true,
        prices: true,
        itemGroup: true,
        unit: true,
        pOUnit: true,
      },
    })

    // Danh sách nhà cung cấp
    const supplier = await this.supplierRepo.find({ where: { isDeleted: false }, select: ['id', 'name', 'code', 'address'] })

    // Tìm sản phẩm
    for (let itemCombo of listCombo) {
      const imgCombo = await this.mediaRepo.findOne({
        where: {
          productId: itemCombo.id,
          table: enumData.MediaTable.Item.code,
          //isDeleted: false,
        },
      })
      const listItemComboMap = await itemCombo.itemCombo
      const listItemInComboId = listItemComboMap.map((p) => p.itemInComboId)
      const listItemInCombo = await this.repo.find({
        where: { id: In(listItemInComboId) },
        relations: {
          unit: true,
          prices: true,
          pOUnit: true,
        },
      })
      let listItem = []
      for (let item of listItemInCombo) {
        const img = await this.mediaRepo.findOne({
          where: {
            productId: itemCombo.id,
            table: enumData.MediaTable.Item.code,
            // isDeleted: false,
          },
        })
        const itemMap = listItemComboMap.find((p) => p.itemInComboId === item.id)
        const itemPrices = await item.prices
        const unit = await item.unit
        const poUnit = await item.pOUnit
        const sup = supplier.find((s) => s.id === item.supplierId)
        listItem.push({
          id: item.id,
          name: item.name,
          code: item.code,
          description: item.description,
          unit: unit?.name,
          poUnit: poUnit?.name,
          price: itemPrices.find((p) => p.isFinal === true)?.priceSell,
          priceOriginal: itemPrices.find((p) => p.isFinal === true)?.priceOriginal,
          quantity: itemMap.quantity,
          img: img?.url,
          supplier: sup?.name,
          supplierAddress: sup?.address,
        })
      }
      let obj = {
        id: itemCombo.id,
        code: itemCombo.code,
        name: itemCombo.name,
        unit: (await itemCombo.unit)?.name,
        poUnit: (await itemCombo.pOUnit)?.name,
        description: itemCombo.description,
        isCombo: itemCombo.isCombo,
        price: (await itemCombo.prices).find((p) => p.isFinal)?.priceSell,
        priceOriginal: (await itemCombo.prices).find((p) => p.isFinal)?.priceOriginal,
        quantity: itemCombo.quantity,
        img: imgCombo?.url,
        listItems: listItem,
        itemTypeId: itemCombo?.itemTypeId,
        itemTypeName: (await itemCombo.itemType)?.name,
        itemTypeCode: (await itemCombo.itemType)?.code,
        itemCategoryId: itemCombo?.itemCategoryId,
        itemCategoryName: (await itemCombo.itemCategory)?.name,
        itemCategoryCode: (await itemCombo.itemCategory)?.code,
        itemGroupId: itemCombo?.itemGroupId,
        itemGroupName: (await itemCombo.itemGroup)?.name,
        itemGroupCode: (await itemCombo.itemGroup)?.code,
        itemGroupType: (await itemCombo.itemGroup)?.groupType,
      }
      res.push(obj)
    }
    return res
  }

  /** Chi lay thong tin combo */
  async findComboByIds(ids: string[]) {
    return await this.repo.find({
      where: {
        id: In(ids),
        //isDeleted: false,
        isCombo: true,
      },
    })
  }

  async findLotNumberInProductDetail(data: { id?: string; expiryDate?: Date }) {
    // let whereCon: any = { isDeleted: false, productId: data.id }
    // if (data.expiryDate) whereCon.expiryDate = Raw((alias) => `DATE(${alias}) = DATE("${moment(data.expiryDate).format('YYYY-MM-DD')}")`)
    // let res: any = await this.inboundDetailRepo.find({ where: whereCon })
    // if (res.length == 0) return []
    // const newArray = this.removeDuplicates(res, 'lotNumber')
    // return newArray
  }

  removeDuplicates(arr: any, prop: any) {
    const uniqueSet = new Set()
    return arr.filter((obj: any) => {
      const propValue = obj[prop]
      if (!uniqueSet.has(propValue)) {
        uniqueSet.add(propValue)
        return true
      }
      return false
    })
  }

  async loadData(data: { isCombo?: boolean }) {
    const whereCon: any = { isDeleted: false }
    if (data?.isCombo != undefined) whereCon.isCombo = data.isCombo
    const rs = await this.repo.find({ where: whereCon, select: { id: true, code: true, name: true } })
    return rs
  }

  /** Tìm đơn tính của sản phẩm */
  async findProductInfo(data: FilterOneDto) {
    const checkProduct = await this.repo.findOne({ where: { id: data.id, isDeleted: false }, select: { id: true, unitId: true } })
    if (!checkProduct) throw new Error(`Không tìm thấy sản phẩm!`)

    const findUnit = await this.unitRepo.findOne({ where: { id: checkProduct.unitId, isDeleted: false } })
    if (!findUnit) throw new Error(`Sản phẩm chưa thiết lập đơn vị tính hoặc đơn vị tính đã bị ngưng hoạt động!`)

    const lstProductDetail: any[] = await this.productDetailRepo.find({
      where: { itemId: data.id, isDeleted: false },
      select: { id: true, expiryDate: true, manufactureDate: true, lotNumber: true, quantity: true },
    })
    const detail = []
    for (const itemDetail of lstProductDetail) {
      if (itemDetail.expiryDate) detail.push(itemDetail)
    }
    const info: any = findUnit
    info.lstProductDetail = detail

    const { warehouseId } = data
    if (warehouseId) {
      const whItemDetails = await this.warehouseProductDetailRepository.find({ where: { warehouseId, productId: data.id } })
      info.lstProductDetail = whItemDetails
    }

    for (let pd of info.lstProductDetail) {
      pd.manufactureDateFmt = pd.manufactureDate ? moment(new Date(pd.manufactureDate)).format('DD/MM/YYYY') : null
      pd.expiryDateFmt = moment(new Date(pd.expiryDate)).format('DD/MM/YYYY')
    }

    return findUnit
  }

  /** Tìm tồn kho của sản phẩm theo hạn sử dụng */
  async findProductInventory(data: ProductExpiryDateDto): Promise<{ quantity: number }> {
    const checkProduct = await this.repo.findOne({ where: { id: data.productId, isDeleted: false }, select: { id: true, unitId: true } })
    if (!checkProduct) throw new Error(`Không tìm thấy sản phẩm!`)

    const findUnit = await this.unitRepo.findOne({ where: { id: checkProduct.unitId, isDeleted: false } })
    if (!findUnit) throw new Error(`Sản phẩm chưa thiết lập đơn vị tính hoặc đơn vị tính đã bị ngưng hoạt động!`)
    let quantity: number = 0

    {
      const findProductDetail = await this.productDetailRepo.find({
        where: { itemId: data.productId, expiryDate: Equal(data.expiryDate) },
      })
      for (let pd of findProductDetail) {
        quantity += +pd.quantity || 0
      }
    }
    return { quantity }
  }

  /** Tìm ngày sản xuất của sản phẩm theo hạn sử dụng */
  async findProductManufactureDate(data: ProductExpiryDateDto): Promise<{ manufactureDate: Date }> {
    const checkProduct = await this.repo.findOne({ where: { id: data.productId, isDeleted: false }, select: { id: true, unitId: true } })
    if (!checkProduct) throw new Error(`Không tìm thấy sản phẩm!`)

    const findUnit = await this.unitRepo.findOne({ where: { id: checkProduct.unitId, isDeleted: false } })
    if (!findUnit) throw new Error(`Sản phẩm chưa thiết lập đơn vị tính hoặc đơn vị tính đã bị ngưng hoạt động!`)

    const findProductDetail = await this.productDetailRepo.findOne({
      where: { itemId: data.productId, expiryDate: Equal(data.expiryDate) },
    })
    return { manufactureDate: findProductDetail?.manufactureDate ?? null }
  }

  async findByPermissionBrand(req: IRequest, data: any, userlogin?: UserDto) {
    // if (userlogin.isAdmin) {
    //   return await this.repo.find({
    //     where: { isDeleted: false, isCombo: data.isCombo },
    //     relations: { details: true },
    //     order: { details: { expiryDate: 'ASC' } },
    //   })
    // }
    // const lstEmpBrand: any = await authApiHelper.findEmpBrand(req, { employeeId: userlogin.employeeId })
    // if (lstEmpBrand.length == 0) return []
    // let brandIds = lstEmpBrand.map((c: any) => c.brandId)
    // brandIds = Array.from(new Set(brandIds))
    // let lstChild: any = []
    // let lstParentId = Array.from(new Set(brandIds))
    // do {
    //   lstChild = await authApiHelper.findListBrand(req, { lstParentId: lstParentId })
    //   lstParentId = await lstChild.map((s) => {
    //     return s.id
    //   })
    //   lstParentId = Array.from(new Set(lstParentId))
    //   brandIds.push(...lstParentId)
    //   brandIds = Array.from(new Set(brandIds))
    // } while (lstChild.length > 0)
    // return await this.repo.find({ where: { isDeleted: false, brandId: In(brandIds), isCombo: data.isCombo }, order: { createdAt: 'ASC' } })
    return []
  }

  async createData(data: ProductCreateDto, req: IRequest, user?: UserDto) {
    if (data.unitId) {
      const findUnit = await this.unitRepo.findOne({ where: { id: data.unitId, isDeleted: false } })
      if (!findUnit) throw new Error(`Sản phẩm chưa thiết lập đơn vị tính hoặc đơn vị tính đã bị ngưng hoạt động!`)
    }

    if (data.itemTypeId) {
      const itemType = await this.itemTypeRepository.findOne({ where: { id: data.itemTypeId, isDeleted: false } })
      if (!itemType) throw new Error(`Loại sản phẩm bị ngưng hoạt động!`)
    }

    if (data.itemGroupId) {
      const itemGroup = await this.itemGroupRepository.findOne({ where: { id: data.itemGroupId, isDeleted: false } })
      if (!itemGroup) throw new Error(`Nhóm sản phẩm phẩm bị ngưng hoạt động!`)
    }

    if (data.itemCategoryId) {
      const itemCategory = await this.itemCategoryRepository.findOne({ where: { id: data.itemCategoryId, isDeleted: false } })
      if (!itemCategory) throw new Error(`Nhóm sản phẩm phẩm bị ngưng hoạt động!`)
    }

    if (data.supplierId) {
      const supplier = await this.supplierRepo.findOne({ where: { id: data.supplierId, isDeleted: false } })
      if (!supplier) throw new Error(`Nhà cung cấp!`)
    }

    if (data.brandId) {
      const brand = await this.brandRepo.findOne({ where: { id: data.brandId, isDeleted: false } })
      if (!brand) throw new Error(`Thương hiệu không tồn tại vui lòng chọn thương hiệu khác`)
    }

    if (data.buyTaxId) {
      const buyTax = await this.taxRepo.findOne({ where: { id: data.buyTaxId, type: enumData.TaxType.BUY.code, isDeleted: false } })
      if (!buyTax) throw new Error(`Thuế mua hàng không tìm thấy!`)
    }

    if (data.sellTaxId) {
      const sellTax = await this.taxRepo.findOne({ where: { id: data.sellTaxId, type: enumData.TaxType.SELL.code, isDeleted: false } })
      if (!sellTax) throw new Error(`Thuế bán hàng không tìm thấy!`)
    }

    const checkCodeExist = await this.repo.findOne({ where: { code: data.code } })
    if (checkCodeExist) throw new Error(`Mã sản phẩm đã tồn tại. Vui lòng kiểm tra lại`)

    return await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(ItemEntity)
      const productComboRepo = trans.getRepository(ItemComboEntity)
      const mediaRepo = trans.getRepository(MediaEntity)
      if (!data.dateTo) data.dateTo = new Date()
      data.code = data.code.trim()
      const newEntity = repo.create(data)
      newEntity.id = uuidv4()
      newEntity.dateTo = new Date(new Date(data.dateTo).setHours(0, 0, 0, 0))
      newEntity.createdAt = new Date()
      newEntity.createdBy = user?.id
      newEntity.vat = data?.vat
      newEntity.orderPlatformType = JSON.stringify(data.orderPlatformType)
      await repo.insert(newEntity)

      // tạo mới hình ảnh sản phẩm item
      if (data.lstMediaProduct && data.lstMediaProduct.length > 0) {
        let lstImages: any = []
        for (let item of data.lstMediaProduct) {
          item.productId = newEntity.id
          item.url = item.url
          item.table = enumData.MediaTable.Item.code
          lstImages.push(item)
        }
        await mediaRepo.insert(lstImages)
      }

      if (data.lstProductCombo && data.lstProductCombo.length > 0) {
        for (let item of data.lstProductCombo) {
          const entity = new ItemComboEntity()
          entity.itemId = newEntity.id
          entity.itemInComboId = item.productId
          entity.quantity = item.quantity
          entity.createdAt = new Date()
          entity.createdBy = user?.id
          await productComboRepo.insert(entity)
        }
      }
      const actionLogCreateDto: ActionLogCreateDto = {
        idHistory: newEntity.id,
        tableName: '',
        oldJson: null,
        newJson: JSON.stringify({ ...newEntity }),
        type: enumData.ActionLogType.CREATE.code,
        description: `Nhân viên [ ${user.username} ] đã thêm mới combo sản phẩm`,
      }
      await this.actionLogService.createData(user, actionLogCreateDto)
      return { message: CREATE_SUCCESS, item: { itemId: newEntity.id, itemCode: newEntity.code, itemName: newEntity.name } }
    })
  }

  async updateData(data: ProductUpdateDto, req: IRequest, user?: UserDto) {
    if (data.unitId) {
      const findUnit = await this.unitRepo.findOne({ where: { id: data.unitId, isDeleted: false } })
      if (!findUnit) throw new Error(`Sản phẩm chưa thiết lập đơn vị tính hoặc đơn vị tính đã bị ngưng hoạt động!`)
    }

    if (data.poUnitId) {
      const findUnit = await this.unitRepo.findOne({ where: { id: data.poUnitId, isDeleted: false } })
      if (!findUnit) throw new Error(`Sản phẩm chưa thiết lập đơn vị tính hoặc đơn vị tính đã bị ngưng hoạt động!`)
    }

    if (data.itemTypeId) {
      const itemType = await this.itemTypeRepository.findOne({ where: { id: data.itemTypeId, isDeleted: false } })
      if (!itemType) throw new Error(`Loại sản phẩm bị ngưng hoạt động!`)
    }

    if (data.itemGroupId) {
      const itemGroup = await this.itemGroupRepository.findOne({ where: { id: data.itemGroupId, isDeleted: false } })
      if (!itemGroup) throw new Error(`Nhóm sản phẩm phẩm bị ngưng hoạt động!`)
    }

    if (data.itemCategoryId) {
      const itemCategory = await this.itemCategoryRepository.findOne({ where: { id: data.itemCategoryId, isDeleted: false } })
      if (!itemCategory) throw new Error(`Nhóm sản phẩm phẩm bị ngưng hoạt động!`)
    }

    if (data.supplierId) {
      const supplier = await this.supplierRepo.findOne({ where: { id: data.supplierId, isDeleted: false } })
      if (!supplier) throw new Error(`Nhà cung cấp!`)
    }

    if (data.brandId) {
      const brand = await this.brandRepo.findOne({ where: { id: data.brandId, isDeleted: false } })
      if (!brand) throw new Error(`Thương hiệu của sản phẩm đã bị ngưng hoạt động vui lòng chọn thương hiệu khác`)
    }

    if (data.buyTaxId) {
      const buyTax = await this.taxRepo.findOne({ where: { id: data.buyTaxId, type: enumData.TaxType.BUY.code, isDeleted: false } })
      if (!buyTax) throw new Error(`Thuế mua hàng không tìm thấy!`)
    }

    if (data.sellTaxId) {
      const sellTax = await this.taxRepo.findOne({ where: { id: data.sellTaxId, type: enumData.TaxType.SELL.code, isDeleted: false } })
      if (!sellTax) throw new Error(`Thuế bán hàng không tìm thấy!`)
    }

    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    const oldEntityData = { ...entity }

    // Nếu sản phẩm là combo thì kiểm tra xem đã có phiếu xuất kho combo đó hay chưa
    /** 
    if (entity.isCombo) {
      const find = await this.outboundDetailRepo.findOne({ where: { productId: entity.id } })
      if (find) throw new Error(`Sản phẩm combo đã có phiếu xuất kho, không thể chỉnh sửa`)

      if (new Date(new Date(data.dateTo).setHours(0, 0, 0, 0)).getTime() < new Date(new Date().setHours(0, 0, 0, 0)).getTime()) {
        throw new Error(`Không thể chỉnh sửa ngày hết hạn trước ngày hiện tại`)
      }
      if (new Date(new Date(data.dateFrom).setHours(0, 0, 0, 0)).getTime() > new Date(new Date(entity.dateTo).setHours(0, 0, 0, 0)).getTime()) {
        throw new Error(`Không thể chỉnh sửa ngày bắt đầu sau ngày hết hạn`)
      }
    }
    */
    return await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(ItemEntity)
      const productComboRepo = trans.getRepository(ItemComboEntity)
      const mediaRepo = trans.getRepository(MediaEntity)

      entity.name = data.name
      entity.description = data.description
      entity.brandId = data.brandId
      entity.isOpenSale = data.isOpenSale
      entity.osCode = data.osCode
      entity.vat = data?.vat
      entity.itemCategoryId = data.itemCategoryId

      entity.itemGroupId = data.itemGroupId
      entity.brandId = data.brandId

      entity.itemTypeId = data.itemTypeId

      entity.supplierId = data?.supplierId

      entity.specifications = data.specifications
      entity.barCode = data.barCode
      entity.companyName = data.companyName
      entity.isCombo = data.isCombo
      entity.dateFrom = data.dateFrom
      entity.dateTo = new Date(new Date(data.dateTo).setHours(0, 0, 0, 0))
      entity.unitId = data.unitId
      entity.quantityUnit = data.quantityUnit
      entity.kg = data.kg
      entity.cbm = data.cbm
      entity.length = data.length
      entity.width = data.width
      entity.height = data.height
      entity.updatedAt = new Date()
      entity.updatedBy = user?.id
      entity.poUnitId = data.poUnitId
      entity.buyTaxId = data.buyTaxId
      entity.sellTaxId = data.sellTaxId
      entity.isPeriodSale = data.isPeriodSale

      entity.canPreOrder = data.canPreOrder
      entity.orderPlatformType = JSON.stringify(data.orderPlatformType)
      await repo.save(entity)
      // tạo mới hình ảnh sản phẩm
      if (data.lstMediaProduct && data.lstMediaProduct.length > 0) {
        await mediaRepo.delete({ productId: entity.id, table: enumData.MediaTable.Item.code })
        let lstImages: any = []
        for (let item of data.lstMediaProduct) {
          item.productId = entity.id
          item.url = item.url
          item.table = enumData.MediaTable.Item.code
          lstImages.push(item)
        }
        await mediaRepo.insert(lstImages)
        // await authApiHelper.createMedia(req, lstImages)
      }

      await productComboRepo.delete({ itemId: data.id })

      if (data.lstProductCombo && data.lstProductCombo.length > 0) {
        for (let item of data.lstProductCombo) {
          const entity = new ItemComboEntity()
          entity.itemId = data.id
          entity.itemInComboId = item.productId
          entity.quantity = item.quantity
          entity.createdAt = new Date()
          entity.createdBy = user?.id
          await productComboRepo.insert(entity)
        }
      }
      const actionLogCreateDto: ActionLogCreateDto = {
        idHistory: entity.id,
        tableName: '',
        oldJson: JSON.stringify(oldEntityData),
        newJson: JSON.stringify({ ...entity }),
        type: enumData.ActionLogType.UPDATE.code,
        description: `Nhân viên [ ${user.username} ] đã cập nhật combo sản phẩm`,
      }
      await this.actionLogService.createData(user, actionLogCreateDto)

      return { message: UPDATE_SUCCESS }
    })
  }

  async pagination(data: PaginationDto, req: IRequest) {
    let whereCon: any = {}
    if (data.where.canPreOrder != undefined) whereCon.canPreOrder = data.where.canPreOrder
    if (data.where.name) whereCon.name = ILike(`%${data.where.name}%`)
    if (data.where.code) whereCon.code = ILike(`%${data.where.code}%`)

    if (data.where.lstCode && data.where.lstCode.length > 0) {
      whereCon.code = In(data.where.lstCode)
    }
    if (data.where.lstId && data.where.lstId.length > 0) {
      whereCon.id = In(data.where.lstId)
    }

    if (data.where.isDeleted === false) whereCon.isDeleted = data.where.isDeleted
    if (data.where.isDeleted === true) whereCon.isDeleted = data.where.isDeleted
    if (data.where.isCombo === true) whereCon.isCombo = true
    if (data.where.isCombo === false) whereCon.isCombo = false
    if (data.where.isDeleted === undefined) {
      let list = [true, false]
      whereCon.isDeleted = In(list)
    }
    if (data.where.orderPlatformType) whereCon.orderPlatformType = ILike(`%${data.where.orderPlatformType}%`)

    let result: any = await this.repo.findAndCount({
      where: whereCon,
      order: { createdAt: 'DESC' },
      skip: data.skip,
      take: data.take,
      relations: {
        itemCombo: { itemInCombo: { itemGroup: true } },
        unit: true,
        pOUnit: true,
        itemType: true,
        itemGroup: true,
        itemCategory: true,
        prices: true,
        supplier: true,
        buyTax: true,
        sellTax: true,
        brand: true,
      },
    })
    if (result[0].length == 0) return [[], 0]
    const dicMedia: any = {}
    {
      const lstMedia = await this.mediaRepo.find({ where: { productId: In(result[0].map((x) => x.id)) } })
      lstMedia.forEach((x) => (dicMedia[x.productId] = x))
    }

    for (let item of result[0]) {
      item.media = dicMedia[item.id]
      if (item.dateTo) {
        item.isCancelCombo = false
        if (new Date(new Date(item.dateTo).setHours(23, 59, 59, 99)).getTime() < new Date(new Date().setHours(23, 59, 59, 99)).getTime())
          item.isCancelCombo = true
      }
      let lstProductComno = await item.itemCombo
      if (lstProductComno.length > 0) {
        for (let item of lstProductComno) {
          let productObj = await item.itemInCombo
          item.productName = productObj.name
          item.productCode = productObj.code
        }
      }
      item.lstProductComno = lstProductComno
      const brand = await item?.__brand__
      const poUnit = await item?.__pOUnit__
      const unit = await item?.__unit__
      const type = await item?.__itemType__
      const group = await item?.__itemGroup__
      const category = await item?.__itemCategory__
      const supplier = await item?.__supplier__
      const buyTax = await item.__buyTax__
      const sellTax = await item.__sellTax__
      item.brandName = brand?.name
      item.brandCode = brand?.code
      item.poUnitName = poUnit?.name
      item.poUnitCode = poUnit?.code
      item.unitName = unit?.name
      item.unitCode = unit?.code
      item.typeName = type?.name
      item.typeCode = type?.code
      item.groupName = group?.name
      item.groupCode = group?.code
      item.categoryName = category?.name
      item.categoryCode = category?.code
      item.supplierName = supplier?.name
      item.supplierCode = supplier?.code
      item.buyTaxName = buyTax?.name
      item.buyTaxCode = buyTax?.code
      item.sellTaxName = sellTax?.name
      item.sellTaxCode = sellTax?.code
      delete item.__buyTax__
      delete item.__sellTax__
      delete item.__itemCombo__
      delete item.__unit__
      delete item.__poUnit__
    }
    return result
  }
  async paginationItemSupplier(data: PaginationDto, req: IRequest) {
    let whereCon: any = {}
    if (data.where.canPreOrder != undefined) whereCon.canPreOrder = data.where.canPreOrder
    if (data.where.name) whereCon.name = ILike(`%${data.where.name}%`)
    if (data.where.code) whereCon.code = ILike(`%${data.where.code}%`)

    if (data.where.lstCode && data.where.lstCode.length > 0) {
      whereCon.code = In(data.where.lstCode)
    }
    if (data.where.lstId && data.where.lstId.length > 0) {
      whereCon.id = In(data.where.lstId)
    }

    if (data.where.isDeleted === false) whereCon.isDeleted = data.where.isDeleted
    if (data.where.isDeleted === true) whereCon.isDeleted = data.where.isDeleted
    if (data.where.isCombo === true) whereCon.isCombo = true
    if (data.where.isCombo === false) whereCon.isCombo = false
    if (data.where.isDeleted === undefined) {
      let list = [true, false]
      whereCon.isDeleted = In(list)
    }

    let result: any = await this.repo.findAndCount({
      where: { ...whereCon, orderPlatformType: ILike(`%${NSItem.EOrderPlatformType.AIPROCUREMENT}%`) },
      order: { createdAt: 'DESC' },
      skip: data.skip,
      take: data.take,
      relations: {
        itemCombo: { itemInCombo: { itemGroup: true } },
        unit: true,
        pOUnit: true,
        itemType: true,
        itemGroup: true,
        itemCategory: true,
        prices: true,
        supplier: true,
        buyTax: true,
        sellTax: true,
        brand: true,
      },
    })
    if (result[0].length == 0) return [[], 0]
    const dicMedia: any = {}
    {
      const lstMedia = await this.mediaRepo.find({ where: { productId: In(result[0].map((x) => x.id)) } })
      lstMedia.forEach((x) => (dicMedia[x.productId] = x))
    }

    for (let item of result[0]) {
      item.media = dicMedia[item.id]
      if (item.dateTo) {
        item.isCancelCombo = false
        if (new Date(new Date(item.dateTo).setHours(23, 59, 59, 99)).getTime() < new Date(new Date().setHours(23, 59, 59, 99)).getTime())
          item.isCancelCombo = true
      }
      let lstProductComno = await item.itemCombo
      if (lstProductComno.length > 0) {
        for (let item of lstProductComno) {
          let productObj = await item.itemInCombo
          item.productName = productObj.name
          item.productCode = productObj.code
        }
      }
      item.lstProductComno = lstProductComno
      const brand = await item?.__brand__
      const poUnit = await item?.__pOUnit__
      const unit = await item?.__unit__
      const type = await item?.__itemType__
      const group = await item?.__itemGroup__
      const category = await item?.__itemCategory__
      const supplier = await item?.__supplier__
      const buyTax = await item.__buyTax__
      const sellTax = await item.__sellTax__
      item.brandName = brand?.name
      item.brandCode = brand?.code
      item.poUnitName = poUnit?.name
      item.poUnitCode = poUnit?.code
      item.unitName = unit?.name
      item.unitCode = unit?.code
      item.typeName = type?.name
      item.typeCode = type?.code
      item.groupName = group?.name
      item.groupCode = group?.code
      item.categoryName = category?.name
      item.categoryCode = category?.code
      item.supplierName = supplier?.name
      item.supplierCode = supplier?.code
      item.buyTaxName = buyTax?.name
      item.buyTaxCode = buyTax?.code
      item.sellTaxName = sellTax?.name
      item.sellTaxCode = sellTax?.code
      delete item.__buyTax__
      delete item.__sellTax__
      delete item.__itemCombo__
      delete item.__unit__
      delete item.__poUnit__
    }
    return result
  }

  async paginationProduct(data: PaginationDto, req: IRequest) {
    let whereCon: any = {}
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)

    if (data.where.lstCode && data.where.lstCode.length > 0) {
      whereCon.code = In(data.where.lstCode)
    }
    if (data.where.lstId && data.where.lstId.length > 0) {
      whereCon.id = In(data.where.lstId)
    }

    if (data.where.isDeleted === false) whereCon.isDeleted = data.where.isDeleted
    if (data.where.isDeleted === true) whereCon.isDeleted = data.where.isDeleted
    whereCon.isCombo = false
    if (data.where.isDeleted === undefined) {
      let list = [true, false]
      whereCon.isDeleted = In(list)
    }

    let result: any = await this.repo.findAndCount({
      where: whereCon,
      order: { createdAt: 'DESC' },
      skip: data.skip,
      take: data.take,
      relations: {
        itemGroup: true,
        prices: true,
      },
    })
    if (result[0].length == 0) return [[], 0]

    for (let item of result[0]) {
      if (item.dateTo) {
        item.isCancelCombo = false
        if (new Date(new Date(item.dateTo).setHours(23, 59, 59, 99)).getTime() < new Date(new Date().setHours(23, 59, 59, 99)).getTime())
          item.isCancelCombo = true
      }
      item.groupName = await item?.__itemGroup__?.name

      delete item.__itemCombo__
      delete item.__unit__
    }
    return result
  }

  async updateIsDelete(id: string, req: IRequest, user?: UserDto) {
    const entity = await this.repo.findOne({ where: { id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    const isInCombo = await this.productComboRepo.find({ where: { itemInComboId: id } })

    let comboIsActive: any = await Promise.all(
      isInCombo.map(async (combo) => {
        let item = await this.repo.find({ where: { id: combo.itemId, isDeleted: false } })
        if (item[0] !== undefined) {
          return true
        }
        return false
      }),
    )

    if (isInCombo.length > 0 && comboIsActive.includes(true) && entity.isDeleted == false) {
      const itemIds = isInCombo.map((combo) => combo.itemId)

      const relatedProducts = await this.repo.findByIds(itemIds)

      const comboNames = relatedProducts
        .map((product) => {
          if (!product.isDeleted) {
            return product.name || product.id
          }
        })
        .join(', ')

      throw new Error(`Sản phẩm "${entity.name}" đang nằm trong combo ${comboNames}. Không thể ngưng hoạt động.`)
    }

    await this.repo.update(entity.id, { updatedBy: user?.id, isDeleted: !entity.isDeleted })
    const actionLogCreateDto: ActionLogCreateDto = {
      idHistory: entity.id,
      tableName: '',
      oldJson: null,
      newJson: null,
      type: enumData.ActionLogType.UPDATE_ACTIVE.code,
      description: `Nhân viên [ ${user.username} ] đã cập nhật trạng thái combo sản phẩm`,
    }
    await this.actionLogService.createData(user, actionLogCreateDto)
    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  async createDataExcel(data: ProductCreateExcelDto[], req: IRequest, user?: UserDto): Promise<any> {
    //console.log(data)

    const duplicateCodes = data.map((item) => item.code).filter((code, index, self) => self.indexOf(code) !== index)
    const today = new Date()
    if (duplicateCodes.length > 0) {
      throw new Error(`Mã sản phẩm bị trùng lặp trong danh sách: ${duplicateCodes.join(', ')}`)
    }
    let dictCategory: any = {}
    {
      const lstCategory: any = await this.itemCategoryRepository.find({ where: { isDeleted: false } })
      lstCategory.forEach((c) => (dictCategory[c.code] = c))
    }
    let dictGroup: any = {}
    {
      const lstGroup: any = await this.itemGroupRepository.find({ where: { isDeleted: false } })
      lstGroup.forEach((c) => (dictGroup[c.code] = c))
    }
    let dictType: any = {}
    {
      const lstType: any = await this.itemTypeRepository.find({ where: { isDeleted: false } })
      lstType.forEach((c) => (dictType[c.code] = c))
    }
    let dictSupplier: any = {}
    {
      const lstSupplier: any = await this.supplierRepo.find({ where: { isDeleted: false } })
      lstSupplier.forEach((c) => (dictSupplier[c.code] = c.id))
    }

    let dictBrand: any = {}
    {
      const lstBrand: any = await this.brandRepo.find({ where: { isDeleted: false } })
      lstBrand.forEach((c) => (dictBrand[c.code] = c))
    }

    let dictBuyTax: any = {}
    {
      const lstBuyTax: any = await this.taxRepo.find({ where: { type: enumData.TaxType.BUY.code, isDeleted: false } })
      lstBuyTax.forEach((c) => (dictBuyTax[c.code] = c))
    }

    let dictSellTax: any = {}
    {
      const lstSellTax: any = await this.taxRepo.find({ where: { type: enumData.TaxType.SELL.code, isDeleted: false } })
      lstSellTax.forEach((c) => (dictSellTax[c.code] = c))
    }

    let lstUnitDontExist = []
    let lstPoUnitDontExist = []
    let lstCategoryDontExist = []
    let lstGroupDontExist = []
    let lstTypeDontExist = []
    let lstSupplierDontExist = []
    let lstBrandDontExist = []
    let lstBuyTaxDontExist = []
    let lstSellTaxDontExist = []
    let lstOrderPlatformTypeDontExist = []
    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(ItemEntity)
      const unitRepo = trans.getRepository(UnitEntity)
      const dictProduct: any = {}
      {
        const lstProductCode = data.mapAndDistinct((x) => x.code)

        const lstProduct = await repo.find({ where: { code: In(lstProductCode) } })

        lstProduct.forEach((c) => (dictProduct[c.code] = c.id))
      }

      let dictUnit: any = {}
      {
        const lstUnit: any = await unitRepo.find({ where: { isDeleted: false } })
        lstUnit.forEach((c) => (dictUnit[c.code] = c))
      }

      let dictOrderPlatformType: any = {}
      {
        const lstOrderPlatformType: any = Object.entries(NSItem.EOrderPlatformType)
        lstOrderPlatformType.forEach((c) => (dictOrderPlatformType[c[0]] = c[1]))
      }

      for (const item of data) {
        if (!dictUnit[item.unitCode].id) lstUnitDontExist.push(item.unitCode)
        if (!dictUnit[item.poUnitCode].id) lstPoUnitDontExist.push(item.poUnitCode)
        for (const orderPlatformType of JSON.parse(item.orderPlatformType)) {
          if (!dictOrderPlatformType[orderPlatformType]) lstOrderPlatformTypeDontExist.push(orderPlatformType)
        }
        if (item.itemCategoryCode && !dictCategory[item.itemCategoryCode]) lstCategoryDontExist.push(item.itemCategoryCode)
        if (item.itemGroupCode && !dictGroup[item.itemGroupCode]) lstGroupDontExist.push(item.itemGroupCode)
        if (item.itemTypeCode && !dictType[item.itemTypeCode]) lstTypeDontExist.push(item.itemTypeCode)
        if (item.supplierCode && !dictSupplier[item.supplierCode]) lstSupplierDontExist.push(item.supplierCode)
        if (item.brandCode && !dictBrand[item.brandCode]) lstBrandDontExist.push(item.brandCode)
        if (item.buyTaxCode && !dictBuyTax[item.buyTaxCode]) lstBuyTaxDontExist.push(item.buyTaxCode)
        if (item.sellTaxCode && !dictSellTax[item.sellTaxCode]) lstSellTaxDontExist.push(item.sellTaxCode)
      }

      let lstTask: any = []
      const dicUrl: any = {}
      {
        data.forEach((x) => (dicUrl[x.code] = x.url))
      }
      for (let item of data) {
        if (!dictUnit[item.unitCode].id) {
          throw new Error(`Sản phẩm ${item.name} Mã ĐVCS ${item.unitCode} không tồn tại `)
        }
        if (!dictUnit[item.poUnitCode].id) {
          throw new Error(`Sản phẩm ${item.name} Mã đơn vị đặt hàng ${item.unitCode} không tồn tại `)
        }
        const type: Partial<ItemTypeEntity> = dictType[item.itemTypeCode]
        const category: Partial<ItemCategoryEntity> = dictCategory[item.itemCategoryCode]
        const group: Partial<ItemGroupEntity> = dictGroup[item.itemGroupCode]
        if (category && category.itemTypeId && category.itemTypeId !== type?.id) {
          throw new Error(`Sản phẩm ${item.name} Mã loại SP ${item.itemTypeCode} không tồn tại `)
        }
        if (!dictBrand[item.brandCode]) {
          throw new Error(`Sản phẩm ${item.name} Mã thương hiệu ${item.brandCode} không tồn tại `)
        }
        // Update nếu trùng code

        if (dictProduct[item.code]) {
          //Nếu cập nhật trạng thái sản phẩm, check xem sản phẩm có nằm trong combo khác không

          if (item.isDeleted !== undefined && !item.isDeleted == true) {
            let itemInCombo: any = await this.productComboRepo.find({ where: { itemInComboId: dictProduct[item.code] } })

            let comboIsActive: any = await Promise.all(
              itemInCombo.map(async (combo: any) => {
                let item = await this.repo.find({ where: { id: combo.itemId, isDeleted: false } })

                if (item[0] !== undefined) {
                  return true
                }
                return false
              }),
            )

            if (itemInCombo.length > 0 && comboIsActive.includes(true)) {
              const itemIds = itemInCombo.map((combo) => combo.itemId)
              const relatedProducts = await this.repo.findByIds(itemIds)

              const comboNames = relatedProducts
                .map((product) => {
                  if (!product.isDeleted) {
                    return product.name || product.id
                  }
                })
                .join(', ')
              throw new Error(`Sản phẩm "${item.name}" đang nằm trong combo ${comboNames}. Không thể ngưng hoạt động.`)
            }
          }
          const product: Partial<ItemEntity> = {
            id: dictProduct[item.code],
            ...(item.name && { name: item.name }),
            ...(item.description && { description: item.description }),
            ...(dictBrand[item.brandCode]?.id && { brandId: dictBrand[item.brandCode]?.id }),
            ...(dictUnit[item.unitCode]?.id && { unitId: dictUnit[item.unitCode].id }),
            ...(dictUnit[item.poUnitCode]?.id && { poUnitId: dictUnit[item.poUnitCode].id }),
            ...(category &&
              category.itemTypeId &&
              category.itemTypeId === type?.id &&
              dictCategory[item.itemCategoryCode]?.id && { itemCategoryId: dictCategory[item.itemCategoryCode]?.id }),
            ...(group &&
              group.itemCategoryId &&
              group.itemCategoryId === category?.id &&
              dictGroup[item.itemGroupCode]?.id && { itemGroupId: dictGroup[item.itemGroupCode].id }),
            ...(dictType[item.itemTypeCode]?.id && { itemTypeId: dictType[item.itemTypeCode]?.id }),
            ...(dictBuyTax[item.buyTaxCode]?.id && { buyTaxId: dictBuyTax[item.buyTaxCode]?.id }),
            ...(dictSellTax[item.sellTaxCode]?.id && { sellTaxId: dictSellTax[item.sellTaxCode]?.id }),
            ...(dictUnit[item.unitCode]?.baseUnit && { quantityUnit: dictUnit[item.unitCode].baseUnit }),
            ...(dictSupplier[item.supplierCode] && { supplierId: dictSupplier[item.supplierCode] }),
            ...(item.barCode && { barCode: item.barCode }),
            createdAt: today,
            createdBy: user?.id,
            ...(item.canPreOrder !== undefined && { canPreOrder: item.canPreOrder }),
            ...(item.isDeleted !== undefined && { isDeleted: !item.isDeleted }),
            ...(item.orderPlatformType && lstOrderPlatformTypeDontExist.length === 0 && { orderPlatformType: item.orderPlatformType }),
          }

          await this.repo.update(dictProduct[item.code], product)

          if (dicUrl[item.code]) {
            await this.mediaRepo.delete({ productId: product.id, table: enumData.MediaTable.Item.code })
            const newMedia = new MediaEntity()
            newMedia.url = dicUrl[item.code]
            newMedia.productId = product.id
            newMedia.table = enumData.MediaTable.Item.code
            newMedia.createdAt = new Date()
            newMedia.createdBy = user?.id
            await this.mediaRepo.insert(newMedia)
          }
          continue
        }

        const newProduct = repo.create({
          ...item,
          itemCategoryId: category && category.itemTypeId && category.itemTypeId === type?.id ? dictCategory[item.itemCategoryCode]?.id : null,
          itemGroupId: group && group.itemCategoryId && group.itemCategoryId === category?.id ? dictGroup[item.itemGroupCode].id : null,
          brandId: dictBrand[item.brandCode]?.id || null,
          itemTypeId: dictType[item.itemTypeCode].id,
          unitId: dictUnit[item.unitCode].id || null,
          poUnitId: dictUnit[item.poUnitCode].id || null,
          supplierId: dictSupplier[item.supplierCode] || null,
          buyTaxId: dictBuyTax[item.buyTaxCode]?.id || null,
          sellTaxId: dictSellTax[item.sellTaxCode]?.id || null,
          quantityUnit: dictUnit[item.unitCode].baseUnit || null,
          createdAt: new Date(),
          createdBy: user?.id,
          barCode: item.barCode,
          canPreOrder: item.canPreOrder,
          orderPlatformType: item.orderPlatformType,
          ...(item.isDeleted !== undefined && { isDeleted: !item.isDeleted }),
        })

        lstTask.push(newProduct)
      }

      await repo.insert(lstTask)
      for (let item of lstTask) {
        if (dicUrl[item.code]) {
          const newMedia = new MediaEntity()
          newMedia.url = dicUrl[item.code]
          newMedia.productId = item.id
          newMedia.table = enumData.MediaTable.Item.code
          newMedia.createdAt = new Date()
          newMedia.createdBy = user?.id
          await this.mediaRepo.insert(newMedia)
        }

        const actionLogCreateDto: ActionLogCreateDto = {
          idHistory: item.id,
          tableName: '',
          oldJson: null,
          newJson: JSON.stringify({ ...item }),
          type: enumData.ActionLogType.CREATE.code,
          description: `Nhân viên [ ${user.username} ] đã thêm mới combo sản phẩm`,
        }
        await this.actionLogService.createData(user, actionLogCreateDto)
      }
    })
    let result: any = {}
    if (lstUnitDontExist.length > 0) result.lstUnitDontExist = lstUnitDontExist
    if (lstCategoryDontExist.length > 0) result.lstCategoryDontExist = lstCategoryDontExist
    if (lstGroupDontExist.length > 0) result.lstGroupDontExist = lstGroupDontExist
    if (lstTypeDontExist.length > 0) result.lstTypeDontExist = lstTypeDontExist
    if (lstSupplierDontExist.length > 0) result.lstSupplierDontExist = lstSupplierDontExist
    if (lstBrandDontExist.length > 0) result.lstBrandDontExist = lstBrandDontExist
    if (lstBuyTaxDontExist.length > 0) result.lstBuyTaxDontExist = lstBuyTaxDontExist
    if (lstSellTaxDontExist.length > 0) result.lstSellTaxDontExist = lstSellTaxDontExist
    if (lstOrderPlatformTypeDontExist.length > 0) result.lstOrderPlatformTypeDontExist = lstOrderPlatformTypeDontExist
    result.message = CREATE_SUCCESS
    return result
  }

  async createDataComboExcel(data: { lstDataTable1: any[]; lstDataTable2: any[] }, user?: UserDto) {
    return this.repo.manager.transaction(async (manager) => {
      if (data.lstDataTable1 && data.lstDataTable1.length > 0) {
        const today = new Date()
        const comboCodes = data.lstDataTable1.map((x) => x.comboCode)
        const duplicateComboCodes = comboCodes.filter((code, index, self) => self.indexOf(code) !== index)
        if (duplicateComboCodes.length > 0) {
          throw new Error(`Mã combo bị trùng: ${duplicateComboCodes.join(', ')}`)
        }
        const existingCombos = await this.repo.find({ where: { code: In(comboCodes) } })
        let existingComboDict: any = {}
        if (existingCombos.length > 0) {
          existingCombos.forEach((combo) => (existingComboDict[combo.code] = combo))
          //throw new Error(`Mã combo đã tồn tại trong hệ thống: ${existingComboCodes.join(', ')}`)
        }

        let comboItemDict = {}
        {
          const comboItems = await this.productComboRepo.find({ where: { isDeleted: false } })
          comboItems.forEach((item) => {
            comboItemDict[`${item.itemId}-${item.itemInComboId}`] = item
          })
        }

        const listGroup = await this.itemGroupRepository.find({ where: { code: In(data.lstDataTable1.map((x) => x.groupCode)) } })
        const mapGroup = listGroup.convertToMap((x) => x.code)
        let dictBuyTax: any = {}
        {
          let buyTax = await this.taxRepo.find({ where: { isDeleted: false, type: enumData.TaxType.BUY.code } })
          buyTax.forEach((x) => (dictBuyTax[x.code] = x))
        }
        let dictSellTax: any = {}
        {
          let sellTax = await this.taxRepo.find({ where: { isDeleted: false, type: enumData.TaxType.SELL.code } })
          sellTax.forEach((x) => (dictSellTax[x.code] = x))
        }

        let dictItemType: any = {}
        {
          let itemType = await this.itemTypeRepository.find({ where: { isDeleted: false } })
          itemType.forEach((itemType: any) => {
            dictItemType[itemType.code] = itemType
          })
        }

        for (let item of data.lstDataTable1) {
          const combo = new ItemEntity()
          combo.name = item.comboName
          combo.code = item.comboCode
          combo.dateFrom = item.fromDate
          combo.dateTo = item.toDate
          combo.kg = item.kg
          combo.barCode = item.barCode
          combo.buyTaxId = dictBuyTax[item.buyTaxCode]?.id || null
          combo.sellTaxId = dictSellTax[item.sellTaxCode]?.id || null
          combo.itemGroupId = mapGroup.get(item.groupCode)?.id
          combo.itemTypeId = dictItemType[item.itemTypeCode]?.id
          let itemCategory = await this.itemCategoryRepository.findOne({
            where: { isDeleted: false, itemTypeId: combo.itemTypeId, code: item.itemCategoryCode },
          })
          if (itemCategory) {
            combo.itemCategoryId = itemCategory.id
          } else {
            throw new Error(`Không tìm thấy [Danh mục sản phẩm] [${item.itemCategoryCode}] trong [Loại sản phẩm] [${item.itemTypeCode}]`)
          }
          combo.description = item.description
          combo.isCombo = true
          combo.vat = item?.vat
          combo.createdBy = user?.id
          combo.createdAt = today
          let comboNew: any
          if (existingComboDict[item.comboCode]) {
            comboNew = existingComboDict[item.comboCode]
            await manager.getRepository(ItemEntity).update({ id: existingComboDict[item.comboCode].id }, combo)
          } else {
            comboNew = await manager.getRepository(ItemEntity).save(combo)
          }

          if (item.url !== null) {
            const newMedia = new MediaEntity()
            newMedia.url = item.url
            newMedia.table = enumData.MediaTable.Item.code
            newMedia.productId = comboNew.id
            newMedia.createdAt = new Date()
            newMedia.createdBy = user?.id
            await this.mediaRepo.insert(newMedia)
          }
          const listItem = data.lstDataTable2.filter((x) => x.comboListCode === item.comboCode)
          const listItemProduct = await this.repo.find({ where: { code: In(listItem.map((x) => x.itemCode)) } })
          const mapItemProduct = listItemProduct.convertToMap((x) => x.code)
          for (let item2 of listItem) {
            const itemCombo = new ItemComboEntity()
            const itemProduct = mapItemProduct.get(item2.itemCode)
            if (!itemProduct) throw new Error(`Không tìm thấy sản phẩm có mã [${item2.itemCode}]`)
            itemCombo.itemId = comboNew.id
            itemCombo.itemInComboId = itemProduct.id
            itemCombo.quantity = item2.quantity
            itemCombo.createdAt = today
            itemCombo.createdBy = user?.id
            let itemComboNew: any

            if (
              comboItemDict[`${itemCombo.itemId}-${itemCombo.itemInComboId}`] &&
              comboItemDict[`${itemCombo.itemId}-${itemCombo.itemInComboId}`].itemInComboId == itemCombo.itemInComboId
            ) {
              await manager
                .getRepository(ItemComboEntity)
                .update({ id: comboItemDict[`${itemCombo.itemId}-${itemCombo.itemInComboId}`].id }, itemCombo)
            } else {
              itemComboNew = await manager.getRepository(ItemComboEntity).save(itemCombo)
              //console.log(2, itemComboNew)
            }
          }
          const actionLogCreateDto: ActionLogCreateDto = {
            idHistory: comboNew.id,
            tableName: '',
            oldJson: null,
            newJson: JSON.stringify({ ...comboNew }),
            type: enumData.ActionLogType.CREATE.code,
            description: `Nhân viên [ ${user.username} ] đã thêm mới combo sản phẩm`,
          }
          await this.actionLogService.createData(user, actionLogCreateDto)
        }
        return { message: CREATE_SUCCESS }
      }
      return
    })
  }

  /** Tăng tồn kho của kho tổng */
  async increaseInventory(data: { lstDetail: any[] }, user: UserDto) {
    return await this.repo.manager.transaction('SERIALIZABLE', async (trans) => {
      const repo = trans.getRepository(ItemEntity)
      const detailRepo = trans.getRepository(ItemDetailEntity)
      const dicProduct: any = {}
      {
        const lstProductId = data.lstDetail.mapAndDistinct((x) => x.productId)
        const lstProduct = await repo.find({ where: { id: In(lstProductId), isDeleted: false } })
        lstProduct.forEach((c) => (dicProduct[c.id] = c))
      }
      for (const detail of data.lstDetail) {
        const product = dicProduct[detail.productId]
        if (!product) throw new Error(`Sản phẩm ${detail.productName} không còn tồn tại!`)
        const quantityOld = +product.quantity
        const quantityNew = quantityOld + +detail.totalQuantity
        await repo.update(product.id, { quantity: quantityNew, expiryDate: detail.expiryDate, createdAt: new Date(), createdBy: user?.id })
        // cập nhật cache
        product.quantity = quantityNew
        // lấy chi tiết sản phẩm trong kho từ cache nếu k có thì lấy từ db, nếu lại k có thì tạo mới
        const dateStr = moment(detail.expiryDate).format('YYYY-MM-DD')
        let productDetail = dicProduct[detail.productId + dateStr]
        if (!productDetail) {
          productDetail = await detailRepo.findOne({
            where: { itemId: detail.productId, expiryDate: Equal(detail.expiryDate) },
          })
        }
        if (!productDetail) {
          const productDetailNew = new ItemDetailEntity()
          productDetailNew.itemId = detail.productId
          productDetailNew.quantity = 0
          productDetailNew.manufactureDate = detail.manufactureDate
          productDetailNew.expiryDate = detail.expiryDate
          productDetailNew.createdAt = new Date()
          productDetailNew.createdBy = user?.id
          productDetailNew.id = uuidv4()
          // lưu db
          await detailRepo.insert(productDetailNew)
          productDetail = productDetailNew
        }
        // lưu cache
        dicProduct[detail.productId + dateStr] = productDetail
        const quantityDetailNew = +productDetail.quantity + +detail.totalQuantity
        // cập nhật cache
        productDetail.quantity = quantityDetailNew
        await detailRepo.update(productDetail.id, { quantity: quantityDetailNew })
      }
      return { message: UPDATE_SUCCESS }
    })
  }

  async updateLock(data: { lstDetail: any[] }, req: IRequest, user?: UserDto) {
    return await this.repo.manager.transaction('READ COMMITTED', async (trans) => {
      const repo = trans.getRepository(ItemEntity)
      const detailRepo = trans.getRepository(ItemDetailEntity)

      const dicProduct: any = {}
      {
        const lstProductId = data.lstDetail.mapAndDistinct((x) => x.productId)
        const lstProduct = await repo.find({ where: { id: In(lstProductId), isDeleted: false } })
        lstProduct.forEach((c) => (dicProduct[c.id] = c))
      }

      for (const detail of data.lstDetail) {
        const product = dicProduct[detail.productId]
        if (!product) throw new Error(`Sản phẩm ${detail.productName} không còn tồn tại!`)
        const pdDetail = await detailRepo.findOne({
          where: { id: detail.productDetailId, isDeleted: false },
        })
        if (!pdDetail)
          throw new Error(
            `Sản phẩm ${detail.productName} có hạn sử dụng ${moment(detail.expiryDate).format(
              'YYYY-MM-DD',
            )} không còn tồn tại.  Vui lòng kiểm tra lại `,
          )
        if (detail.quantity > pdDetail.quantityLockEmp)
          throw new Error(`Không thể phân kho cho phiếu này bởi số lượng yêu cầu vượt quá số lượng có thể phân kho. Vui lòng kiểm tra lại`)
        const quantityOld = +product.quantityLockEmp
        const quantityNew = quantityOld - +detail.quantity
        product.quantityLockEmp = quantityNew
        await repo.update(product.id, { quantityLockEmp: quantityNew, createdAt: new Date(), createdBy: user?.id })
        const quantityDetailNew = +pdDetail.quantityLockEmp - +detail.quantity
        pdDetail.quantityLockEmp = quantityDetailNew
        await detailRepo.update(pdDetail.id, { quantityLockEmp: quantityDetailNew })
      }

      return { message: UPDATE_SUCCESS }
    })
  }

  async paginationWarehouse(data: PaginationDto, req: IRequest, userlogin?: UserDto) {
    let whereCon: any = {}
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.brandId) whereCon.brandId = Like(`%${data.where.brandId}%`)
    if (data.where.expiryDate && data.where.expiryDate.length > 0) {
      whereCon.expiryDate = Raw(
        (alias) =>
          `DATE(${alias}) BETWEEN DATE("${moment(data.where.expiryDate[0]).format('YYYY-MM-DD')}") AND DATE("${moment(
            data.where.expiryDate[1],
          ).format('YYYY-MM-DD')}")`,
      )
    }
    whereCon.isDeleted = false
    let result: any = await this.repo.findAndCount({
      where: whereCon,
      order: { details: { expiryDate: 'DESC' } },
      skip: data.skip,
      take: data.take,
      relations: { details: true },
    })
    if (result[0].length == 0) return [[], 0]
    const dictBrand: any = {}
    {
      const lstId = result[0].mapAndDistinct((x) => x.brandId)
      const lstBrand: any = await this.brandRepo.find({ where: { isDeleted: false, id: In(lstId) } })
      lstBrand.forEach((c) => (dictBrand[c.id] = c))
    }
    const dictPrice: any = {}
    {
      const lstId = result[0].mapAndDistinct((x) => x.id)
      const lstPrice: any = await this.productPriceRepo.find({
        where: { itemId: In(lstId), isDeleted: false, isFinal: true },
        order: { createdAt: 'DESC' },
      })
      for (const item of lstPrice) dictPrice[item.productId] = item
    }
    const lstId = result[0].mapAndDistinct((x) => x.id)
    // const lstDonDangSoan: any = await authApiHelper.findListOrder(req, { lstProductId: lstId, status: enumData.OrderStatus.DonDangSoan.code })
    // const lstDonLenDon: any = await authApiHelper.findListOrder(req, { lstProductId: lstId, isDaLenDon: true })
    for (let item of result[0]) {
      if (item.brandId) item.brandName = dictBrand[item.brandId].name
      item.priceSell = dictPrice[item.id]?.priceSell
      item.expand = false
      item.lstDetail = await item.__details__
      // for (let dt of await item.__details__) {
      //   const quantityDaLenDon = lstDonLenDon
      //     .filter((x: any) => x.productId === dt.productId && x.productDetailId === dt.id)
      //     .reduce((sum, current) => sum + Number(current.quantity), 0)
      //   dt.quantityDaLenDon = quantityDaLenDon || 0
      //   const quantityTotalDangSoan = lstDonDangSoan
      //     .filter((x: any) => x.productId === dt.productId && x.productDetailId === dt.id)
      //     .reduce((sum, current) => sum + Number(current.quantity), 0)
      //   dt.quantityDangSoan = quantityTotalDangSoan || 0
      // }
      item.quantityDaLenDon = await item.__details__.reduce((sum, current) => sum + Number(current.quantityDaLenDon), 0)
      item.quantityDangSoan = await item.__details__.reduce((sum, current) => sum + Number(current.quantityDangSoan), 0)
      delete item.__details__
    }
    return result
  }

  async findProductDetail(data: FilterIdDto) {
    const res: any = await this.productDetailRepo.findOne({
      where: { id: data.producDetailId, itemId: data.productId, isDeleted: false },
      relations: { item: true },
    })
    if (!res) throw new Error('Sản phẩm và hạn sử dụng không còn tại. Vui lòng kiểm tra lại')
    const productObj = await res?.__item__
    res.quantityProduct = productObj.quantity
    res.quantityProductLock = productObj.quantityLock
    delete res?.__item__
    return res
  }

  async paginationProductPrice(data: PaginationDto, req: IRequest, userlogin?: UserDto) {
    let whereCon: any = {}
    if (data.where.name) whereCon.name = ILike(`%${data.where.name}%`)
    if (data.where.code) whereCon.code = ILike(`%${data.where.code}%`)
    if (data.where.brandId) whereCon.brandId = data.where.brandId
    if (data.where.isDeleted === false) whereCon.isDeleted = data.where.isDeleted
    if (data.where.isDeleted === true) whereCon.isDeleted = data.where.isDeleted
    if (data.where.productId) whereCon.id = data.where.productId
    if (data.where.isDeleted == undefined) {
      let list = [true, false]
      whereCon.isDeleted = In(list)
    }
    if (data.where.expiryDate && data.where.expiryDate.length > 0) {
      whereCon.expiryDate = Raw(
        (alias) =>
          `DATE(${alias}) BETWEEN DATE("${moment(data.where.expiryDate[0]).format('YYYY-MM-DD')}") AND DATE("${moment(
            data.where.expiryDate[1],
          ).format('YYYY-MM-DD')}")`,
      )
    }

    let result: any = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      relations: { prices: true },
      order: { createdAt: 'DESC' },
    })

    if (result[0].length == 0) return [[], 0]

    const dictBrand: any = {}
    {
      const lstId = result[0].mapAndDistinct((x) => x.brandId)
      const lstBrand: any = await this.brandRepo.find({ where: { isDeleted: false, id: In(lstId) } })
      lstBrand.forEach((c) => (dictBrand[c.id] = c))
    }

    const dictPrice: any = {}
    {
      const lstId = result[0].mapAndDistinct((x) => x.id)
      const lstPrice: any = await this.productPriceRepo.find({
        where: { itemId: In(lstId), isDeleted: false, isFinal: true },
        order: { createdAt: 'DESC' },
      })
      for (const item of lstPrice) dictPrice[item.itemId] = item
    }

    const dictInbound: any = {}
    {
      const lstId = result[0].mapAndDistinct((x) => x.id)
      const lstInboundDetail: any = await this.inboundDetailRepo.find({
        where: { productId: In(lstId), isDeleted: false },
      })
      if (lstInboundDetail.length > 0) {
        const lstInoundId = lstInboundDetail.map((x) => x.inboundId)
        const lstInbound = await this.inboundRepo.find({ where: { id: In(lstInoundId) } })
        const lstIndId = lstInbound.map((x) => x.id)
        if (lstIndId.length > 0) {
          const listInboundDetail: any = await this.inboundDetailRepo.find({
            where: { productId: In(lstId), isDeleted: false, inboundId: In(lstIndId) },
          })
          for (const item of listInboundDetail) dictInbound[item.productId] = item
        }
      }
    }

    for (let item of result[0]) {
      let pdPrice = await item?.__prices__
      let itemPrice = pdPrice.find((x) => x.itemId === item.id && x.isFinal === true)

      if (itemPrice) {
        item.priceCapital = itemPrice?.priceCapital || 0
        item.priceInput = itemPrice?.priceInput || 0
        item.priceSell = itemPrice?.priceSell || 0
        item.priceOriginal = itemPrice?.priceOriginal || 0
      } else {
        item.priceCapital = dictInbound[item.id]?.costPrice || 0
        item.priceInput = dictInbound[item.id]?.priceVND || 0
        item.priceSell = dictPrice[item.id]?.priceSell || 0
        item.priceOriginal = dictPrice[item.id]?.priceOriginal || 0
      }

      if (item.brandId) item.brandName = dictBrand[item.brandId]?.name

      item.expand = false
      delete item.__prices__
    }

    return result
  }

  async findProductDetailExpirydate(data: FilterIdDto) {
    let whereCon: any = { isDeleted: false }
    if (data.lstId?.length > 0) whereCon.itemId = In(data.lstId)
    if (data.productId) whereCon.itemId = data.productId
    if (data.id) whereCon.id = data.id
    const res: any = await this.productDetailRepo.find({
      where: whereCon,
      relations: { item: true },
      order: { expiryDate: 'DESC' },
    })
    if (res.length === 0) return []
    for (let item of res) {
      const productObj = await item?.__item__
      item.quantityProduct = productObj.quantity
      item.quantityProductLock = productObj.quantityLock
      item.expiryDateString = moment(item.expiryDate).format('DD-MM-YYYY')
      item.manufactureDateString = moment(item.manufactureDate).format('DD-MM-YYYY')
      delete item?.__item__
    }

    return res
  }

  async findListProductCombo(data: FilterIdDto) {
    let whereCon: any = { isDeleted: false }
    if (data.productId) whereCon.itemId = data.productId
    let res: any = await this.productComboRepo.find({ where: whereCon, relations: { itemInCombo: { unit: true, details: true } } })
    if (res.length === 0) return []

    const lstProductInComboId: string[] = res.map((e) => e.itemInComboId)
    const lstProduct: any[] = await this.repo.find({ where: { id: In(lstProductInComboId), isDeleted: false }, relations: { unit: true } })
    for (let item of lstProduct) {
      item.unitName = item.__unit__?.name
      item.unitCode = item.__unit__?.code
      delete item.__unit__
    }
    return lstProduct
  }

  async updateActiveList(data: ProductUpdateIsActiveListDto, req: IRequest, user?: UserDto) {
    await this.repo.manager.transaction('READ COMMITTED', async (trans) => {
      const repo = trans.getRepository(ItemEntity)
      const inboundDetailRepo = trans.getRepository(InboundDetailEntity)
      const outboundDetailRepo = trans.getRepository(OutboundDetailEntity)

      let whereCon: any = {}
      whereCon.inbound = {}
      whereCon.inbound.status = enumData.InboundStatus.New.code
      whereCon.productId = In(data.listId)

      const checkInbond = await inboundDetailRepo.find({ where: whereCon })
      if (checkInbond && checkInbond.length > 0) throw new Error(`Sản phẩm tồn tại trong phiếu nhập kho có trạng thái mới tạo, vui lòng kiểm tra lại`)

      let whereCon1: any = {}
      whereCon1.outbound = {}
      whereCon1.outbound.status = enumData.OutboundStatus.NEW.code
      whereCon1.productId = In(data.listId)

      const checkOutbound = await outboundDetailRepo.find({ where: whereCon1 })
      if (checkOutbound && checkOutbound.length > 0)
        throw new Error(`Sản phẩm tồn tại trong phiếu xuất kho có trạng thái mới tạo, vui lòng kiểm tra lại`)

      for (let item of data.listId) {
        const entity = await repo.findOne({ where: { id: item } })
        if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

        await repo.update({ id: item }, { isDeleted: !entity.isDeleted, updatedBy: user?.id })
      }
    })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  async paginationInventory(data: PaginationDto, req: IRequest, userlogin?: UserDto) {
    let whereCon: any = { isDeleted: false }
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.brandId) whereCon.brandId = data.where.brandId
    if (data.where.isCombo != undefined) whereCon.isCombo = data.where.isCombo
    if (data.where.productId) whereCon.id = data.where.productId
    if (data.where.expiryDate && data.where.expiryDate.length > 0) {
      whereCon.expiryDate = Raw(
        (alias) =>
          `DATE(${alias}) BETWEEN DATE("${moment(data.where.expiryDate[0]).format('YYYY-MM-DD')}") AND DATE("${moment(
            data.where.expiryDate[1],
          ).format('YYYY-MM-DD')}")`,
      )
    }

    if (data.where.manufactureDate && data.where.manufactureDate.length > 0) {
      const lstDetail = await this.productDetailRepo.find({
        where: {
          manufactureDate: Raw(
            (alias) =>
              `DATE(${alias}) BETWEEN DATE("${moment(data.where.manufactureDate[0]).format('YYYY-MM-DD')}") AND DATE("${moment(
                data.where.manufactureDate[1],
              ).format('YYYY-MM-DD')}")`,
          ),
        },
      })
      if (lstDetail.length === 0) return [[], 0]
      const lstdId = lstDetail.map((x) => x.itemId)
      whereCon.id = In(lstdId)
    }

    whereCon.details = {}

    if (data.where.lotNumber) {
      whereCon.details.lotNumber = Like(`%${data.where.lotNumber}%`)
      // const lstDetail = await this.productDetailRepo.find({ where: { lotNumber: Like(`%${data.where.lotNumber}%`) } })
      // if (lstDetail.length === 0) return [[], 0]

      // const lstProductId = coreHelper.selectDistinct(lstDetail, 'productId')
      // whereCon.id = In(lstProductId)
    }

    if (data.where.warehouseId) {
      const lstDetail = await this.warehouseProductRepo.find({ where: { warehouseId: data.where.warehouseId, isDeleted: false } })
      if (lstDetail.length === 0) return [[], 0]
      const lstdId = lstDetail.map((x) => x.productId)
      whereCon.id = In(lstdId)
    }

    if (data.where.storeId) {
      const lstProductIds = []
      const whs = await this.warehouseRepo.find({ where: { storeId: data.where.storeId } })
      for (const wh of whs) {
        const lstDetail = await this.warehouseProductRepo.find({ where: { warehouseId: wh.id, isDeleted: false } })
        const lstdId = lstDetail.map((x) => x.productId)
        lstProductIds.push(...lstdId)
      }
      whereCon.id = In(Array.from(new Set(lstProductIds)))
    }

    whereCon.quantity = MoreThan(0)
    whereCon.details.quantity = MoreThan(0)

    let result: any = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      relations: { details: true },
      order: { expiryDate: 'DESC' },
    })
    if (result[0].length == 0) return [[], 0]

    const dictBrand: any = {}
    {
      // const lstBrand: any = await authApiHelper.findListBrand(req, { isRelation: true })
      // lstBrand.forEach((c) => (dictBrand[c.id] = c))
    }
    const lstId = coreHelper.selectDistinct(result[0], 'id')
    // const lstDonDangSoan: any = await authApiHelper.findListOrder(req, { lstProductId: lstId, status: enumData.OrderStatus.DonDangSoan.code })
    // const lstDonLenDon: any = await authApiHelper.findListOrder(req, { lstProductId: lstId, isDaLenDon: true })
    let date = Date.now()
    let days = new Date()
    days.setDate(days.getDate() + 7)

    for (let item of result[0]) {
      const lstDetail = await item.__details__
      let lstArrDetail: any = []
      if (item.brandId) item.brandName = dictBrand[item.brandId]?.name
      for (let dt of await item.__details__) {
        // const quantityTotalDangSoan = lstDonDangSoan
        //   .filter((x: any) => x.productId === dt.productId && x.productDetailId === dt.id)
        //   .reduce((sum, current) => sum + Number(current.quantity), 0)
        // dt.quantityDangSoan = quantityTotalDangSoan || 0

        // const quantityDaLenDon = lstDonLenDon
        //   .filter((x: any) => x.productId === dt.productId && x.productDetailId === dt.id)
        //   .reduce((sum, current) => sum + Number(current.quantity), 0)
        // dt.quantityDaLenDon = quantityDaLenDon || 0

        let dateHienTai = moment(new Date()).format('YYYY-MM-DD')
        let datehetHan = moment(dt.expiryDate).format('YYYY-MM-DD')
        let dateSanXuat = moment(dt.manufactureDate).format('YYYY-MM-DD')
        let rsDateHetHan = new Date(datehetHan)
        let rsDateSanXuat = new Date(dateSanXuat)
        let rsDateHienTai = new Date(dateHienTai)
        let TimeSanXuatHetHan = rsDateHetHan.getTime() - rsDateSanXuat.getTime()
        let totalTimeSanXuatHetHan = TimeSanXuatHetHan / (1000 * 3600 * 24)
        let DateHienTaiCanTim = rsDateHienTai.getTime() - rsDateSanXuat.getTime()
        let TimeCanTim = DateHienTaiCanTim / (1000 * 3600 * 24)
        let c = totalTimeSanXuatHetHan - TimeCanTim
        let phamTram = (c / totalTimeSanXuatHetHan) * 100
        dt.phamTram = phamTram
        if (dt.phamTram <= 50) {
          dt.colorButton = 'red'
        }

        lstArrDetail.push({
          quantity: dt.quantity,
          expiryDate: dt.expiryDate,
          manufactureDate: dt.manufactureDate,
          lotNumber: dt.lotNumber,
          phamTram: dt.phamTram,
          colorButton: dt.colorButton,
          quantityLockEmp: dt.quantityLockEmp,
        })
      }
      item.lstDetailArr = lstArrDetail

      item.quantityDangSoan = await item.__details__.reduce((sum, current) => sum + Number(current.quantityDangSoan), 0)
      item.quantityDaLenDon = await item.__details__.reduce((sum, current) => sum + Number(current.quantityDaLenDon), 0)

      if (lstDetail && lstDetail.length > 0) {
        const group = coreHelper.groupByArray(lstDetail, 'productId')
        if (
          moment(item.expiryDate).format('YYYY-MM-DD') >= moment(date).format('YYYY-MM-DD') ||
          moment(item.expiryDate).format('YYYY-MM-DD') <= moment(days).format('YYYY-MM-DD')
        ) {
          for (let itemGroup of group) {
            for (let x of itemGroup.list) {
              if (x.quantity > 0) {
                if (item.id === itemGroup.heading) {
                  let dateEx = moment(x.expiryDate).format('YYYY-MM-DD')
                  let dateCheck = moment(date).format('YYYY-MM-DD')
                  let dateCheck1 = moment(days).format('YYYY-MM-DD')
                  if (dateEx < dateCheck) {
                    item.isWarning = true
                  }
                  if (dateCheck < dateEx && dateCheck1 > dateEx) {
                    item.isWarning = true
                  }
                }
              }
            }
          }
        }
      }

      delete item.__details__
    }

    return result
  }

  async findDetailExpiryDate(id: string, req: IRequest) {
    const productObj: any = await this.repo.findOne({
      where: { id: id, isDeleted: false },
      relations: { details: true },
      order: { details: { expiryDate: 'DESC' } },
    })
    if (!productObj) {
      throw new Error(ERROR_NOT_FOUND_DATA)
    }
    let lstDetail = await productObj.__details__
    productObj.__inboundDetails__ = this.inboundDetailRepo.find({ where: { productId: productObj.id }, relations: { inbound: true } })
    productObj.__outboundDetails__ = this.outboundDetailRepo.find({ where: { productId: productObj.id }, relations: { outbound: true } })

    for (let item of lstDetail) {
      let dateHienTai = moment(new Date()).format('YYYY-MM-DD')
      let datehetHan = moment(item.expiryDate).format('YYYY-MM-DD')
      let dateSanXuat = moment(item.manufactureDate).format('YYYY-MM-DD')
      let rsDateHetHan = new Date(datehetHan)
      let rsDateSanXuat = new Date(dateSanXuat)
      let rsDateHienTai = new Date(dateHienTai)
      let TimeSanXuatHetHan = rsDateHetHan.getTime() - rsDateSanXuat.getTime()
      let totalTimeSanXuatHetHan = TimeSanXuatHetHan / (1000 * 3600 * 24)
      let DateHienTaiCanTim = rsDateHienTai.getTime() - rsDateSanXuat.getTime()
      let TimeCanTim = DateHienTaiCanTim / (1000 * 3600 * 24)
      let c = totalTimeSanXuatHetHan - TimeCanTim
      let phamTram = (c / totalTimeSanXuatHetHan) * 100
      item.phamTram = phamTram
    }
    productObj.lstDetail = lstDetail
    productObj.lstManufactureDate = lstDetail.filter((x) => x.manufactureDate !== null).map((x) => x.manufactureDate)
    productObj.lstExpiryDate = lstDetail.filter((x) => x.expiryDate !== null).map((x) => x.expiryDate)
    productObj.lstLotNumber = lstDetail?.filter((x) => x.lotNumber !== null).map((x) => x.lotNumber)
    productObj.lstInbound = await productObj.__inboundDetails__

    for (let item of productObj.lstInbound) {
      item.inboundCode = await item.__inbound__?.code
      item.inboundStatus = await item.__inbound__?.status
      delete item.__inbound__
    }

    productObj.lstOutbound = await productObj.__outboundDetails__

    for (let item of productObj.lstOutbound) {
      item.outboundCode = await item.__outbound__?.code
      item.outboundStatus = await item.__outbound__?.status
      delete item.__outbound__
    }

    delete productObj.__inboundDetails__
    delete productObj.__outboundDetails__
    delete productObj.__details__

    return productObj
  }

  /** Gán item là favorite combo */
  async assignFavoriteCombo(id: string, isFavoriteCombo: boolean, user?: UserDto) {
    const actionLogCreateDto: ActionLogCreateDto = {
      idHistory: id,
      tableName: '',
      oldJson: null,
      newJson: null,
      type: enumData.ActionLogType.CREATE.code,
      description: `Nhân viên [ ${user.username} ] đã cập nhật danh sách yêu thích combo sản phẩm`,
    }
    await this.actionLogService.createData(user, actionLogCreateDto)
    return this.repo.update({ id }, { isFavoriteCombo })
  }

  async listComboProductsInWarehouse(data: FilterIdDto) {
    if (!data.productId || !data.warehouseId) {
      throw new Error('Product ID & Warehouse ID is required')
    }
    let res: any = await this.productComboRepo.find({ where: { itemId: data.productId } })
    const dataItem = await this.repo.find({ where: { isDeleted: false } })

    const lstProductInComboId: string[] = res.map((e) => e.itemInComboId)
    const lstProductWarehouse = await this.warehouseProductDetailRepository.find({
      where: {
        productId: In(lstProductInComboId),
        warehouseId: data.warehouseId,
      },
    })

    const dataProductWarehouse = lstProductWarehouse.map((item) => {
      const product = dataItem.find((val) => val.id === item.productId)
      return {
        ...item,
        productName: product?.name,
        productCode: product?.code,
      }
    })
    return dataProductWarehouse
  }
}
