import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator'

export class EmployeeUpdateDto {
  @ApiProperty({ description: 'Id nhân viên' })
  @IsNotEmpty({ message: 'Id không được trống' })
  @IsUUID()
  id: string

  @ApiProperty({ description: 'Mã nhân viên' })
  @IsNotEmpty({ message: 'Mã nhân viên không được trống' })
  code: string

  @ApiProperty({ description: 'Họ và tên nhân viên' })
  @IsNotEmpty({ message: 'Họ tên không được trống' })
  name: string

  @ApiProperty({ description: 'Email' })
  @IsNotEmpty({ message: 'Email không được trống' })
  email: string

  @ApiProperty({ description: 'Id phòng ban' })
  @IsNotEmpty({ message: '<PERSON>òng ban không được trống' })
  departmentId: string

  @ApiProperty({ description: 'Mô tả' })
  @IsOptional()
  description: string

  @ApiProperty({ description: 'Số điện thoại' })
  @IsNotEmpty({ message: 'Số điện thoại không được trống' })
  @IsString()
  phone: string

  @ApiPropertyOptional({ description: 'Địa chỉ' })
  @IsOptional()
  address?: string

  @ApiPropertyOptional({ description: 'Tĩnh/Thành phố' })
  @IsOptional()
  cityId?: string;

  @ApiPropertyOptional({ description: 'Phường/Xã' })
  @IsOptional()
  wardId?: string;

  @ApiPropertyOptional({ description: 'Quận/Huyện' })
  @IsOptional()
  districtId?: string;

  @ApiPropertyOptional({ description: 'Mã người update' })
  @IsOptional()
  updatedBy?: string

  @ApiPropertyOptional({ description: 'Quyền quản trị viên' })
  @IsOptional()
  isAdmin?: boolean
}
