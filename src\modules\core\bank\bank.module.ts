import { Module } from "@nestjs/common";
import { TypeOrmExModule } from "../../../typeorm";
import { BankController } from "./bank.controller";
import { BankService } from "./bank.service";
import { BankRepository, BankBranchRepository } from "../../../repositories";
import { BankPublicController } from "./bankPublic.controller";

@Module({
  imports: [TypeOrmExModule.forCustomRepository([
    BankRepository,
    BankBranchRepository,
  ])],
  controllers: [BankController, BankPublicController],
  providers: [BankService],
  exports: [BankService]
})
export class BankModule { }