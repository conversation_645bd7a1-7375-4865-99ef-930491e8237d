import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator'
import { PageResponse, PaginationDto } from '../../../../dto'

export class ProductDto extends PaginationDto {
  @ApiProperty({ description: 'Product ID' })
  @IsNotEmpty()
  @IsString()
  productId: string

  @ApiProperty({ description: 'ID nhóm SP' })
  @IsOptional()
  @IsString()
  itemGroupId: string
}
