import { Body, Controller, Post, Req, UseGuards } from "@nestjs/common";
import { ApiOperation, ApiTags } from "@nestjs/swagger";
import { IntegrationService } from "../services/integration.service";
import { DetailPurchaseOrderDto, ListPoReq } from "../../core/purchaseOrder/dto";
import { PaginationDto } from "../../../dto";
import { Request as IRequest } from 'express'
import { CardSalesHistoryPartnerSearchReq, ListCardReq, ListIdReq, ListPartnerReq, UUIDReq } from "../dto/integration.dto";
import { VerifyTokenGuard } from "../../common/guards/verify-token.guard";
import { omsApiHelper } from "../../../helpers/omsApiHelper";
import { JwtAuthGuard } from "../../common";

@ApiTags('Integration')
@UseGuards(JwtAuthGuard)
@Controller('api/integration')
export class IntegrationController {
  constructor(
    private readonly service: IntegrationService,
  ) { }

  // @ApiOperation({ summary: '<PERSON><PERSON>y danh sách PO' })
  // @Post('/list/po')
  // public async purchaseOrderList(@Body() params: ListPoReq) {
  //   return this.service.listPurchaseOrder(params)
  // }

  // @ApiOperation({ summary: 'Xem chi tiết PO' })
  // @Post('/detail/po')
  // public async purchaseOrderDetail(@Body() body: DetailPurchaseOrderDto) {
  //   return this.service.detailPurchaseOrder(body)
  // }

  // @ApiOperation({ summary: 'Lấy danh sách SO' })
  // @Post('/list/so')
  // public async saleOrderList(@Req() req: IRequest, @Body() params: PaginationDto) {
  //   return this.service.listSaleOrder(req, params)
  // }

  // @ApiOperation({ summary: 'Xem chi tiết SO' })
  // @Post('/detail/so')
  // public async saleOrderDetail(@Req() req: IRequest, @Body() id: string) {
  //   return this.service.detailSaleOrder(req, id)
  // }

  // @ApiOperation({ summary: 'Lấy danh sách thẻ' })
  // @Post('/list/card')
  // public async listCard(@Req() req: IRequest, @Body() params: ListCardReq) {
  //   return this.service.listCard(req, params)
  // }

  // @ApiOperation({ summary: 'Lấy danh sách điểm giao hàng' })
  // @Post('/list/partner')
  // public async listPartner(@Req() req: IRequest, @Body() params: ListPartnerReq) {
  //   return this.service.listPartner(req, params)
  // }

  // @ApiOperation({ summary: 'Lấy danh sách thẻ đã bán' })
  // @Post('/history/card')
  // public async cardSalesHistory(@Req() req: IRequest, @Body() params: CardSalesHistoryPartnerSearchReq) {
  //   return this.service.cardSalesHistory(req, params)
  // }

  // @ApiOperation({ summary: 'Cập nhật trạng thái đơn hàng' })
  // @Post('/update/so-status')
  // public async updateSOStatus(@Req() req: IRequest, @Body() data: ListIdReq) {
  //   return this.service.updateSOStatus(req, data)
  // }

  //#region PAYMENT
  @Post('payments/transaction/re-activate')
  public async reActivateTransaction(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.reActivateTransaction(req, data)
  }
  //#endregion
}
