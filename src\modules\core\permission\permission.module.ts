import { Module } from '@nestjs/common'
import { PermissionService } from './permission.service'
import { PermissionController } from './permission.controller'
import { TypeOrmExModule } from '../../../typeorm'
import { PermissionRepository } from '../../../repositories/core/permission.repository'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([PermissionRepository])],
  providers: [PermissionService],
  controllers: [PermissionController],
})
export class PermissionModule {}
