import { <PERSON><PERSON><PERSON>, Column, ManyTo<PERSON>ne, <PERSON>in<PERSON><PERSON><PERSON><PERSON>, OneToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { DepartmentEntity } from './department.entity'
import { UserEntity } from './user.entity'
@Entity('employee')
export class EmployeeEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
    unique: true,
  })
  code: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  name: string

  @Column({
    type: 'timestamptz',
    nullable: true,
  })
  birthday: Date

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  phone: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  email: string

  @Column({
    type: 'text',
    nullable: true,
  })
  description: string

  @Column({
    type: 'varchar',
    nullable: false,
  })
  departmentId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  userId: string

  /** Là nhân viên trưởng nhóm */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  managerId: string

  /** có quyền vào app Mobile */
  @Column({
    nullable: true,
    default: false,
  })
  isMobile: boolean

  @Column({
    type: 'decimal',
    precision: 20,
    scale: 10,
    nullable: true,
  })
  latitude: number

  @Column({
    type: 'decimal',
    precision: 20,
    scale: 10,
    nullable: true,
  })
  longitude: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  address: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  cityId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  wardId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  districtId: string

  @ManyToOne(() => DepartmentEntity, (p) => p.employees)
  @JoinColumn({ name: 'departmentId', referencedColumnName: 'id' })
  department: Promise<DepartmentEntity>

  @OneToOne((type) => UserEntity, (p) => p.employee)
  @JoinColumn({ name: 'userId', referencedColumnName: 'id' })
  user: Promise<UserEntity>

  // @OneToMany((type) => PayslipEntity, (p) => p.employee)
  // payslips: Promise<PayslipEntity[]>

  // @OneToMany((type) => EmpDebtEntity, (p) => p.employee)
  // debts: Promise<EmpDebtEntity[]>

  // @OneToMany((type) => OrderEntity, (p) => p.employeeShipper)
  // orders: Promise<OrderEntity[]>

  // @OneToMany((type) => OrderReturnEntity, (p) => p.employee)
  // orderReturns: Promise<OrderReturnEntity[]>

  // @OneToMany((type) => PayslipShipperEntity, (p) => p.employee)
  // payslipShippers: Promise<PayslipShipperEntity[]>

  // @OneToMany((type) => EmpBrandEntity, (p) => p.employee)
  // empBrands: Promise<EmpBrandEntity[]>

  // @OneToMany((type) => WorkManagementEntity, (p) => p.employeeWorks)
  // employees: Promise<WorkManagementEntity[]>

  // @OneToMany(() => PoEntity, (p) => p.employee)
  // pos: Promise<PoEntity[]>

  // @OneToMany(() => OrderDocumentEntity, (p) => p.employeeShipper)
  // orderDocuments: Promise<OrderDocumentEntity[]>

  // @OneToMany(() => ReceiptEntity, (p) => p.employee)
  // receipts: Promise<ReceiptEntity[]>

  // @OneToMany(() => ReceiptEntity, (p) => p.employeeCollect)
  // receipt: Promise<ReceiptEntity[]>

  // @OneToMany(() => OrderGroupEntity, (p) => p.employee)
  // orderGroup: Promise<OrderGroupEntity[]>

  // @OneToMany(() => EmployeeProductEntity, (p) => p.employee)
  // employeeProduct: Promise<EmployeeProductEntity[]>

  // @OneToMany(() => EmployeeProductEntity, (p) => p.employee)
  // employeeApproved: Promise<EmployeeProductEntity[]>

  // @OneToMany(() => EmployeeRequestWarehouseDetailEntity, (p) => p.employee)
  // employeeRequestWarehouseDetail: Promise<EmployeeRequestWarehouseDetailEntity[]>

  // @OneToMany(() => EmployeeRequestWarehouseEntity, (p) => p.employeeApproved)
  // employeeRequestWarehouse: Promise<EmployeeRequestWarehouseEntity[]>

  // @OneToMany(() => OrderEntity, (p) => p.employeeSale)
  // order: Promise<OrderEntity[]>

  // @OneToMany(() => CostAllocationEntity, (p) => p.employeeApproved)
  // costAllocationApproved: Promise<CostAllocationEntity[]>

  // @OneToMany(() => CostAllocationEntity, (p) => p.employee)
  // costAllocation: Promise<CostAllocationEntity[]>

  // @OneToMany(() => PayslipEntity, (p) => p.employeeSpend)
  // payslip: Promise<PayslipEntity[]>

  // @OneToMany(() => OrderReturnEntity, (p) => p.employeeApproved)
  // orderReturn: Promise<OrderReturnEntity[]>

  // @OneToMany(() => CustomerChildEntity, (p) => p.employee)
  // customerChilds: Promise<CustomerChildEntity[]>

  // @OneToMany(() => EmployeeProductEntity, (p) => p.employee)
  // employeeReject: Promise<EmployeeProductEntity[]>
}
