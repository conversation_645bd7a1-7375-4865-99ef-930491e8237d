import { ApiProperty } from '@nestjs/swagger'

/** Interface Lấy ds theo các điều kiện */
export class CreateStoreDto {
  partnerId: string
  code: string
  name: string
  taxCode: string
  phone: string
  email: string
  address: string
  contactPhone: string
}

export class CreatePartnerDto {
  code: string
  name: string
  taxCode?: string
  phone?: string
  email?: string
  address?: string
  contactPhone?: string
}

export class DeclareCardPeriod {
  name: string
  periodCode: string
  numberOfPeriods: number
  term: number
}

export class DeclareCardDuration {
  name: string
  durationCode: string
  duration: number
  unit?: string
}

export class DeclareCardType {
  name: string
  codeType: string
  isProperty: boolean
  isPrepaid: boolean
  note?: string
}
