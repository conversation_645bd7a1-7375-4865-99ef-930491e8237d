import { Entity, Column, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from './base.entity';

@Entity('delivery_schedule')
export class DeliveryScheduleEntity extends BaseEntity {
  @ApiProperty({ example: 'DS121225000001', description: 'Mã lịch giao hàng' })
  @Column({ type: 'varchar', length: 50, nullable: false, unique: true })
  code: string;

  @ApiProperty({ example: '12/11/2024', description: 'Ngày giao hàng' })
  @Column({ type: 'timestamptz', nullable: false })
  deliveryDate: Date;

  @ApiProperty({ example: 'mbc_001', description: 'ID của MBC' })
  @Column({ type: 'varchar', length: 50, nullable: false })
  mbcId: string;

  @ApiProperty({ example: 'htx_01', description: 'ID của 3PL (Nhà vận chuyển)' })
  @Column({ type: 'varchar', length: 50, nullable: false })
  thirdPartyLogisticsId: string;

  @ApiProperty({ example: 'product_123', description: 'ID của sản phẩm' })
  @Column({ type: 'varchar', length: 50, nullable: false })
  productId: string;

  @ApiProperty({ example: 'Cái', description: 'Đơn vị tính' })
  @Column({ type: 'varchar', length: 50, nullable: false })
  unit: string;

  @ApiProperty({ example: 500, description: 'Nhu cầu đơn hàng' })
  @Column({ type: 'int', nullable: false })
  orderDemand: number;

  @ApiProperty({ example: 'Ngày cụ thể', description: 'Loại thời gian cung cấp' })
  @Column({ type: 'varchar', length: 100, nullable: false })
  supplyTimeType: string;

  @ApiProperty({ example: '12/12/2024', description: 'Thời gian cung cấp' })
  @Column({ type: 'timestamptz', nullable: false })
  supplyTime: Date;
}
