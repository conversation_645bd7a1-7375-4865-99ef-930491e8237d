import { Injectable } from '@nestjs/common'
import { Request as IRequest } from 'express'
import { plainToClass } from 'class-transformer'
import { Like } from 'typeorm'
import { DepartmentCreateDto } from './dto/departmentCreate.dto'
import { DepartmentUpdateDto } from './dto/departmentUpdate.dto'
import { EmployeeService } from '../employee/employee.service'
import { AuthService } from '../auth/auth.service'
import { DepartmentRepository, EmployeeRepository } from '../../../repositories'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { DepartmentEntity, EmployeeEntity } from '../../../entities'
import { CREATE_SUCCESS, enumData, ERROR_CODE_TAKEN, ERROR_NOT_FOUND_DATA } from '../../../constants'
// import { surveyAuthApiHelper } from '../../helpers'

@Injectable()
export class DepartmentService {
  constructor(
    private readonly repository: DepartmentRepository,
    private readonly employeeRepo: EmployeeRepository,
    private readonly authService: AuthService,
  ) {}

  public async find(user: UserDto, data: any) {
    return await this.repository.find(data)
  }

  /** Tạo mới */
  async createData(userLogin: UserDto, data: DepartmentCreateDto) {
    data.code = data.code.trim().toUpperCase()
    const isCodeTaken = await this.repository.manager.getRepository(DepartmentEntity).findOneBy({
      code: data.code,
      isDeleted: false,
    })
    if (isCodeTaken) {
      throw new Error(ERROR_CODE_TAKEN)
    }
    return this.repository.manager.transaction(async (transaction) => {
      let dep = new DepartmentEntity()
      dep.code = data.code
      dep.name = data.name
      dep.description = data.description
      // dep.companyId = userLogin.companyId

      dep.createdBy = userLogin.id
      const depCreate = transaction.getRepository(DepartmentEntity).create({ ...dep })
      const depEntity = await transaction.getRepository(DepartmentEntity).save(depCreate)

      return depEntity
    })
  }

  /** Cập nhật */
  async updateData(userLogin: UserDto, data: DepartmentUpdateDto) {
    return this.repository.manager.transaction(async (transaction) => {
      const entity = await transaction.getRepository(DepartmentEntity).findOne({ where: { id: data.id, isDeleted: false } })
      if (!entity) {
        throw new Error(ERROR_NOT_FOUND_DATA)
      }
      entity.name = data.name
      entity.description = data.description

      entity.updatedAt = new Date()
      entity.updatedBy = userLogin.id
      const updatedEntity = await transaction.getRepository(DepartmentEntity).save(entity)
      const updatedData = plainToClass(DepartmentUpdateDto, updatedEntity)

      return updatedData
    })
  }

  /** Phân trang */
  async pagination(userLogin: UserDto, data: PaginationDto) {
    const condition: any = {}
    if (data.where.name) {
      condition.name = Like(`%${data.where.name}%`)
    }
    if (data.where.code) {
      condition.code = Like(`%${data.where.code}%`)
    }
    if (data.where.statusId === enumData.StatusFilter.Active.value) {
      condition.isDeleted = false
    } else if (data.where.statusId === enumData.StatusFilter.InActive.value) {
      condition.isDeleted = true
    }
    return await this.repository.findAndCount({
      where: condition,
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
    })
  }

  /** Cập nhật trạng thái */
  async updateIsDelete(userLogin: UserDto, data: FilterOneDto, req: IRequest) {
    // const entity: any = await this.repository.findOne({
    //   where: { id: data.id },
    //   relations: {
    //     employee: true,
    //   },
    // })
    // if (!entity) {
    //   throw new Error(ERROR_NOT_FOUND_DATA)
    // }
    // return await this.repository.manager.transaction(async (trans) => {
    //   const newIsDeleted = !entity.isDeleted
    //   entity.isDeleted = newIsDeleted
    //   entity.updatedAt = new Date()
    //   entity.updatedBy = userLogin.id
    //   entity.updatedBy = userLogin.username
    //   const updatedEntity = await trans.getRepository(DepartmentEntity).save(entity)
    //   /** Ngưng hoạt động phòng ban thì sẽ ngưng hoạt động nhân viên thuộc phòng ban đó */
    //   if (newIsDeleted === true) {
    //     for (const item of entity?.__employee__) {
    //       await this.authService.updateStatusUserSurvey(userLogin, {
    //         companyId: userLogin.companyId,
    //         userId: item.userId,
    //         isDeleted: newIsDeleted,
    //       })
    //       await trans
    //         .getRepository(EmployeeEntity)
    //         .update({ id: item.id }, { isDeleted: newIsDeleted, updatedBy: userLogin.id, updatedAt: new Date() })
    //     }
    //   }
    //   return { message: UPDATE_ACTIVE_SUCCESS, updatedEntity }
    // })
  }

  /** Lấy thông tin chi tiết */
  public async loadDetail(userLogin: UserDto, data: FilterOneDto) {
    return this.repository.manager.transaction(async (transaction) => {
      let rs: any = await transaction.getRepository(DepartmentEntity).findOne({
        where: { isDeleted: false, id: data.id },
      })
      if (!rs) {
        throw new Error(ERROR_NOT_FOUND_DATA + ' [ Phòng Ban ]')
      }
      let lstEmp = await transaction.getRepository(EmployeeEntity).find({
        where: { isDeleted: false, departmentId: data.id },
      })
      if (lstEmp.length > 0) {
        rs.employees = lstEmp
        const manager = lstEmp.find((s: any) => s.isManager == true)
        if (manager) {
          rs.manager = manager
          rs.managerId = manager.id
        }
      }
      return rs
    })
  }

  /** Thêm phòng ban bằng file excel */
  public async createDataByExcel(user: UserDto, data: DepartmentCreateDto[], req: IRequest) {
    await this.repository.manager.transaction('READ UNCOMMITTED', async (trans) => {
      const lstTask = []
      let lstDepart: any[] = await this.repository.find({
        where: {},
      })
      /** Danh sách phòng ban */
      const dictDepart: any = {}
      lstDepart.forEach((d) => {
        dictDepart[d.code] = d.id
      })
      for (const item of data) {
        if (dictDepart[item.code]) throw new Error(`Mã danh mục [${item.code}] đã có trong hệ thống`)
      }

      const setExcelDepartment = new Set()
      data.forEach((d) => {
        if (setExcelDepartment.has(d.code) === true) {
          throw new Error(`Mã phòng ban [${d.code}] lặp lại trong file excel`)
        }
        setExcelDepartment.add(d.code)
      })
      for (let item of data) {
        const department = new DepartmentEntity()
        department.createdBy = user.id
        //department.companyId = user.companyId
        department.createdAt = new Date()
        department.code = item.code
        department.name = item.name
        department.description = item.description
        const task = this.repository.save(department)
        lstTask.push(task)
      }
      await Promise.all(lstTask)
    })
    return { message: CREATE_SUCCESS }
  }
}
