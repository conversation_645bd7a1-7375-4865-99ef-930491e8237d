import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import { MediaRepository } from '../../../repositories'
import { MediaService } from './media.service'
import { MediaController } from './media.controller'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([MediaRepository])],
  controllers: [MediaController],
  providers: [MediaService],
})
export class MediaModule {}
