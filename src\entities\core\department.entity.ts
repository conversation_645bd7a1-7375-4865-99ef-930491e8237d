import { Entity, Column, OneToMany } from 'typeorm'
import { EmployeeEntity } from './employee.entity'
import { BaseEntity } from './base.entity'

/** Phòng ban */
@Entity('department')
export class DepartmentEntity extends BaseEntity {
  /** Tên */
  @Column({ type: 'varchar', length: 50, nullable: false })
  name: string

  /** Mã */
  @Column({ type: 'varchar', length: 50, nullable: false })
  code: string

  /** <PERSON>ô tả */
  @Column({ type: 'varchar', length: 250, nullable: true })
  description: string

  /** Danh sách nhân viên */
  @OneToMany(() => EmployeeEntity, (p) => p.department)
  employees: Promise<EmployeeEntity[]>
}
