import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString, IsDate } from 'class-validator'

export class SurveyMemberDeleteDto {
  @ApiProperty({ description: 'Phiếu khảo sát' })
  @IsNotEmpty({ message: 'Phiếu khảo sát không được trống' })
  @IsString()
  surveyId: string

  @ApiProperty({ description: 'id' })
  @IsNotEmpty({ message: 'id không được trống' })
  @IsString()
  id: string
}
