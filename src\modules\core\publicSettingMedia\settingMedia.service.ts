import { Injectable } from '@nestjs/common'
import { FilterOneDto, UserDto } from '../../../dto'
import { PaginationDto } from '../../../dto/pagination.dto'
import { SettingMediaRepository } from '../../../repositories'
import { CurrentUser } from '../../common/decorators'
import { MediaCreateDto } from './dto'

@Injectable()
export class PublicSettingMediaService {
  constructor(private readonly repository: SettingMediaRepository) {}

  public async findMediaByType(type: string) {
    return await this.repository.find({ where: { type: type } })
  }
  public async find(data: any) {
    let results = await this.repository.find(data)
    // results = results.filter((x) => x.itemInComboId == null && x.productId == null && x.isDeleted == false)
    // results = results.filter((x) => x.isDeleted == false)
    if (results.length === 0) {
      return { banners: null, categories: [] }
    }
    const transformedData = {
      banners: results[0].banner,
      categories: results.map((item) => ({
        typeName: item.typeName,
        content: item.content,
        type: item.type,
        term: item.term,
        link: item.link,
        url: item.url,
        img: item.img && item.img.length > 0 ? item.img[0] : '',
        personalDeposit: item.personalDeposit,
        corporateDeposit: item.corporateDeposit,
        commission: item.commission,
        isDeleted: item.isDeleted,
        id: item.id,
      })),
    }
    for (let item of transformedData.categories) {
      if (item.type == 'TOURIST') item.type = 'TOURISM'
    }
    return transformedData
  }

  public async findAllBusiness() {
    const results = await this.repository.find({ where: { isDeleted: false } })
    return results.map((p) => {
      return {
        ...p,
        term: '',
        content: '',
      }
    })
  }

  /** Hàm lấy số tiền cần đầu tư theo từng loại CTV */
  public async findPrice() {
    const results = await this.repository.find({ where: { isDeleted: false } })

    const transformedData = results.map((item) => ({
      personalDeposit: item.personalDeposit,
      corporateDeposit: item.corporateDeposit,
      type: item.type,
    }))

    return transformedData
  }

  public async findOne(data: FilterOneDto) {
    const foundSettingMedia = await this.repository.findOne({
      where: {
        id: data.id,
      },
    })
    if (!foundSettingMedia) {
      throw new Error('Không tìm thấy')
    }

    return foundSettingMedia
  }

  public async createData(data: MediaCreateDto, @CurrentUser() user: UserDto): Promise<any> {
    return await this.repository.createData(data, user)
  }

  public async updateData(data: MediaCreateDto, @CurrentUser() user: UserDto): Promise<any> {
    return await this.repository.updateData(data, user)
  }

  public async pagination(data: PaginationDto) {
    return await this.repository.pagination(data)
  }
}
