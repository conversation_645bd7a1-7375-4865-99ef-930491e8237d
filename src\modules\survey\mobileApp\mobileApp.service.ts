import { Injectable, NotAcceptableException } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import * as AWS from 'aws-sdk'
import { Request as IRequest } from 'express'
import { customAlphabet } from 'nanoid'
import { Brackets, In, IsNull, Like } from 'typeorm'
import { enumData, ERROR_NOT_FOUND_DATA, ERROR_YOU_DO_NOT_HAVE_PERMISSION, UPDATE_SUCCESS } from '../../../constants'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { SurveyEntity, SurveyHistoryEntity, SurveyMemberEntity, SurveyQuestionEntity, TopicEntity } from '../../../entities/survey'
import { coreHelper } from '../../../helpers'
import {
  EmployeeRepository,
  QuestionRepository,
  SurveyHistoryRepository,
  SurveyMemberRepository,
  SurveyQuestionListDetailRepository,
  SurveyQuestionRepository,
  SurveyRepository,
  TopicRepository,
  UserRepository,
  UserSurveyRepository,
} from '../../../repositories'
import { TopicService } from '../topic/topic.service'
import { SurveyAnswerQuestionDto, SurveyFilterOneDto } from './dto'
const nanoid = customAlphabet('0123456789abcdefghijklmnopqrstuvwxyz', 10)
@Injectable()
export class MobileAppService {
  AWS_S3_BUCKET_NAME: string
  s3: AWS.S3
  constructor(
    private readonly repo: SurveyRepository,
    private readonly userSurveyRepo: UserSurveyRepository,
    private surveyMemberRepo: SurveyMemberRepository,
    private surveyQuestionRepo: SurveyQuestionRepository,
    private questionRepo: QuestionRepository,
    private surveyQuestionListDetail: SurveyQuestionListDetailRepository,
    private surveyHistoryRepo: SurveyHistoryRepository,
    private employeeRepo: EmployeeRepository,
    private topicRepo: TopicRepository,
    private readonly userRepo: UserRepository,
    private readonly configService: ConfigService,
    private readonly topPicService: TopicService,
  ) {
    this.AWS_S3_BUCKET_NAME = this.configService.get<string>('AWS_S3_BUCKET_NAME') || ''
    const ACCESS_KEY_ID = this.configService.get<string>('AWS_S3_ACCESS_KEY_ID')
    const SECRET_ACCESS_KEY = this.configService.get<string>('AWS_S3_SECRET_ACCESS_KEY')

    this.s3 = new AWS.S3({
      accessKeyId: ACCESS_KEY_ID,
      secretAccessKey: SECRET_ACCESS_KEY,
    })
  }

  /** DS phiếu khảo sát phân trang */
  public async surveyPagination(user: UserDto, data: PaginationDto) {
    // if (!user.userId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    let whereCon: any = { isDeleted: false }
    // Tìm theo mã
    if (data.where.code) {
      whereCon.code = Like(`%${data.where.code}%`)
    }
    // Tìm theo  tên
    if (data.where.name) {
      whereCon.name = Like(`%${data.where.name}%`)
    }

    if (data.where.topicId) {
      whereCon.topicId = Like(`%${data.where.topicId}%`)
    }

    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
    })
    let lstStatus = coreHelper.convertObjToArray(enumData.SurveyStatus)
    for (let item of res[0]) {
      const statusItem = lstStatus.find((x) => x.code === item.status)
      if (statusItem) {
        item.statusName = statusItem.name
      }
    }
    return res
  }

  /** DS phiếu khảo sát chưa làm phân trang */
  public async surveyPaginationNotDoing(user: UserDto, data: PaginationDto) {
    // if (!user.userId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    let whereCon: any = { isDeleted: false }
    const lstSurveyMember: any = await this.surveyMemberRepo.find({
      where: {
        // userId: user.userId,
        isDeleted: false,
        // companyId: user.companyId,
        status: enumData.SurveyMemberStatus.NotStart.code,
      },
    })

    if (lstSurveyMember && lstSurveyMember.length > 0) {
      let lstSurveyId = lstSurveyMember.map((x) => x.surveyId)
      let lstSurveyMemberId = lstSurveyMember.map((x) => x.id)
      const lstQuestion = await this.surveyQuestionRepo.find({
        where: {
          isDeleted: false,
          // companyId: user.companyId,
          surveyId: In(lstSurveyId),
          surveyMemberId: In(lstSurveyMemberId),
        },
      })
      if (lstQuestion && lstQuestion.length > 0) {
        let lstId = lstQuestion.map((x) => x.surveyId)
        whereCon.id = In(lstId)
      } else {
        whereCon.id = ''
      }
      const res: any[] = await this.repo.findAndCount({
        where: whereCon,
        skip: data.skip,
        take: data.take,
        order: { createdAt: 'DESC' },
        relations: { members: true },
      })
      let lstStatus = coreHelper.convertObjToArray(enumData.SurveyStatus)
      let lstStatusMember = coreHelper.convertObjToArray(enumData.SurveyMemberStatus)
      for (let item of res[0]) {
        const statusItem = lstStatus.find((x) => x.code === item.status)
        if (statusItem) {
          item.statusName = statusItem.name
        }
        let lstMember = await item.__members__
        if (lstMember && lstMember.length > 0) {
          const members = lstMember.find((x) => x.userId === user.userId && x.surveyId === item.id)
          if (members) {
            const statusMemberItem = lstStatusMember.find((x) => x.code === members.status)
            if (statusMemberItem) {
              item.statusMemberName = statusMemberItem.name
              item.statusMemberCode = statusMemberItem.code
            }
          }
        }
        delete item.__members__
      }
      return res
    } else {
      return []
    }
  }

  /** Tính điểm  */
  calScore(item: any, value: string) {
    let score = 0
    if (item.type === enumData.QuestionType.Number.code && value && value.trim() != '') {
      let temp = 0
      const x = +value
      // Tính theo chiều thuận
      if (item.isCalUp) {
        if (x >= item.percentRule) {
          temp = item.percent
        } else {
          temp = (x * item.percent) / item.percentRule // giá trị * tỉ trọng / điều kiện b
        }
      }
      // Tính theo chiều nghịch
      else {
        if (x <= item.percentRule) {
          temp = item.percent
        } else if (x >= item.percentDownRule) {
          temp = 0
        } else {
          temp = ((item.percentDownRule - x) * item.percent) / (item.percentDownRule - item.percentRule)
        }
      }

      if (isNaN(temp)) {
        score += 0
      } else if (!isFinite(temp)) {
        score += 0
      } else {
        score += temp
      }
    } else if (item.type === enumData.QuestionType.List.code) {
      const itemChosen = item.__questionlistDetails__.find((p) => p.id === value)
      const tem = itemChosen ? itemChosen.value : 0
      const temp = (tem * item.percent) / 100
      if (isNaN(temp)) {
        score += 0
      } else if (!isFinite(temp)) {
        score += 0
      } else {
        score += temp
      }
    }

    if (isNaN(score) || !isFinite(score)) return 0

    return score
  }

  /** Lấy ds câu hỏi cho nhân viên khảo sát */
  public async surveyQuestion(user: UserDto, data: SurveyFilterOneDto) {
    const survey = await this.repo.findOne({
      where: { id: data.surveyId },
    })
    if (!survey) throw new Error('Phiếu khảo sát không còn tồn tại')
    if (survey.isDeleted) throw new Error('Phiếu khảo sát đã ngưng hoạt động')
    if (survey.status === enumData.SurveyStatus.New.code) throw new Error('Phiếu khảo sát chưa bắt đầu vui lòng liên hệ Admin để bắt đầu khảo sát')
    if (survey.status === enumData.SurveyStatus.Cancel.code) throw new Error('Phiếu khảo sát đã hủy vui lòng kiểm tra lại')
    let lstSurveyQuestion: any = await this.surveyQuestionRepo.find({
      where: { surveyId: data.surveyId, isDeleted: false, surveyMemberId: IsNull() },
    })
    if (lstSurveyQuestion.length > 0) {
      let lstSurveyQuestionId = lstSurveyQuestion.map((x: { questionId: any }) => x.questionId)
      if (lstSurveyQuestionId.length > 0) {
        let lstQuestion: any = await this.questionRepo.find({
          where: {
            id: In(lstSurveyQuestionId),
            isDeleted: false,
          },
          relations: { questionlistDetails: true, childs: { questionlistDetails: true } },
          order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } },
        })
        let lstQuestionId: any[] = lstQuestion.map((x) => x.id)
        let lstQuestionParent: any[] = lstQuestion.filter((x) => x.parentId !== null)
        let mapQuestionDistinct = lstQuestionParent.mapAndDistinct((x) => x.parentId)
        for (let item of mapQuestionDistinct) {
          if (item == null) continue
          let question = lstQuestion.find((x) => x.id == item)
          let lstChilds = await question.childs
          lstChilds = lstChilds.filter((x) => lstQuestionId.includes(x.id))
          let lstChildId = lstChilds.map((x) => x.id)
          lstQuestion = lstQuestion.filter((x) => !lstChildId.includes(x.id))
          delete question.__childs__
          question.childs = lstChilds
        }
        return lstQuestion
      }
    }
    return []
  }

  /** câu hỏi và tính điểm */
  async surveyCalScore(user: UserDto, data: SurveyFilterOneDto) {
    const surveyQuestionValueRepo = this.repo.manager.getRepository(SurveyQuestionEntity)

    // kiểm tra quyền
    if (!user.userId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const res: any = await this.repo.findOne({
      where: { id: data.surveyId },
    })

    if (!res) throw new Error('Phiếu khảo sát không còn tồn tại')
    if (res.isDeleted) throw new Error('Phiếu khảo sát đã ngưng hoạt động')

    const surveyMember: any = await this.surveyMemberRepo.findOne({
      where: {
        userId: user.userId,
        isDeleted: false,
        surveyId: res.id,
      },
    })
    const setType = new Set()
    setType.add(enumData.QuestionType.Number.code)
    setType.add(enumData.QuestionType.List.code)
    if (surveyMember) {
      let surveyQuestionValue: any = await this.surveyQuestionRepo.find({
        where: { surveyId: res.id, isDeleted: false, surveyMemberId: surveyMember.id },
      })
      if (surveyQuestionValue.length > 0) {
        let lstQuestionId = surveyQuestionValue.map((x) => x.questionId)
        if (lstQuestionId.length > 0) {
          const lstQuestion: any = await this.questionRepo.find({
            where: {
              id: In(lstQuestionId),
              isDeleted: false,
            },
            relations: { questionlistDetails: true, childs: { questionlistDetails: true } },
            order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } },
          })
          //Lấy tiêu chí tính điểm (cấp 1 & kiểu number hoặc list)

          var lstQuestionCal = lstQuestion.filter((c) => c.parentId === null && setType.has(c.type))
          let scoreMember = 0
          let isHighlight = false
          let isNotHaveMinValue = false
          for (const question of lstQuestionCal) {
            const itemChilds = question.__childs__ || []
            if (itemChilds.length > 0) {
              let scoreMemberChild = 0
              for (const itemChild of itemChilds) {
                const objValueChild = surveyQuestionValue.find((c: any) => c.questionId === itemChild.id)
                if (objValueChild) {
                  const tem = this.calScore(itemChild, objValueChild.value)
                  objValueChild.score = tem

                  // Lưu điểm
                  await surveyQuestionValueRepo.update(objValueChild.id, { score: tem, updatedBy: user.id })
                  scoreMemberChild += tem
                  isHighlight = this.checkHighlightTechItem(itemChild, objValueChild.value)
                  isNotHaveMinValue = this.checkMinValueTechItem(itemChild, objValueChild.value)
                }
              }
              const temp = (question.percent * scoreMemberChild) / 100
              scoreMember += temp
              // Lưu điểm
              await surveyQuestionValueRepo.update({ questionId: question.id, surveyMemberId: surveyMember.id }, { score: temp, updatedBy: user.id })
            } else {
              const objValue = surveyQuestionValue.find((c: any) => c.questionId === question.id)
              if (objValue) {
                const temp = this.calScore(question, objValue.value)
                objValue.score = temp
                scoreMember += temp

                isHighlight = this.checkHighlightTechItem(question, objValue.value)
                isNotHaveMinValue = this.checkMinValueTechItem(question, objValue.value)

                // Lưu điểm
                await surveyQuestionValueRepo.update(
                  { questionId: question.id, surveyMemberId: surveyMember.id },
                  { score: temp, updatedBy: user.id },
                )
              }
            }
          }
          if (isNaN(scoreMember) || !isFinite(scoreMember)) scoreMember = 0

          // Lưu điểm
          await this.surveyMemberRepo.update(surveyMember.id, {
            score: scoreMember,
            updatedBy: user.id,
            updatedAt: new Date(),
            status: enumData.SurveyMemberStatus.Done.code,
          })
          res.score = scoreMember
        }
      }
    }
    const history = new SurveyHistoryEntity()
    history.surveyId = res.id
    history.createdBy = user.id
    // history.companyId = user.companyId
    history.description = `Nhân viên [${user.username}] vừa hoàn thành bài khảo sát. Điểm số của bài khảo sát [${res.score}]`
    history.createdAt = new Date()
    history.status = enumData.SurveyStatus.Doing.code
    await this.surveyHistoryRepo.save(history)
    return res
  }

  /**  Check highlight đánh giá kỹ thuật */
  checkHighlightTechItem(item: any, value: string) {
    let isHighlight = false
    if (!item.hightlightValue) {
      return isHighlight
    }

    if (item.type === enumData.QuestionType.Number.code && value && value.trim() != '') {
      if (+value >= item.hightlightValue) {
        isHighlight = true
      }
    } else if (item.type === enumData.QuestionType.List.code) {
      const itemChosen = item.__questionlistDetails__.find((p) => p.id === value)
      const temp = itemChosen ? itemChosen.value : 0
      if (temp >= item.hightlightValue) {
        isHighlight = true
      }
    }

    return isHighlight
  }

  /** Check isNotHaveMinValue đánh giá kỹ thuật */
  checkMinValueTechItem(item: any, value: string) {
    let isNotHaveMinValue = false
    if (!item.requiredMin) {
      return isNotHaveMinValue
    }

    if (item.type === enumData.QuestionType.Number.code && value && value.trim() != '') {
      if (+value < item.requiredMin) {
        isNotHaveMinValue = true
      }
    } else if (item.type === enumData.QuestionType.List.code) {
      const itemChosen = item.__questionlistDetails__.find((p) => p.id === value)
      const temp = itemChosen ? itemChosen.value : 0
      if (temp < item.requiredMin) {
        isNotHaveMinValue = true
      }
    }

    return isNotHaveMinValue
  }

  /** Trả lời và lưu câu trả lời */
  async surveyAnswerQuestion(data: SurveyAnswerQuestionDto) {
    const surveyItem = await this.repo.findOne({ where: { id: data.surveyId, isDeleted: false } })
    if (!surveyItem) throw new Error('Phiếu khảo sát không còn tồn tại!')

    const today = new Date()
    if (today > surveyItem.timeEnd) throw new Error('phiếu khảo sát không còn khả năng do hết hạn khảo sát.')

    await this.repo.manager.transaction(async (manager) => {
      const surveyMemberRepo = manager.getRepository(SurveyMemberEntity)
      const surveyMember = await surveyMemberRepo.findOne({
        where: { id: data.surveyMemberId, isDeleted: false },
      })
      if (!surveyMember) throw new Error(ERROR_NOT_FOUND_DATA)
      const surveyQuestionValueRepo = manager.getRepository(SurveyQuestionEntity)
      for (let index = 0; index < data.questionInfo.length; index++) {
        const item = data.questionInfo[index]
        if (item.value) {
          let surveyQuestion = new SurveyQuestionEntity()
          let question = await this.surveyQuestionListDetail.findOne({ where: { id: item.questionId } })
          let topicId = await manager.getRepository(TopicEntity).findOne({ where: { id: question.topicId } })
          if (!topicId) throw new Error(ERROR_NOT_FOUND_DATA)
          surveyQuestion.value = item.value
          surveyQuestion.topicId = topicId.id
          surveyQuestion.questionId = item.questionId
          let survey = await manager.getRepository(SurveyEntity).findOne({ where: { id: data.surveyId } })
          if (!survey) throw new Error(ERROR_NOT_FOUND_DATA)
          surveyQuestion.surveyId = data.surveyId
          surveyQuestion.surveyMemberId = data.surveyMemberId
          surveyQuestion.createdAt = today

          await surveyQuestionValueRepo.save(surveyQuestion)
        }
      }
      const surveyHistory = manager.getRepository(SurveyHistoryEntity)
      const history = new SurveyHistoryEntity()
      history.surveyId = surveyItem.id
      history.createdAt = today
      history.status = enumData.SurveyStatus.Doing.code
      // await surveyHistory.save(history)
    })

    if (surveyItem.status === enumData.SurveyStatus.New.code) {
      surveyItem.status = enumData.SurveyStatus.Doing.code
      await this.repo.save(surveyItem)
    }
    return { message: UPDATE_SUCCESS }
  }

  /** Lấy ds nhân viên khảo sát */
  public async surveyLoadMember(user: UserDto, data: SurveyFilterOneDto) {
    const lstMember: any = await this.surveyMemberRepo.find({
      where: {
        // companyId: user.companyId,
        isDeleted: false,
        surveyId: data.surveyId,
      },
      relations: { survey: true },
    })
    return lstMember
  }

  public async surveyFindDetail(user: UserDto, data: FilterOneDto, req: IRequest) {
    const entity: any = await this.repo.findOne({
      where: { id: data.id },
      relations: { histories: true, questions: true },
    })
    if (!entity) throw new Error('Phiếu khảo sát không tồn tại')
    let lstStatus = coreHelper.convertObjToArray(enumData.SurveyStatus)
    const statusItem = lstStatus.find((x) => x.code === entity.status)
    if (statusItem) {
      entity.statusName = statusItem.name
      entity.statusColor = statusItem.color
    }
    let lstMember = await entity.members
    let lstStatusMember = coreHelper.convertObjToArray(enumData.SurveyMemberStatus)
    for (let item of lstMember) {
      const statusItem = lstStatusMember.find((x) => x.code === item.status)
      if (statusItem) {
        item.statusName = statusItem.name
      }
      for (let qs of item.__surveyQuestions__) {
        const qsItem = await this.questionRepo.findOne({ where: { id: qs.questionId } })
        if (qsItem) {
          qs.questionName = qsItem.name
        }
      }
    }
    let lstHitory = await entity.histories
    for (let item of lstHitory) {
      const user: any = await this.userRepo.findOne({ where: { id: item.createdBy } })
      if (user) {
        item.createdByName = user?.username
      }
    }
    entity.lstHitory = lstHitory.sort((a: any, b: any) => (a.createdAt > b.createdAt ? 1 : -1))
    entity.lstMember = lstMember

    const findTopic = (await entity.__questions__) && entity.__questions__.length > 0 ? entity.__questions__[0] : []
    if (findTopic) {
      const topic = await this.topicRepo.findOne({ where: { id: findTopic.topicId, isDeleted: false } })
      if (topic) {
        entity.topicName = topic.name
      }
    }

    delete entity.__histories__
    delete entity.__members__
    delete entity.__questions__
    return entity
  }
  async uploadSingle(file: Express.Multer.File) {
    const current = new Date()
    let temp: string[] = file.originalname ? file.originalname.split('.') : []
    let ext = temp.length > 1 ? `.${temp[temp.length - 1]}` : ''
    let LINK_UPLOAD_S3 = process.env.LINK_UPLOAD_S3
    let fileName = `${current.getFullYear()}${current.getMonth() + 1}${current.getDate()}-${nanoid()}${ext}`
    const key = `${LINK_UPLOAD_S3}/${fileName}`
    const params = {
      Bucket: this.AWS_S3_BUCKET_NAME,
      Key: key,
      Body: file.buffer,
      ACL: 'public-read',
    }
    return new Promise<any>((resolve, reject) => {
      this.s3.upload(params, (err: any, data: any) => {
        if (err) {
          reject(err)
        } else {
          resolve({ fileName, fileUrl: data.Location })
        }
      })
    })
  }

  async uploadFile(files: Array<Express.Multer.File>) {
    const lstTask = []
    for (const file of files) {
      lstTask.push(this.uploadSingle(file))
    }

    return Promise.all(lstTask)
  }
  public async findAll(data: any) {
    let whereCon: any = { isDeleted: false }
    if (data.where.topicId) {
      whereCon.questions = {}
      whereCon.questions.topicId = data.where.topicId
    }
    if (data.where.status) {
      whereCon.status = data.where.status
    }
    return await this.repo.find({
      where: whereCon,
      relations: {
        questions: true,
      },
      order: { createdAt: 'DESC' },
    })
  }

  public async pagination(data: PaginationDto) {
    const where = data.where || {}

    const queryBuilder = this.userSurveyRepo.createQueryBuilder('user').where('user.isDeleted = :isDeleted', { isDeleted: false })

    if (where.keyword) {
      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where('user.phone LIKE :keyword', { keyword: `%${where.keyword}%` }).orWhere('user.username LIKE :keyword', {
            keyword: `%${where.keyword}%`,
          })
        }),
      )
    }

    if (where.type) {
      queryBuilder.andWhere('user.type = :type', { type: where.type })
    }

    if (where.username) {
      queryBuilder.andWhere('LOWER(TRIM(user.username)) LIKE :username', { username: `%${where.username.trim().toLowerCase()}%` })
    }

    queryBuilder.orderBy('user.createdAt', 'DESC').skip(data.skip).take(data.take)

    const res: any = await queryBuilder.getManyAndCount()

    const dictSurveyMember: any = {}
    const resSurveyMember = await this.surveyMemberRepo.find({ where: {} })

    for (const item of resSurveyMember) {
      dictSurveyMember[item.userId] = {
        company: item.company,
        tax: item.tax,
      }
    }

    for (let item of res[0]) {
      const surveyData = dictSurveyMember[item.id]
      item.company = surveyData?.company || null
      item.tax = surveyData?.tax || null
    }

    return res
  }

  /** lấy bộ câu hỏi đầu tiên của danh mục câu hỏi  */
  public async findQuestionOfFirstTopic(data: { topicId: string }) {
    if (!data?.topicId) throw new Error('categoryId is required')
    return await this.topPicService.findQuestionOfFirstTopic(data.topicId)
  }
}
