import { MigrationInterface, QueryRunner } from "typeorm";

export class updateEntityTrackingDeliveryNote1747111806459 implements MigrationInterface {
    name = 'updateEntityTrackingDeliveryNote1747111806459'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "delivery_note_tracking" ADD "trackingType" character varying`);
        await queryRunner.query(`ALTER TABLE "delivery_note_tracking" ADD "expectedDate" TIMESTAMP WITH TIME ZONE`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "delivery_note_tracking" DROP COLUMN "expectedDate"`);
        await queryRunner.query(`ALTER TABLE "delivery_note_tracking" DROP COLUMN "trackingType"`);
    }

}
