import { Module } from '@nestjs/common'
import { SurveyMemberController } from './surveyMember.controller'
import { SurveyMemberService } from './surveyMember.service'

import { SurveyQuestionModule } from '../surveyQuestion/surveyQuestion.module'
import { NotifyModule } from '../notify/notify.module'
import { TypeOrmExModule } from '../../../typeorm'
import { EmployeeRepository, QuestionRepository, SurveyMemberRepository, SurveyQuestionRepository, SurveyRepository } from '../../../repositories'
import { EmployeeModule } from '../../core/employee/employee.module'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      SurveyRepository,
      SurveyMemberRepository,
      SurveyQuestionRepository,
      QuestionRepository,
      QuestionRepository,
      EmployeeRepository,
    ]),
    EmployeeModule,
    SurveyQuestionModule,
    NotifyModule,
  ],
  controllers: [SurveyMemberController],
  providers: [SurveyMemberService],
  exports: [SurveyMemberService],
})
export class SurveyMemberModule {}
