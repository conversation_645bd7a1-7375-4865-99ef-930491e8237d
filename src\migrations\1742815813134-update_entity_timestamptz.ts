import { MigrationInterface, QueryRunner } from "typeorm";

export class updateEntityTimestamptz1742815813134 implements MigrationInterface {
    name = 'updateEntityTimestamptz1742815813134'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "action_log" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "action_log" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "delivery_note_child_detail" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "delivery_note_child_detail" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "bank" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "bank" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "bank_branch" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "bank_branch" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "delivery_note_child" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "delivery_note_child" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "contracts" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "contracts" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "contracts" ALTER COLUMN "effectiveDate" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "contracts" ALTER COLUMN "expirationDate" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "supplier_history" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "supplier_history" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "department" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "department" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "employee" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "employee" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "employee" ALTER COLUMN "birthday" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "supplier" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "supplier" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "city" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "city" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "district" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "district" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "ward" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "ward" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "brand" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "brand" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "unit" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "unit" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "item_detail" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "item_detail" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "item_detail" ALTER COLUMN "manufactureDate" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "item_detail" ALTER COLUMN "expiryDate" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "item_combo" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "item_combo" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "item_group" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "item_group" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "item_category" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "item_category" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "item_type" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "item_type" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "item_price" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "item_price" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "tax" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "tax" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "item" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "item" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "item" ALTER COLUMN "expiryDate" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "item" ALTER COLUMN "dateFrom" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "item" ALTER COLUMN "dateTo" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "packing" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "packing" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "media" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "media" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "region_city" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "region_city" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "region_district" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "region_district" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "region_ward" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "region_ward" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "region" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "region" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "setting_string" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "setting_string" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "setting_media" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "setting_media" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "partner" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "partner" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "partner_map" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "partner_map" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "news_category" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "news_category" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "news" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "news" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "purchase_order" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "purchase_order" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "purchase_order" ALTER COLUMN "deliveryDate" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "purchase_order_item" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "purchase_order_item" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "purchase_order_history" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "purchase_order_history" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "purchase_order_so" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "purchase_order_so" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "purchase_order_approve" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "purchase_order_approve" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "supply_chain_config" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "supply_chain_config" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "supply_chain_config_detail" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "supply_chain_config_detail" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "supply_chain_config_approve" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "supply_chain_config_approve" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "supply_chain_config_time" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "supply_chain_config_time" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "delivery_note" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "delivery_note" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "delivery_note_tracking" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "delivery_note_tracking" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "delivery_note_lastmile_product" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "delivery_note_lastmile_product" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "po_schedule_history" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "po_schedule_history" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "po_schedule_history" ALTER COLUMN "performedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "po_schedule" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "po_schedule" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "delivery_schedule" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "delivery_schedule" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "delivery_schedule" ALTER COLUMN "deliveryDate" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "delivery_schedule" ALTER COLUMN "supplyTime" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "order_config" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "order_config" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "contact" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "contact" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "s_question_list_detail" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "s_question_list_detail" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "s_question" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "s_question" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "s_survey_member" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "s_survey_member" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "s_survey_question" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "s_survey_question" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "s_notify" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "s_notify" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "s_survey_question_list_detail" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "s_survey_question_list_detail" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "s_survey" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "s_survey" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "s_survey" ALTER COLUMN "timeStart" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "s_survey" ALTER COLUMN "timeEnd" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "s_survey_history" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "s_survey_history" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "s_topic" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "s_topic" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "s_categories" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "s_categories" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "s_company" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "s_company" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "s_company_categories" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "s_company_categories" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "s_email_history" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "s_email_history" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "warehouse_product_safety" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "warehouse_product_safety" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "warehouse" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "warehouse" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "warehouse_transfer_history" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "warehouse_transfer_history" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "warehouse_product_detail" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "warehouse_product_detail" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "warehouse_product_detail" ALTER COLUMN "manufactureDate" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "warehouse_product_detail" ALTER COLUMN "expiryDate" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "warehouse_product" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "warehouse_product" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "warehouse_transfer" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "warehouse_transfer" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "warehouse_transfer" ALTER COLUMN "receivedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "warehouse_transfer" ALTER COLUMN "approvedDate" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "warehouse_transfer_detail" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "warehouse_transfer_detail" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "warehouse_transfer_detail" ALTER COLUMN "expiryDate" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "warehouse_transfer_detail" ALTER COLUMN "manufactureDate" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "inbound_detail_cost_price" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "inbound_detail_cost_price" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "inbound_detail_price" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "inbound_detail_price" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "inbound_detail" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "inbound_detail" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "inbound_detail" ALTER COLUMN "manufactureDate" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "inbound_detail" ALTER COLUMN "expiryDate" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "inbound_cost_allocation" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "inbound_cost_allocation" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "inbound" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "inbound" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "inbound" ALTER COLUMN "approvedDate" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "inbound_history" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "inbound_history" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "outbound" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "outbound" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "outbound" ALTER COLUMN "orderCreatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "outbound" ALTER COLUMN "preparedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "outbound" ALTER COLUMN "approvedDate" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "outbound_detail" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "outbound_detail" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "outbound_detail" ALTER COLUMN "expiryDate" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "outbound_detail" ALTER COLUMN "manufactureDate" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "outbound_history" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "outbound_history" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "check_inventory_detail" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "check_inventory_detail" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "check_inventory_detail" ALTER COLUMN "expiryDate" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "check_inventory_detail" ALTER COLUMN "manufactureDate" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "check_inventory" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "check_inventory" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "check_inventory" ALTER COLUMN "approvedDate" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "check_inventory_detail_by_employee" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "check_inventory_detail_by_employee" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "product_inventory_history" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "product_inventory_history" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "check_inventory_history" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "check_inventory_history" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "s_user" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "s_user" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "s_user" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "s_user" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "check_inventory_history" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "check_inventory_history" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "product_inventory_history" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "product_inventory_history" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "check_inventory_detail_by_employee" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "check_inventory_detail_by_employee" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "check_inventory" ALTER COLUMN "approvedDate" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "check_inventory" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "check_inventory" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "check_inventory_detail" ALTER COLUMN "manufactureDate" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "check_inventory_detail" ALTER COLUMN "expiryDate" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "check_inventory_detail" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "check_inventory_detail" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "outbound_history" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "outbound_history" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "outbound_detail" ALTER COLUMN "manufactureDate" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "outbound_detail" ALTER COLUMN "expiryDate" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "outbound_detail" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "outbound_detail" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "outbound" ALTER COLUMN "approvedDate" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "outbound" ALTER COLUMN "preparedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "outbound" ALTER COLUMN "orderCreatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "outbound" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "outbound" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "inbound_history" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "inbound_history" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "inbound" ALTER COLUMN "approvedDate" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "inbound" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "inbound" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "inbound_cost_allocation" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "inbound_cost_allocation" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "inbound_detail" ALTER COLUMN "expiryDate" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "inbound_detail" ALTER COLUMN "manufactureDate" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "inbound_detail" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "inbound_detail" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "inbound_detail_price" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "inbound_detail_price" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "inbound_detail_cost_price" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "inbound_detail_cost_price" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "warehouse_transfer_detail" ALTER COLUMN "manufactureDate" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "warehouse_transfer_detail" ALTER COLUMN "expiryDate" TYPE date`);
        await queryRunner.query(`ALTER TABLE "warehouse_transfer_detail" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "warehouse_transfer_detail" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "warehouse_transfer" ALTER COLUMN "approvedDate" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "warehouse_transfer" ALTER COLUMN "receivedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "warehouse_transfer" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "warehouse_transfer" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "warehouse_product" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "warehouse_product" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "warehouse_product_detail" ALTER COLUMN "expiryDate" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "warehouse_product_detail" ALTER COLUMN "manufactureDate" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "warehouse_product_detail" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "warehouse_product_detail" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "warehouse_transfer_history" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "warehouse_transfer_history" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "warehouse" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "warehouse" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "warehouse_product_safety" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "warehouse_product_safety" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "s_email_history" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "s_email_history" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "s_company_categories" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "s_company_categories" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "s_company" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "s_company" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "s_categories" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "s_categories" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "s_topic" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "s_topic" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "s_survey_history" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "s_survey_history" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "s_survey" ALTER COLUMN "timeEnd" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "s_survey" ALTER COLUMN "timeStart" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "s_survey" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "s_survey" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "s_survey_question_list_detail" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "s_survey_question_list_detail" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "s_notify" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "s_notify" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "s_survey_question" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "s_survey_question" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "s_survey_member" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "s_survey_member" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "s_question" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "s_question" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "s_question_list_detail" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "s_question_list_detail" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "contact" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "contact" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "order_config" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "order_config" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "delivery_schedule" ALTER COLUMN "supplyTime" TYPE date`);
        await queryRunner.query(`ALTER TABLE "delivery_schedule" ALTER COLUMN "deliveryDate" TYPE date`);
        await queryRunner.query(`ALTER TABLE "delivery_schedule" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "delivery_schedule" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "po_schedule" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "po_schedule" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "po_schedule_history" ALTER COLUMN "performedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "po_schedule_history" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "po_schedule_history" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "delivery_note_lastmile_product" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "delivery_note_lastmile_product" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "delivery_note_tracking" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "delivery_note_tracking" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "delivery_note" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "delivery_note" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "supply_chain_config_time" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "supply_chain_config_time" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "supply_chain_config_approve" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "supply_chain_config_approve" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "supply_chain_config_detail" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "supply_chain_config_detail" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "supply_chain_config" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "supply_chain_config" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "purchase_order_approve" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "purchase_order_approve" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "purchase_order_so" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "purchase_order_so" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "purchase_order_history" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "purchase_order_history" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "purchase_order_item" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "purchase_order_item" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "purchase_order" ALTER COLUMN "deliveryDate" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "purchase_order" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "purchase_order" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "news" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "news" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "news_category" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "news_category" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "partner_map" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "partner_map" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "partner" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "partner" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "setting_media" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "setting_media" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "setting_string" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "setting_string" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "region" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "region" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "region_ward" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "region_ward" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "region_district" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "region_district" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "region_city" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "region_city" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "media" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "media" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "packing" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "packing" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "item" ALTER COLUMN "dateTo" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "item" ALTER COLUMN "dateFrom" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "item" ALTER COLUMN "expiryDate" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "item" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "item" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "tax" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "tax" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "item_price" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "item_price" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "item_type" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "item_type" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "item_category" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "item_category" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "item_group" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "item_group" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "item_combo" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "item_combo" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "item_detail" ALTER COLUMN "expiryDate" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "item_detail" ALTER COLUMN "manufactureDate" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "item_detail" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "item_detail" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "unit" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "unit" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "brand" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "brand" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "ward" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "ward" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "district" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "district" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "city" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "city" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "supplier" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "supplier" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "employee" ALTER COLUMN "birthday" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "employee" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "employee" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "department" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "department" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "supplier_history" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "supplier_history" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "contracts" ALTER COLUMN "expirationDate" TYPE date`);
        await queryRunner.query(`ALTER TABLE "contracts" ALTER COLUMN "effectiveDate" TYPE date`);
        await queryRunner.query(`ALTER TABLE "contracts" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "contracts" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "delivery_note_child" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "delivery_note_child" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "bank_branch" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "bank_branch" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "bank" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "bank" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "delivery_note_child_detail" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "delivery_note_child_detail" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "action_log" ALTER COLUMN "updatedAt" TYPE TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "action_log" ALTER COLUMN "createdAt" TYPE TIMESTAMP`);
    }
}
