import { Injectable, ConflictException, NotFoundException } from '@nestjs/common'
import { NewsCategoryRepository } from '../../../repositories'
import { ERROR_NOT_FOUND_DATA, ERROR_CODE_TAKEN, UPDATE_ACTIVE_SUCCESS, CREATE_SUCCESS, UPDATE_SUCCESS, IMPORT_SUCCESS } from '../../../constants'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { In, Like } from 'typeorm'
import { NewsCategoryCreateDto, NewsCategoryUpdateDto } from './dto'
import { NewsCategoryEntity } from '../../../entities'

@Injectable()
export class NewsCategoryService {
  constructor(private readonly repo: NewsCategoryRepository) {}

  public async find(data: any) {
    const whereCon: any = { isDeleted: false }
    if (data.name) whereCon.name = Like(`%${data.name}%`)
    if (data.code) whereCon.code = Like(`%${data.code}%`)
    if (data.lstId?.length > 0) whereCon.id = In(data.lstId)
    if (data.lstCode?.length > 0) whereCon.code = In(data.lstCode)
    if (data.type) whereCon.type = data.type
    return await this.repo.find({ where: whereCon, order: { name: 'ASC' } })
  }

  public async createData(user: UserDto, data: NewsCategoryCreateDto) {
    const checkCodeExist = await this.repo.findOne({ where: { code: data.code } })
    if (checkCodeExist) throw new ConflictException(ERROR_CODE_TAKEN)
    const newEntity = this.repo.create({ ...data })
    newEntity.createdBy = user.id
    newEntity.createdAt = new Date()
    await this.repo.insert(newEntity)
    return { message: CREATE_SUCCESS }
  }

  public async updateData(user: UserDto, data: NewsCategoryUpdateDto) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    if (entity.code != data.code) {
      const checkCodeExist = await this.repo.findOne({ where: { code: data.code } })
      if (checkCodeExist) throw new ConflictException(ERROR_CODE_TAKEN)
    }
    entity.name = data.name
    entity.code = data.code
    entity.type = data.type
    entity.updatedAt = new Date()
    entity.updatedBy = user.id
    await this.repo.update(entity.id, entity)
    return { message: UPDATE_SUCCESS }
  }

  public async pagination(data: PaginationDto) {
    let whereCon: any = {}
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    if (data.where.type) whereCon.type = data.where.type
    const result = await this.repo.findAndCount({
      where: whereCon,
      order: { createdAt: 'DESC', code: 'DESC' },
      skip: data.skip,
      take: data.take,
    })
    return result
  }

  public async updateIsDelete(id: string, user: UserDto) {
    const entity = await this.repo.findOne({ where: { id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    await this.repo.update({ id: entity.id }, { isDeleted: !entity.isDeleted })
    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  async findOne(data: FilterOneDto) {
    const whereCon: any = { id: data.id, isDeleted: false }
    return await this.repo.findOneBy(whereCon)
  }

  public async createDataExcel(data: NewsCategoryCreateDto[], user: UserDto) {
    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(NewsCategoryEntity)
      const lstInsert: NewsCategoryEntity[] = []
      const dicCode: any = {}
      {
        const lstItemCategory: any[] = await repo.find({
          where: { isDeleted: false },
          select: { id: true, code: true },
        })
        lstItemCategory.forEach((c) => (dicCode[c.code] = c))
      }
      const dicCodeFile: any = {}
      for (const [idx, item] of data.entries()) {
        if (dicCodeFile[item.code]) throw new Error(`[ Dòng ${idx + 1} - Mã Loại item [${item.code}] trùng với dòng ${dicCodeFile[item.code]} ]`)
        if (dicCode[item.code]) throw new Error(`[ Dòng ${idx + 1} - Mã Loại item [${item.code}] đã được sử dụng ]`)
        const newItemCategory = repo.create({
          ...item,
          createdAt: new Date(),
          createdBy: user.id,
        })
        lstInsert.push(newItemCategory)
        dicCodeFile[item.code] = idx + 1
      }
      await repo.insert(lstInsert)
    })
    return { message: IMPORT_SUCCESS }
  }
}
