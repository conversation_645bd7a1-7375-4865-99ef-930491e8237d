import { Controller, UseGuards, Post, Body, UseInterceptors, UploadedFile, Query, Req, Get, Res } from '@nestjs/common'
import { JwtAuthGuard } from '../../common/guards'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { PurchaseOrderService } from './purchaseOrder.service'
import {
  CreatePOFromSODto,
  CreatePurchaseOrderDto,
  DetailPurchaseOrderDto,
  DistributeViewPODto,
  ExportPoSupplierDto,
  ImportPoRefDto,
  ListHistoryPoDto,
  ListPoDto,
  ListPoItemDto,
  ListPOwithSODto,
  UpdatePurchaseOrderDto,
  UploadPreOrderPODto,
} from './dto'
import { FileInterceptor } from '@nestjs/platform-express'
import { Request as IRequest, Response } from 'express'
import { UpdateStatusPurchaseOrderDto } from './dto/updateStatus.dto'
import { CurrentUser } from '../../common'
import { UserDto } from '../../../dto'

@ApiBearerAuth()
@ApiTags('Purchase Order')
@UseGuards(JwtAuthGuard)
@Controller('purchase-order')
export class PurchaseOrderController {
  constructor(private readonly service: PurchaseOrderService) { }

  @Post('list-po-item')
  public async findPoItems(@Body() params: ListPoItemDto) {
    return await this.service.findPoItems(params)
  }

  @Post('list')
  public async find(@Body() params: ListPoDto) {
    return await this.service.findAll(params)
  }

  @Post('list-supplier-po')
  public async findSupplierPo(@Body() params: ListPoDto) {
    return await this.service.findSupplierPo(params)
  }

  // Danh sách SO tham chiếu của 1 PO
  @Post('list-so-by-po')
  public async findSoByPo(@Body() body: ListPOwithSODto, @Req() req: IRequest) {
    return await this.service.findSoByPo(body, req)
  }

  @Post('list-partner')
  public async findPoWithPartner(@Body() params: ListPoDto) {
    return await this.service.findPurchaseOrderWithPartner(params)
  }

  @Post('list-partner-cancel')
  public async findPoWithPartnerCancel(@Body() params: ListPoDto) {
    return await this.service.findPurchaseOrderWithPartnerCancel(params)
  }

  @Get('list-history')
  public async find_history(@Query() params: ListHistoryPoDto) {
    return await this.service.findHistory(params)
  }

  @Get('detail')
  public async detail(@Query() queries: DetailPurchaseOrderDto) {
    return await this.service.detail(queries)
  }

  @Post('create')
  public async create(@Body() data: CreatePurchaseOrderDto, @Req() req: IRequest) {
    return await this.service.createPO(data, req)
  }

  @Post('update')
  public async update(@Body() data: UpdatePurchaseOrderDto, @Req() req: IRequest) {
    return await this.service.updatePO(data, req)
  }

  @Get('send-purchase-order')
  public async sendPurchaseOrder(@Query() query: UpdateStatusPurchaseOrderDto, @Req() req: IRequest) {
    return await this.service.sendPurchaseOrder(query, req)
  }

  @Get('approve-purchase-order')
  public async approvePurchaseOrder(@Query() query: UpdateStatusPurchaseOrderDto, @Req() req: IRequest, @CurrentUser() user: UserDto) {
    return await this.service.approvePurchaseOrder(query, req, user)
  }

  @Post('upload-combo-overview')
  @UseInterceptors(FileInterceptor('file'))
  public async importComboToOverview(@UploadedFile() file: Express.Multer.File, @Req() req: IRequest) {
    return await this.service.importComboOverview(file, req)
  }

  @Post('upload-item-manual')
  @UseInterceptors(FileInterceptor('file'))
  public async importItemManual(@UploadedFile() file: Express.Multer.File, @Req() req: IRequest) {
    return await this.service.importItemOverview(file, req)
  }

  @Post('upload-combo-manual')
  @UseInterceptors(FileInterceptor('file'))
  public async importComboManual(@UploadedFile() file: Express.Multer.File, @Req() req: IRequest) {
    return await this.service.importComboManual(file, req)
  }

  @Post('upload-combo-so')
  public async importComboFroSO(@Body() body: CreatePOFromSODto, @Req() req: IRequest) {
    return await this.service.importSO(body, req)
  }

  @Post('upload-manual-po')
  public async uploadManualPO(@Body() body: UploadPreOrderPODto, @Req() req: IRequest) {
    return await this.service.uploadManualPO(body, req)
  }

  @Post('view-detail-distribute')
  public async detailDistribute(@Body() data: DistributeViewPODto) {
    return await this.service.detailDistributePurchaseOrder(data)
  }

  /** Hủy PO */
  @Post('cancel-purchase-order')
  public async cancelPurchaseOrder(@Body() data: UpdateStatusPurchaseOrderDto, @Req() req: IRequest) {
    return await this.service.cancelPurchaseOrder(data, req)
  }

  /** Không duyệt PO */
  @Post('reject-purchase-order')
  public async rejectPurchaseOrder(@Body() data: UpdateStatusPurchaseOrderDto, @Req() req: IRequest) {
    return await this.service.rejectPurchaseOrder(data, req)
  }

  /** Tạo PO từ file excel */
  @ApiOperation({ summary: 'Tạo PO từ file excel' })
  @Post('upload-po-supplier')
  @UseInterceptors(FileInterceptor('file'))
  public async importPoSupplier(@UploadedFile() file: Express.Multer.File, @Req() req: IRequest, @CurrentUser() user: UserDto) {
    const { id } = user
    return await this.service.importPoSupplier(file, req, id)
  }

  @ApiOperation({ summary: 'Gửi thông báo đơn hàng' })
  @Post('send_po_notification')
  public async sendPoNotification(@Body() data: any, @CurrentUser() user: UserDto) {
    return await this.service.sendPoNotification(data.id, user)
  }

  //export data po NCC
  @Post('export-po-supplier')
  public async exportPoSupplier(@Body() data: ExportPoSupplierDto, @Req() req: IRequest) {
    return await this.service.exportPoSupplier(data, req)
  }


  // Read Summary PO Ref
  @Post('import-po-ref')
  public async importPoRef(@Body() data: ImportPoRefDto, @Req() req: IRequest) {
    return await this.service.importPoRef(data, req)
  }

  //test API
  // @Post('test')
  // public async testPoPms(@Res() res: Response, @Req() req: IRequest) {
  //   res.setHeader('Content-Type', 'text/plain')
  //   res.setHeader('Transfer-Encoding', 'chunked')
  //   let percent = 0
  //   const interval = setInterval(() => {
  //     percent += 10
  //     res.write(`Progress: ${percent}%\n`)
  //     if (percent >= 100) {
  //       clearInterval(interval)
  //       res.end('Done\n')
  //     }
  //   }, 1000)

  //   // return this.service.syncPoPMS(po.poId, req)
  // }
}
