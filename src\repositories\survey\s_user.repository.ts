import { Raw, Repository } from 'typeorm'
import { CustomRepository } from '../../typeorm'
import { UserSurveyEntity } from '../../entities'

@CustomRepository(UserSurveyEntity)
export class UserSurveyRepository extends Repository<UserSurveyEntity> {
  findByUserName(username: string) {
    let where: any = {}
    where.username = Raw((alias) => ` ${alias} =  '${username}'`)
    return this.findOne({ where })
  }
}
