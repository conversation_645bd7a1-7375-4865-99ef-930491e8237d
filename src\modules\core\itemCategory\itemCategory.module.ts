import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import { ItemCategoryController } from './itemCategory.controller'
import { ItemCategoryRepository, ItemGroupRepository, ItemTypeRepository, MediaRepository } from '../../../repositories'
import { ItemCategoryService } from './itemCategory.service'
import { ItemCategoryPublicController } from './itemCategoryPublic.controller'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([ItemCategoryRepository, ItemTypeRepository, MediaRepository, ItemGroupRepository])],
  controllers: [ItemCategoryController, ItemCategoryPublicController],
  providers: [ItemCategoryService],
  exports: [ItemCategoryService],
})
export class ItemCategoryModule { }
