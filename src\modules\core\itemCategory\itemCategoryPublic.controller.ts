import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { JwtAuthGuard } from '../../common/guards'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { CurrentUser } from '../../common/decorators'
import { CategoryWithTypeLstDto, ItemCategoryCreateDto, ItemCategoryUpdateDto } from './dto'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { ItemCategoryService } from './itemCategory.service'
import { ItemCategoryCreateExcelDto } from './dto/itemCategoryCreateExcel.dto'
import { AuthGuard } from '../../common/guards/auth.guard'

@ApiTags('ItemCategory')
@Controller('item_category_public')
export class ItemCategoryPublicController {
  constructor(private readonly service: ItemCategoryService) { }

  @Post('find')
  public async find(@Body() data: any) {
    return await this.service.find(data)
  }

  /** L<PERSON>y ra danh sách Category Item (cấp 2) và Group Item (cấp 3) bằng item type  */
  @Post('find-category-by-type')
  public async findCategory(@Body() data: CategoryWithTypeLstDto) {
    const { typeId } = data
    return await this.service.findCategoryByType(typeId)
  }

  @Post('pagination')
  public async pagination(@Body() data: PaginationDto) {
    return await this.service.pagination(data)
  }

  @Post('find_one')
  public async findOne(@Body() data: FilterOneDto) {
    return await this.service.findOne(data)
  }

}
