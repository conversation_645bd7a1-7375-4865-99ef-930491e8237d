import { Module } from '@nestjs/common'

import { CompanyController } from './company.controller'
import { CompanyService } from './company.service'
import { QuestionModule } from '../question/question.module'
import { TypeOrmExModule } from '../../../typeorm'
import { CategoriesRepository, CompanyCategoriesRepository, CompanyRepository, TopicRepository, UserRepository } from '../../../repositories'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([CompanyRepository, UserRepository, CompanyCategoriesRepository, CategoriesRepository, TopicRepository]),
    QuestionModule,
  ],
  controllers: [CompanyController],
  providers: [CompanyService],
})
export class CompanyModule {}
