import { Module } from '@nestjs/common'
import {
  DepartmentRepository,
  EmployeeRepository,
  NotifyRepository,
  QuestionRepository,
  SurveyHistoryRepository,
  SurveyMemberRepository,
  SurveyQuestionListDetailRepository,
  SurveyQuestionRepository,
  SurveyRepository,
  TopicRepository,
  UserRepository,
  UserSurveyRepository,
} from '../../../repositories'
import { TypeOrmExModule } from '../../../typeorm'
import { AuthModule } from '../../core/auth/auth.module'
import { CategoriesModule } from '../categories/categories.module'
import { TopicModule } from '../topic/topic.module'
import { MobileAppController } from './mobileApp.controller'
import { MobileAppService } from './mobileApp.service'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      SurveyRepository,
      SurveyMemberRepository,
      SurveyQuestionRepository,
      QuestionRepository,
      SurveyHistoryRepository,
      EmployeeRepository,
      TopicRepository,
      DepartmentRepository,
      NotifyRepository,
      UserRepository,
      UserSurveyRepository,
      SurveyQuestionListDetailRepository,
    ]),
    CategoriesModule,
    TopicModule,
    AuthModule,
  ],
  controllers: [MobileAppController],
  providers: [MobileAppService],
  exports: [MobileAppService],
})
export class MobileAppModule {}
