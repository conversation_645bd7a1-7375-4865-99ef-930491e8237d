import { enumData } from '../constants/enumData'

class ArrayHelper {
  public groupByArray(data: any, key: any) {
    const groupedObj = data.reduce((prev: any, cur: any) => {
      if (!prev[cur[key]]) {
        prev[cur[key]] = [cur]
      } else {
        prev[cur[key]].push(cur)
      }
      return prev
    }, {})
    return Object.keys(groupedObj).map((Heading) => ({
      heading: Heading,
      list: groupedObj[Heading],
    }))
  }

  public sumArray(arr: any[], prop: string) {
    let total = 0
    for (let i = 0, len = arr.length; i < len; i++) {
      total += parseFloat(arr[i][prop])
    }
    return total
  }

  public convertObjToArray(obj: any) {
    const arr = []
    // tslint:disable-next-line:forin
    for (const key in obj) {
      const value = obj[key]
      arr.push(value)
    }
    return arr
  }

  public getEnumName(code: string, enumDataType: any) {
    const data = this.convertObjToArray(enumDataType)
    const item = data.find((p) => p.code === code)
    return item ? item.name : ''
  }

  public getOrderStatusName(code: string) {
    return this.getEnumName(code, enumData.OrderStatus)
  }

  public getFuncitionTypeName(code: string) {
    return this.getEnumName(code, enumData.ActionLog)
  }

  public getTypeName(code: string) {
    return this.getEnumName(code, enumData.Type)
  }

  public getPaymentName(code: string) {
    return this.getEnumName(code, enumData.PaymentStatus)
  }
}

export const arrayHelper = new ArrayHelper()
