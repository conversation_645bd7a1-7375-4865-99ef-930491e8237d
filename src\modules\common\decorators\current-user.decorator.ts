import { createParamDecorator, ExecutionContext } from '@nestjs/common'
import { plainToClass } from 'class-transformer'
import { SupplierDto, UserDto } from '../../../dto'

/** <PERSON><PERSON><PERSON> thông tin user đang request */
export const CurrentUser = createParamDecorator((data: string, ctx: ExecutionContext) => {
  const request = ctx.switchToHttp().getRequest()
  return plainToClass(UserDto, request.user)
})

export const CurrentUserPortal = createParamDecorator((data: string, ctx: ExecutionContext) => {
  const request = ctx.switchToHttp().getRequest()
  return plainToClass(SupplierDto, request.user)
})
