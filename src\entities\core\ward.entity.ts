import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn, OneToMany } from 'typeorm'
import { DistrictEntity } from './district.entity'
import { SupplierEntity } from './supplier.entity'

@Entity('ward')
export class WardEntity extends BaseEntity {
  @Column({ type: 'varchar', length: 50, nullable: false })
  code: string

  @Column({ type: 'varchar', length: 250, nullable: false })
  name: string

  @Column({ type: 'varchar', nullable: false })
  districtId: string
  @ManyToOne(() => DistrictEntity, (p) => p.wards)
  @JoinColumn({ name: 'districtId', referencedColumnName: 'id' })
  district: Promise<DistrictEntity>

  @OneToMany((type) => SupplierEntity, (p) => p.ward)
  suppliers: Promise<SupplierEntity[]>
}
