import { Module } from '@nestjs/common'
import { SupplierController } from './supplier.controller'
import { SupplierService } from './supplier.service'
import { ActionLogModule } from '../actionLog/actionLog.module'
import { TypeOrmExModule } from '../../../typeorm'
import {
  CityRepository,
  DistrictRepository,
  OperationalAreaRepository,
  PartnerMapRepository,
  SupplierRepository,
  UserRepository,
  WarehouseRepository,
} from '../../../repositories'
import { SupplierPublicController } from './supplierPublic.controller'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      SupplierRepository,
      PartnerMapRepository,
      WarehouseRepository,
      CityRepository,
      DistrictRepository,
      OperationalAreaRepository,
      UserRepository,
    ]),
    ActionLogModule,
  ],
  controllers: [SupplierController, SupplierPublicController],
  providers: [SupplierService],
  exports: [SupplierService],
})
export class SupplierModule {}
