import { ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString } from 'class-validator'

export class PartyDto {
  @ApiPropertyOptional({ description: 'Member id' })
  @IsOptional()
  id?: string
  @ApiPropertyOptional({ description: 'Member id' })
  @IsOptional()
  memberId?: string
  @ApiPropertyOptional({ description: 'Tên đối tác' })
  @IsOptional()
  fullName: string
  @ApiPropertyOptional({ description: 'CCCD' })
  @IsOptional()
  personalId: string
  @ApiPropertyOptional({ description: 'Mã số thuế (Mã số doanh nghiệp) đối tác' })
  @IsOptional()
  taxCode: string
  @ApiPropertyOptional({ description: 'Số điện thoại đối tác' })
  @IsOptional()
  phone: string
  @ApiPropertyOptional({ description: 'Địa chỉ đối tác' })
  @IsOptional()
  address: string
  @ApiPropertyOptional({ description: 'Mã phường xã' })
  @IsOptional()
  wardCode: string
  @ApiPropertyOptional({ description: 'Mã quận huyện' })
  @IsOptional()
  districtCode: string
  @ApiPropertyOptional({ description: 'Mã tỉnh thành' })
  @IsOptional()
  provinceCode: string

  @ApiPropertyOptional({ description: 'Người đại diện đối tác' })
  @IsOptional()
  representative: string
  @ApiPropertyOptional({ description: 'Chức vụ đối tác' })
  @IsOptional()
  position: string
  @ApiPropertyOptional({ description: 'Ngân hàng' })
  @IsOptional()
  bankCode: string
  @ApiPropertyOptional({ description: 'Tên chủ tài khoản' })
  @IsOptional()
  bankAccountNumber: string
  @ApiPropertyOptional({ description: 'Số tài khoản đối tác' })
  @IsOptional()
  bankAccountName: string
  @ApiPropertyOptional({ description: 'Fax' })
  @IsOptional()
  fax: string
  @ApiPropertyOptional({ description: 'Email' })
  @IsOptional()
  email: string
  @ApiPropertyOptional({ description: 'Bộ phận' })
  @IsOptional()
  part: string
}

export class ComboDto {
  @ApiPropertyOptional({ description: 'Id combo' })
  @IsOptional()
  comboId: string

  @ApiPropertyOptional({ description: 'Tên combo' })
  @IsOptional()
  comboName: string

  @ApiPropertyOptional({ description: 'Ngày giao hàng của combo' })
  @IsOptional()
  date: number

  @ApiPropertyOptional({ description: 'Giá combo' })
  @IsOptional()
  price: number
}

export class CardDto {
  @ApiPropertyOptional({ description: 'Id thẻ' })
  @IsOptional()
  id: string

  @ApiPropertyOptional({ description: 'Id loại thẻ' })
  @IsOptional()
  typeId: string
  @ApiPropertyOptional({ description: 'Id kỳ thẻ' })
  @IsOptional()
  periodId: string
  @ApiPropertyOptional({ description: 'Id thời hạn thẻ' })
  @IsOptional()
  durationId: string

  @ApiPropertyOptional({ description: 'Tên thẻ' })
  @IsOptional()
  name: string

  @ApiPropertyOptional({ description: 'Danh sách combo' })
  @IsOptional()
  lstCombo: ComboDto[]
}

export class ContractGenerateDto {
  @ApiPropertyOptional({ description: 'Bên B' })
  @IsOptional()
  party: PartyDto

  @ApiPropertyOptional({ description: 'Loại hình hợp đồng điện tử (COMPANY/ PERSONAL)' })
  @IsOptional()
  eContractType: string

  @ApiPropertyOptional({ description: 'DS Mã Dịch vụ' })
  @IsOptional()
  mediaCodes: string[]

  @ApiPropertyOptional({ description: 'Là cộng tác viên' })
  @IsOptional()
  isCTV: boolean

  @ApiPropertyOptional({ description: 'Mã giới thiệu' })
  @IsOptional()
  referralCode: string

  @ApiPropertyOptional({ description: 'Số điện thoại người giới thiệu' })
  @IsOptional()
  invitePhone?: string

  @ApiPropertyOptional({ description: 'Thẻ' })
  @IsOptional()
  card: CardDto

  @ApiPropertyOptional({ description: 'Id đối tác' })
  @IsOptional()
  partnerId: string
}
