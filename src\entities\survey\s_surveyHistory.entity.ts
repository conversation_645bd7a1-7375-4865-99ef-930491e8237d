import { Enti<PERSON>, Column, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, <PERSON>ToOne, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '../core/base.entity'
import { SurveyEntity } from './s_survey.entity'
import { TopicEntity } from './s_topic.entity'
import { CategoriesEntity } from './s_categories.entity'

/** Lịch sử survey */
@Entity('s_survey_history')
export class SurveyHistoryEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string

  /** Trạng thái hiện tại */
  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  status: string

  /** Id phiếu khảo sát */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  surveyId: string
  @ManyToOne(() => SurveyEntity, (p) => p.histories)
  @JoinColumn({ name: 'surveyId', referencedColumnName: 'id' })
  survey: Promise<SurveyEntity>

  /** Id chủ đề khả<PERSON> sát */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  topicId: string
  @ManyToOne(() => TopicEntity, (p) => p.histories)
  @JoinColumn({ name: 'topicId', referencedColumnName: 'id' })
  topic: Promise<TopicEntity>

  /** Id danh mục */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  categoryId: string
  @ManyToOne(() => CategoriesEntity, (p) => p.histories)
  @JoinColumn({ name: 'categoryId', referencedColumnName: 'id' })
  category: Promise<CategoriesEntity>

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  description: string
}
