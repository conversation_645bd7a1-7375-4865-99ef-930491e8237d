import { Injectable } from '@nestjs/common'
import { Request as IRequest } from 'express'
import * as moment from 'moment'
import { customAlphabet } from 'nanoid'
import { Between, In, IsNull, LessThanOrEqual, Like, MoreThanOrEqual, Not } from 'typeorm'
import { NotifyCreateDto } from '../notify/dto'
import { NotifyService } from '../notify/notify.service'
import { SendAllNotifyDto, SendNotifyDto } from './dto/sendNotify.dto'
import { SurveyCreateDto } from './dto/surveyCreate.dto'
import { SurveyUpdateDto } from './dto/surveyUpdate.dto'
import { v4 as uuidv4 } from 'uuid'
import {
  EmployeeRepository,
  SurveyHistoryRepository,
  SurveyMemberRepository,
  SurveyQuestionListDetailRepository,
  SurveyQuestionRepository,
  SurveyRepository,
  TopicRepository,
  UserRepository,
  UserSurveyRepository,
} from '../../../repositories'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { CREATE_SUCCESS, enumData, ERROR_NOT_FOUND_DATA, UPDATE_ACTIVE_SUCCESS, UPDATE_SUCCESS } from '../../../constants'
import { coreHelper } from '../../../helpers'
import {
  SurveyEntity,
  SurveyHistoryEntity,
  SurveyMemberEntity,
  SurveyQuestionEntity,
  SurveyQuestionListDetailEntity,
  TopicEntity,
} from '../../../entities/survey'
import { DataPipeline } from 'aws-sdk'
import { SurveyCreateExcelDto } from './dto/surveyCreateExcel.dto'
const nanoid = customAlphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890', 10)
var dateNow = new Date(new Date().setHours(0, 0, 0, 0))

@Injectable()
export class SurveyService {
  constructor(
    private repo: SurveyRepository,
    private surveyMemberRepo: SurveyMemberRepository,
    private surveyQuestionRepo: SurveyQuestionRepository,
    private surveyHistoryRepo: SurveyHistoryRepository,
    private employeeRepo: EmployeeRepository,
    private topicRepo: TopicRepository,
    private notifyService: NotifyService,
    private readonly userRepo: UserRepository,
    private surveyQuestionListDetailRepo: SurveyQuestionListDetailRepository,
  ) {}

  /** Phân trang phiếu khảo sát */
  public async pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = {}

    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.createdAt) whereCon.createdAt = data.where.createdAt
    if (data.where.status) whereCon.status = data.where.status

    if (data.where.timeStart && data.where.timeEnd) {
      whereCon.timeStart = MoreThanOrEqual(new Date(data.where.timeStart))
      whereCon.timeEnd = LessThanOrEqual(new Date(data.where.timeEnd))
    } else if (data.where.timeStart) {
      whereCon.timeStart = MoreThanOrEqual(new Date(data.where.timeStart))
    } else if (data.where.timeEnd) {
      whereCon.timeEnd = LessThanOrEqual(new Date(data.where.timeEnd))
    }

    if (data.where.topicId) {
      const surveyQuestionRecords = await this.surveyQuestionRepo.find({
        where: { topicId: data.where.topicId },
        select: {
          surveyId: true,
        },
      })

      const surveyIds = surveyQuestionRecords.map((q) => q.surveyId)
      if (surveyIds.length > 0) {
        whereCon.id = In(surveyIds)
      } else {
        return [[], 0]
      }
    }

    if (data.where.categoryId) {
      const lstTopic = await this.topicRepo.find({ where: { categoryId: data.where.categoryId } })
      if (lstTopic && lstTopic.length > 0) {
        const surveyQuestionRecords = await this.surveyQuestionRepo.find({
          where: { topicId: In(lstTopic.map((x) => x.id)) },
          select: {
            surveyId: true,
          },
        })

        const surveyIds = surveyQuestionRecords.map((q) => q.surveyId)
        if (surveyIds.length > 0) {
          whereCon.id = In(surveyIds)
        } else {
          return [[], 0]
        }
      }
    }

    const resSurvey: any = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      relations: { questions: true, members: true },
      order: { createdAt: 'DESC' },
    })

    let lstStatus = coreHelper.convertObjToArray(enumData.SurveyStatus)
    for (const item of resSurvey[0]) {
      let findTopic = null
      if (data.where.topicId) findTopic = item.__questions__.find((question: any) => question.topicId == data.where.topicId)
      else {
        if (item.__questions__.length <= 0) continue
        findTopic = item.__questions__[0]
      }
      item.topicCount = findTopic ? 1 : 0
      item.memberCount = item.__members__.filter((member: any) => !member.isDeleted).length
      item.memberDoneCount = item.__members__.filter(
        (member: any) => !member.isDeleted && member.status === enumData.SurveyMemberStatus.Done.code,
      ).length
      item.lstMember = item.__members__.map((x) => x.userId)
      item.categoryId = findTopic?.categoryId
      item.topicId = findTopic?.topicId

      const topic = await this.topicRepo.findOne({ where: { id: item.topicId } })
      const category = await topic.category
      item.categoryName = category?.name
      item.topicName = topic?.name

      item.numberAnswer = item.__questions__
        .filter((q: any) => q.surveyMemberId && q.topicId === topic.id)
        .reduce((acc: Set<string>, cur: any) => {
          acc.add(JSON.stringify(cur.createdAt))
          return acc
        }, new Set()).size

      item.showBtnCancel = item.status === enumData.SurveyStatus.New.code
      item.showBtnDoing = item.status === enumData.SurveyStatus.New.code
      item.showBtnComplete = item.status === enumData.SurveyStatus.Doing.code
      item.showBtnEdit = item.status === enumData.SurveyStatus.New.code
      item.showBtnEmployee = true
      item.showBtnDone = item.status === enumData.SurveyStatus.Complete.code
      delete item.__questions__
      delete item.__members__

      const statusItem = lstStatus.find((x) => x.code === item.status)
      if (statusItem) {
        item.statusName = statusItem.name
        item.statusColor = statusItem.color
      }
    }

    return resSurvey
  }

  public async findAll(data: any) {
    let whereCon: any = { isDeleted: false }
    if (data.where.topicId) {
      whereCon.questions = {}
      whereCon.questions.topicId = data.where.topicId
    }
    if (data.where.status) {
      whereCon.status = data.where.status
    }
    return await this.repo.find({
      where: whereCon,
      relations: {
        questions: true,
      },
    })
  }

  /** Tạo mới phiếu khảo sát */
  public async createData(user: UserDto, data: SurveyCreateDto, req: IRequest) {
    const date = new Date()
    const now = new Date([date.getFullYear(), date.getMonth() + 1, date.getDate()].join('-')).getTime()
    if (now > new Date(data.timeStart).getTime() || now > new Date(data.timeEnd).getTime()) {
      throw new Error('Không thể chọn thời gian nhỏ hơn hiện tại')
    }
    if (new Date(data.timeStart).getTime() > new Date(data.timeEnd).getTime()) {
      throw new Error('Thời gian bắt đầu không thể nhỏ hơn thời gian kết thúc')
    }
    let lstNotify = []
    await this.repo.manager.transaction(async (trans) => {
      const survey = new SurveyEntity()
      survey.code = 'SURVEY_' + `${nanoid()}`
      survey.name = data.name
      survey.timeStart = data.timeStart
      survey.timeEnd = data.timeEnd
      survey.description = data.description
      survey.createdAt = new Date()
      survey.status = enumData.SurveyStatus.New.code
      let newEntity = await trans.getRepository(SurveyEntity).save(survey)

      if (data.topicId) {
        const topicItem = await trans.getRepository(TopicEntity).findOne({
          where: { id: data.topicId, isDeleted: false },
          relations: { questions: { questionlistDetails: true, childs: true } },
        })
        if (!topicItem) throw new Error('Chủ đề không còn tồn tại!. Vui lòng kiểm tra lại.')
        let lstTopicQuestion = (await topicItem.questions).filter((x) => x.isDeleted == false)
        if (lstTopicQuestion.length === 0) throw new Error('Chủ đề chưa được thiết lập câu hỏi !. Vui lòng kiểm tra lại.')
        const lstSurveyQuestionListDetail: SurveyQuestionListDetailEntity[] = []
        const lstSurveyQuestion: SurveyQuestionEntity[] = []
        const lstQuestionParent = lstTopicQuestion.filter((x) => x.parentId === null)
        for (const qs of lstQuestionParent) {
          const surveyQuestionListDetail = new SurveyQuestionListDetailEntity()
          surveyQuestionListDetail.name = qs.name
          surveyQuestionListDetail.questionListDetail = JSON.stringify(
            (await qs.questionlistDetails)
              .filter((x) => x.isDeleted === false)
              .map((x) => ({
                id: x.id,
                name: x.name,
                value: x.value,
                sort: x.sort,
              })),
          )
          surveyQuestionListDetail.surveyId = newEntity.id
          surveyQuestionListDetail.type = qs.type
          surveyQuestionListDetail.isRequired = qs.isRequired
          surveyQuestionListDetail.level = qs.level
          surveyQuestionListDetail.sort = qs.sort
          surveyQuestionListDetail.parentId = qs.parentId
          surveyQuestionListDetail.topicId = topicItem.id
          surveyQuestionListDetail.createdAt = date
          surveyQuestionListDetail.id = uuidv4()
          lstSurveyQuestionListDetail.push(surveyQuestionListDetail)

          const lstChild = await qs.childs
          for (let child of lstChild) {
            const surveyQuestionListDetailChild = new SurveyQuestionListDetailEntity()
            surveyQuestionListDetailChild.name = child.name
            surveyQuestionListDetailChild.questionListDetail = JSON.stringify(
              (await child.questionlistDetails)
                .filter((x) => x.isDeleted === false)
                .map((x) => ({
                  id: x.id,
                  name: x.name,
                  value: x.value,
                  sort: x.sort,
                })),
            )
            surveyQuestionListDetailChild.surveyId = newEntity.id
            surveyQuestionListDetailChild.type = child.type
            surveyQuestionListDetailChild.isRequired = child.isRequired
            surveyQuestionListDetailChild.level = child.level
            surveyQuestionListDetailChild.sort = child.sort
            surveyQuestionListDetailChild.parentId = surveyQuestionListDetail.id
            surveyQuestionListDetailChild.topicId = topicItem.id
            surveyQuestionListDetailChild.createdAt = date
            surveyQuestionListDetailChild.id = uuidv4()
            lstSurveyQuestionListDetail.push(surveyQuestionListDetailChild)

            const surveyQuestionChild = new SurveyQuestionEntity()
            surveyQuestionChild.surveyId = newEntity.id
            surveyQuestionChild.questionId = surveyQuestionListDetailChild.id
            surveyQuestionChild.topicId = topicItem.id
            surveyQuestionChild.createdAt = date
            lstSurveyQuestion.push(surveyQuestionChild)
          }

          const surveyQuestion = new SurveyQuestionEntity()
          surveyQuestion.surveyId = newEntity.id
          surveyQuestion.questionId = surveyQuestionListDetail.id
          surveyQuestion.topicId = topicItem.id
          surveyQuestion.createdAt = date
          lstSurveyQuestion.push(surveyQuestion)
        }
        await trans.getRepository(SurveyQuestionListDetailEntity).insert(lstSurveyQuestionListDetail)
        await trans.getRepository(SurveyQuestionEntity).insert(lstSurveyQuestion)
      }

      const history = new SurveyHistoryEntity()
      history.surveyId = newEntity.id
      history.createdAt = new Date()
      history.createdBy = user.id
      history.status = enumData.SurveyStatus.New.code
      history.description = `Nhân viên [${user.username}] vừa thêm mới phiếu khảo sát có mã là [${newEntity.code}] `
      await trans.getRepository(SurveyHistoryEntity).save(history)
    })
    await this.notifyService.createData(user, lstNotify, req)
    return { message: CREATE_SUCCESS }
  }

  /** Cập nhật phiếu khảo sát */
  public async updateData(user: UserDto, data: SurveyUpdateDto) {
    await this.repo.manager.transaction(async (trans) => {
      const entity = await trans.getRepository(SurveyEntity).findOne({ where: { isDeleted: false, id: data.id } })
      const today = new Date()
      if (!entity) throw new Error('Phiếu khảo sát không còn tồn tại!')
      if (entity.status === enumData.SurveyStatus.Cancel.code || entity.status === enumData.SurveyStatus.Complete.code)
        throw new Error('Phiếu khảo sát đã bị hủy hoặc hoàn thành. Không thể cập nhật được. Vui lòng kiểm tra lại !')

      if (entity.status === enumData.SurveyStatus.Doing.code)
        throw new Error('Phiếu khảo sát thực hiện. Không thể cập nhật được. Vui lòng kiểm tra lại !')

      //entity.companyId = user.companyId
      entity.name = data.name
      entity.description = data.description
      entity.timeStart = data.timeStart
      entity.timeEnd = data.timeEnd
      entity.updatedAt = new Date()
      entity.updatedBy = user?.id
      await trans.getRepository(SurveyQuestionEntity).delete({ surveyId: entity.id })
      await trans.getRepository(SurveyMemberEntity).delete({ surveyId: entity.id })

      const history = new SurveyHistoryEntity()
      history.surveyId = entity.id
      history.createdAt = new Date()
      history.createdBy = user?.id
      history.description = `Nhân viên [${user?.username}] vừa cập nhật phiếu khảo sát có mã là [${entity.code}] `
      history.status = enumData.SurveyStatus.New.code
      await trans.getRepository(SurveyHistoryEntity).save(history)

      await trans.getRepository(SurveyQuestionListDetailEntity).delete({ surveyId: entity.id })
      const topicItem = await trans
        .getRepository(TopicEntity)
        .findOne({ where: { id: data.topicId, isDeleted: false }, relations: { questions: true } })
      if (!topicItem) throw new Error('Chủ đề không còn tồn tại!. Vui lòng kiểm tra lại.')
      let lstTopicQuestion = await topicItem.questions
      if (lstTopicQuestion.length === 0) throw new Error('Chủ đề chưa được thiết lập câu hỏi !. Vui lòng kiểm tra lại.')
      const lstSurveyQuestionListDetail: SurveyQuestionListDetailEntity[] = []
      const lstSurveyQuestion: SurveyQuestionEntity[] = []
      const lstQuestionParent = lstTopicQuestion.filter((x) => x.parentId === null)
      for (const qs of lstTopicQuestion) {
        const surveyQuestionListDetail = new SurveyQuestionListDetailEntity()
        surveyQuestionListDetail.createdAt = today
        surveyQuestionListDetail.surveyId = entity.id
        surveyQuestionListDetail.topicId = topicItem.id
        surveyQuestionListDetail.name = qs.name
        surveyQuestionListDetail.questionListDetail = JSON.stringify(
          (await qs.questionlistDetails)
            .filter((x) => x.isDeleted === false)
            .map((x) => ({
              name: x.name,
              value: x.value,
              sort: x.sort,
            })),
        )
        surveyQuestionListDetail.type = qs.type
        surveyQuestionListDetail.isRequired = qs.isRequired
        surveyQuestionListDetail.level = qs.level
        surveyQuestionListDetail.sort = qs.sort
        surveyQuestionListDetail.id = uuidv4()
        lstSurveyQuestionListDetail.push(surveyQuestionListDetail)

        const lstChild = await qs.childs
        for (let child of lstChild) {
          const surveyQuestionListDetailChild = new SurveyQuestionListDetailEntity()
          surveyQuestionListDetailChild.createdAt = today
          surveyQuestionListDetailChild.topicId = topicItem.id
          surveyQuestionListDetailChild.name = child.name
          surveyQuestionListDetailChild.questionListDetail = JSON.stringify(
            (await child.questionlistDetails).map((x) => ({
              name: x.name,
              value: x.value,
              sort: x.sort,
            })),
          )
          surveyQuestionListDetailChild.parentId = surveyQuestionListDetail.id
          surveyQuestionListDetailChild.surveyId = entity.id
          surveyQuestionListDetailChild.type = child.type
          surveyQuestionListDetailChild.isRequired = child.isRequired
          surveyQuestionListDetailChild.level = child.level
          surveyQuestionListDetailChild.sort = child.sort
          surveyQuestionListDetailChild.id = uuidv4()
          lstSurveyQuestionListDetail.push(surveyQuestionListDetailChild)

          const surveyQuestionChild = new SurveyQuestionEntity()
          surveyQuestionChild.surveyId = entity.id
          surveyQuestionChild.questionId = surveyQuestionListDetailChild.id
          surveyQuestionChild.topicId = topicItem.id
          surveyQuestionChild.createdAt = today
          lstSurveyQuestion.push(surveyQuestionChild)
        }

        const surveyQuestion = new SurveyQuestionEntity()
        surveyQuestion.surveyId = entity.id
        surveyQuestion.questionId = surveyQuestionListDetail.id
        surveyQuestion.topicId = topicItem.id
        surveyQuestion.createdAt = today
        lstSurveyQuestion.push(surveyQuestion)
      }
      await trans.getRepository(SurveyQuestionListDetailEntity).insert(lstSurveyQuestionListDetail)
      await trans.getRepository(SurveyQuestionEntity).insert(lstSurveyQuestion)
      await trans.getRepository(SurveyEntity).save(entity)
    })

    return { message: UPDATE_SUCCESS }
  }

  /** Cập nhật trạng thái hoạt động - ngưng hoạt động */
  public async updateActive(user: UserDto, data: FilterOneDto) {
    const lstTask = [
      this.repo.findOne({ where: { id: data.id } }),
      this.surveyMemberRepo.findOne({ where: { surveyId: data.id, isDeleted: false } }),
      this.surveyQuestionRepo.findOne({ where: { surveyId: data.id, isDeleted: false } }),
    ]
    const [foundSurvey, foundQuestion, foundMember] = await Promise.all(lstTask)
    if (!foundSurvey) {
      throw new Error('Phiếu khảo sát không tồn tại')
    }
    if (foundMember) {
      throw new Error('Không thể ngưng hoạt động phiếu khảo sát đã được thêm nhân viên')
    }
    if (foundQuestion) {
      throw new Error('Không thể ngưng hoạt động phiếu khảo sát đã có câu hỏi')
    }
    const newIsDeleted = !foundSurvey.isDeleted
    await this.repo.update(data.id, { isDeleted: newIsDeleted, updatedAt: new Date() })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  /** Tìm kiếm dựa vào ngày tháng */
  public async findByMonthYear(user: UserDto, data: { month?: number; year: number }) {
    if (data.month)
      return await this.repo.find({
        where: {
          isDeleted: false,
          timeStart: Between(new Date(data.year, data.month, 1), new Date(data.year, data.month + 1, 0)),
        },
        order: { timeStart: 'DESC' },
      })
    return await this.repo.find({
      where: {
        isDeleted: false,
        timeStart: Between(new Date(data.year, 0, 1), new Date(data.year, 11, 31)),
      },
    })
  }

  public async autoUpdateStatus() {
    await this.repo.manager.transaction(async (trans) => {
      const lstSurvey = await trans.getRepository(SurveyEntity).find({
        where: {
          isDeleted: false,
          status: Not(In([enumData.SurveyStatus.Cancel.code, enumData.SurveyStatus.Complete.code])),
        },
      })
      if (lstSurvey.length > 0) {
        for (let item of lstSurvey) {
          // nếu ngày hiện tại lớn hơn or bằng thời gian bắt đầu thì chuyển trạng thái đang thực hiện
          if (moment(dateNow).format('YYYY-MM-DD') >= moment(item.timeStart).format('YYYY-MM-DD')) {
            if (item.status === enumData.SurveyStatus.New.code) {
              await trans.getRepository(SurveyEntity).update(
                {
                  id: item.id,
                },
                {
                  status: enumData.SurveyStatus.Doing.code,
                  updatedAt: new Date(),
                },
              )
              const history = new SurveyHistoryEntity()
              history.surveyId = item.id
              history.createdAt = new Date()
              history.createdBy = 'System Update'
              history.description = `Hệ thống tự động update thực hiện phiếu khảo sát do quá thời gian bắt đầu.`
              history.status = enumData.SurveyStatus.Doing.code
              //history.companyId = item.companyId
              await trans.getRepository(SurveyHistoryEntity).save(history)
              return
            }
            // nếu ngày hiện tại lớn hơn thời gian kết thúc thì chuyển trạng thái đã hoàn thành
            if (moment(dateNow).format('YYYY-MM-DD') > moment(item.timeEnd).format('YYYY-MM-DD')) {
              if (item.status === enumData.SurveyStatus.Doing.code) {
                await trans.getRepository(SurveyEntity).update(
                  {
                    id: item.id,
                  },
                  {
                    status: enumData.SurveyStatus.Complete.code,
                    updatedAt: new Date(),
                  },
                )
                const history = new SurveyHistoryEntity()
                history.surveyId = item.id
                history.createdAt = new Date()
                history.createdBy = 'System Update'
                history.description = `Hệ thống tự động update hoàn thành phiếu khảo sát do quá thời gian kết thúc.`
                history.status = enumData.SurveyStatus.Complete.code
                //history.companyId = item.companyId
                await trans.getRepository(SurveyHistoryEntity).save(history)
              } else if (item.status === enumData.SurveyStatus.New.code) {
                await trans.getRepository(SurveyEntity).update(
                  {
                    id: item.id,
                  },
                  {
                    status: enumData.SurveyStatus.Cancel.code,
                    updatedAt: new Date(),
                  },
                )
                const history = new SurveyHistoryEntity()
                history.surveyId = item.id
                history.createdAt = new Date()
                history.createdBy = 'System Update'
                history.description = `Hệ thống tự động update hủy phiếu khảo sát do quá thời gian kết thúc.`
                history.status = enumData.SurveyStatus.Cancel.code
                //history.companyId = item.companyId
                await trans.getRepository(SurveyHistoryEntity).save(history)
              }
            }
          }
        }
      }
    })
  }

  /** Chuyển trạng thái phiếu khảo sát sang đã hủy */
  public async updateStatusCancel(user: UserDto, data: FilterOneDto) {
    const entity = await this.repo.findOne({ where: { id: data.id }, relations: { members: true, questions: true } })
    if (!entity) throw new Error('Phiếu khảo sát không tồn tại')
    if (entity.status !== enumData.SurveyStatus.New.code)
      throw new Error('Trạng thái hiện tại của phiếu khảo sát không hợp lệ. Không thể chuyển trạng thái. Vui lòng kiểm tra lại')
    let lstMember = (await entity.members).filter((x) => x.isDeleted === false)
    if (lstMember.length > 0) {
      // throw new Error('Không thể hủy phiếu khảo sát đang có nhân viên thực hiện')
      for (const member of lstMember) {
        await this.surveyMemberRepo.update(member.id, { isDeleted: true, updatedBy: user.id, updatedAt: new Date() })
      }
    }

    let lstQuestions = (await entity.questions).filter((x) => x.isDeleted === false)
    if (lstQuestions.length > 0) {
      // throw new Error('Không thể hủy phiếu khảo sát đang có câu hỏi')
      for (const question of lstQuestions) {
        await this.surveyQuestionRepo.update(question.id, { isDeleted: true, updatedBy: user.id, updatedAt: new Date() })
      }
    }

    await this.repo.update(data.id, { status: enumData.SurveyStatus.Cancel.code, updatedBy: user.id, updatedAt: new Date() })

    const history = new SurveyHistoryEntity()
    history.surveyId = entity.id
    //history.companyId = user.companyId
    history.createdAt = new Date()
    history.createdBy = user.id
    history.description = `Nhân viên [${user.username}] vừa cập nhật trạng thái [${enumData.SurveyStatus.Cancel.name}] phiếu khảo sát có mã là [${entity.code}] `
    history.status = enumData.SurveyStatus.New.code
    await this.surveyHistoryRepo.save(history)

    return { message: UPDATE_SUCCESS }
  }
  public async updateStatusPending(user: UserDto, data: FilterOneDto) {
    const entity = await this.repo.findOne({ where: { id: data.id }, relations: { members: true, questions: true } })
    if (!entity) throw new Error('Phiếu khảo sát không tồn tại')
    if (entity.status !== enumData.SurveyStatus.Doing.code)
      throw new Error('Trạng thái hiện tại của phiếu khảo sát không hợp lệ. Không thể chuyển trạng thái. Vui lòng kiểm tra lại')
    let lstMember = (await entity.members).filter((x) => x.isDeleted === false)
    if (lstMember.length > 0) {
      // throw new Error('Không thể hủy phiếu khảo sát đang có nhân viên thực hiện')
      for (const member of lstMember) {
        await this.surveyMemberRepo.update(member.id, { isDeleted: true, updatedBy: user.id, updatedAt: new Date() })
      }
    }

    let lstQuestions = (await entity.questions).filter((x) => x.isDeleted === false)
    if (lstQuestions.length > 0) {
      // throw new Error('Không thể hủy phiếu khảo sát đang có câu hỏi')
      for (const question of lstQuestions) {
        await this.surveyQuestionRepo.update(question.id, { isDeleted: true, updatedBy: user.id, updatedAt: new Date() })
      }
    }

    await this.repo.update(data.id, { status: enumData.SurveyStatus.Pending.code, updatedBy: user.id, updatedAt: new Date() })

    const history = new SurveyHistoryEntity()
    history.surveyId = entity.id
    //history.companyId = user.companyId
    history.createdAt = new Date()
    history.createdBy = user.id
    history.description = `Nhân viên [${user.username}] vừa cập nhật trạng thái [${enumData.SurveyStatus.Cancel.name}] phiếu khảo sát có mã là [${entity.code}] `
    history.status = enumData.SurveyStatus.New.code
    await this.surveyHistoryRepo.save(history)

    return { message: UPDATE_SUCCESS }
  }

  /** Chuyển trạng thái phiếu khảo sát từ đã huỷ sang mới tạo */
  public async updateStatusReOpen(user: UserDto, data: FilterOneDto) {
    const entity = await this.repo.findOne({ where: { id: data.id }, relations: { members: true, questions: true } })
    if (!entity) throw new Error('Phiếu khảo sát không tồn tại')
    if (entity.status !== enumData.SurveyStatus.Pending.code)
      throw new Error('Trạng thái hiện tại của phiếu khảo sát không hợp lệ. Không thể chuyển trạng thái. Vui lòng kiểm tra lại')
    let lstMember = (await entity.members).filter((x) => x.isDeleted === true)
    if (lstMember.length > 0) {
      // throw new Error('Không thể hủy phiếu khảo sát đang có nhân viên thực hiện')
      for (const member of lstMember) {
        await this.surveyMemberRepo.update(member.id, { isDeleted: false, updatedBy: user.id, updatedAt: new Date() })
      }
    }

    let lstQuestions = (await entity.questions).filter((x) => x.isDeleted === true)
    if (lstQuestions.length > 0) {
      // throw new Error('Không thể hủy phiếu khảo sát đang có câu hỏi')
      for (const question of lstQuestions) {
        await this.surveyQuestionRepo.update(question.id, { isDeleted: false, updatedBy: user.id, updatedAt: new Date() })
      }
    }

    await this.repo.update(data.id, { status: enumData.SurveyStatus.Doing.code, updatedBy: user.id, updatedAt: new Date() })

    const history = new SurveyHistoryEntity()
    history.surveyId = entity.id
    //history.companyId = user.companyId
    history.createdAt = new Date()
    history.createdBy = user.id
    history.description = `Nhân viên [${user.username}] vừa cập nhật trạng thái [${enumData.SurveyStatus.New.name}] phiếu khảo sát có mã là [${entity.code}] `
    history.status = enumData.SurveyStatus.New.code
    await this.surveyHistoryRepo.save(history)

    return { message: UPDATE_SUCCESS }
  }

  /** Chuyển trạng thái phiếu khảo sát sang hoàn thành */
  public async updateStatusComplete(user: UserDto, data: FilterOneDto) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error('Phiếu khảo sát không tồn tại')
    if (entity.status !== enumData.SurveyStatus.Doing.code)
      throw new Error('Trạng thái hiện tại của phiếu khảo sát không hợp lệ. Không thể chuyển trạng thái. Vui lòng kiểm tra lại')
    await this.repo.update(data.id, { status: enumData.SurveyStatus.Complete.code, updatedBy: user.id, updatedAt: new Date() })

    const history = new SurveyHistoryEntity()
    history.surveyId = entity.id
    //history.companyId = user.companyId
    history.createdAt = new Date()
    history.createdBy = user.id
    history.description = `Nhân viên [${user.username}] vừa cập nhật tráng thái [${enumData.SurveyStatus.Complete.name}] phiếu khảo sát có mã là [${entity.code}] `
    history.status = enumData.SurveyStatus.New.code
    await this.surveyHistoryRepo.save(history)

    return { message: UPDATE_SUCCESS }
  }

  /** Chuyển trạng thái phiếu khảo sát sang đang thực hiện */
  public async updateStatusDoing(user: UserDto, data: FilterOneDto) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error('Phiếu khảo sát không tồn tại')
    if (entity.status !== enumData.SurveyStatus.New.code)
      throw new Error('Trạng thái hiện tại của phiếu khảo sát không hợp lệ. Không thể chuyển trạng thái. Vui lòng kiểm tra lại')
    // const surveyMember = await this.surveyMemberRepo.findOne({ where: { surveyId: entity.id, isDeleted: false } })
    // if (!surveyMember) throw new Error('Không thể chuyển trạng thái của phiếu khảo sát. Vui lòng thêm ít nhất một nhân viên vào phiếu khảo sát')

    // const surveyQuestion = await this.surveyQuestionRepo.findOne({
    //   where: { surveyId: entity.id, isDeleted: false },
    // })
    // if (!surveyQuestion) throw new Error('Không thể chuyển trạng thái của phiếu khảo sát. Vui lòng thêm ít nhất một bộ câu hỏi cho phiếu khảo sát')

    await this.repo.update(data.id, { status: enumData.SurveyStatus.Doing.code, updatedAt: new Date() })

    const history = new SurveyHistoryEntity()
    history.surveyId = entity.id
    //history.companyId = user.companyId
    history.createdAt = new Date()
    history.createdBy = user.id
    history.description = `Nhân viên [${user.username}] vừa cập nhật trạng thái [${enumData.SurveyStatus.Doing.name}] phiếu khảo sát có mã là [${entity.code}] `
    history.status = enumData.SurveyStatus.New.code
    await this.surveyHistoryRepo.save(history)

    return { message: UPDATE_SUCCESS }
  }

  /** Lấy thông tin chi tiết của phiếu khảo sát */
  public async findDetail(user: UserDto, data: FilterOneDto) {
    const entity: any = await this.repo.findOne({
      where: { id: data.id },
      relations: { members: true, histories: true, questions: true },
    })
    if (!entity) throw new Error('Phiếu khảo sát không tồn tại')
    let lstStatus = coreHelper.convertObjToArray(enumData.SurveyStatus)
    const statusItem = lstStatus.find((x) => x.code === entity.status)
    if (statusItem) {
      entity.statusName = statusItem.name
      entity.statusColor = statusItem.color
    }

    /** Lấy danh sách thành viên */
    let lstMember = await entity.members
    let lstStatusMember = coreHelper.convertObjToArray(enumData.SurveyMemberStatus)
    for (let item of lstMember) {
      const statusItem = lstStatusMember.find((x) => x.code === item.status)
      if (statusItem) {
        item.statusName = statusItem.name
      }

      const itemUser = await this.employeeRepo.findOne({
        where: {
          userId: item.userId,
          // companyId: user.companyId,
        },
      })
      if (itemUser) {
        item.userName = itemUser.name
        item.userCode = itemUser.code
        item.isDeletedUser = itemUser.isDeleted
      }
    }

    /** Lấy lịch sử */
    let lstHitory = await entity.histories
    for (let item of lstHitory) {
      const lstUser: any = await this.userRepo.find({ where: {} })
      const user: any = lstUser.find((x: any) => x.id == item.createdBy)
      if (user) {
        item.createdByName = user?.username
      }
    }
    entity.lstHitory = lstHitory.sort((a: any, b: any) => (a.createdAt > b.createdAt ? 1 : -1))
    entity.lstMember = lstMember

    const findTopic = (await entity.__questions__) && entity.__questions__.length > 0 ? entity.__questions__[0] : []
    if (findTopic) {
      const topic = await this.topicRepo.findOne({ where: { id: findTopic.topicId, isDeleted: false } })
      if (topic) {
        entity.topicName = topic.name
      }
    }

    delete entity.__histories__
    delete entity.__members__
    delete entity.__questions__
    return entity
  }

  /** Thêm phiếu khảo sát bằng file excel */
  public async createDataByExcel(user: UserDto, data: SurveyCreateExcelDto[]) {
    await this.repo.manager.transaction('READ UNCOMMITTED', async (trans) => {
      let lstData = []
      let lstSurvey: any[] = await this.repo.find({
        where: {},
      })
      /** Danh sách phiếu khảo sát */
      const dictSurvey: any = {}
      lstSurvey.forEach((d) => {
        dictSurvey[d.code] = d.id
      })

      const dictTopic: any = {}
      {
        const lstTopic = await this.topicRepo.find({ where: { isDeleted: false }, select: { id: true, code: true } })
        lstTopic.forEach((x) => (dictTopic[x.code] = x.id))
      }
      const lstSurveyQuestionListDetail: SurveyQuestionListDetailEntity[] = []
      const lstSurveyQuestion: SurveyQuestionEntity[] = []
      const today = new Date()
      for (let item of data) {
        const survey = new SurveyEntity()
        survey.createdBy = user.id
        survey.createdAt = today
        survey.code = 'SURVEY_' + nanoid()
        survey.id = uuidv4()
        survey.name = item.name
        survey.timeStart = item.timeStart
        survey.timeEnd = item.timeEnd
        survey.description = item.description
        survey.status = enumData.SurveyStatus.New.code
        lstData.push(survey)
        const topicId = dictTopic[item.topicCode]
        if (!topicId) throw new Error(ERROR_NOT_FOUND_DATA + `Mã chủ đề [${item.topicCode}]`)
        const topicItem = await trans.getRepository(TopicEntity).findOne({
          where: { id: topicId, isDeleted: false },
          relations: { questions: { questionlistDetails: true, childs: true } },
        })
        if (!topicItem) throw new Error('Chủ đề không còn tồn tại!. Vui lòng kiểm tra lại.')
        let lstTopicQuestion = (await topicItem.questions).filter((x) => x.isDeleted == false)
        if (lstTopicQuestion.length === 0) throw new Error('Chủ đề chưa được thiết lập câu hỏi !. Vui lòng kiểm tra lại.')

        const lstQuestionParent = lstTopicQuestion.filter((x) => x.parentId === null)
        for (const qs of lstQuestionParent) {
          const surveyQuestionListDetail = new SurveyQuestionListDetailEntity()
          surveyQuestionListDetail.name = qs.name
          surveyQuestionListDetail.questionListDetail = JSON.stringify(
            (await qs.questionlistDetails)
              .filter((x) => x.isDeleted === false)
              .map((x) => ({
                id: x.id,
                name: x.name,
                value: x.value,
                sort: x.sort,
              })),
          )
          surveyQuestionListDetail.surveyId = survey.id
          surveyQuestionListDetail.type = qs.type
          surveyQuestionListDetail.isRequired = qs.isRequired
          surveyQuestionListDetail.level = qs.level
          surveyQuestionListDetail.sort = qs.sort
          surveyQuestionListDetail.parentId = qs.parentId
          surveyQuestionListDetail.topicId = topicItem.id
          surveyQuestionListDetail.createdAt = today
          surveyQuestionListDetail.id = uuidv4()
          lstSurveyQuestionListDetail.push(surveyQuestionListDetail)

          const lstChild = await qs.childs
          for (let child of lstChild) {
            const surveyQuestionListDetailChild = new SurveyQuestionListDetailEntity()
            surveyQuestionListDetailChild.name = child.name
            surveyQuestionListDetailChild.questionListDetail = JSON.stringify(
              (await child.questionlistDetails)
                .filter((x) => x.isDeleted === false)
                .map((x) => ({
                  id: x.id,
                  name: x.name,
                  value: x.value,
                  sort: x.sort,
                })),
            )
            surveyQuestionListDetailChild.surveyId = survey.id
            surveyQuestionListDetailChild.type = child.type
            surveyQuestionListDetailChild.isRequired = child.isRequired
            surveyQuestionListDetailChild.level = child.level
            surveyQuestionListDetailChild.sort = child.sort
            surveyQuestionListDetailChild.parentId = surveyQuestionListDetail.id
            surveyQuestionListDetailChild.topicId = topicItem.id
            surveyQuestionListDetailChild.createdAt = today
            surveyQuestionListDetailChild.id = uuidv4()
            lstSurveyQuestionListDetail.push(surveyQuestionListDetailChild)

            const surveyQuestionChild = new SurveyQuestionEntity()
            surveyQuestionChild.surveyId = survey.id
            surveyQuestionChild.questionId = surveyQuestionListDetailChild.id
            surveyQuestionChild.topicId = topicItem.id
            surveyQuestionChild.createdAt = today
            lstSurveyQuestion.push(surveyQuestionChild)
          }

          const surveyQuestion = new SurveyQuestionEntity()
          surveyQuestion.surveyId = survey.id
          surveyQuestion.questionId = surveyQuestionListDetail.id
          surveyQuestion.topicId = topicItem.id
          surveyQuestion.createdAt = today
          lstSurveyQuestion.push(surveyQuestion)
        }
      }
      await this.repo.insert(lstData)
      await trans.getRepository(SurveyQuestionListDetailEntity).insert(lstSurveyQuestionListDetail)
      await trans.getRepository(SurveyQuestionEntity).insert(lstSurveyQuestion)
      /** Thêm vào bảng lịch sử */
      const lstHitory = []
      for (const survey of lstData) {
        const history = new SurveyHistoryEntity()
        history.surveyId = survey.id
        history.createdAt = new Date()
        history.createdBy = user.id
        history.description = `Nhân viên [${user.username}] vừa tạo phiếu khảo sát có mã là [${survey.code}] `
        history.status = enumData.SurveyStatus.New.code
        lstHitory.push(history)
      }
      await this.surveyHistoryRepo.insert(lstHitory)
    })
    return { message: CREATE_SUCCESS }
  }
}
