import { Injectable } from '@nestjs/common'
import { CheckInventoryDetailRepository, CheckInventoryRepository, ItemRepository, WarehouseRepository } from '../../../repositories'
import { PaginationDto, UserDto } from '../../../dto'
import { Between, In, Like, Raw } from 'typeorm'
import * as moment from 'moment'
import { coreHelper } from '../../../helpers'
import { Request as IRequest } from 'express'
import { enumData } from '../../../constants'
import { omsApiHelper } from '../../../helpers/omsApiHelper'
@Injectable()
export class CheckInventoryDetailService {
  constructor(
    private readonly repo: CheckInventoryDetailRepository,
    private readonly checkInventoryRepository: CheckInventoryRepository,
    private warehouseRepository: WarehouseRepository,
    private readonly itemRepository: ItemRepository,
  ) { }
  /** Hàm phân trang phiếu kiểm kho */
  async pagination(data: PaginationDto, req: IRequest) {
    try {
      const whereCon: any = {}
      whereCon.checkInventory = {}
      whereCon.checkInventory.status = enumData.CheckInventoryStatus.APPROVED.code
      if (data.where.productId) whereCon.productId = data.where.productId
      if (data.where.approvedBy) {
        whereCon.checkInventory = {}
        whereCon.checkInventory.approvedBy = data.where.approvedBy
      }
      if (data.where.createdAt) {
        const formattedDate = new Date(data.where.createdAt).toISOString().split("T")[0];
        whereCon.createdAt = Raw(
          (alias) => `DATE(${alias}) = '${formattedDate}'`
        );
      }
      if (data.where.approvedDate) {
        const ds = new Date(new Date(data.where.approvedDate).setHours(0, 0, 0, 0))
        const de = new Date(new Date(data.where.approvedDate).setHours(23, 59, 59, 99))
        whereCon.checkInventory.approvedDate = Between(ds, de)
      }

      // Tìm người xác nhận soạn hàng
      if (data.where.approvedByName) {
        // let lstApproveUserId: string[] = await authApiHelper.getLstUserIdByName(req, { name: data.where.approvedByName })
        // if (lstApproveUserId.length > 0) whereCon.approvedBy = In(lstApproveUserId)
        // else return [[], 0]
      }

      // whereCon.product = {}
      if (data.where.brandId) {
        whereCon.product.brandId = Like(`%${data.where.brandId}%`)
      }
      if (data.where.storeId) {
        const warehouse = await this.warehouseRepository.findOne({ where: { storeId: data.where.storeId } })
        const checkInventory = await this.checkInventoryRepository.find({ where: { warehouseId: warehouse.id } })
        if (checkInventory.length > 0) {
          const checkInventoryIds = checkInventory.map(item => item.id);
          whereCon.checkInventoryId = In(checkInventoryIds);
        }
      }

      const [lst, total]: any = await this.repo.findAndCount({
        where: whereCon,
        skip: data.skip,
        take: data.take,
        relations: { checkInventory: true },
      })
      if (lst.length == 0) return { data: [], total }

      for (let item of lst) {
        item.approvedBy = item?.__checkInventory__?.approvedBy
      }

      const listMemIds = lst.map(val => val.approvedBy);
      const member: any = await omsApiHelper.getMemberByListId(req, listMemIds);

      for (let e of lst) {
        const item = await this.itemRepository.findOne({ where: { id: e.productId } })
        e.productCode = item.code
        e.productName = item.name
        //e.approvedByName = dicApprove[e.approvedBy]?.name ?? ''
        e.approvedDate = e.__checkInventory__?.approvedDate
        delete e.__product__
        delete e.__checkInventory__
      }

      const listMemCreateIds = lst.map(val => val.createdBy);
      const memberCreate: any[] = await omsApiHelper.getMemberByListId(req, listMemCreateIds) || [];

      const listMemApproveIds = lst.map(val => val.approvedBy);
      const memberApprove: any[] = await omsApiHelper.getMemberByListId(req, listMemApproveIds) || [];

      const mappingResult = lst.map((val) => {
        const memCreate: any = memberCreate.find((m: any) => m.id === val.createdBy)
        const memApproved: any = memberApprove.find((m: any) => m.id === val.approvedBy)

        return {
          ...val,
          createdByName: memCreate?.fullName,
          approvedByName: memApproved?.fullName
        }
      })

      return { data: mappingResult, total }
    } catch (error) {
      throw new Error(error);
    }
  }
}
