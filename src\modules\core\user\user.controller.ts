import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { UserService } from './user.service'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { UserFilterDto } from './dto'
import { UserDto } from '../../../dto'
import { JwtAuthGuard } from '../../common/guards'
import { CurrentUser } from '../../common/decorators'

@ApiBearerAuth()
@ApiTags('User')
@Controller('user')
export class UserController {
  constructor(private readonly service: UserService) {}

  @ApiOperation({ summary: 'Lấy ds user theo điều kiện' })
  @UseGuards(JwtAuthGuard)
  @Post('find')
  public async find(@CurrentUser() user: UserDto, @Body() data: UserFilterDto) {
    return await this.service.find(user, data)
  }

  @ApiOperation({ summary: 'Lấy ds user supplier' })
  @UseGuards(JwtAuthGuard)
  @Post('find_supplier_account')
  public async findSupplierAccount(@CurrentUser() user: UserDto, @Body() data: UserFilterDto) {
    return await this.service.findSupplierAccount(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động của user' })
  @UseGuards(JwtAuthGuard)
  @Post('set_active')
  public async setActive(@Body() data: any) {
    return await this.service.setActive(data)
  }

  @ApiOperation({ summary: 'Lấy 1 danh mục' })
  @UseGuards(JwtAuthGuard)
  @Post('find_one')
  public async findOne(@CurrentUser() user: UserDto, @Body() data: { id?: string; code?: string }) {
    return await this.service.findOne(user, data)
  }
}
