import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import { CityRepository, DistrictRepository } from '../../../repositories'
import { DistrictService } from './district.service'
import { DistrictController } from './district.controller'
import { DistrictPublicController } from './districtPublic.controller'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([DistrictRepository, CityRepository])],
  controllers: [DistrictController, DistrictPublicController],
  providers: [DistrictService],
  exports: [DistrictService],
})
export class DistrictModule {}
