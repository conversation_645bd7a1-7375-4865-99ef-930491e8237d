import { Controller, UseGuards, Post, Body, Req } from '@nestjs/common'
import { SupplierService } from './supplier.service'
import { SupplierCreateDto } from './dto/supplierCreate.dto'
import { SupplierUpdateDto } from './dto/supplierUpdate.dto'
import { SupplierCreateExcelDto, SupplierRegisterDto, SupplierUpdateIsActiveDto } from './dto'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { CurrentUser, CurrentUserPortal, JwtAuthGuard } from '../../common'
import { PaginationDto, UserDto } from '../../../dto'
import { ResetPasswordSupplier } from './dto/supplier.dto'
import { UserRegisterDto, UserUpdateDto, UserUpdatePasswordDto } from '../auth/dto/register.dto'

@ApiBearerAuth()
@ApiTags('Supplier')
@UseGuards(JwtAuthGuard)
@Controller('suppliers')
export class SupplierController {
  constructor(private readonly service: SupplierService) {}

  @Post('find')
  public async find(@Body() data: any) {
    return await this.service.find(data)
  }

  @Post('pagination')
  public async pagination(@Body() data: PaginationDto) {
    return await this.service.pagination(data)
  }

  @Post('create_data')
  public async createData(@CurrentUserPortal() user: UserDto, @Body() data: SupplierCreateDto) {
    return await this.service.createData(user, data)
  }

  @Post('update_data')
  public async updateData(@CurrentUserPortal() user: UserDto, @Body() data: SupplierUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @Post('update_active')
  public async updateActive(@Body() data: SupplierUpdateIsActiveDto, @CurrentUserPortal() user: UserDto) {
    return await this.service.updateIsDelete(data.id, user)
  }

  @Post('find_detail')
  public async findDetail(@Body() data: SupplierUpdateIsActiveDto) {
    return await this.service.findDetail(data)
  }

  @Post('create_data_excel')
  public async createDataExcel(@CurrentUserPortal() user: UserDto, @Body() data: SupplierCreateExcelDto[]) {
    return await this.service.createDataExcel(user, data)
  }

  @Post('reset-password')
  public async resetPassword(@Body() data: ResetPasswordSupplier) {
    const { supplierId, newPassword } = data
    return await this.service.resetPassword(supplierId, newPassword)
  }

  @ApiOperation({ summary: 'Hàm đăng ký Doanh nghiệp mới' })
  @Post('supplier_registration')
  public async supplierRegistration(@Req() req: Request, @Body() data: SupplierRegisterDto) {
    return await this.service.supplierRegistration(req, data)
  }

  @ApiOperation({ summary: 'Lấy thông tin  NCC' })
  @Post('get_supplier_info')
  public async getSupplierInfo(@CurrentUserPortal() user: UserDto) {
    return await this.service.getSupplierInfo(user)
  }

  // Đăng ký tài khoảng nhà cung cấp
  @ApiOperation({ summary: 'Đăng ký tài khoảng nhà cung cấp' })
  @Post('supplier/register')
  public async registerSupplier(@CurrentUser() user: UserDto, @Body() data: UserRegisterDto) {
    return await this.service.registerSupplier(user, data)
  }

  //update tài khoản nhà cung cấp

  @ApiOperation({ summary: 'Cập nhật tài khoản nhà cung cấp' })
  @Post('supplier/update')
  public async updateSupplier(@Body() data: UserUpdateDto) {
    return await this.service.updateSupplier(data)
  }

  //update password
  @ApiOperation({ summary: 'Đổi mật khẩu nhà cung cấp' })
  @Post('supplier/update-password')
  public async updatePasswordSupplier(@Body() data: UserUpdatePasswordDto) {
    return await this.service.updatePasswordSupplier(data)
  }
}
