import { Column, Entity, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { ItemEntity } from './item.entity'
import { SupplierEntity } from './supplier.entity'

@Entity('pricing')
export class PricingEntity extends BaseEntity {
  @Column({ type: 'varchar', length: 255, nullable: false })
  code: string

  @Column({ type: 'numeric', nullable: false })
  price: number

  @Column({ type: 'uuid', nullable: false })
  itemId: string

  @ManyToOne(() => ItemEntity, (p) => p.id, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'itemId', referencedColumnName: 'id' })
  item: Promise<ItemEntity>

  @Column({ type: 'uuid', nullable: false })
  supplierId: string

  @ManyToOne(() => SupplierEntity, (p) => p.id, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  @Column({ type: 'text', nullable: true })
  description: string
}
