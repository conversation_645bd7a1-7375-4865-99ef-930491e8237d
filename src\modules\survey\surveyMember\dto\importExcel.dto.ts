import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString, IsDate } from 'class-validator'

export class ImportExcelDto {
  @ApiProperty({ description: 'Phiếu khảo sát' })
  @IsNotEmpty({ message: 'Phiếu khảo sát không được trống' })
  @IsString()
  surveyId: string

  @ApiProperty({ description: 'danh sách mã nhân viên' })
  @IsNotEmpty({ message: 'danh sách mã nhân viên không được trống' })
  lstEmployeeCode: string[]
}
