import { <PERSON>ti<PERSON>, Column, OneToMany, ManyToOne, Jo<PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm'
import { BaseEntity } from './base.entity'
import { PartnerEntity } from './partner.entity'
import { SettingMediaEntity } from './settingMedia.entity'

@Entity('contracts')
export class ContractEntity extends BaseEntity {
  //Bên A
  @Column({ type: 'text', nullable: true })
  infoA: string

  // Bên B
  @Column({ type: 'varchar', length: 255, nullable: true })
  partyName: string

  @Column({ type: 'varchar', length: 50, nullable: true })
  taxCode: string

  @Column({ type: 'varchar', length: 50, nullable: true })
  phone: string

  @Column({ type: 'varchar', length: 255, nullable: true })
  address: string

  @Column({ type: 'varchar', length: 255, nullable: true })
  representative: string

  @Column({ type: 'varchar', length: 255, nullable: true })
  position: string

  @Column({ type: 'varchar', length: 50, nullable: true })
  bankAccount: string

  @Column({ type: 'varchar', length: 50, nullable: true })
  fax: string

  @Column({ type: 'varchar', length: 50, nullable: true })
  bank: string

  @Column({ type: 'varchar', length: 50, nullable: true })
  bankNumber: string

  /** thẻ */
  @Column({ type: 'varchar', length: 50, nullable: true })
  cardId: string

  @Column({ type: 'varchar', length: 50, nullable: true })
  personalId: string

  @Column({ type: 'text', nullable: true })
  sign: string

  @Column({ type: 'varchar', length: 255, nullable: true })
  memberId: string

  /** Partner */
  @Column({ type: 'varchar', length: 255, nullable: true })
  partnerId: string
  @Column({ type: 'varchar', length: 255, nullable: true })
  partnerName: string

  /** Thong tin chung */
  @Column({ type: 'varchar', nullable: true })
  mediaCode: string

  // Hợp đồng chi tiết
  @Column({ type: 'varchar', length: 100, nullable: true })
  contractCode: string

  @Column({ type: 'varchar', length: 255, nullable: true })
  contractName: string

  @Column({ type: 'timestamptz', nullable: true })
  effectiveDate: Date

  @Column({ type: 'timestamptz', nullable: true })
  expirationDate: Date

  @Column({ type: 'varchar', length: 255, nullable: true })
  templateApplied: string

  // Điều khoản
  @Column({ name: 'terms', type: 'text', nullable: true })
  term: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  eContractType: string

  /** Link pdf hợp đồng điện tử */
  @Column({
    nullable: true,
    type: 'text',
  })
  eContractUrl: string

  @Column({
    nullable: true,
    type: 'boolean',
  })
  isCTV: boolean

  /** Link file */
  @Column({ type: 'text', nullable: true })
  fileUrl: string

  /** Mã giới thiệu */
  @Column({ type: 'varchar', length: 50, nullable: true })
  referralCode: string

  /** Số điện thoại người giới thiệu */
  @Column({ type: 'varchar', length: 50, nullable: true })
  invitePhone: string
}
