import { Body, Controller, Param, Post, Req, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { Request as IRequest } from 'express'
import { ApeAuthGuard } from '../common/guards/ape-auth.guard'
import { QuestionCreateDto, QuestionCreateMasterDataDto, QuestionListDetailCreateDto, QuestionListDetailUpdateDto, QuestionUpdateDto } from './dto'
import { QuestionService } from './question.service'
import { PaginationDto, UserDto } from '../../../dto'
import { JwtAuthGuard } from '../../common/guards'
import { CurrentUser } from '../../common/decorators'

@UseGuards(ApeAuthGuard)
@ApiBearerAuth()
@ApiTags('Question')
@Controller('question')
export class QuestionController {
  constructor(private readonly service: QuestionService) {}

  @ApiOperation({ summary: 'Tìm câu hỏi theo id' })
  @Post('find_one')
  public async findOne(@CurrentUser() user: UserDto, @Body() data: any) {
    return await this.service.findOne(user, data)
  }

  @ApiOperation({ summary: 'Tạo mới câu hỏi' })
  @Post('create_data')
  public async create_data(@CurrentUser() user: UserDto, @Body() data: QuestionCreateDto) {
    return await this.service.create_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật câu hỏi' })
  @Post('update_data')
  @UseGuards(JwtAuthGuard)
  public async tech_update_data(@CurrentUser() user: UserDto, @Body() data: QuestionUpdateDto) {
    return await this.service.update_data(user, data)
  }

  @ApiOperation({ summary: 'Danh sách phân trang' })
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động' })
  @Post('update_active')
  public async update_active(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.update_active(user, data.id)
  }

  @ApiOperation({ summary: 'Lấy các câu hỏi cấp 1 của câu hỏi' })
  @Post('question_find')
  public async question_find(@Body() data: { topicId: string; isGetRelation: boolean }) {
    return await this.service.question_find(data)
  }

  @ApiOperation({ summary: 'Danh sách các lựa chọn của câu hỏi kiểu List' })
  @Post('questionlistdetail_pagination')
  public async questionlistdetail_pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.questionlistdetail_pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo mới lựa chọn cho câu hỏi kiểu List' })
  @Post('questionlistdetail_create_data')
  //@UseGuards(JwtAuthGuard)
  public async questionlistdetail_create_data(@CurrentUser() user: UserDto, @Body() data: QuestionListDetailCreateDto) {
    return await this.service.questionlistdetail_create_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật lựa chọn cho câu hỏi kiểu List' })
  @Post('questionlistdetail_update_data')
  //@UseGuards(JwtAuthGuard)
  public async questionlistdetail_update_data(@CurrentUser() user: UserDto, @Body() data: QuestionListDetailUpdateDto) {
    return await this.service.questionlistdetail_update_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động lựa chọn cho câu hỏi kiểu List' })
  @Post('questionlistdetail_update_active')
  @UseGuards(JwtAuthGuard)
  public async questionlistdetail_update_active(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.questionlistdetail_update_active(user, data.id)
  }

  @ApiOperation({ summary: 'Xóa tất cả câu hỏi' })
  @Post('delete_all')
  @UseGuards(JwtAuthGuard)
  public async delete_all(@CurrentUser() user: UserDto, @Body() data: { topicId: string }) {
    return await this.service.delete_all(user, data.topicId)
  }

  @ApiOperation({ summary: 'Danh sách danh mục từ master data' })
  @Post('create_master_data_question')
  @UseGuards(JwtAuthGuard)
  public async createMasterData(@Body() data: QuestionCreateMasterDataDto[]) {
    return await this.service.createMasterData(data)
  }

  @ApiOperation({ summary: 'Import danh sách câu hỏi theo chủ đề' })
  @Post('import/:topicId')
  @UseGuards(JwtAuthGuard)
  public async import(@Param('topicId') topicId: string, @CurrentUser() user: UserDto, @Body() data: { lstDataTable1: any[]; lstDataTable2: any[] }) {
    return await this.service.import(topicId, user, data)
  }
}
