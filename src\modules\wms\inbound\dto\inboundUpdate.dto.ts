import { IsNotEmpty, IsString, IsUUID } from 'class-validator'
import { InboundCreateDto } from './inboundCreate.dto'
import { ApiProperty } from '@nestjs/swagger'

export class InboundUpdateDto extends InboundCreateDto {
  @ApiProperty({ description: "Id của PNX" })
  @IsNotEmpty()
  @IsString()
  id: string

  @ApiProperty({ description: "Id người cập nhật" })
  @IsNotEmpty()
  @IsUUID()
  updateBy: string
}
