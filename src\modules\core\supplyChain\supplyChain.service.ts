import { CREATE_SUCCESS, NSSupplyChain, UPDATE_SUCCESS } from './../../../constants/index';
import { Injectable } from '@nestjs/common';
import { SupplyChainConfigApproveRepository, SupplyChainConfigDetailRepository, SupplyChainConfigRepository, SupplyChainConfigTimeRepository } from '../../../repositories';
import { CreateSupplyChainDto, ListSupplyChaiDto, UpdateSupplyChainDto } from './dto/supplyChain.dto';
import { UPDATE_ACTIVE_SUCCESS } from '../../../constants';
import { UserDto } from '../../../dto';

@Injectable()
export class SupplyChainService {
  constructor(
    private readonly supplyChainConfigRepo: SupplyChainConfigRepository,
    private readonly supplyChainConfigDetailRepo: SupplyChainConfigDetailRepository,
    private readonly supplyChainConfigApprove: SupplyChainConfigApproveRepository,
    private readonly supplyChainConfigTimeRepo: SupplyChainConfigTimeRepository
  ) { }

  /** Lấy danh sách chuỗi cung ứng */
  async getSupplyChains(params: ListSupplyChaiDto) {
    const { supplyChainCode, status } = params
    const wheres = [`sc."isDeleted" = FALSE`];
    if (supplyChainCode) {
      wheres.push(`sc."supplyChainCode" LIKE '%${supplyChainCode}%'`)
    }

    if (status) {
      wheres.push(`sc."status"  = '${status}'`)
    }

    const sql = `
    SELECT 
      sc."id",
      sc."supplyChainCode",
      sc."createdAt",
      sc."note",
      sc."status",
      sup."id" AS "supplierId",
      sup."name" as "supplierName",
      u."fullName" AS "createdBy"
    FROM  
      supply_chain_config sc
      LEFT JOIN supplier "sup" ON sup."id" = sc."supplierId"
      LEFT JOIN "user" as "u" ON u.id = sc."createdBy"::uuid
    WHERE ${wheres.join(' AND ')}
    ORDER BY 
      sc."supplyChainCode"
    `
    const result = await this.supplyChainConfigRepo.queryPagination(sql, params)
    return result
  }

  /** Lấy chi tiết chuỗi cung ứng */
  async getSupplyChainDetails(id: string) {
    const supplyChain = await this.supplyChainConfigRepo.findOne({ where: { id } }) // Cấp 1
    if (!supplyChain) {
      throw new Error('Supply chain not found')
    }
    const details = await this.supplyChainConfigDetailRepo.find({ where: { supplyChainId: supplyChain.id } }); // Cấp 2
    const approve = await this.supplyChainConfigApprove.find({
      where: { supplyChainId: supplyChain.id },
      order: { approvalLevel: 'ASC', supplyChainDetailId: 'ASC' }
    })

    const mappingApprove = details.map((val) => {
      const approvalLst = approve.filter(i => i.supplyChainDetailId === val.id) // Cấp 3
      return {
        ...val,
        approvalLst
      }
    })

    return { supplyChain, detail: mappingApprove }
  }

  /** Tạo mới chuối cung ứng */
  async createSupplyChain(createSupplyChainDto: CreateSupplyChainDto, user: UserDto) {
    try {
      const { details, ...supplyChainData } = createSupplyChainDto;
      supplyChainData.createdBy = user.id;
      supplyChainData.partnerId = supplyChainData.partnerId || null;
      const savedSupplyChain = await this.supplyChainConfigRepo.save(supplyChainData)

      const detailEntities = details.map((detail) => ({
        ...detail,
        supplyChainId: savedSupplyChain.id,
      }))

      // Check Data
      for (const detail of detailEntities) {
        const approveLst = detail.approvalLst;
        const approvalLevels = approveLst.map(entity => entity.approvalLevel);
        const hasDuplicates = approvalLevels.some((level, index) => approvalLevels.indexOf(level) !== index);
        if (hasDuplicates) {
          throw new Error('Không được trùng cấp duyệt');
        }

        const approvers = approveLst.map(entity => entity.approverId);
        const hasDuplicateApprovers = approvers.some((id, index) => approvers.indexOf(id) !== index);
        if (hasDuplicateApprovers) {
          throw new Error('Trùng supplier duyệt');
        }
      }

      // Lưu Data
      for (const detail of detailEntities) {
        const newDetail = await this.supplyChainConfigDetailRepo.save(detail);
        const approveLst = detail.approvalLst;
        const mappingApproveLst = approveLst.map((approval) => ({
          ...approval,
          supplyChainId: savedSupplyChain.id,
          supplyChainDetailId: newDetail.id
        }))
        await this.supplyChainConfigApprove.save(mappingApproveLst)

        // const configTimeLst = detail.configTimeLst;
        // await this.supplyChainConfigTimeRepo.save({
        //   supplyChainDetailId: newDetail.id,
        //   ...configTimeLst
        // })
      }

      return { message: CREATE_SUCCESS }
    } catch (error) {
      throw new Error(error?.detail)
    }
  }

  /** Cập nhật chuỗi cung ứng */
  async updateSupplyChain(id: string, updateSupplyChainDto: UpdateSupplyChainDto) {
    try {
      const { details, ...supplyChainData } = updateSupplyChainDto;
      const supplyChain = await this.supplyChainConfigRepo.findOne({ where: { id } })
      if (!supplyChain) {
        throw new Error('Không tìm thấy chuỗi cung ứng')
      }
      delete supplyChainData.supplyChainId
      await this.supplyChainConfigRepo.update(id, supplyChainData)

      // Check Data Approval
      if (details?.length > 0) {
        for (const detail of details) {
          if (detail?.approvalLst.length > 0) {
            const approvalLevels = detail.approvalLst.map(entity => entity.approvalLevel);
            const hasDuplicates = approvalLevels.some((level, index) => approvalLevels.indexOf(level) !== index);
            if (hasDuplicates) {
              throw new Error('Không được trùng cấp duyệt');
            }

            const approvers = detail.approvalLst.map(entity => entity.approverId);
            const hasDuplicateApprovers = approvers.some((id, index) => approvers.indexOf(id) !== index);
            if (hasDuplicateApprovers) {
              throw new Error('Trùng supplier duyệt');
            }
          }
        }
      }

      // Update Data
      if (details?.length > 0) {
        await this.supplyChainConfigDetailRepo.delete({ supplyChainId: id });
        for (const detail of details) {
          const updateDetail = await this.supplyChainConfigDetailRepo.save({ ...detail, supplyChainId: id });

          if (detail?.approvalLst.length > 0) {
            await this.supplyChainConfigApprove.delete({ supplyChainDetailId: detail.id })
            const mappingApprove = detail.approvalLst.map((approval) => ({
              ...approval,
              regionId: updateDetail.regionId,
              supplyChainId: id,
              supplyChainDetailId: updateDetail.id
            }))
            await this.supplyChainConfigApprove.save(mappingApprove)
          }

          /** Cấu hình thời gian duyệt, giao cho chuổi để gán lên PO */
          // if (detail.configTimeLst) {
          //   const configTimeLst = detail.configTimeLst;
          //   await this.supplyChainConfigTimeRepo.update(
          //     { supplyChainDetailId: detail.id },
          //     {
          //       ...configTimeLst
          //     }
          //   )
          // }
        }
      }
      return { message: UPDATE_SUCCESS }
    } catch (error) {
      throw new Error(error);
    }
  }

  /** Cập nhật trạng thái DEACTIVE/ACTIVE */
  async updateSupplyChainStatus(id: string, status: NSSupplyChain.ESupplierType) {
    try {
      const supplyChain = await this.supplyChainConfigRepo.findOne({ where: { id } })
      if (!supplyChain) {
        throw new Error('Supply chain not found')
      }
      await this.supplyChainConfigRepo.update(id, { status })
      return { message: UPDATE_ACTIVE_SUCCESS }
    } catch (error) {
      throw new Error('Update Status Supply chain failed')
    }
  }

  /** Tìm kiếm chuỗi cung ứng theo supplyChainCode và status */
  async searchSupplyChain(params: ListSupplyChaiDto) {
    return this.getSupplyChains(params)
  }

}
