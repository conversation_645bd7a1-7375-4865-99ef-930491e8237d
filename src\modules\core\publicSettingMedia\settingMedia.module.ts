import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import { SettingMediaRepository } from '@/repositories'
import { PublicSettingMediaService } from './settingMedia.service'
import { PublicSettingMediaController } from './settingMedia.controller'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([SettingMediaRepository])],
  controllers: [PublicSettingMediaController],
  providers: [PublicSettingMediaService],
  exports: [PublicSettingMediaService],
})
export class PublicSettingMediaModule {}
