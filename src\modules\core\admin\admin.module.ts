import { <PERSON>du<PERSON> } from '@nestjs/common'
import { AdminController } from './admin.controller'
import { LuckyBillConfigModule } from '../../lucky-bill/lucky-bill-config/lucky-bill-config.module'
import { TypeOrmExModule } from '../../../typeorm'
import { LuckyBillConfigRepository } from '../../../repositories/lucky-bill/lucky-bill-config.repository'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([LuckyBillConfigRepository])],
  controllers: [AdminController],
  providers: [],
  exports: [],
})
export class AdminModule {}
