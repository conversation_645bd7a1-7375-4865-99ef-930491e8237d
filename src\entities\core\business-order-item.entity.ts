import { Column, <PERSON>ti<PERSON>, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { BusinessOrderEntity } from './business-order.entity'

@Entity('business_order_item')
export class BusinessOrderItemEntity extends BaseEntity {
  @Column({ type: 'uuid', nullable: false })
  businessOrderId: string

  @Column({ type: 'numeric', nullable: false })
  totalPrice: number

  @Column({ type: 'numeric', nullable: false })
  totalPriceVat: number

  @Column({ type: 'numeric', nullable: false })
  vat: string

  @Column({ type: 'uuid', nullable: false })
  itemId: string

  @Column({ type: 'varchar', nullable: false })
  unit: string

  @Column({ type: 'numeric', nullable: false })
  quantity: number

  @ManyToOne(() => BusinessOrderEntity, (p) => p.id, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'businessOrderId', referencedColumnName: 'id' })
  businessOrder: Promise<BusinessOrderEntity>
}
