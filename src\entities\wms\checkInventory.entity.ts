import { BaseEntity } from '../core/base.entity'
import { Entity, Column, OneToMany, ManyToOne, JoinColumn } from 'typeorm'
import { CheckInventoryDetailEntity } from './checkInventoryDetail.entity'
import { CheckInventoryHistoryEntity, InboundEntity, OutboundEntity, WarehouseEntity } from '.'

/** <PERSON><PERSON><PERSON> kho */
@Entity('check_inventory')
export class CheckInventoryEntity extends BaseEntity {
  /** Mã phiếu */
  @Column({ type: 'varchar', length: 36, nullable: true })
  code: string

  /** Kho kiểm */
  @Column({ type: 'varchar', nullable: true })
  warehouseId: string
  @ManyToOne(() => WarehouseEntity, (p) => p.checkInventories)
  @JoinColumn({ name: 'warehouseId', referencedColumnName: 'id' })
  warehouse: Promise<WarehouseEntity>

  /** Trạng thái phiếu kiểm - enumData.CheckInventoryStatus */
  @Column({ type: 'varchar', length: 36, nullable: true, default: 'NEW' })
  status: string

  /** Ngày duyệt phiếu xuất kho */
  @Column({ type: 'timestamptz', nullable: true })
  approvedDate: Date

  /**
   * Người duyệt
   * Lấy id người nhấn nút duyệt
   *  */
  @Column({ type: 'varchar', length: 36, nullable: true })
  approvedBy: string

  @Column({ type: 'text', nullable: true })
  description: string

  /** Danh sách sản phẩm trong phiếu kiểm kho */
  @OneToMany(() => CheckInventoryDetailEntity, (p) => p.checkInventory)
  details: Promise<CheckInventoryDetailEntity[]>

  /** Lịch sử kiểm kho */
  @OneToMany(() => CheckInventoryHistoryEntity, (p) => p.checkInventory)
  histories: Promise<CheckInventoryHistoryEntity[]>

  /** Danh sách phiếu xuất kho được tạo khi duyệt phiếu kiểm kho */
  @OneToMany(() => OutboundEntity, (p) => p.checkInventory)
  outbounds: Promise<OutboundEntity[]>

  /** Danh sách phiếu xuất nhập được tạo khi duyệt phiếu kiểm nhập */
  @OneToMany(() => InboundEntity, (p) => p.checkInventory)
  inbounds: Promise<InboundEntity[]>
}
