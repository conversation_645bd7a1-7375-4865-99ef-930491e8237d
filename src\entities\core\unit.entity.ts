import { BaseEntity } from './base.entity'
import { Entity, Column, OneToMany } from 'typeorm'
import { ItemEntity } from './item.entity'

@Entity('unit')
export class UnitEntity extends BaseEntity {
  @Column({ type: 'varchar', length: 50, nullable: true })
  code: string

  @Column({ type: 'varchar', length: 250, nullable: true })
  name: string

  /** Đơn vị cơ sở */
  @Column({ nullable: true })
  baseUnit: number

  /** Ghi chú */
  @Column({ type: 'text', nullable: true })
  description: string

  /** Danh sách sản phẩm */
  @OneToMany(() => ItemEntity, (p) => p.unit)
  products: Promise<ItemEntity[]>

  /** Danh sách sản phẩm theo Đơn vị đặt hàng */
  @OneToMany(() => ItemEntity, (p) => p.pOUnit)
  pOProducts: Promise<ItemEntity[]>
}
