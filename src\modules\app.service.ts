import { Injectable } from '@nestjs/common'
import { customAlphabet } from 'nanoid'
import { coreHelper } from '../helpers'

@Injectable()
export class AppService {
  /** Kiểm tra tình trạng server */
  healthCheck() {
    return 'This server is healthy.'
  }

  /** Kiểm tra thời gian theo timezone của server */
  checkTimeZone() {
    const newDate = new Date()
    const newDateTz = coreHelper.newDateTZ()

    return `newDate: ${newDate}\nnewDateTZ+7: ${newDateTz}`
  }

  delay(ms = 1800000) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve('Đợi chờ là hạnh phúc.')
      }, ms)
    })
  }

  /**
   * Hàm tự tạo code random
   * @param prefix
   * @param length
   * @returns
   */
  public autoGenCode(prefix: string, random?: RandomSuffix, length?: number) {
    let code = prefix
    if (random && random == 'timestamp') {
      const today = new Date()
      const suffix = `${today.getFullYear()}${today.getMonth()}${today.getDate()}`
      code += '_' + suffix
    } else if (length) {
      const suffix = customAlphabet('1234567890QWERTYUIOPASDFGHJKLZXCVBNM', length)
      code += '_' + suffix()
    }
    return code
  }
}

type RandomSuffix = 'alphabet' | 'timestamp'
