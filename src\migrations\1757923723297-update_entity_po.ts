import { MigrationInterface, QueryRunner } from "typeorm";

export class updateEntityPo1757923723297 implements MigrationInterface {
    name = 'updateEntityPo1757923723297'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "purchase_order_ref" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "createdBy" character varying(36), "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT now(), "updatedBy" character varying(36), "isDeleted" boolean NOT NULL DEFAULT false, "poId" uuid NOT NULL, "poIdRef" uuid NOT NULL, CONSTRAINT "PK_ef8f7c3e6e767435a835633ed70" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_b305bca6bb71eb92384f98bcbf" ON "purchase_order_ref" ("poId") `);
        await queryRunner.query(`CREATE INDEX "IDX_a43d7229db356d6ca410db911e" ON "purchase_order_ref" ("poId", "poIdRef") `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_a43d7229db356d6ca410db911e"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_b305bca6bb71eb92384f98bcbf"`);
        await queryRunner.query(`DROP TABLE "purchase_order_ref"`);
    }

}
