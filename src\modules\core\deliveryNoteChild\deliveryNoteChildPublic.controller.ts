import { Body, Controller, Get, Post, Query, Req, UseGuards } from "@nestjs/common";
import { ApiBearerAuth, ApiTags } from "@nestjs/swagger";
import { DeliveryNoteChildService } from "./deliveryNoteChild.service";
import { CreateInboundFromDeliveryNoteChildDto, DetailDeliverNoteChildDto, FilterDeliverNoteChildDetailDto, FilterDeliverNoteChildDto, UUIDDto } from "./dto/deliveryNoteChild.dto";
import { Request as IRequest } from 'express';

@ApiBearerAuth()
@ApiTags('DeliveryNoteChild')
@Controller('delivery-note-child-public')
export class DeliveryNoteChildPublicController {
  constructor(private readonly deliveryNoteService: DeliveryNoteChildService) { }
  @Get("pagination")
  async paginationDeliveryNote(@Query() params: FilterDeliverNoteChildDto) {
    return this.deliveryNoteService.paginationDeliveryNoteChild(params);
  }

  @Post("pagination-child")
  async paginationDetails(@Body() body: FilterDeliverNoteChildDetailDto) {
    return this.deliveryNoteService.paginationDeliveryNoteChildDetail(body);
  }

  @Get("detail")
  public async detail(@Query() query: DetailDeliverNoteChildDto, @Req() req: IRequest) {
    const { id } = query;
    return await this.deliveryNoteService.detailDeliveryNoteChild(id, req);
  }

  @Post("create-inbound")
  public async createInbound(@Body() body: CreateInboundFromDeliveryNoteChildDto, @Req() req: IRequest) {
    return await this.deliveryNoteService.createInboundWithCombo(body, req);
  }

  @Post("send-partner")
  public async sendPartnerWarehouse(@Body() body: UUIDDto) {
    return await this.deliveryNoteService.sendPartner(body)
  }

}