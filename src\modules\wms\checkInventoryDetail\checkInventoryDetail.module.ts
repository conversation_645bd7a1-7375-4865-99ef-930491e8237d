import { Modu<PERSON> } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import { CheckInventoryDetailRepository, CheckInventoryRepository, ItemRepository, WarehouseRepository } from '../../../repositories'
import { CheckInventoryDetailService } from './checkInventoryDetail.service'
import { CheckInventoryDetailController } from './checkInventoryDetail.controller'
import { CheckInventoryDetailPublicController } from './checkInventoryDetailPublic.controller'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([CheckInventoryDetailRepository, ItemRepository, WarehouseRepository, CheckInventoryRepository])],
  controllers: [CheckInventoryDetailController, CheckInventoryDetailPublicController],
  providers: [CheckInventoryDetailService],
  exports: [CheckInventoryDetailService],
})
export class CheckInventoryDetailModule {}
