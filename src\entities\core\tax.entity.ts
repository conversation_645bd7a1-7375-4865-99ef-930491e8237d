import { Entity, Column, Index, Unique, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { ItemEntity } from './item.entity'

@Entity('tax')
export class TaxEntity extends BaseEntity {
  /** Mã thuế */
  @Index({ unique: true })
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  code: string

  /** <PERSON><PERSON><PERSON> thuế */
  @Column({ type: 'varchar', nullable: true })
  type: string

  /** Tên mức thuế */
  @Column({ type: 'varchar', nullable: true })
  name: string

  /** <PERSON><PERSON>n trăm thuế*/
  @Column({ type: 'varchar', nullable: true })
  percent: number

  @OneToMany((type) => ItemEntity, (p) => p.buyTax)
  itemBuyTaxes: Promise<ItemEntity[]>

  @OneToMany((type) => ItemEntity, (p) => p.sellTax)
  itemSellTaxes: Promise<ItemEntity[]>
}
