import { IsNotEmpty, IsUUID } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { ItemCategoryCreateDto } from './itemCategoryCreate.dto'

/** Interface Cập nhật quốc gia. */
export class ItemCategoryUpdateDto extends ItemCategoryCreateDto {
  @ApiProperty({ description: 'Id quốc gia.', example: 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx' })
  @IsNotEmpty()
  @IsUUID()
  id: string
}
