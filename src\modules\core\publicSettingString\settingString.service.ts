import { Injectable } from '@nestjs/common'
import { SettingStringRepository } from '../../../repositories'

import { In, Like } from 'typeorm'
import { UPDATE_SUCCESS, enumData } from '../../../constants'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { SettingStringEntity } from '../../../entities'
import { coreHelper } from '../../../helpers'
import { SettingStringUpdateDto } from './dto/settingStringUpdate.dto'
import { SettingStringUpdateActiveStatusDto } from './dto/settingStringUpdateActiveStatus.dto'

@Injectable()
export class PublicSettingStringService {
  constructor(private readonly repo: SettingStringRepository) {}

  public async findBanner() {
    let res = await this.repo.find({
      where: {
        code: In([
          enumData.SettingString.APP_SALE_BANNER_1.code,
          enumData.SettingString.APP_SALE_BANNER_2.code,
          enumData.SettingString.APP_SALE_BANNER_3.code,
          enumData.SettingString.APP_SALE_BANNER_4.code,
        ]),
      },
    })
    res.forEach((r: any) => (r.banner = r.banner[0]))
    return res
  }

  public async findBannerCustomer() {
    let res = await this.repo.find({
      where: {
        code: In([
          enumData.SettingString.APP_CUSTOMER_BANNER_1.code,
          enumData.SettingString.APP_CUSTOMER_BANNER_2.code,
          enumData.SettingString.APP_CUSTOMER_BANNER_3.code,
          enumData.SettingString.APP_CUSTOMER_BANNER_4.code,
        ]),
      },
    })
    res.forEach((r: any) => (r.banner = r.banner[0]))
    return res
  }

  public async findBannerLed() {
    let res = await this.repo.find({
      where: {
        code: In([
          enumData.SettingString.APP_LED_AD_1.code,
          enumData.SettingString.APP_LED_AD_2.code,
          enumData.SettingString.APP_LED_AD_3.code,
          enumData.SettingString.APP_LED_AD_4.code,
        ]),
      },
    })
    res.forEach((r: any) => (r.banner = r.banner[0]))
    return res
  }

  public async findBannerSME360() {
    let res = await this.repo.find({
      where: {
        code: In([
          enumData.SettingString.APP_SME360_1.code,
          enumData.SettingString.APP_SME360_2.code,
          enumData.SettingString.APP_SME360_3.code,
          enumData.SettingString.APP_SME360_4.code,
        ]),
      },
    })
    res.forEach((r: any) => (r.banner = r.banner[0]))
    return res
  }

  public async findBannerHealth() {
    let res = await this.repo.find({
      where: {
        code: In([
          enumData.SettingString.APP_HEALTH_1.code,
          enumData.SettingString.APP_HEALTH_2.code,
          enumData.SettingString.APP_HEALTH_3.code,
          enumData.SettingString.APP_HEALTH_4.code,
        ]),
      },
    })
    res.forEach((r: any) => (r.banner = r.banner[0]))
    return res
  }

  public async findBannerTourism() {
    let res = await this.repo.find({
      where: {
        code: In([
          enumData.SettingString.APP_TOURISM_1.code,
          enumData.SettingString.APP_TOURISM_2.code,
          enumData.SettingString.APP_TOURISM_3.code,
          enumData.SettingString.APP_TOURISM_4.code,
        ]),
      },
    })
    res.forEach((r: any) => (r.banner = r.banner[0]))
    return res
  }

  public async findBannerSocial() {
    let res = await this.repo.find({
      where: {
        code: In([
          enumData.SettingString.APP_AN_SINH_1.code,
          enumData.SettingString.APP_AN_SINH_2.code,
          enumData.SettingString.APP_AN_SINH_3.code,
          enumData.SettingString.APP_AN_SINH_4.code,
        ]),
      },
    })
    res.forEach((r: any) => (r.banner = r.banner[0]))
    return res
  }

  public async findBannerFood() {
    let res = await this.repo.find({
      where: {
        code: In([
          enumData.SettingString.APP_FOOD_1.code,
          enumData.SettingString.APP_FOOD_2.code,
          enumData.SettingString.APP_FOOD_3.code,
          enumData.SettingString.APP_FOOD_4.code,
        ]),
      },
    })
    res.forEach((r: any) => (r.banner = r.banner[0]))
    return res
  }

  public async findTermAppSale() {
    return await this.repo.findOne({
      where: {
        code: In([enumData.SettingString.APP_SALE_TERM.code]),
      },
    })
  }

  public async findTermSale() {
    let res = await this.repo.find({
      where: {
        code: In([
          enumData.SettingString.APP_SALE_BANNER_1.code,
          enumData.SettingString.APP_SALE_BANNER_2.code,
          enumData.SettingString.APP_SALE_BANNER_3.code,
          enumData.SettingString.APP_SALE_BANNER_4.code,
        ]),
      },
    })
    return res.map((item) => ({
      content: item.valueString,
      image: item.banner[0],
    }))
  }

  public async findAll() {
    return await this.repo.find()
  }

  public async loadData() {
    return await this.repo.find()
  }

  public async find(data: any) {
    return await this.repo.find(data)
  }

  /** Hàm load dữ liệu trong select box */
  public async loadDataSelectBox(user: UserDto) {
    return await this.repo.find({
      select: {
        id: true,
        code: true,
        name: true,
      },
      order: {
        name: 'ASC',
      },
    })
  }

  public async findById(id: string) {
    const foundSettingString = await this.repo.findOne({
      where: {
        id: id,
      },
    })
    if (!foundSettingString) {
      throw new Error('Không tìm thấy setting string theo ID')
    }
    return foundSettingString
  }

  public async findOneByCode(data: FilterOneDto) {
    const foundSettingString = await this.repo.findOne({
      where: {
        code: data.code,
      },
    })
    if (!foundSettingString) {
      throw new Error('Không tìm thấy setting string theo code')
    }

    return foundSettingString
  }

  public async findListByCode(data: any) {
    const foundSettingString = await this.repo.findOne({
      where: {
        code: In(data.lstCode),
      },
    })
    if (!foundSettingString) {
      throw new Error('Không tìm thấy setting string theo danh sách code')
    }

    return foundSettingString
  }

  public async update(user: UserDto, data: SettingStringUpdateDto) {
    await this.repo.delete({})

    const listTask = []

    for (const settingStringItem of data.listSettingString) {
      const newSettingStringItem = new SettingStringEntity()
      newSettingStringItem.name = settingStringItem.name
      newSettingStringItem.code = settingStringItem.code
      newSettingStringItem.value = settingStringItem.value
      newSettingStringItem.valueString = settingStringItem.valueString
      newSettingStringItem.type = settingStringItem.type
      if (settingStringItem.banner && settingStringItem.banner !== null) {
        newSettingStringItem.banner = settingStringItem.banner
      } else {
        newSettingStringItem.banner = []
      }
      newSettingStringItem.createdBy = user?.id
      newSettingStringItem.updatedBy = user?.id
      newSettingStringItem.updatedAt = new Date()
      listTask.push(newSettingStringItem)
    }

    await this.repo.insert(listTask)
    return {
      message: UPDATE_SUCCESS,
    }
  }

  public async updateActiveStatus(user: UserDto, data: SettingStringUpdateActiveStatusDto) {
    const { id } = data

    const foundSettingString = await this.repo.findOne({
      where: {
        id,
      },
    })

    if (!foundSettingString) throw new Error('Không tìm thấy setting string')

    foundSettingString.isDeleted = !foundSettingString.isDeleted
    foundSettingString.updatedAt = new Date()

    if (user?.id) foundSettingString.updatedBy = user.id

    const updatedSettingString = await this.repo.save(foundSettingString)
    return {
      message: 'Cập nhật trạng thái thành công!',
      data: updatedSettingString,
    }
  }

  async pagination(user: UserDto, data: PaginationDto) {
    const whereCondition: any = {}
    if (data.where) {
      if (data.where.isDeleted != undefined) whereCondition.isDeleted = data.where.isDeleted
      if (data.where.code) whereCondition.code = Like(`%${data.where.code}%`)
      if (data.where.name) whereCondition.name = Like(`%${data.where.name}%`)
    }

    const defaultSettingStrings = coreHelper.convertObjToArray(enumData.SettingString)

    const resSettingString = await this.repo.findAndCount({
      where: whereCondition,
      order: {
        code: 'ASC',
      },
    })

    let result = defaultSettingStrings
    let length = defaultSettingStrings.length
    if (resSettingString[0].length >= defaultSettingStrings.length) {
      result = resSettingString[0]
      length = resSettingString[1]
    }
    let listResSettingCode = resSettingString[0].map((x) => x.code)
    let mapResSetting = resSettingString[0].convertToMap((x) => x.code)
    for (let item of result) {
      if (listResSettingCode.includes(item.code)) {
        Object.assign(item, mapResSetting.get(item.code))
      }
    }
    return [result, length]
  }
}
