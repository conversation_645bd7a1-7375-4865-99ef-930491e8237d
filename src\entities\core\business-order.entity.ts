import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { WarehouseEntity } from '..'

@Entity('business_order')
export class BusinessOrderEntity extends BaseEntity {
  @Column({ type: 'uuid', nullable: false })
  businessPurchaseOrderId: string

  @Column({ type: 'numeric' })
  totalPrice: number

  //trạng thái duyệt
  @Column({ type: 'varchar', nullable: false })
  approveStatus: string

  //trạng thái giao hàng
  @Column({ type: 'varchar', nullable: false })
  deliveryStatus: string

  @Column({ type: 'uuid', nullable: false })
  warehouseId: string

  @ManyToOne(() => WarehouseEntity, (p) => p.id)
  @JoinColumn({ name: 'warehouseId', referencedColumnName: 'id' })
  warehouse: Promise<WarehouseEntity>
}
