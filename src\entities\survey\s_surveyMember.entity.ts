import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from 'typeorm'
import { BaseEntity } from '../core/base.entity'
import { SurveyEntity } from './s_survey.entity'
import { SurveyQuestionEntity } from './s_surveyQuestion.entity'
import { enumData } from '../../constants'

/** Phiếu khảo sát - nhân viên */
@Entity('s_survey_member')
export class SurveyMemberEntity extends BaseEntity {
  /** Id phiếu khảo sát */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  surveyId: string
  @ManyToOne(() => SurveyEntity, (p) => p.questions)
  @JoinColumn({ name: 'surveyId', referencedColumnName: 'id' })
  survey: Promise<SurveyEntity>

  /** Id member */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  userId: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  address: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  phone: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  email: string

  @Column({
    type: 'varchar',
    length: 50,
    default: 'Company',
    nullable: true,
  })
  type: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  tax: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  company: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  companyId: string

  @Column({
    type: 'float',
    nullable: true,
  })
  score: number

  @Column({
    nullable: true,
    default: enumData.SurveyMemberStatus.NotStart.code,
  })
  status: string
}
