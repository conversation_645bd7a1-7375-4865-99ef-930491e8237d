import { IsNotEmpty, IsOptional, IsString } from 'class-validator'
import { NSItemGroup } from '../../../../constants/NSItemGroup'
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'

export class ItemGroupCreateExcelDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  code: string

  @ApiPropertyOptional({
    enum: NSItemGroup.EGroupType,
    default: NSItemGroup.EGroupType.PRODUCT,
  })
  @IsOptional()
  groupType: NSItemGroup.EGroupType = NSItemGroup.EGroupType.PRODUCT

  @ApiProperty()
  @IsOptional()
  description: string

  @ApiProperty()
  @IsOptional()
  itemCategoryCode?: string

  lstMediaProduct?: MediaDto[]
}

export class MediaDto {
  @ApiProperty({ description: 'Id sản phẩm' })
  @IsString()
  productId?: string

  @ApiProperty({ description: 'Đ<PERSON>ờng dẩn của file' })
  @IsNotEmpty({ message: 'Đường dẩn của file không được để trống' })
  @IsString()
  url: string

  content: string
  table: string

  @ApiProperty({ description: 'Tên file' })
  @IsNotEmpty({ message: 'Tên file không được để trống' })
  @IsString()
  name: string
}
