import { InboundCostAllocationEntity, InboundDetailCostPriceEntity, InboundDetailEntity, InboundEntity, InboundHistoryEntity } from '../../entities'
import { Repository } from 'typeorm'
import { CustomRepository } from '../../typeorm'

@CustomRepository(InboundEntity)
export class InboundRepository extends Repository<InboundEntity> {}

@CustomRepository(InboundCostAllocationEntity)
export class InboundCostAllocationRepository extends Repository<InboundCostAllocationEntity> {}
@CustomRepository(InboundDetailEntity)
export class InboundDetailRepository extends Repository<InboundDetailEntity> {}

@CustomRepository(InboundDetailCostPriceEntity)
export class InboundDetailCostPriceRepository extends Repository<InboundDetailCostPriceEntity> {}

@CustomRepository(InboundHistoryEntity)
export class InboundHistoryRepository extends Repository<InboundHistoryEntity> {}
