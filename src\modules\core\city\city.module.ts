import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import { CityRepository, RegionCityRepository } from '../../../repositories'
import { CityService } from './city.service'
import { CityController } from './city.controller'
import { CityPublicController } from './cityPublic.controller'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([CityRepository, RegionCityRepository])],
  controllers: [CityController, CityPublicController],
  providers: [CityService],
  exports: [CityService],
})
export class CityModule { }
