import { Injectable, Req } from "@nestjs/common";
import { PurchaseOrderService } from "../../core/purchaseOrder/purchaseOrder.service";
import { DetailPurchaseOrderDto, ListPoDto, ListPoReq } from "../../core/purchaseOrder/dto";
import { omsApiHelper } from "../../../helpers/omsApiHelper";
import { Request as IRequest } from 'express'
import { PaginationDto } from "../../../dto";
import { CardSalesHistoryPartnerSearchReq, ListCardReq, ListIdReq, ListPartnerReq, UUIDReq } from "../dto/integration.dto";

@Injectable()
export class IntegrationService {
  constructor(
    private readonly purchaseOrderService: PurchaseOrderService,
  ) { }

  // Dùng skip, take
  public async listPurchaseOrder(params: ListPoReq) {
    return await this.purchaseOrderService.purchaseOrderList(params);
  }

  // Chi tiết PO
  public async detailPurchaseOrder(query: DetailPurchaseOrderDto) {
    return await this.purchaseOrderService.detail(query);
  }

  // Dùng skip, take
  public async listSaleOrder(req: IRequest, params: PaginationDto) {
    return await omsApiHelper.getOrderByIds(req, params)
  }

  // Chi tiết đơn hàng
  public async detailSaleOrder(req: IRequest, id: string) {
    return await omsApiHelper.getDetailOrder(req, id)
  }

  // Danh sách thẻ
  public async listCard(req: IRequest, params: ListCardReq) {
    return await omsApiHelper.listCard(req, params)
  }

  // Danh sách điểm giao hàng
  public async listPartner(req: IRequest, params: ListPartnerReq) {
    return await omsApiHelper.listPartner(req, params)
  }

  // Lịch sử thẻ đã bán
  public async cardSalesHistory(req: IRequest, params: CardSalesHistoryPartnerSearchReq) {
    return await omsApiHelper.cardSalesHistory(req, params)
  }

  // Cập nhật trạng thái đơn hàng
  public async updateSOStatus(req: IRequest, data: ListIdReq) {
    const { ids, status } = data
    return await omsApiHelper.updateSOStatus(req, { ids, status })
  }
}