import { Column, Entity, Index } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { BaseEntity } from './base.entity'
import { NSPo } from '../../constants';

@Entity('delivery_note_tracking')
export class DeliveryNoteTrackingEntity extends BaseEntity {
  @ApiProperty({ description: "ID của Delivery" })
  @Column("uuid")
  @Index()
  deliveryNoteId: string;

  @ApiProperty({ description: "ID của PO" })
  @Column("uuid")
  @Index()
  poId: string;

  @ApiProperty({ description: "Code" })
  @Column("varchar", { nullable: true })
  code: string;

  @ApiProperty({ description: "ID của PO Group" })
  @Column("varchar")
  poGroup: string;

  @ApiProperty({ description: "ID điểm bắt đầu" })
  @Column("uuid", { nullable: true })
  from: string

  @ApiProperty({ description: "ID điểm kết thúc" })
  @Column("uuid", { nullable: true })
  to: string

  @ApiProperty({ description: "Tứ tự chặng" })
  @Column("numeric", { nullable: true, default: 0 })
  step: number

  @ApiProperty({ description: "Điểm cuối" })
  @Column("boolean", { nullable: true, default: false })
  isLast: Boolean

  @ApiProperty({ description: "Điểm đóng gói" })
  @Column("boolean", { nullable: true, default: false })
  isPacking: Boolean

  @ApiProperty({ description: "Loại phiếu EDeliveryTrackingType" })
  @Column("varchar", { nullable: true })
  trackingType: NSPo.EDeliveryTrackingType

  @ApiProperty({ description: "Ngày mong muốn hoàn thành phiếu" })
  @Column("timestamptz", { nullable: true })
  expectedDate: Date

  @ApiPropertyOptional({
    description: `Trạng thái Tracking ${Object.values(NSPo.EDeliveryTracking).join(' | ')}`,
    enum: NSPo.EDeliveryTracking,
    default: NSPo.EDeliveryTracking.PENDING,
  })
  @Column({ default: NSPo.EDeliveryTracking.PENDING })
  receivingStatus: NSPo.EDeliveryTracking;

  @ApiPropertyOptional({
    description: `Danh sách tệp đính kèm`
  })
  @Column("jsonb", { nullable: true })
  files: string[];

  @ApiProperty({ description: "ID của Supplier (NCC)" })
  @Column("uuid", { nullable: true })
  supplierId: string

  @ApiProperty({ description: "ID của Warehouse (Kho)" })
  @Column("uuid", { nullable: true })
  warehouseId: string
}