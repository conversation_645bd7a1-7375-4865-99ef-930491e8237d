import { <PERSON>ti<PERSON>, Column, <PERSON>ToMany, ManyTo<PERSON>ne, Jo<PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm'
import { BaseEntity } from '../core/base.entity'
import { QuestionEntity } from './s_question.entity'
import { CategoriesEntity } from './s_categories.entity'
import { SurveyHistoryEntity } from './s_surveyHistory.entity'

/** Chủ đề */
@Entity('s_topic')
export class TopicEntity extends BaseEntity {
  /** Tên */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  name: string

  /** Mã */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  /** Mô tả */
  @Column({
    type: 'text',
    nullable: true,
  })
  description: string

  @Column({
    type: 'boolean',
    default: false,
    nullable: true,
  })
  isTail: boolean

  /** Danh sách câu hỏi */
  @OneToMany(() => QuestionEntity, (p) => p.topic)
  questions: Promise<QuestionEntity[]>

  /** Chủ đề */
  @Column({
    nullable: true,
  })
  categoryId: string
  @ManyToOne(() => CategoriesEntity, (p) => p.topics)
  @JoinColumn({ name: 'categoryId', referencedColumnName: 'id' })
  category: Promise<CategoriesEntity>

  /** Lịch sử */
  @OneToMany(() => SurveyHistoryEntity, (p) => p.topic)
  histories: Promise<SurveyHistoryEntity[]>
}
