import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'

export class ContactDto {
  @ApiProperty({ description: 'Tên khách hàng' })
  @IsNotEmpty()
  @IsString()
  fullName: string

  @ApiProperty({ description: 'Quan tâm đến' })
  @IsNotEmpty()
  @IsString()
  interest: string

  @ApiProperty({ description: 'Email khách hàng' })
  @IsNotEmpty()
  @IsString()
  email: string

  @ApiProperty({ description: 'Chủ đề' })
  @IsString()
  topic: string

  @ApiProperty({ description: 'Trạng thái đã đọc' })
  isRead: boolean

  @ApiProperty({ description: 'Tin nhắn của khách hàng' })
  @IsNotEmpty()
  @IsString()
  message: string
}
