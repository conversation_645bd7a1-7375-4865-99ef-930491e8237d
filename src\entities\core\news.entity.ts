import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm'
import { enumData } from '../../constants'
import { BaseEntity } from './base.entity'
import { NewsCategoryEntity } from './newsCategory.entity'

/** Tin tức */
@Entity('news')
export class NewsEntity extends BaseEntity {
  /** Mã tin tức */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: false,
  })
  code: string

  /** Tên tin tức */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  title: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  image: string

  @Column({ type: 'varchar', nullable: true })
  newsCategoryId: string
  @ManyToOne(() => NewsCategoryEntity, (p) => p.news)
  @JoinColumn({ name: 'newsCategoryId', referencedColumnName: 'id' })
  newsCategory: Promise<NewsCategoryEntity>

  /** <PERSON><PERSON><PERSON> trị chữ */
  @Column({
    type: 'text',
    nullable: true,
  })
  content: string
}
