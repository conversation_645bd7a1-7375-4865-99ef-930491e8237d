import { ConflictException, Injectable } from '@nestjs/common'
import { CompanyCreateDto, CompanyCreateExcelDto, CompanyUpdateDto } from './dto'
import { Like } from 'typeorm'
import { QuestionService } from '../question/question.service'
import { CategoriesRepository, CompanyCategoriesRepository, CompanyRepository, TopicRepository, UserRepository } from '../../../repositories'
import { PaginationDto, UserDto } from '../../../dto'
import { CREATE_SUCCESS, enumData, ERROR_CODE_TAKEN, ERROR_NOT_FOUND_DATA, UPDATE_ACTIVE_SUCCESS, UPDATE_SUCCESS } from '../../../constants'
import { CategoriesEntity, CompanyCategoriesEntity, CompanyEntity } from '../../../entities/survey'
import { IdDto } from '../../../dto/id.dto'
import { UserEntity } from '../../../entities'

@Injectable()
export class CompanyService {
  constructor(
    private readonly repo: CompanyRepository,
    private readonly userRepo: UserRepository,
    private companyCategoryRepo: CompanyCategoriesRepository,
    private categoriesRepo: CategoriesRepository,
    private topicRepo: TopicRepository,
    private questionService: QuestionService,
  ) {}

  /** Lấy ds công ty (phân trang) */
  public async pagination(data: PaginationDto) {
    const whereCon: any = {}
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.taxCode) whereCon.taxCode = Like(`%${data.where.taxCode}%`)

    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      relations: { companyCategories: true },
      order: { createdAt: 'DESC' },
    })

    for (const item of res[0]) {
      item.numUser = item.__users__?.length || 0
      delete item.__users__
      item.numCate = item.__companyCategories__?.length || 0
      item.lstCate = item.__companyCategories__
      let arrCate = item?.__companyCategories__?.filter((x) => x.isDeleted === false)
      if (arrCate) {
        item.lstCateId = arrCate.map((x: any) => x.categoriesId) || []
      }

      delete item.__companyCategories__
    }

    return res
  }

  /** Tạo công ty */
  public async createData(user: UserDto, data: CompanyCreateDto) {
    if (!data.username) throw new Error('Vui lòng nhập tài khoản!')
    if (!data.password) throw new Error('Vui lòng nhập mật khẩu!')

    const isUser = await this.userRepo.findOne({
      where: { username: data.username },
      select: { id: true },
    })
    if (isUser) throw new ConflictException(`Tài khoản [${data.username}] đã được sử dụng!`)
    if (data.taxCode) {
      const taxNumber = await this.repo.findOne({ where: { taxCode: data.taxCode }, select: { id: true } })
      if (taxNumber) throw new Error('Mã Số Thuế Đã Được Sử Dụng')
    }

    const isCodeTaken = await this.repo.findOne({
      where: { code: data.code },
      select: { id: true },
    })
    if (isCodeTaken) throw new ConflictException(`Mã công ty [${data.code}] đã được sử dụng!`)

    const companyNew = this.repo.create(data)
    companyNew.createdBy = user.id
    companyNew.createdAt = new Date()
    const Company = await companyNew.save()

    const newUserEntity = await this.userRepo.create({
      companyId: Company.id,
      username: data.username,
      password: data.password,
      email: data.email,
      type: enumData.UserType.CompanyAdmin.code,
      createdBy: user.id,
      createdAt: new Date(),
    })
    await newUserEntity.save()

    if (data.lstCateId && data.lstCateId.length == 0) throw new Error(`Thêm ít nhất một danh mục. Vui lòng kiểm tra lại`)

    if (data.lstCateId && data.lstCateId.length > 0) {
      for (let item of data.lstCateId) {
        const checkCat = await this.categoriesRepo.findOne({ where: { id: item } })
        if (!checkCat) throw new Error(`Không tồn tại danh mục. Vui lòng kiểm tra lại`)
        if (checkCat.isDeleted === true) throw new Error(`Danh mục đang ngưng hoạt động. Vui lòng kiểm tra lại`)
        const companyCat = new CompanyCategoriesEntity()
        companyCat.categoriesId = item
        companyCat.companyId = Company.id
        companyCat.createdAt = new Date()
        companyCat.createdBy = user.id
        await this.companyCategoryRepo.save(companyCat)
      }
    }

    return { message: CREATE_SUCCESS }
  }

  /** Cập nhật công ty */
  public async updateData(user: UserDto, data: CompanyUpdateDto) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    if (data.taxCode != entity.taxCode) {
      if (data.taxCode) {
        const taxNumber = await this.repo.findOne({ where: { taxCode: data.taxCode }, select: { id: true } })
        if (taxNumber) throw new Error('Mã Số Thuế Đã Được Sử Dụng')

        entity.taxCode = data.taxCode
      }
    }

    if (entity.code != data.code) {
      const isTaken = await this.repo.findOne({
        where: { code: data.code },
        select: { id: true },
      })
      if (isTaken) throw new ConflictException(ERROR_CODE_TAKEN)

      entity.code = data.code
    }
    if (entity.email != data.email) {
      // cập nhật email cho user admin Company
      await this.userRepo.update(
        { companyId: entity.id, type: enumData.UserType.CompanyAdmin.code },
        { email: data.email, updatedBy: user.id, updatedAt: new Date() },
      )
      entity.email = data.email
    }
    entity.updatedBy = user.id
    entity.updatedAt = new Date()
    entity.name = data.name
    entity.domain = data.domain
    entity.contactName = data.contactName
    entity.taxCode = data.taxCode
    entity.phone = data.phone
    entity.address = data.address
    entity.bankName = data.bankName
    entity.bankBranch = data.bankBranch
    entity.bankAccount = data.bankAccount
    entity.contactName = data.contactName
    entity.contactNumberPhone = data.contactNumberPhone

    if (data.lstCateId && data.lstCateId.length > 0) {
      for (let item of data.lstCateId) {
        const checkCat = await this.categoriesRepo.findOne({ where: { id: item } })
        if (!checkCat) throw new Error(`Không tồn tại danh mục. Vui lòng kiểm tra lại`)
        if (checkCat.isDeleted === true) throw new Error(`Danh mục đang ngưng hoạt động. Vui lòng kiểm tra lại`)
        const checkCate = await this.companyCategoryRepo.findOne({ where: { categoriesId: item, companyId: entity.id } })
        if (checkCate) {
          checkCate.categoriesId = item
          checkCate.companyId = entity.id
          checkCate.updatedAt = new Date()
          checkCate.updatedBy = user.id
          await this.companyCategoryRepo.save(checkCate)
        } else {
          const companyCat = new CompanyCategoriesEntity()
          companyCat.categoriesId = item
          companyCat.companyId = entity.id
          companyCat.createdAt = new Date()
          companyCat.createdBy = user.id
          await this.companyCategoryRepo.save(companyCat)
        }
      }
    }

    await entity.save()

    return { message: UPDATE_SUCCESS }
  }

  /** Cập nhật trạng thái hoạt động công ty */
  public async updateActive(user: UserDto, data: IdDto) {
    const entity = await this.repo.findOne({ where: { id: data.id }, relations: { companyCategories: true } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    let companyCate = (await entity.companyCategories).filter((x) => x.isDeleted === false)
    if (companyCate && companyCate.length > 0)
      throw new Error(`Không thể ngưng hoạt động công ty được vì đã có danh mục tồn tại. Vui lòng kiểm tra lại`)
    await this.repo.update(data.id, {
      isDeleted: !entity.isDeleted,
      updatedAt: new Date(),
      updatedBy: user.id,
    })

    const lstUser = await this.userRepo.find({ where: { companyId: entity.id } })
    for (let item of lstUser) {
      await this.userRepo.update(
        {
          id: item.id,
          companyId: entity.id,
        },
        {
          isDeleted: !entity.isDeleted,
          updatedAt: new Date(),
          updatedBy: user.id,
        },
      )
    }

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  /** Lấy tất cả user của công ty */
  public async loadUser(data: { companyId: string; isDeleted?: boolean }) {
    const whereCon: any = { companyId: data.companyId }
    if (data.isDeleted != undefined) whereCon.isDeleted = data.isDeleted

    const res: any[] = await this.userRepo.find({
      where: whereCon,
      order: { type: 'ASC' },
    })

    for (const item of res) {
      item.typeName = enumData.UserType[item.type].name
    }

    return res
  }

  /** Lấy tất cả danh mục của công ty */
  public async loadCategories(data: { companyId: string; isDeleted?: boolean }) {
    const whereCon: any = { companyId: data.companyId }
    if (data.isDeleted != undefined) whereCon.isDeleted = data.isDeleted
    const res: any[] = await this.companyCategoryRepo.find({
      where: whereCon,
      order: { createdAt: 'DESC' },
    })
    for (const item of res) {
      let cate = await item.categories
      item.categoriesName = cate.name
      item.categoriesCode = cate.code
      delete item.__categories__
      const lstTopic = await this.topicRepo.find({
        where: { isDeleted: false },
      })
      item.numTopic = lstTopic.length || 0
    }
    return res
  }

  /** tạo mới công ty bằng excel */
  public async createDataByExcel(user: UserDto, data: CompanyCreateExcelDto[]) {
    await this.repo.manager.transaction('READ UNCOMMITTED', async (trans) => {
      const repo = trans.getRepository(CompanyEntity)
      const cateRepo = trans.getRepository(CategoriesEntity)
      const userRepo = trans.getRepository(UserEntity)
      // danh sách công ty
      const dictCompany: any = {}
      {
        const lstCompany = await repo.find({ where: { isDeleted: false } })
        lstCompany.forEach((c) => (dictCompany[c.code] = c.id))
      }

      // danh sách danh mục
      const dictCate: any = {}
      {
        const lstCate = await cateRepo.find({ where: { isDeleted: false } })
        lstCate.forEach((c) => (dictCate[c.code] = c.id))
      }

      // danh sách user
      const dictUser: any = {}
      {
        const lstUser = await userRepo.find({ where: { isDeleted: false } })
        lstUser.forEach((c) => (dictUser[c.username] = c.id))
      }
      if (data.length === 0) throw new Error(`Vui lòng thêm ít nhất 1 dòng dữ liệu`)
      for (const item of data) {
        if (dictCompany[item.code]) throw new Error(`Mã công ty [${item.code}] đã có trong hệ thống`)
        if (!item.username) throw new Error('Vui lòng nhập tài khoản!')
        if (!item.password) throw new Error('Vui lòng nhập mật khẩu!')
        if (dictUser[item.username]) throw new Error(`Tài khoản [${item.username}] đã có trong hệ thống`)
        let arrCate: any = item.lstCateCode.split(',')
        if (arrCate.length === 0) throw new Error(`Vui lòng thêm ít nhất một danh mục`)
        if (arrCate.length > 0) {
          for (let x of arrCate) {
            if (!dictCate[x]) throw new Error(`Mã danh mục [${x}] không có trong hệ thống`)
          }
        }
        const taxNumber = await repo.findOne({ where: { taxCode: item.taxCode }, select: { id: true } })
        if (taxNumber) throw new Error('Mã Số Thuế Đã Được Sử Dụng')
      }
      for (let item of data) {
        const company = new CompanyEntity()
        company.createdBy = user.id
        company.createdAt = new Date()
        company.name = item.name
        company.code = item.code
        company.domain = item.domain || `${item.code}.survey.io`
        company.contactName = item.contactName
        company.taxCode = item.taxCode
        company.phone = item.phone
        company.address = item.address
        company.bankName = item.bankName
        company.bankBranch = item.bankBranch
        company.bankAccount = item.bankAccount
        company.contactName = item.contactName
        company.contactNumberPhone = item.contactNumberPhone
        const newEntity = await trans.getRepository(CompanyEntity).save(company)
        let arrCate: any = item.lstCateCode.split(',')
        if (arrCate.length > 0) {
          for (let x of arrCate) {
            const companyCat = new CompanyCategoriesEntity()
            companyCat.categoriesId = dictCate[x]
            companyCat.companyId = newEntity.id
            companyCat.createdAt = new Date()
            companyCat.createdBy = user.id
            await trans.getRepository(CompanyCategoriesEntity).save(companyCat)
          }
        }
        const userEntity = new UserEntity()
        userEntity.companyId = newEntity.id
        userEntity.username = item.username
        userEntity.password = item.password.toString()
        userEntity.email = item.email
        userEntity.type = enumData.UserType.CompanyAdmin.code
        userEntity.createdBy = user.id
        userEntity.createdAt = new Date()
        await trans.getRepository(UserEntity).save(userEntity)
      }
    })
    return { message: CREATE_SUCCESS }
  }

  /** Cập nhật trạng thái hoạt động công ty */
  public async updateActiveCompanyCate(user: UserDto, data: IdDto) {
    const entity = await this.companyCategoryRepo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    await this.companyCategoryRepo.update(data.id, {
      isDeleted: !entity.isDeleted,
      updatedAt: new Date(),
      updatedBy: user.id,
    })
    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  /** hàm sync data topic về site dử liệu admin  */
  // public async syncDataTopic(user: UserDto, data: { companyId: string; categoriesId?: string }) {
  //   const company = await this.repo.findOne({ where: { id: data.companyId, isDeleted: false } })
  //   if (!company) throw new Error(`Công ty đã bị xóa hoặc ngưng hoạt động. Vui lòng kiểm tra lại.`)
  //   const cate = await this.categoriesRepo.findOne({ where: { id: data.categoriesId, isDeleted: false } })
  //   if (!cate) throw new Error(`Danh mục ngưng hoạt động hoặc đã bị xóa. Vui lòng kiểm tra lại`)
  //   const check = await this.companyCategoryRepo.findOne({ where: { categoriesId: cate.id, companyId: company.id, isSync: true } })
  //   if (check) throw new Error(`Đã đồng bộ dử liệu. Vui lòng kiểm tra lại`)
  //   const lstTopic = await this.topicRepo.find({ where: { categoriesId: cate.id, isDeleted: false } })
  //   if (lstTopic.length === 0) throw new Error(`Chưa thiết lập chủ đề cho danh mục [${cate.name}]. Vui lòng kiểm tra lại`)
  //   let lstData: any = []
  //   for (let item of lstTopic) {
  //     lstData.push({
  //       name: item.name,
  //       code: item.code,
  //       categoriesId: item.categoriesId,
  //       description: item.description,
  //       createdBy: user.id,
  //       companyId: company.id,
  //     })
  //   }
  //   // TODO ĐỒNG BỘ CÂU HỎI và topic
  //   await surveyApiHelper.createTopicMasterData(lstData)
  //   for (let item of lstTopic) {
  //     let lstQuestion: any = await this.questionService.getQuestionListByTopic({ topicId: item.id })
  //     if (lstQuestion && lstQuestion.length > 0) {
  //       let lstQuestionData: any = []
  //       for (let x of lstQuestion) {
  //         lstQuestionData.push({
  //           topicCode: item.code,
  //           topicName: item.name,
  //           code: x.code,
  //           categoriesId: item.categoriesId,
  //           companyId: company.id,
  //           createdBy: user.id,
  //           name: x.name,
  //           isRequired: x.isRequired,
  //           type: x.type,
  //           isHighlight: x.isHighlight,
  //           hightlightValue: x.hightlightValue,
  //           sort: x.sort,
  //           percent: x.percent,
  //           percentRule: x.percentRule,
  //           level: x.level,
  //           description: x.description,
  //           parentId: x.parentId,
  //           scoreDLC: x.scoreDLC,
  //           requiredMin: x.requiredMin,
  //           isCalUp: x.isCalUp,
  //           percentDownRule: x.percentDownRule,
  //           lstDetail: await x.__questionListDetails__,
  //           childs: await x.childs,
  //         })
  //       }
  //       await surveyApiHelper.createQuestionMasterData(lstQuestionData)
  //     }
  //   }
  //   await this.companyCategoryRepo.update(
  //     {
  //       categoriesId: cate.id,
  //       companyId: company.id,
  //     },
  //     {
  //       isSync: true,
  //     },
  //   )
  //   return { message: 'Đồng bộ dữ liệu thành công' }
  // }
  /** Hàm tìm kiếm */
  public async find(user: UserDto, data: { isDeleted?: boolean }) {
    let whereCon: any = {}
    if (data.isDeleted != undefined) whereCon.isDeleted = data.isDeleted
    const res = await this.repo.find({ where: whereCon, order: { name: 'ASC' } })
    return res
  }
}
