import { Column, Entity } from 'typeorm'
import { BaseEntity } from '.'

@Entity('contact')
export class ContactEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  fullName: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  interest: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  email: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  topic: string

  @Column({
    type: 'bool',
    default: false,
  })
  isRead: boolean

  @Column({
    type: 'varchar',
    length: 10000,
    nullable: true,
  })
  message: string
}
