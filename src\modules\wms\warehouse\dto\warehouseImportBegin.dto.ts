import { ApiProperty } from '@nestjs/swagger'
import { IsString, IsOptional } from 'class-validator'

export class InboundCreateDto {
  @ApiProperty({ description: 'Mã kho' })
  @IsOptional()
  @IsString()
  warehouseCode: string

  @ApiProperty({ description: 'Mã sp' })
  @IsOptional()
  @IsString()
  itemCode: string

  @ApiProperty({ description: 'Tên sp' })
  @IsOptional()
  @IsString()
  itemName: string

  @ApiProperty({ description: 'sl' })
  @IsOptional()
  quantityBegin: number

  @ApiProperty({ description: 'Ngày sx' })
  @IsOptional()
  manufactureDate: Date

  @ApiProperty({ description: 'Hạn sd' })
  @IsOptional()
  expiryDate: Date

  @ApiProperty({ description: 'Số lô' })
  @IsOptional()
  @IsString()
  lotNumber: string
}
