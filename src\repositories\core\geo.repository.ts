import { Repository } from 'typeorm'
import { CustomRepository } from '../../typeorm'
import { CityEntity, DistrictEntity, RegionCityEntity, RegionEntity, WardEntity } from '../../entities'

@CustomRepository(CityEntity)
export class CityRepository extends Repository<CityEntity> { }

@CustomRepository(DistrictEntity)
export class DistrictRepository extends Repository<DistrictEntity> { }

@CustomRepository(WardEntity)
export class WardRepository extends Repository<WardEntity> { }

@CustomRepository(RegionEntity)
export class RegionRepository extends Repository<RegionEntity> { }

@CustomRepository(RegionCityEntity)
export class RegionCityRepository extends Repository<RegionCityEntity> { }