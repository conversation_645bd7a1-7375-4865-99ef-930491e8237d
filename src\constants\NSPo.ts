export namespace NSPo {
    export enum EPoType {
        WITHCOMBO = 'WITHCOMBO', // THEO COMBO
        WITHPRODUCT = 'WITHPRODUCT', // THEO SẢN PHẨM
        MANUAL = 'MANUAL', // PO Đặt trước
        OTHER = "OTHER", // Bán Lẻ
        SUPPLIER = "SUPPLIER", // PO Tạo từ NCC
    }

    export enum EPoStatus {
        NEWLYCREATED = 'NEWLYCREATED', // MỚI TẠO
        SENT = 'SENT', // ĐÃ GỬI TỚI NCC,NPP / 3PL
        PENDING_APPROVE = 'PENDING_APPROVE', // CHỜ DUYỆT
        PENDING_CONFIRM = 'PENDING_CONFIRM', // CHỜ XÁC NHẬN
        APPROVED = 'APPROVED', // ĐÃ DUYỆT
        COMPLETE = 'COMPLETE', // HOÀN THÀNH
        CONFIRMED = 'CONFIRMED', // 
        CANCEL = 'CANCEL', // HỦY
        REJECT = 'REJECT' // TỪ CHỐI
    }

    export enum EPoStatusPayment {
        UNPAID = 'UNPAID', // CHƯA THANH TOÁN
        PAID = 'PAID', // ĐÃ THANH TOÁN
        PARTIALLYPAYMENT = 'PARTIALLYPAYMENT', // THÁNH TOÁN MỘT PHẦN
    }

    export enum EPoTypePayment {
        CASH = 'CASH', // TIỀN MẶT
        TRANSFER = 'TRANSFER', // CHUYỂN KHOẢNG
    }

    export enum EPoStatusDistribute {
        NEWLYCREATED = 'NEWLYCREATED', // MỚI TẠO
        DELIVERED = 'DELIVERED', // ĐÃ GIAO HÀNG
    }

    export enum EDeliverytNoteStatus {
        NEWLYCREATED = 'NEWLYCREATED', // MỚI TẠO
        SENT = 'SENT', // ĐÃ GỬI 
    }

    export enum EDeliveryStatus {
        NEWLYCREATED = 'NEWLYCREATED', // MỚI TẠO
        PARTIALLY_RECEIVED = 'PARTIALLY_RECEIVED',   // Đã nhận 1 phần
        RECEIVED = 'RECEIVED',   // Đã nhận
        PACKED = 'PACKED', // Đã đóng gói
        PENDING_PACKING = 'PENDING_PACKING', // Chờ đóng gói
        COMPLETE = 'COMPLETE', // Hoàn thành
        SENT = 'SENT', // ĐÃ GỬI 
        PARTIALLY_SENT = 'PARTIALLY_SENT', // ĐÃ GỬI 1 
        PACKAGING_PENDING = 'PACKAGING_PENDING', // Đóng gói 1 phần
    }

    export enum EDeliveryTracking {
        PENDING = 'PENDING',     // Chờ nhận
        SENT = 'SENT', // ĐÃ GỬI 
        RECEIVED = 'RECEIVED',   // Đã nhận
        PACKED = 'PACKED', // Đã đóng gói
        PACKAGING_PENDING = 'PACKAGING_PENDING', // Chờ đóng gói
        DELIVERY_PENDING = 'DELIVERY_PENDING', // Chờ giao hàng
        CANCEL = 'CANCEL', // Hủy
    }

    export enum EDeliveryTrackingType {
        // Phiếu nhận
        RECEIVING = 'RECEIVING', // Phiếu nhận
        // Phiếu giao
        DELIVERY = 'DELIVERY', // Phiếu giao
        // Phiếu đóng gói
        PACKAGING = 'PACKAGING', // Phiếu đóng gói
    }

    export enum EReceivingStatus {
        PENDING = 'PENDING',     // Chờ nhận
        RECEIVED = 'RECEIVED',   // Đã nhận
        COMPLETE = 'COMPLETE', // HOÀN THÀNH
    }

    export enum EDeliveryNoteSendInboundStatus {
        SENT_MBC = 'SENT_MBC', // Đã gửi MBC hoặc Gửi thằng tới người nhận
        SENT_CUSTOMER = 'SENT_CUSTOMER',
        SEND_PENDING = 'SEND_PENDING',
        DELIVERING = 'DELIVERING',  // Đang giao
    }

    export enum EDeliveryNoteInboundStatus {
        INSTOCK = 'INSTOCK',     // Đã nhập kho
        NOTINSTOCK = 'NOTINSTOCK',     // Chưa nhập kho
        PARTIALLY_INSTOCK = 'PARTIALLY_INSTOCK', // Nhập kho một phần
        NOTRECEIVED = 'NOTRECEIVED', // Không nhập kho
        COMPLETED = 'COMPLETED', // Hoàn Thành
        PARTIALLY_SENT = 'PARTIALLY_COMPLETED', // Hoàn Thành một phần
        DELIVERING = 'DELIVERING',  // Đang giao
    }

    export enum DeliveryLastMileType {
        MBC = "MBC",
        CUSTOMER = "CUSTOMER"
    }
}   