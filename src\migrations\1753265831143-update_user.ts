import { MigrationInterface, QueryRunner } from 'typeorm'

export class updateUser1753265831143 implements MigrationInterface {
  name = 'updateUser1753265831143'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" ADD "phone" character varying(50)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "phone"`)
  }
}
