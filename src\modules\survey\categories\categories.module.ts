import { Module } from '@nestjs/common'
import { CategoriesController } from './categories.controller'
import { CategoriesService } from './categories.service'
import { TypeOrmExModule } from '../../../typeorm'
import { CategoriesRepository, SurveyHistoryRepository, SurveyRepository, TopicRepository, UserSurveyRepository } from '../../../repositories/survey'
import { MobileAppModule } from '../mobileApp/mobileApp.module'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([CategoriesRepository, TopicRepository, SurveyRepository, SurveyHistoryRepository, UserSurveyRepository]),
  ],
  controllers: [CategoriesController],
  providers: [CategoriesService],
  exports: [CategoriesService],
})
export class CategoriesModule {}
