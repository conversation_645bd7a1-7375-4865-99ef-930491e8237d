import { Column, <PERSON><PERSON><PERSON>, Jo<PERSON><PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { BaseEntity } from '../core/base.entity'
import { InboundEntity } from './inbound.entity'
import { InboundDetailEntity } from './inboundDetail.entity'

@Entity('inbound_detail_price')
export class InboundDetailPriceEntity extends BaseEntity {
  /** Giá nhập cũ */
  @Column({ nullable: true, default: 0, type: 'float' })
  priceOld: number

  /** Giá nhập mới */
  @Column({ nullable: true, default: 0, type: 'float' })
  priceNew: number

  /** Id PNK */
  @Column({ type: 'varchar', nullable: true })
  inboundId?: string
  @ManyToOne(() => InboundEntity, (p) => p.prices)
  @JoinColumn({ name: 'inboundId', referencedColumnName: 'id' })
  inbound: Promise<InboundEntity>

  /** Id chi tiết PNK */
  @Column({ type: 'varchar', nullable: true })
  inboundDetailId?: string
  @ManyToOne(() => InboundDetailEntity, (p) => p.prices)
  @JoinColumn({ name: 'inboundDetailId', referencedColumnName: 'id' })
  inboundDetail: Promise<InboundDetailEntity>

  /** Ghi chú, ví dụ `Duyệt PNK [${detail.code}]<br>Nhập thêm: ${detail.totalQuantity}<br>HSD ${dateStr}` */
  @Column({ type: 'text', nullable: true })
  description: string
}
