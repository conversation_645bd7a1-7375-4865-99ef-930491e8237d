import { MigrationInterface, QueryRunner } from 'typeorm'

export class updateUserRelation1753327465232 implements MigrationInterface {
  name = 'updateUserRelation1753327465232'

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Xóa constraint cũ trên supplier
    await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT IF EXISTS "FK_e8902c50550ff82dd0143913c0a"`)
    await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT IF EXISTS "UQ_e8902c50550ff82dd0143913c0a"`)

    // Không drop column "userId" trên supplier

    // Xóa constraint cũ trên permission
    await queryRunner.query(`ALTER TABLE "permission" DROP CONSTRAINT IF EXISTS "FK_c60570051d297d8269fcdd9bc47"`)

    // Không drop column "userId" trên permission

    // Đổi kiểu dữ liệu (nế<PERSON> cần) trên permission.userId sang character varying, nếu cột đang là uuid (tùy schema)
    // await queryRunner.query(`ALTER TABLE "permission" ALTER COLUMN "userId" TYPE character varying`);
    // Nếu chắc chắn là cần đổi sang varchar

    // Xóa constraint cũ trên user
    await queryRunner.query(`ALTER TABLE "user" DROP CONSTRAINT IF EXISTS "FK_031cdc2c9c5eb56d48b5bdb4e54"`)
    await queryRunner.query(`ALTER TABLE "user" DROP CONSTRAINT IF EXISTS "UQ_031cdc2c9c5eb56d48b5bdb4e54"`)

    // Tạo constraint mới
    await queryRunner.query(`
            ALTER TABLE "permission" 
            ADD CONSTRAINT "FK_c60570051d297d8269fcdd9bc47" 
            FOREIGN KEY ("userId") REFERENCES "user"("employeeId") ON DELETE NO ACTION ON UPDATE NO ACTION
        `)

    await queryRunner.query(`
            ALTER TABLE "user"
            ADD CONSTRAINT "FK_031cdc2c9c5eb56d48b5bdb4e54"
            FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" DROP CONSTRAINT IF EXISTS "FK_031cdc2c9c5eb56d48b5bdb4e54"`)
    await queryRunner.query(`ALTER TABLE "permission" DROP CONSTRAINT IF EXISTS "FK_c60570051d297d8269fcdd9bc47"`)

    await queryRunner.query(`ALTER TABLE "user" ADD CONSTRAINT "UQ_031cdc2c9c5eb56d48b5bdb4e54" UNIQUE ("supplierId")`)
    await queryRunner.query(`
            ALTER TABLE "user" 
            ADD CONSTRAINT "FK_031cdc2c9c5eb56d48b5bdb4e54" 
            FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `)

    // Không xóa/đổi cột "userId" trên permission

    await queryRunner.query(`
            ALTER TABLE "permission" 
            ADD CONSTRAINT "FK_c60570051d297d8269fcdd9bc47"
            FOREIGN KEY ("userId") REFERENCES "user"("employeeId") ON DELETE NO ACTION ON UPDATE NO ACTION
        `)

    // Không xóa/đổi cột "userId" trên supplier

    await queryRunner.query(`ALTER TABLE "supplier" ADD CONSTRAINT "UQ_e8902c50550ff82dd0143913c0a" UNIQUE ("userId")`)
    await queryRunner.query(`
            ALTER TABLE "supplier"
            ADD CONSTRAINT "FK_e8902c50550ff82dd0143913c0a"
            FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `)
  }
}
