import { ApiPropertyOptional } from '@nestjs/swagger'
import { PageRequest } from '../../../../dto'
import { IsOptional, IsUUID } from 'class-validator'

export class WithdrawRequestReq extends PageRequest {
  @ApiPropertyOptional({ description: 'ID member' })
  @IsOptional()
  @IsUUID('4')
  memberId?: string

  @ApiPropertyOptional({
    isArray: true,
    description: 'Trạng thái lệnh rút tiền',
  })
  @IsOptional()
  status?: string[]
}
