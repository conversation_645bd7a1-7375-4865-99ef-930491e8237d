import { Injectable, ConflictException, NotFoundException } from '@nestjs/common'
import { UserRepository } from '../../../repositories'
import {
  ERROR_NOT_FOUND_DATA,
  ERROR_CODE_TAKEN,
  UPDATE_ACTIVE_SUCCESS,
  CREATE_SUCCESS,
  UPDATE_SUCCESS,
  IMPORT_SUCCESS,
  enumData,
} from '../../../constants'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { In, Like } from 'typeorm'
import { StoreUpdateDto } from './dto'
import { CreateStoreDto } from '../../../helpers/dto/apiCaller.dto'
import { omsApiHelper } from '../../../helpers/omsApiHelper'
import { Request as IRequest } from 'express'
@Injectable()
export class StoreService {
  constructor(private readonly userRepository: UserRepository) {}

  public async find(data: any) {
    // const whereCon: any = { isDeleted: false }
    // if (data.name) whereCon.name = Like(`%${data.name}%`)
    // if (data.code) whereCon.code = Like(`%${data.code}%`)
    // if (data.lstId?.length > 0) whereCon.id = In(data.lstId)
    // if (data.lstCode?.length > 0) whereCon.code = In(data.lstCode)
    // return await this.repo.find({ where: whereCon })
  }

  public async createData(user: UserDto, data: CreateStoreDto, req: IRequest) {
    // await this.userRepository.save(newUser)
    await omsApiHelper.createStore(req, data)
    return { message: CREATE_SUCCESS }
  }

  public async updateData(user: UserDto, data: StoreUpdateDto) {
    // const entity = await this.repo.findOne({ where: { id: data.id } })

    return { message: UPDATE_SUCCESS }
  }

  public async pagination(data: PaginationDto, req: IRequest) {
    const rs = await omsApiHelper.paginationStore(req, data)
    return [rs.data, rs.total]
  }

  public async updateIsDelete(id: string, user: UserDto) {
    // const entity = await this.repo.findOne({ where: { id } })
    // if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    // await this.repo.update({ id: entity.id }, { isDeleted: !entity.isDeleted, updatedBy: user.id })
    // return { message: UPDATE_ACTIVE_SUCCESS }
  }

  async findOne(data: FilterOneDto) {
    // const whereCon: any = { id: data.id, isDeleted: false }
    // return await this.repo.findOneBy(whereCon)
  }

  public async createDataExcel(data: CreateStoreDto[], user: UserDto) {
    return { message: IMPORT_SUCCESS }
  }
}
