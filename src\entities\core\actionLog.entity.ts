import { Column, Entity } from 'typeorm'
import { BaseEntity } from './base.entity'

@Entity('action_log')
export class ActionLogEntity extends BaseEntity {
  /** tên người tạo */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  userCreateName: string

  /** <PERSON>ại thao tác enum ActionLogType */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  type: string

  /** Data cũ */
  @Column({
    type: 'text',
    nullable: true,
  })
  oldJson: string

  /** Data mới */
  @Column({
    type: 'text',
    nullable: true,
  })
  newJson: string

  /** Id dòng lịch sử */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  idHistory: string

  /** Tên bảng của Id */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  tableName: string

  /** <PERSON><PERSON> tả thao tác lịch sử */
  @Column({
    type: 'text',
    nullable: false,
  })
  description: string
}
