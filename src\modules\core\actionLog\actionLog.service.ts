import { Injectable } from '@nestjs/common'
import { Between, EntityManager, FindOptionsWhere, ILike } from 'typeorm'
import { ActionLogEntity } from '../../../entities'
import { PaginationDto, UserDto } from '../../../dto'
import { ActionLogRepository } from '../../../repositories'
import { ActionLogCreateDto } from './dto'
import { v4 as uuidv4 } from 'uuid'

@Injectable()
export class ActionLogService {
  constructor(private repo: ActionLogRepository) { }

  async createData(user: UserDto, actionLog: ActionLogCreateDto) {
    return this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(ActionLogEntity)
      const newLog = new ActionLogEntity()
      newLog.id = uuidv4()
      newLog.createdBy = user.id
      newLog.userCreateName = user?.username
      newLog.idHistory = actionLog.idHistory
      if (actionLog.newJson) newLog.newJson = actionLog.newJson
      if (actionLog.oldJson) newLog.oldJson = actionLog.oldJson
      newLog.type = actionLog.type
      newLog.tableName = actionLog.tableName
      if (actionLog.description) newLog.description = actionLog.description

      await repo.save(newLog)
    })
  }

  async pagination(paginationData: PaginationDto) {
    const whereCon: any = {}
    if (paginationData.where?.idHistory) {
      whereCon.idHistory = paginationData.where.idHistory
    }
    if (paginationData.where?.tableName) {
      whereCon.tableName = paginationData.where.tableName
    }



    const [data, total] = await this.repo.findAndCount({
      where: whereCon,
      order: { createdAt: 'DESC' },
      skip: paginationData.skip,
      take: paginationData.take,
    })
    return { data, total }
  }

  // Lưu lịch sử kích hoạt tay giao dịch pending
  async createPendingTransaction(user: UserDto, actionLog: ActionLogCreateDto) {
    return this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(ActionLogEntity)
      const newLog = new ActionLogEntity()
      newLog.id = uuidv4()
      newLog.type = 'UPDATE_ACTIVE'
      newLog.createdBy = user.id
      newLog.userCreateName = user?.username
      newLog.idHistory = actionLog.idHistory
      newLog.oldJson = actionLog.oldJson
      newLog.newJson = actionLog.newJson
      newLog.tableName = 'payment_transaction'
      if (actionLog.description) newLog.description = `Nhân viên ${user?.username} đã kích hoạt giao dịch ${actionLog.description}` // [code] Code payment_transaction

      await repo.save(newLog)
    })
  }

  // Lấy danh sách lịch sử kích hoạt tay giao dịch
  async paginationPendingTransaction(paginationData: PaginationDto) {
    const whereCon: any = {
      tableName: 'payment_transaction'
    }
    if (paginationData.where?.idHistory) {
      whereCon.idHistory = paginationData.where.idHistory
    }

    if (paginationData.where?.createdAtFrom || paginationData.where?.createdAtTo) {
      whereCon.createdAt = Between(paginationData.where.createdAtFrom, paginationData.where.createdAtTo)
    }

    if (paginationData.where?.transactionId) {
      whereCon.id = paginationData.where.transactionId
    }



    const [data, total] = await this.repo.findAndCount({
      where: whereCon,
      order: { createdAt: 'DESC' },
      skip: paginationData.skip,
      take: paginationData.take,
    })
    return { data, total }
  }
}
