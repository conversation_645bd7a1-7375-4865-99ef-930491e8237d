import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import { ActionLogRepository, BankRepository, ItemComboRepository, ItemRepository } from '../../../repositories'
import { ContractController } from './contract.controller'
import { ContractService } from './contract.service'
import { ContractRepository } from '../../../repositories/core/contract.repository'
import { UploadFileModule } from '../uploadFile/uploadFile.module'
import { PublicContractController } from './publicContract.controller'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([ContractRepository, ItemRepository, ItemComboRepository, BankRepository]), UploadFileModule],
  controllers: [ContractController, PublicContractController],
  providers: [ContractService],
  exports: [ContractService],
})
export class ContractModule {}
