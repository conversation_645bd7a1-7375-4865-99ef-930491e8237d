import { Body, Controller, Get, Post, Query, Req, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { DeliveryNoteChildService } from './deliveryNoteChild.service'
import {
  CreateInboundFromDeliveryNoteChildDto,
  DetailDeliverNoteChildDto,
  FilterDeliverNoteChildDetailDto,
  FilterDeliverNoteChildDto,
  UUIDDto,
} from './dto/deliveryNoteChild.dto'
import { Request as IRequest } from 'express'
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard'
import { ImportSendPartnerDto } from './dto/ImportSendPartnerDto'
@ApiTags('DeliveryNoteChild')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('delivery-note-child')
export class DeliveryNoteChildController {
  constructor(private readonly deliveryNoteService: DeliveryNoteChildService) {}
  @Get('pagination')
  async paginationDeliveryNote(@Query() params: FilterDeliverNoteChildDto) {
    return this.deliveryNoteService.paginationDeliveryNoteChild(params)
  }

  @Get('pagination-supplier')
  async paginationDeliveryNoteSupplier(@Query() params: FilterDeliverNoteChildDto) {
    return this.deliveryNoteService.paginationDeliveryNoteChildSupplier(params)
  }

  @Post('pagination-child')
  async paginationDetails(@Body() body: FilterDeliverNoteChildDetailDto) {
    return this.deliveryNoteService.paginationDeliveryNoteChildDetail(body)
  }

  @Get('detail')
  public async detail(@Query() query: DetailDeliverNoteChildDto, @Req() req: IRequest) {
    const { id } = query
    return await this.deliveryNoteService.detailDeliveryNoteChild(id, req)
  }

  @Post('create-inbound')
  public async createInbound(@Body() body: CreateInboundFromDeliveryNoteChildDto, @Req() req: IRequest) {
    return await this.deliveryNoteService.createInboundWithCombo(body, req)
  }

  @Post('send-partner')
  public async sendPartnerWarehouse(@Body() body: UUIDDto, @Req() req: IRequest) {
    return await this.deliveryNoteService.sendPartner(body, req)
  }

  @Post('import-lastmile')
  public async importExcelLastMile(@Body() data: ImportSendPartnerDto, @Req() req: IRequest) {
    return await this.deliveryNoteService.importExcelLastMile(data, req)
  }

  @Post('print-lastmile')
  async printOrder(@Body() body: any) {
    return await this.deliveryNoteService.printLastMile(body)
  }
}
