import { ApiProperty } from '@nestjs/swagger'
import { IsArray, IsObject, IsOptional } from 'class-validator'

/** Interface phân trang */
export class PaginationDto {
  @ApiProperty({ description: '<PERSON><PERSON><PERSON><PERSON> ki<PERSON>n lọc', example: { code: 'xxxx', name: 'xxx xxxx xxxx' } })
  where: any
  // where: WhereCondition
  @ApiProperty({ description: 'Số record bỏ qua', example: 0 })
  skip: number
  @ApiProperty({ description: 'Số record lấy', example: 10 })
  take: number

  @IsObject()
  @IsOptional()
  order?: any

  @IsArray()
  @IsOptional()
  relations?: any
}

export class PageRequest {
  @ApiProperty({
    example: 10,
  })
  pageSize: number = 10
  @ApiProperty({
    example: 1,
  })
  pageIndex: number = 1
}
export class PageResponse<T = any> {
  // @ApiProperty()
  data: T[]

  @ApiProperty()
  total: number
}
