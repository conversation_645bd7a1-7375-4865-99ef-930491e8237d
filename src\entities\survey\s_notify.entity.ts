import { <PERSON><PERSON><PERSON>, Column, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from '../core/base.entity'
import { SurveyEntity } from './s_survey.entity'

@Entity('s_notify')
export class NotifyEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  title: string

  @Column({
    type: 'text',
    nullable: true,
  })
  content: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  status: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  userId: string

  /** Id phiếu khảo sát */
  @Column({
    type: 'varchar',
    nullable: false,
  })
  surveyId: string
  @ManyToOne(() => SurveyEntity, (p) => p.notifies)
  @JoinColumn({ name: 'surveyId', referencedColumnName: 'id' })
  survey: Promise<SurveyEntity>
}
