import { ApiProperty } from '@nestjs/swagger'
import { PageRequest } from '../../../../dto'

/** Interface tìm user */
export class UserFilterDto extends PageRequest {
  @ApiProperty({ description: 'Danh sách Id vai trò' })
  lstRoleId?: string[]

  @ApiProperty({ description: 'Danh sách Id tài khoản' })
  lstId?: string[]

  @ApiProperty({ description: 'Loại user' })
  type?: string

  @ApiProperty({ description: 'trạng thái user' })
  isDeleted?: string

  @ApiProperty({ description: 'Tên tài khoản' })
  username?: string

  @ApiProperty({ description: 'Id nhà cung cấp' })
  supplierId?: string
}
