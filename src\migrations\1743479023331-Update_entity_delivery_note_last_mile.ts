import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateEntityDeliveryNoteLastMile1743479023331 implements MigrationInterface {
    name = 'UpdateEntityDeliveryNoteLastMile1743479023331'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "delivery_note_child_detail" ADD "type" character varying NOT NULL DEFAULT 'MBC'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "delivery_note_child_detail" DROP COLUMN "type"`);
    }

}
