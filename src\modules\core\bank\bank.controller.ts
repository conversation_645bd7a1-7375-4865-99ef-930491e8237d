import { Body, Controller, Get, Post, Put, Query, UploadedFile, UseGuards, UseInterceptors } from "@nestjs/common";
import { BankService } from "./bank.service";
import { ApiBearerAuth, ApiTags } from "@nestjs/swagger";
import { CreateBankDto, UpdateBankDto, DeleteDto, BankBranchLstDto } from "./dto/bank.dto";
import { FileInterceptor } from "@nestjs/platform-express";
import { PageRequest } from "../../../dto";
import { JwtAuthGuard } from "../../common";

@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@ApiTags('Bank')
@Controller('bank')
export class BankController {
  constructor(private readonly bankService: BankService) { }

  @Get("list")
  async getBankList() {
    return this.bankService.getBankList();
  }

  @Get("pagination")
  async getBankPagination(@Query() params: PageRequest) {
    return this.bankService.getBankPagination(params);
  }

  @Get("/bank-branch")
  async getBrandOfBank(@Query() BankBranchLstDto: BankBranchLstDto) {
    return this.bankService.getBranchByBankCode(BankBranchLstDto);
  }

  @Post("/create")
  async createBank(@Body() data: CreateBankDto) {
    return this.bankService.createBank(data);
  }

  @Put("/update")
  async updateBank(@Body() data: UpdateBankDto) {
    return this.bankService.update(data);
  }

  @Post("/delete")
  async deleteBank(@Body() body: DeleteDto) {
    const { id } = body;
    return this.bankService.delete(id);
  }

  @Post("/delete-branch")
  async deleteBankBranch(@Body() body: DeleteDto) {
    const { id } = body;
    return this.bankService.deleteBranch(id);
  }

  @Post("/import")
  @UseInterceptors(FileInterceptor('file'))
  async importBank(@UploadedFile() file: Express.Multer.File) {
    return this.bankService.importData(file);
  }

}