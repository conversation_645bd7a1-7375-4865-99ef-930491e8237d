import { IsNotEmpty, IsString } from 'class-validator'

export class MinusRandomEmployeeProductQuantityByExpiryDateDto {
  /** Sản phẩm */
  @IsNotEmpty()
  @IsString()
  productId: string

  /** <PERSON>ả<PERSON> phẩm thực */
  @IsNotEmpty()
  @IsString()
  productDetailId: string

  /** Hạn sử dụng */
  @IsNotEmpty()
  expiryDate: string

  /** <PERSON>h<PERSON><PERSON> lượng trừ */
  @IsNotEmpty()
  quantity: number
}
