import { Controller, Get, Post, Put, Body, Param, Query, UseGuards } from '@nestjs/common';
import { SupplyChainService } from './supplyChain.service';
import { CreateSupplyChainDto, ListSupplyChaiDto, UpdateStatusDto, UpdateSupplyChainDto } from './dto/supplyChain.dto';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { CurrentUser, JwtAuthGuard } from '../../common';
import { UserDto } from '../../../dto';

@ApiBearerAuth()
@ApiTags('Supply Chain')
@UseGuards(JwtAuthGuard)
@Controller('supply-chain')
export class SupplyChainController {
  constructor(private readonly supplyChainService: SupplyChainService) { }

  @Get('list')
  async getSupplyChains(@Query() params: ListSupplyChaiDto) {
    return await this.supplyChainService.getSupplyChains(params);
  }

  @Get('detail/:id')
  async getSupplyChainDetails(@Param('id') id: string) {
    const supplyChain = await this.supplyChainService.getSupplyChainDetails(id);
    return supplyChain;
  }

  @Post('create')
  async createSupplyChain(@Body() createSupplyChainDto: CreateSupplyChainDto, @CurrentUser() user: UserDto) {
    return await this.supplyChainService.createSupplyChain(createSupplyChainDto, user);
  }

  @Put('update')
  async updateSupplyChain(@Body() updateSupplyChainDto: UpdateSupplyChainDto) {
    const { supplyChainId } = updateSupplyChainDto
    return await this.supplyChainService.updateSupplyChain(supplyChainId, updateSupplyChainDto);
  }

  @Put('update-status')
  async updateSupplyChainStatus(@Body() body: UpdateStatusDto) {
    const { supplyChainId, status } = body
    return await this.supplyChainService.updateSupplyChainStatus(supplyChainId, status);
  }

  @Get('search')
  async searchSupplyChain(@Query() params: ListSupplyChaiDto) {
    return await this.supplyChainService.searchSupplyChain(params);
  }
}
