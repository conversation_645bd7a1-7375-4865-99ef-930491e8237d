import { Injectable } from '@nestjs/common'
import {
  CheckInventoryRepository,
  InboundRepository,
  OutboundRepository,
  ItemDetailRepository,
  ItemRepository,
  WarehouseProductDetailRepository,
  WarehouseProductRepository,
  WarehouseRepository,
} from '../../../repositories'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { EntityManager, Equal, In, Like, Raw } from 'typeorm'
import { ERROR_NOT_FOUND_DATA, enumData } from '../../../constants'
import * as moment from 'moment'
import { coreHelper } from '../../../helpers'
import {
  CheckInventoryCreateByExcelDto,
  CheckInventoryCreateDto,
  CheckInventoryUpdateDto,
  MinusRandomEmployeeProductQuantityByExpiryDateDto,
} from './dto'
import {
  CheckInventoryDetailByEmployeeEntity,
  CheckInventoryDetailEntity,
  CheckInventoryEntity,
  CheckInventoryHistoryEntity,
  ItemDetailEntity,
  ItemEntity,
  UnitEntity,
  WarehouseProductDetailEntity,
  WarehouseProductEntity,
} from '../../../entities'
import { v4 as uuidv4 } from 'uuid'
import { Request as IRequest } from 'express'
import { InboundCreateFromOutsideDto, InboundDetailCreateFromOutsideDto } from '../warehouseTransfer/dto'
import { InboundService } from '../inbound/inbound.service'
import { OutboundService } from '../outbound/outbound.service'
import { OutboundCreateDto, OutboundDetailCreateDto } from '../outbound/dto'
import { omsApiHelper } from '../../../helpers/omsApiHelper'

interface CheckInventoryEntityExtend extends CheckInventoryEntity {
  __details__?: CheckInventoryDetailEntity[]
  __histories__?: CheckInventoryHistoryEntity[]
}
@Injectable()
export class CheckInventoryService {
  constructor(
    private readonly repo: CheckInventoryRepository,
    private readonly warehouseRepo: WarehouseRepository,
    private readonly productDetailRepo: ItemDetailRepository,
    private readonly productRepo: ItemRepository,
    private readonly warehouseProductRepo: WarehouseProductRepository,
    private readonly warehouseProductDetailRepo: WarehouseProductDetailRepository,
    private readonly inboundRepo: InboundRepository,
    private readonly outboundRepo: OutboundRepository,
    private readonly inboundService: InboundService,
    private readonly outboundService: OutboundService,
    private warehouseRepository: WarehouseRepository,
  ) { }

  /** Hàm tìm tất cả phiếu kiểm kho */
  async find(data: any) {
    return await this.repo.find(data)
  }

  /** Hàm tìm thông tin chi tiết phiếu kiểm kho */
  async findDetail(data: FilterOneDto, req: IRequest) {
    const entity: any = await this.repo.findOne({
      where: { id: data.id, isDeleted: false },
      relations: { details: { checkInventoryDetailByEmployees: true }, histories: true, warehouse: true },
    })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    for (let od of entity.__details__) {
      od.__product__ = await this.productRepo.findOne({ where: { id: od.productId }, relations: { unit: true, details: true } })
    }

    // entity.createdByName = await authApiHelper.getCreatedByName(req, entity.createdBy)
    entity.lstCheckInventoryDetail = entity.__details__
    entity.lstHistory = entity.__histories__
    entity.warehouseName = entity.__warehouse__.name
    entity.statusName = enumData.CheckInventoryStatus[entity.status].name
    entity.statusColor = enumData.CheckInventoryStatus[entity.status].color

    for (let od of entity.lstCheckInventoryDetail) {
      od.expand = false
      od.lstCheckInventoryDetailByEmployees = od.__checkInventoryDetailByEmployees__
      od.productName = od.__product__?.name
      od.productCode = od.__product__?.code
      od.unitName = od.__product__.__unit__?.name
      od.lstProductDetail = od.__product__?.__details__ || []
      od.lstEmployee = od.__checkInventoryDetailByEmployees__
      od.expand = false
      for (let pd of od.lstProductDetail) pd.expiryDateFmt = moment(pd.expiryDate).format('DD/MM/YYYY')
      delete od.__product__
    }

    delete entity.__details__
    delete entity.__histories__
    delete entity.__warehouse__
    const memberCreate: any = await omsApiHelper.getMemberByListId(req, [entity.createdBy]) || [];
    const memberPrepare: any = await omsApiHelper.getMemberByListId(req, [entity.preparedBy]) || [];
    const memberApprove: any = await omsApiHelper.getMemberByListId(req, [entity.approvedBy]) || [];
    entity.createdByName = memberCreate[0]?.fullName;
    entity.preparedByName = memberPrepare[0]?.fullName;
    entity.approveByName = memberApprove[0]?.fullName;
    return entity
  }

  /** Hàm lấy mã tự sinh để hiện thị lên frontEnd */
  async getCodeAutoGen(): Promise<{ code: string }> {
    // Sinh code theo quy tắc ddmmyyy_xxxx (xxxx tăng dần) VD: 01102023_0001
    const curDate = moment(new Date()).format('DDMMYYYY')

    let genCode: string
    // Đếm số lượng phiếu kiểm kho trong ngày
    let count = await this.repo.count({ where: { code: Like(`PKK_${curDate}_%`) }, select: { id: true } })
    if (!count) count = 0

    genCode = this.genCode(count, 'PKK')

    return { code: genCode }
  }

  private genCode(count: number, plusString: string = ''): string {
    const curDate = moment(new Date()).format('DDMMYYYY')
    let numberString: string = '0001'
    let genCode: string
    const incrementedNumber = Number(numberString) + count
    const newLastPart = String(incrementedNumber).padStart(numberString.length, '0')
    genCode = `${curDate}_${newLastPart}`
    if (plusString.length > 0) genCode = `${plusString}_${curDate}_${newLastPart}`
    return genCode
  }

  /** Hàm phân trang phiếu kiểm kho */
  async pagination(data: PaginationDto, req: IRequest) {
    const whereCon: any = {}
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.warehouseId) whereCon.warehouseId = data.where.warehouseId

    if (data.where.createdAt) {
      const formattedDate = new Date(data.where.createdAt).toISOString().split("T")[0];
      whereCon.createdAt = Raw(
        (alias) => `DATE(${alias}) = '${formattedDate}'`
      );
    }
    if (data.where.approvedDate) {
      const formattedDate = new Date(data.where.approvedDate).toISOString().split("T")[0];
      whereCon.approvedDate = Raw(
        (alias) => `DATE(${alias}) = '${formattedDate}'`
      );
    }
    if (data.where.storeId) {
      const warehouse = await this.warehouseRepository.findOne({ where: { storeId: data.where.storeId } })
      if (warehouse) {
        whereCon.warehouseId = warehouse.id;
      }
    }
    if (data.where.status) {
      if (typeof data.where.status == 'string') {
        data.where.status = [data.where.status]
      }
      whereCon.status = In(data.where.status)
    }

    if (data.where.approvedBy) whereCon.approvedBy = data.where.approvedBy
    if (data.where.createdBy) whereCon.createdBy = data.where.createdBy

    whereCon.details = {}
    // whereCon.details.product = {}
    // if (data.where.brandId) {
    //   whereCon.details.product.brandId = Like(`%${data.where.brandId}%`)
    // }

    // TODO 1. Lấy danh được danh sách brandId
    // TODO 2. Từ brandID lấy ra danh sách productId
    // TODO 3. Từ productId lấy ra danh sách checkInventoryDetail
    // TODO 4. Từ checkInventoryDetail lấy ra checkInventory

    let relations: any = {}
    if (data.where.isExcel) {
      relations = { warehouse: true, details: true }
    } else {
      relations = { warehouse: true }
    }

    const [lstCheckInventory, total]: any = await this.repo.findAndCount({
      where: whereCon,
      order: { createdAt: 'DESC' },
      skip: data.skip,
      take: data.take,
      relations: relations,
    })
    if (lstCheckInventory.length == 0) return { data: [], total }

    const listMemCreateIds = lstCheckInventory.map(val => val.createdBy);
    const memberCreate: any[] = await omsApiHelper.getMemberByListId(req, listMemCreateIds) || [];


    const listMemApproveIds = lstCheckInventory.map(val => val.approvedBy);
    const memberApprove: any[] = await omsApiHelper.getMemberByListId(req, listMemApproveIds) || [];

    for (let e of lstCheckInventory) {
      e.warehouseName = e.__warehouse__?.name ?? ''
      e.statusName = enumData.CheckInventoryStatus[e.status]?.name ?? ''
      e.statusColor = enumData.CheckInventoryStatus[e.status]?.color ?? ''
      delete e.__warehouse__
    }

    const mappingResult = lstCheckInventory.map((val) => {
      const memCreate: any = memberCreate.find((m: any) => m.id === val.createdBy)
      const memApproved: any = memberApprove.find((m: any) => m.id === val.approvedBy)

      return {
        ...val,
        createdByName: memCreate?.fullName || '',
        approvedByName: memApproved?.fullName || ''
      }
    })
    const filteredResult = mappingResult.filter((item) => {
      const matchesCreatedBy = data.where.createdByName
        ? item.createdByName.toLowerCase().includes(data.where.createdByName.toLowerCase())
        : true;

      const matchesApprovedBy = data.where.approvedByName
        ? item.approvedByName.toLowerCase().includes(data.where.approvedByName.toLowerCase())
        : true;

      return matchesCreatedBy && matchesApprovedBy;
    });
    return { data: filteredResult, total: filteredResult.length }
  }

  async createHistory(data: { description: string; checkInventoryId: string }, manager: EntityManager) {
    const checkInventoryHistory = new CheckInventoryHistoryEntity()
    checkInventoryHistory.createdAt = new Date()
    checkInventoryHistory.checkInventoryId = data.checkInventoryId
    checkInventoryHistory.description = data.description
    await manager.getRepository(CheckInventoryHistoryEntity).insert(checkInventoryHistory)
  }

  /** Hàm tạo mới phiếu kiểm kho */
  async createData(data: CheckInventoryCreateDto, req?: IRequest) {
    try {
      const checkWarehouse = await this.warehouseRepo.findOne({ where: { id: data.warehouseId, isDeleted: false }, select: { id: true } })
      if (!checkWarehouse) throw new Error(`Không tìm thấy kho vật lý hoặc kho vật lý đã bị ngưng hoạt động!`)

      // #region Check tồn kho

      // Danh sách hạn sử dụng đã format dạng YYYY-MM-DD
      let lstProductDetailId: string[] = data.lstCheckInventoryDetail.map((wtd) => wtd.productDetailId)

      // Danh sách id sản phẩm
      const lstProductId = data.lstCheckInventoryDetail.mapAndDistinct((wtd) => wtd.productId)

      // Dic Product
      let dicProduct: any = {}
      {
        const lstProduct = await this.productRepo.find({
          where: { id: In(lstProductId) },
          select: { id: true, code: true, name: true, quantity: true, quantityLock: true },
        })
        dicProduct = coreHelper.arrayToObject(lstProduct)
      }

      const dicProductDetail: any = {}
      {
        let lstProductDetail: ItemDetailEntity[] = []
        lstProductDetail = await this.productDetailRepo.find({
          where: {
            id: In(lstProductDetailId),
          },
          select: { id: true, itemId: true, expiryDate: true, quantity: true, quantityLock: true, quantityLockEmp: true, lotNumber: true },
        })

        if (lstProductDetail.length == 0) throw new Error(`Không tìm thấy danh sách sản phẩm trong kho`)

        // Số lượng: quantity
        // Số lượng đã lên đơn tạm: quantityLock
        // Số lượng lock khi phân kho: quantityLockEmp

        for (let pd of lstProductDetail) {
          dicProductDetail[pd.id] = pd
        }
      }

      let dicWarehouseProduct: any = {}
      let dicWarehouseProductDetail: any = {}
      {
        const lstWarehouseProductDetail = await this.warehouseProductDetailRepo.find({
          where: { productDetailId: In(lstProductDetailId), productId: In(lstProductId), warehouseId: data.warehouseId, isDeleted: false },
          select: {
            id: true,
            warehouseProductId: true,
            warehouseId: true,
            productId: true,
            productDetailId: true,
            expiryDate: true,
            quantity: true,
            quantityLock: true,
          },
        })
        let lstWarehouseProductId: string[] = []
        for (let wpd of lstWarehouseProductDetail) {
          dicWarehouseProductDetail[wpd.warehouseId + wpd.productId + wpd.productDetailId] = wpd
          lstWarehouseProductId.push(wpd.warehouseProductId)
        }

        const lstWarehouseProduct = await this.warehouseProductRepo.find({
          where: { id: In(lstWarehouseProductId), isDeleted: false },
          select: { id: true, warehouseId: true, productId: true, quantity: true, quantityLock: true },
        })

        for (let wp of lstWarehouseProduct) dicWarehouseProduct[wp.warehouseId + wp.productId] = wp
      }

      for (let obd of data.lstCheckInventoryDetail) {
        let productDetail = dicProductDetail[obd.productDetailId]
        if (!productDetail) throw new Error(`Không tìm thấy sản phẩm trong kho`)

        const product = dicProduct[obd.productId]
        if (!product) throw new Error(`Không tìm thấy sản phẩm thực`)

        const warehouseProduct = dicWarehouseProduct[data.warehouseId + obd.productId]
        if (!warehouseProduct) throw new Error(`Không tìm thấy sản phẩm trong kho vật lý`)

        const warehouseProductDetail = dicWarehouseProductDetail[data.warehouseId + obd.productId + productDetail.id]
        if (!warehouseProductDetail) throw new Error(`Không tìm thấy chi tiết sản phẩm trong kho vật lý`)
      }

      // #endregion

      // Sinh code theo quy tắc ddmmyyy_xxxx (xxxx tăng dần) VD: 01102023_0001
      const curDate = moment(new Date()).format('DDMMYYYY')

      let genCode: string
      // Đếm số lượng phiếu kiểm kho trong ngày
      let count = await this.repo.count({ where: { code: Like(`PKK_${curDate}_%`) }, select: { id: true } })
      if (!count) count = 0

      genCode = this.genCode(count, 'PKK')
      return await this.repo.manager.transaction(async (trans) => {
        const lstTask: CheckInventoryDetailEntity[] = []
        const repo = trans.getRepository(CheckInventoryEntity)
        const checkInventoryDetailRepo = trans.getRepository(CheckInventoryDetailEntity)
        const checkInventory = new CheckInventoryEntity()
        checkInventory.id = uuidv4()
        checkInventory.code = genCode
        checkInventory.warehouseId = data.warehouseId
        checkInventory.createdAt = new Date(data.createdAt)

        checkInventory.createdBy = data.createBy
        checkInventory.description = data?.description ?? null
        await repo.insert(checkInventory)

        for (let detail of data.lstCheckInventoryDetail) {
          const checkInventoryDetail = new CheckInventoryDetailEntity()
          checkInventoryDetail.checkInventoryId = checkInventory.id
          checkInventoryDetail.productId = detail.productId
          checkInventoryDetail.productDetailId = detail.productDetailId
          checkInventoryDetail.expiryDate = new Date(detail.expiryDate)
          checkInventoryDetail.manufactureDate = detail?.manufactureDate ? new Date(detail.manufactureDate) : null
          checkInventoryDetail.lotNumber = detail.lotNumber
          checkInventoryDetail.inventory = detail.inventory
          checkInventoryDetail.quantity = detail.quantity
          checkInventoryDetail.quantityDiff = +detail.quantity - +detail.inventory

          // #region Tính phần trăm hạn sử dụng
          const rsDateHetHan = checkInventoryDetail.expiryDate
          const rsDateSanXuat = checkInventoryDetail.manufactureDate
          const rsDateHienTai = new Date()

          const timeSanXuatHetHan = (rsDateHetHan.getTime() - rsDateSanXuat.getTime()) / (1000 * 3600 * 24)
          const dateHienTaiCanTim = rsDateHienTai.getTime() - rsDateSanXuat.getTime()
          const timeCanTim = dateHienTaiCanTim / (1000 * 3600 * 24)

          const c = timeSanXuatHetHan - timeCanTim
          const phamTram = (c / timeSanXuatHetHan) * 100
          checkInventoryDetail.expiryPercent = phamTram
          // #endregion

          checkInventoryDetail.createdBy = data.createBy
          lstTask.push(checkInventoryDetail)
        }

        const member: any = await omsApiHelper.getMemberByListId(req, [data.createBy]);

        await checkInventoryDetailRepo.insert(lstTask)
        let description: string = `Nhân viên ${member[0]?.fullName} tạo mới phiếu kiểm kho`

        await this.createHistory({ checkInventoryId: checkInventory.id, description }, trans)

        return { message: `Tạo mới phiếu kiểm kho có mã ${checkInventory.code} thành công` }
      })
    } catch (error) {
      throw new Error(error);
    }
  }

  /** Hàm chỉnh sửa phiếu kiểm kho */
  async updateData(data: CheckInventoryUpdateDto, req?: IRequest) {
    const checkWarehouse = await this.warehouseRepo.findOne({ where: { id: data.warehouseId, isDeleted: false }, select: { id: true } })
    if (!checkWarehouse) throw new Error(`Không tìm thấy kho vật lý hoặc kho vật lý đã bị ngưng hoạt động!`)

    const check = await this.repo.findOne({ where: { id: data.id, isDeleted: false }, select: { id: true, status: true } })
    if (!check) throw new Error(`Không tìm thấy phiếu kiểm kho hoặc phiếu kiểm kho đã bị ngưng hoạt động!`)

    if (check.status != enumData.CheckInventoryStatus.NEW.code)
      throw new Error(`Chỉ có thể chỉnh sửa phiếu kiểm kho ở trạng thái [ ${enumData.CheckInventoryStatus.NEW.code} ]`)

    return await this.repo.manager.transaction(async (trans) => {
      const lstTask: CheckInventoryDetailEntity[] = []
      const repo = trans.getRepository(CheckInventoryEntity)
      const checkInventoryDetailRepo = trans.getRepository(CheckInventoryDetailEntity)
      check.warehouseId = data.warehouseId
      check.updatedBy = data.updateBy
      check.description = data?.description ?? null
      await repo.update(check.id, check)

      // #region Check tồn kho

      // Danh sách hạn sử dụng đã format dạng YYYY-MM-DD
      let lstProductDetailId: string[] = data.lstCheckInventoryDetail.map((wtd) => wtd.productDetailId)

      // Danh sách id sản phẩm
      const lstProductId = data.lstCheckInventoryDetail.mapAndDistinct((wtd) => wtd.productId)

      // Dic Product
      let dicProduct: any = {}
      {
        const lstProduct = await this.productRepo.find({
          where: { id: In(lstProductId), isDeleted: false },
          select: { id: true, code: true, name: true, quantity: true, quantityLock: true },
        })
        dicProduct = coreHelper.arrayToObject(lstProduct)
      }

      const dicProductDetail: any = {}
      {
        let lstProductDetail: ItemDetailEntity[] = []
        lstProductDetail = await this.productDetailRepo.find({
          where: {
            id: In(lstProductDetailId),
          },
          select: { id: true, itemId: true, expiryDate: true, quantity: true, quantityLock: true, quantityLockEmp: true, lotNumber: true },
        })

        if (lstProductDetail.length == 0) throw new Error(`Không tìm thấy danh sách sản phẩm trong kho`)

        // Số lượng: quantity
        // Số lượng đã lên đơn tạm: quantityLock
        // Số lượng lock khi phân kho: quantityLockEmp

        for (let pd of lstProductDetail) {
          dicProductDetail[pd.id] = pd
        }
      }

      let dicWarehouseProduct: any = {}
      let dicWarehouseProductDetail: any = {}
      {
        const lstWarehouseProductDetail = await this.warehouseProductDetailRepo.find({
          where: { productDetailId: In(lstProductDetailId), productId: In(lstProductId), warehouseId: data.warehouseId, isDeleted: false },
          select: {
            id: true,
            warehouseProductId: true,
            warehouseId: true,
            productId: true,
            productDetailId: true,
            expiryDate: true,
            quantity: true,
            quantityLock: true,
          },
        })
        let lstWarehouseProductId: string[] = []
        for (let wpd of lstWarehouseProductDetail) {
          dicWarehouseProductDetail[wpd.warehouseId + wpd.productId + wpd.productDetailId] = wpd
          lstWarehouseProductId.push(wpd.warehouseProductId)
        }

        const lstWarehouseProduct = await this.warehouseProductRepo.find({
          where: { id: In(lstWarehouseProductId), isDeleted: false },
          select: { id: true, warehouseId: true, productId: true, quantity: true, quantityLock: true },
        })

        for (let wp of lstWarehouseProduct) dicWarehouseProduct[wp.warehouseId + wp.productId] = wp
      }

      // Xoá sản phẩm trong phiếu kiểm kho
      await checkInventoryDetailRepo.delete({ checkInventoryId: check.id })

      for (let detail of data.lstCheckInventoryDetail) {
        let productDetail = dicProductDetail[detail.productDetailId]
        if (!productDetail) throw new Error(`Không tìm thấy sản phẩm trong kho`)

        const product = dicProduct[detail.productId]
        if (!product) throw new Error(`Không tìm thấy sản phẩm thực`)

        const warehouseProduct = dicWarehouseProduct[data.warehouseId + detail.productId]
        if (!warehouseProduct) throw new Error(`Không tìm thấy sản phẩm trong kho vật lý`)

        const warehouseProductDetail = dicWarehouseProductDetail[data.warehouseId + detail.productId + productDetail.id]
        if (!warehouseProductDetail) throw new Error(`Không tìm thấy chi tiết sản phẩm trong kho vật lý`)

        const checkInventoryDetail = new CheckInventoryDetailEntity()
        checkInventoryDetail.checkInventoryId = check.id
        checkInventoryDetail.productId = detail.productId
        checkInventoryDetail.productDetailId = detail.productDetailId
        checkInventoryDetail.expiryDate = new Date(detail.expiryDate)
        checkInventoryDetail.manufactureDate = new Date(detail.manufactureDate)
        checkInventoryDetail.lotNumber = detail.lotNumber
        checkInventoryDetail.inventory = detail.inventory
        checkInventoryDetail.quantity = detail.quantity
        checkInventoryDetail.quantityDiff = +detail.quantity - +detail.inventory

        // #region Tính phần trăm hạn sử dụng
        const rsDateHetHan = checkInventoryDetail.expiryDate
        const rsDateSanXuat = checkInventoryDetail.manufactureDate
        const rsDateHienTai = new Date()

        const timeSanXuatHetHan = (rsDateHetHan.getTime() - rsDateSanXuat.getTime()) / (1000 * 3600 * 24)
        const dateHienTaiCanTim = rsDateHienTai.getTime() - rsDateSanXuat.getTime()
        const timeCanTim = dateHienTaiCanTim / (1000 * 3600 * 24)

        const c = timeSanXuatHetHan - timeCanTim
        const phamTram = (c / timeSanXuatHetHan) * 100
        checkInventoryDetail.expiryPercent = phamTram
        // #endregion

        checkInventoryDetail.createdBy = data.updateBy
        checkInventoryDetail.updatedBy = data.updateBy
        checkInventoryDetail.updatedAt = new Date()
        lstTask.push(checkInventoryDetail)
      }

      await checkInventoryDetailRepo.insert(lstTask)
      const member: any = await omsApiHelper.getMemberByListId(req, [data.createBy]);

      const description = `Nhân viên ${member[0]?.username} cập nhật phiếu kiểm kho`
      await this.createHistory({ checkInventoryId: check.id, description }, trans)

      return { message: `Cập nhật phiếu kiểm kho có mã {${check.code}} thành công` }
    })
  }

  private async codeDefaultNK() {
    const code = 'NK' + '_' + moment(new Date()).format('DDMMYYYY') + '_'
    const objData = await this.inboundRepo.findOne({
      where: { code: Like(`%${code}%`) },
      order: { code: 'DESC' },
    })
    let sortString = '0'
    if (objData) {
      sortString = objData.code.substring(code.length, code.length + 4)
    }
    const lastSort = parseInt(sortString)
    sortString = ('000' + (lastSort + 1)).slice(-4)

    return code + sortString
  }

  /** Hàm duyệt phiếu kiểm kho */
  async updateApprove(data: FilterOneDto, req: IRequest) {
    const entity: CheckInventoryEntityExtend = await this.repo.findOne({ where: { id: data.id }, relations: { details: true } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    // #region kiểm tra Phiếu nhập kho chờ duyệt và phiếu xuất kho Đang soạn hàng.

    // const checkExistInbound = await this.inboundRepo.findOne({
    //   where: { isDeleted: false, status: enumData.InboundStatus.New.code, warehouseId: entity.warehouseId },
    //   select: { id: true },
    // })
    // if (checkExistInbound) throw new Error(`Không thể duyệt phiếu kiểm kho vì có phiếu nhập kho chờ duyệt!`)

    const checkExistOutbound = await this.outboundRepo.findOne({
      where: { isDeleted: false, status: enumData.OutboundStatus.PREPARING.code, warehouseId: entity.warehouseId },
      select: { id: true },
    })
    if (checkExistOutbound) throw new Error(`Không thể duyệt phiếu kiểm kho vì có phiếu xuất kho đang soạn hàng!`)
    // #endregion

    if (entity.status != enumData.CheckInventoryStatus.NEW.code)
      throw new Error(`Chỉ có thể duyệt phiếu ở trạng thái [ ${enumData.CheckInventoryStatus.NEW.name} ]`)

    const lstCheckInventoryDetail = entity.__details__

    const dicCheckInventoryDetail: any = {}

    // Danh sách hạn sử dụng đã format dạng YYYY-MM-DD
    let lstProductDetailId: string[] = []

    for (let wtd of lstCheckInventoryDetail) {
      dicCheckInventoryDetail[wtd.productDetailId] = wtd
      lstProductDetailId.push(wtd.productDetailId)
    }

    // Danh sách id sản phẩm
    const lstProductId = lstCheckInventoryDetail.mapAndDistinct((wtd) => wtd.productId)

    // Dic Product
    let dicProduct: any = {}
    {
      const lstProduct = await this.productRepo.find({
        where: { id: In(lstProductId) },
        select: { id: true, code: true, name: true, quantity: true, quantityLock: true, quantityLockEmp: true },
      })
      dicProduct = coreHelper.arrayToObject(lstProduct)
    }

    const dicProductDetail: any = {}
    {
      const lstProductDetail: ItemDetailEntity[] = await this.productDetailRepo.find({
        where: {
          id: In(lstProductDetailId),
        },
        select: { id: true, itemId: true, expiryDate: true, quantity: true, quantityLock: true, quantityLockEmp: true },
      })

      if (lstProductDetail.length == 0) throw new Error(`Không tìm thấy danh sách sản phẩm trong kho`)

      // Số lượng: quantity
      // Số lượng đã lên đơn tạm: quantityLock
      // Số lượng lock khi phân kho: quantityLockEmp

      for (let pd of lstProductDetail) {
        // Tạo dic dicProductDetail
        dicProductDetail[pd.id] = pd
      }
    }

    let dicWarehouseProduct: any = {}
    let dicWarehouseProductDetail: any = {}
    {
      const lstWarehouseProductDetail = await this.warehouseProductDetailRepo.find({
        where: { productDetailId: In(lstProductDetailId), productId: In(lstProductId), warehouseId: entity.warehouseId, isDeleted: false },
        select: {
          id: true,
          warehouseProductId: true,
          warehouseId: true,
          productId: true,
          productDetailId: true,
          expiryDate: true,
          quantity: true,
          quantityLock: true,
        },
      })
      let lstWarehouseProductId: string[] = []
      for (let wpd of lstWarehouseProductDetail) {
        dicWarehouseProductDetail[wpd.warehouseId + wpd.productId + wpd.productDetailId] = wpd
        lstWarehouseProductId.push(wpd.warehouseProductId)
      }

      const lstWarehouseProduct = await this.warehouseProductRepo.find({
        where: { id: In(lstWarehouseProductId), isDeleted: false },
        select: { id: true, warehouseId: true, productId: true, quantity: true, quantityLock: true },
      })

      for (let wp of lstWarehouseProduct) dicWarehouseProduct[wp.warehouseId + wp.productId] = wp
    }

    for (let obd of lstCheckInventoryDetail) {
      const productDetail = dicProductDetail[obd.productDetailId]
      if (!productDetail) throw new Error(`Không tìm thấy sản phẩm trong kho`)

      const product = dicProduct[obd.productId]
      if (!product) throw new Error(`Không tìm thấy sản phẩm thực`)

      const warehouseProduct = dicWarehouseProduct[entity.warehouseId + obd.productId]
      if (!warehouseProduct) throw new Error(`Không tìm thấy sản phẩm trong kho vật lý`)

      const warehouseProductDetail = dicWarehouseProductDetail[entity.warehouseId + obd.productId + productDetail.id]
      if (!warehouseProductDetail) throw new Error(`Không tìm thấy chi tiết sản phẩm trong kho vật lý`)

      // Nếu khối lượng giảm thì tạo phiếu xuất kho
      // #region kiểm tra điều kiện trước khi trừ tồn kho (Khi xuất kho)
      if (+obd.quantityDiff < 0) {
        if (Math.abs(+obd.quantityDiff) > +productDetail.quantity - +productDetail.quantityLock)
          if (!productDetail)
            throw new Error(
              `Khối lượng xuất của sản phẩm ${product.name} ${Math.abs(+obd.quantityDiff)} lớn hơn khối lượng tồn kho ${+productDetail.quantity - +productDetail.quantityLock
              }!`,
            )

        if (Math.abs(+obd.quantityDiff) > +product.quantity - +product.quantityLock)
          if (!product)
            throw new Error(
              `Khối lượng xuất của sản phẩm ${product.name} ${Math.abs(+obd.quantityDiff)} lớn hơn khối lượng tồn kho ${+product.quantity - +product.quantityLock
              }!`,
            )

        if (Math.abs(+obd.quantityDiff) > +warehouseProductDetail.quantity - +warehouseProductDetail.quantityLock)
          if (!warehouseProductDetail)
            throw new Error(
              `Khối lượng xuất của sản phẩm ${product.name} ${Math.abs(+obd.quantityDiff)} lớn hơn khối lượng tồn kho ${+warehouseProductDetail.quantity - +warehouseProductDetail.quantityLock
              }!`,
            )

        if (Math.abs(+obd.quantityDiff) > +warehouseProduct.quantity - +warehouseProduct.quantityLock)
          if (!warehouseProduct)
            throw new Error(
              `Khối lượng xuất của sản phẩm ${product.name} ${Math.abs(+obd.quantityDiff)} lớn hơn khối lượng tồn kho ${+warehouseProduct.quantity - +warehouseProduct.quantityLock
              }!`,
            )
      }
      // #endregion
    }

    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(CheckInventoryEntity)
      const productRepo = trans.getRepository(ItemEntity)
      const productDetailRepo = trans.getRepository(ItemDetailEntity)
      const warehouseProductRepo = trans.getRepository(WarehouseProductEntity)
      const warehouseProductDetailRepo = trans.getRepository(WarehouseProductDetailEntity)
      const checkInventoryDetailByEmployeeRepo = trans.getRepository(CheckInventoryDetailByEmployeeEntity)
      const minusRandomEmployeeProductQuantityDto: MinusRandomEmployeeProductQuantityByExpiryDateDto[] = []
      const unitRepo = trans.getRepository(UnitEntity)
      for (let obd of lstCheckInventoryDetail) {
        const productDetail = dicProductDetail[obd.productDetailId]

        const product = dicProduct[obd.productId]

        const warehouseProduct = dicWarehouseProduct[entity.warehouseId + obd.productId]

        const warehouseProductDetail = dicWarehouseProductDetail[entity.warehouseId + obd.productId + productDetail.id]

        // #region Thêm vào mảng để trừ tồn kho nhân viên
        if (+obd.quantityDiff < 0) {
          minusRandomEmployeeProductQuantityDto.push({
            productId: product.id,
            productDetailId: productDetail.id,
            expiryDate: productDetail.expiryDate,
            quantity: +obd.quantityDiff,
          })
        }

        // #endregion

        // #region Cộng hoặc trừ tồn bảng product và productDetail
        // Trừ của productDetail
        productDetail.quantity = +productDetail.quantity + +obd.quantityDiff
        productDetail.quantityLockEmp = +productDetail.quantityLockEmp + +obd.quantityDiff
        productDetail.updatedAt = new Date()
        productDetail.updatedBy = data.approveBy
        await productDetailRepo.update(productDetail.id, productDetail)

        // Trừ của product
        product.quantity = +product.quantity + +obd.quantityDiff
        product.quantityLockEmp = +product.quantityLockEmp + +obd.quantityDiff
        product.updatedAt = new Date()
        product.updatedBy = data.approveBy
        await productRepo.update(product.id, product)
        // #endregion

        // #region Trừ bảng warehouseProduct và warehouseProductDetail
        // Trừ của warehouseProductDetail
        warehouseProductDetail.quantity = +warehouseProductDetail.quantity + +obd.quantityDiff
        warehouseProductDetail.updatedAt = new Date()
        warehouseProductDetail.updatedBy = data.approveBy
        await warehouseProductDetailRepo.update(warehouseProductDetail.id, warehouseProductDetail)

        // Trừ của warehouseProduct
        warehouseProduct.quantity = +warehouseProduct.quantity + +obd.quantityDiff
        warehouseProduct.updatedAt = new Date()
        warehouseProduct.updatedBy = data.approveBy
        await warehouseProductRepo.update(warehouseProduct.id, warehouseProduct)
        // #endregion
      }

      // #region trừ tồn kho nhân viên random và lưu lại lịch sử trừ tồn
      // if (minusRandomEmployeeProductQuantityDto.length > 0) {
      //   const productInfoWithEmployee: any = await authApiHelper.minusRandomEmployeeProductQuantity(req, minusRandomEmployeeProductQuantityDto)

      //   if (productInfoWithEmployee.length > 0) {
      //     for (let e of productInfoWithEmployee) {
      //       const cID = dicCheckInventoryDetail[e.productDetailId]
      //       if (!cID) throw new Error(`Không tìm thấy sản phẩm trong phiếu kiểm kho!`)
      //       const newCIDEmployee = new CheckInventoryDetailByEmployeeEntity()
      //       newCIDEmployee.checkInventoryDetailId = cID.id
      //       newCIDEmployee.employeeId = e.employeeId
      //       newCIDEmployee.employeeCode = e.employeeCode
      //       newCIDEmployee.employeeName = e.employeeName
      //       newCIDEmployee.inventory = +e.preMinusQuantity
      //       newCIDEmployee.quantityDiff = +e.minusQuantity
      //       newCIDEmployee.quantity = +e.preMinusQuantity + +e.minusQuantity

      //       checkInventoryDetailByEmployeeRepo.insert(newCIDEmployee)
      //     }
      //   }
      // }

      // #endregion

      // #region sinh phiếu nhập kho
      // Gom 2 danh sách sản phẩm
      // 1 danh sách nhập kho
      const lstInbound: any[] = lstCheckInventoryDetail.filter((cid: any) => +cid.quantityDiff > 0)
      if (lstInbound.length > 0) {
        let lstDetail: InboundDetailCreateFromOutsideDto[] = []

        for (let ib of lstInbound) {
          const unitObj: any = await unitRepo.findOne({ where: { id: ib.unitId, isDeleted: false } })
          if (!unitObj) throw new Error(`Đơn vị tính không còn tồn tại!`)
          lstDetail.push({
            productId: ib.productId,
            productDetailId: ib.productDetailId,
            productName: dicProduct[ib.productId].name,
            productCode: dicProduct[ib.productId].code,
            unitId: unitObj.id,
            expiryDate: new Date(ib.expiryDate),
            manufactureDate: ib.manufactureDate ? new Date(ib.manufactureDate) : null,
            lotNumber: ib.lotNumber,
            inventory: ib.inventory,
            quantity: ib.quantityDiff,
          })
        }

        const inboundCreateDto: InboundCreateFromOutsideDto = {
          type: enumData.InboundType.CHECK_INVENTORY.code,
          warehouseId: entity.warehouseId,
          checkInventoryId: entity.id,
          lstDetail: lstDetail,
        }

        await this.inboundService.createDataApproved(inboundCreateDto, req, trans)
      }
      // #endregion

      // #region Xuất kho
      const lstOutbound: any[] = lstCheckInventoryDetail.filter((cid: any) => +cid.quantityDiff < 0)
      if (lstOutbound.length > 0) {
        let lstDetail: OutboundDetailCreateDto[] = []

        for (let ib of lstOutbound) {
          lstDetail.push({
            productDetailId: ib.productDetailId,
            productId: ib.productId,
            expiryDate: new Date(ib.expiryDate),
            manufactureDate: ib.manufactureDate ? new Date(ib.manufactureDate) : null,
            lotNumber: ib.lotNumber,
            inventory: ib.inventory,
            quantity: -ib.quantityDiff,
          })
        }

        const outboundCreateDto: OutboundCreateDto = {
          type: enumData.OutboundType.CHECK_INVENTORY.code,
          createdAt: new Date(),
          warehouseId: entity.warehouseId,
          checkInventoryId: entity.id,
          lstOutboundDetail: lstDetail,
        }

        await this.outboundService.createDataApproved(outboundCreateDto, req, trans)
      }
      // #endregion

      // Xoá relation khi update
      delete entity.__details__

      entity.status = enumData.CheckInventoryStatus.APPROVED.code
      entity.approvedBy = data.approveBy
      entity.approvedDate = new Date()
      await repo.update(entity.id, entity)

      const member: any = await omsApiHelper.getMemberByListId(req, [data.approveBy]);

      // Tạo lịch sử
      const description = `Nhân viên ${member[0]?.fullName} duyệt phiếu kiểm kho, chuyển trạng thái của phiếu từ ${enumData.CheckInventoryStatus.NEW.name} thành ${enumData.CheckInventoryStatus.APPROVED.name}`
      await this.createHistory({ checkInventoryId: entity.id, description }, trans)
    })
    return { message: 'Duyệt phiếu kiểm kho thành công!' }
  }

  /** Hàm huỷ phiếu kiểm kho */
  async updateCancel(data: FilterOneDto, req?: IRequest) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    if (entity.status != enumData.CheckInventoryStatus.NEW.code)
      throw new Error(`Chỉ có thể huỷ phiếu ở trạng thái [ ${enumData.CheckInventoryStatus.NEW.name} ]`)

    entity.status = enumData.CheckInventoryStatus.CANCEL.code
    entity.updatedBy = data.approveBy
    await this.repo.update(entity.id, entity)

    const member: any = await omsApiHelper.getMemberByListId(req, [data.approveBy]);

    const description = `Nhân viên ${member[0]?.fullName} huỷ phiếu kiểm kho`
    await this.createHistory({ checkInventoryId: entity.id, description }, this.repo.manager)

    return { message: 'Huỷ phiếu kiểm kho thành công!' }
  }

  /** Hàm tạo mới phiếu kiểm kho */
  async createDataByExcel(data: CheckInventoryCreateByExcelDto[], user?: any, req?: IRequest) {
    const lstData = data["lstData"]

    if (lstData.length < 1) throw new Error('Vui lòng điền ít nhất 1 dòng thông tin kiểm kho!')

    for (let dt of lstData) {
      dt.warehouseCode = dt.warehouseCode.trim()
      dt.productCode = dt.productCode.trim()
    }

    let dicWarehouse: Record<string, any> = {}
    let dicWarehouseProduct: Record<string, any> = {}
    let dicWarehouseProductDetail: Record<string, any> = {}
    {
      const lstWarehouseCode = lstData.mapAndDistinct((it) => it.warehouseCode)
      const lstWarehouse: any[] = await this.warehouseRepo.find({
        where: { code: In(lstWarehouseCode), isDeleted: false },
        relations: { products: { details: true } },
      })
      if (lstWarehouse.length == 0) throw new Error(`Không tìm thấy danh sách kho theo mã kho trong file excel!`)
      dicWarehouse = coreHelper.arrayToObject(lstWarehouse, 'code')

      for (let w of lstWarehouseCode) if (!dicWarehouse[w]) throw new Error(`Mã kho [ ${w} ] không tồn tại hoặc đã bị ngưng hoạt động!`)

      // for (let w of lstWarehouse) {
      //   dicWarehouse[w.code] = w
      //   for (let p of w.__products__) {
      //     dicWarehouseProduct[w.code + p.__product__.code] = p
      //     for (let detail of p.__details__)
      //       dicWarehouseProductDetail[w.code + p.__product__.code + +moment(new Date(detail.expiryDate)).format('DDMMYYYY')] = p
      //   }
      // }
    }

    let dicProduct: Record<string, any> = {}
    const dicProductDetail: Record<string, any> = {}
    {
      const lstProductCode = lstData.mapAndDistinct((it) => it.productCode)
      const lstProduct: any[] = await this.productRepo.find({
        where: { code: In(lstProductCode) },
        select: { id: true, code: true, name: true, quantity: true, quantityLock: true },
        relations: { details: true },
      })
      if (lstProduct.length == 0) throw new Error(`Không tìm thấy danh sách sản phẩm theo mã sản phẩm trong file excel!`)
      dicProduct = coreHelper.arrayToObject(lstProduct, 'code')

      for (let pc of lstProductCode) if (!dicProduct[pc]) throw new Error(`Mã sản phẩm [ ${pc} ] không tồn tại hoặc đã bị ngưng hoạt động!`)

      // Tạo dicProductDetail của mỗi product
      for (let p of lstProduct) for (let pd of p.__details__) dicProductDetail[p.code + moment(new Date(pd.expiryDate)).format('DDMMYYYY')] = pd
    }

    for (let obd of lstData) {
      let productDetail = dicProductDetail[obd.productCode + moment(obd.expiryDate).format('DDMMYYYY')]
      if (!productDetail) throw new Error(`Không tìm thấy sản phẩm thực [ ${obd.productCode} ]`)

      const product = dicProduct[obd.productCode]
      if (!product) throw new Error(`Không tìm thấy sản phẩm [ ${obd.productCode} ]`)

      const warehouseProduct = dicWarehouseProduct[obd.warehouseCode + obd.productCode]
      if (!warehouseProduct) throw new Error(`Không tìm thấy sản phẩm [ ${obd.productCode} ] trong kho vật lý [ ${obd.warehouseCode} ]`)

      const warehouseProductDetail = dicWarehouseProductDetail[obd.warehouseCode + obd.productCode + moment(obd.expiryDate).format('DDMMYYYY')]
      if (!warehouseProductDetail)
        throw new Error(
          `Không tìm thấy chi tiết sản phẩm [ ${obd.productCode} ] có hạn sử dụng [ ${moment(obd.expiryDate).format(
            'DD/MM/YYYY',
          )} ] trong kho vật lý [ ${obd.warehouseCode} ]`,
        )
    }

    // #endregion

    let inputData: any[] = []
    // Loại bỏ khoảng trắng
    // Gộp các dòng chung mã code với nhau
    for (let dt of lstData) {
      let findCode = inputData.find((i) => i.code == dt.code)
      if (findCode) {
        findCode.lstCheckInventoryDetail.push({
          productCode: dt.productCode,
          expiryDate: dt.expiryDate,
          quantity: dt.quantity,
        })
        findCode.description = dt?.description ?? findCode?.description ?? null
      } else {
        findCode = {}
        findCode.code = dt.code
        findCode.warehouseCode = dt.warehouseCode
        findCode.createdAt = dt.createdAt
        findCode.lstCheckInventoryDetail = [
          {
            productCode: dt.productCode,
            expiryDate: dt.expiryDate,
            quantity: dt.quantity,
          },
        ]
        findCode.description = dt?.description ?? null
        inputData.push(findCode)
      }
    }

    // Sinh code theo quy tắc ddmmyyy_xxxx (xxxx tăng dần) VD: 01102023_0001
    const curDate = moment(new Date()).format('DDMMYYYY')

    let genCode: string
    // Đếm số lượng phiếu kiểm kho trong ngày
    let count = await this.repo.count({ where: { code: Like(`PKK_${curDate}_%`) }, select: { id: true } })
    if (!count) count = 0
    return await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(CheckInventoryEntity)
      const checkInventoryDetailRepo = trans.getRepository(CheckInventoryDetailEntity)

      for (let data of inputData) {
        genCode = this.genCode(count, 'PKK')

        const lstTask: CheckInventoryDetailEntity[] = []
        const checkInventory = new CheckInventoryEntity()
        checkInventory.id = uuidv4()
        checkInventory.code = genCode
        checkInventory.warehouseId = dicWarehouse[data.warehouseCode].id
        checkInventory.createdAt = new Date(data.createdAt)

        checkInventory.createdBy = data.createBy
        checkInventory.description = data?.description ?? null
        await repo.insert(checkInventory)

        for (let detail of data.lstCheckInventoryDetail) {
          const product = dicProduct[detail.productCode]
          const productDetail = dicProductDetail[detail.productCode + moment(detail.expiryDate).format('DDMMYYYY')]
          const checkInventoryDetail = new CheckInventoryDetailEntity()
          checkInventoryDetail.checkInventoryId = checkInventory.id
          checkInventoryDetail.productId = product.id
          checkInventoryDetail.productDetailId = productDetail.id
          checkInventoryDetail.expiryDate = new Date(detail.expiryDate)
          checkInventoryDetail.manufactureDate = productDetail?.manufactureDate ? new Date(productDetail.manufactureDate) : null
          checkInventoryDetail.lotNumber = productDetail?.lotNumber ?? null
          checkInventoryDetail.inventory = productDetail.quantity
          checkInventoryDetail.quantity = detail.quantity
          checkInventoryDetail.quantityDiff = +detail.quantity - +productDetail.quantity

          // #region Tính phần trăm hạn sử dụng
          const rsDateHetHan = checkInventoryDetail.expiryDate
          const rsDateSanXuat = checkInventoryDetail.manufactureDate
          const rsDateHienTai = new Date()

          const timeSanXuatHetHan = (rsDateHetHan.getTime() - rsDateSanXuat.getTime()) / (1000 * 3600 * 24)
          const dateHienTaiCanTim = rsDateHienTai.getTime() - rsDateSanXuat.getTime()
          const timeCanTim = dateHienTaiCanTim / (1000 * 3600 * 24)

          const c = timeSanXuatHetHan - timeCanTim
          const phamTram = (c / timeSanXuatHetHan) * 100
          checkInventoryDetail.expiryPercent = phamTram
          // #endregion

          checkInventoryDetail.createdBy = user.createBy
          lstTask.push(checkInventoryDetail)
        }

        const member: any = await omsApiHelper.getMemberByListId(req, [user.createBy]);

        await checkInventoryDetailRepo.insert(lstTask)
        let description: string = `Nhân viên ${member[0]?.username} tạo mới phiếu kiểm kho`

        await this.createHistory({ checkInventoryId: checkInventory.id, description }, trans)

        count++
      }

      return { message: `Tạo mới DS phiếu kiểm kho bằng excel thành công!` }
    })
  }
}
