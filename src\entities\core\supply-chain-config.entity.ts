import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { NSSupplyChain } from '../../constants'
import { BaseEntity } from './base.entity'
import { Entity, Column, Index } from 'typeorm'

@Entity('supply_chain_config')
export class SupplyChainConfigEntity extends BaseEntity {
  @Column({ type: 'uuid', nullable: false })
  supplierId: string

  @Column({ type: "varchar", nullable: false, unique: true })
  @Index()
  supplyChainCode: string

  @Column({ type: 'uuid', nullable: true })
  partnerId: string

  @Column({ type: 'varchar', nullable: true })
  note: string

  @ApiPropertyOptional({
    description: `Trạng thái thanh toán ${Object.values(NSSupplyChain.ESupplierType).join(' | ')}`,
    enum: NSSupplyChain.ESupplierType,
    default: NSSupplyChain.ESupplierType.ACTIVE,
  })
  @Column({ default: NSSupplyChain.ESupplierType.ACTIVE })
  status: NSSupplyChain.ESupplierType
}

@Entity('supply_chain_config_detail')
export class SupplyChainConfigDetailEntity extends BaseEntity {
  @Column({ type: 'uuid', nullable: false })
  supplyChainId: string

  @Column({ type: 'uuid', nullable: false })
  regionId: string

  @Column({ type: 'uuid', nullable: false })
  distributorId: string

  @Column({ type: 'uuid', nullable: false })
  deliveryId: string

  /** Người gói hàng */
  @Column({ type: 'uuid', nullable: false })
  packerId: string

  /** End user */
  @Column({ type: 'uuid', nullable: true })
  endUserId: string
}

@Entity('supply_chain_config_approve')
export class SupplyChainConfigApproveEntity extends BaseEntity {
  @ApiProperty({ description: 'ID Supply Chain' })
  @Column({ type: 'uuid', nullable: false })
  supplyChainId: string

  @ApiProperty({ description: 'ID Supply Chain Detail' })
  @Column({ type: 'uuid', nullable: true })
  supplyChainDetailId: string

  @ApiProperty({ description: 'Id vùng, trường hợp cấu hình cho từng vùng' })
  @Column({ type: 'uuid', nullable: true })
  regionId: string

  @ApiProperty({ description: 'Id đối tượng duyệt' })
  @Column({ type: 'uuid', nullable: false })
  approverId: string

  @ApiProperty({ description: 'Mã code đối tượng duyệt' })
  @Column({ type: 'varchar', nullable: false })
  approverCode: string

  @ApiProperty({ description: 'Tên đối tượng duyệt' })
  @Column({ type: 'varchar', nullable: false })
  approverName: string

  @ApiProperty({ description: 'Cấp duyệt của đối tượng' })
  @Column({ type: 'int', nullable: false })
  approvalLevel: number
}

@Entity('supply_chain_config_time')
export class SupplyChainConfigTimeEntity extends BaseEntity {
  @ApiProperty({ description: 'ID Supply Chain Detail' })
  @Column({ type: 'uuid', nullable: true })
  supplyChainDetailId: string

  @ApiProperty({ description: "Số ngày phải duyệt xong" })
  @Column({ nullable: true })
  approvalTime: string;

  @ApiProperty({ description: "Số ngày NCC phải giao tới 3PL " })
  @Column({ nullable: true })
  supplierDeliveryTime: string;

  @ApiProperty({ description: "Số ngày 3PL phải giao hàng tới MBC" })
  @Column({ nullable: true })
  thirdPartyDeliveryTime: string;
}
