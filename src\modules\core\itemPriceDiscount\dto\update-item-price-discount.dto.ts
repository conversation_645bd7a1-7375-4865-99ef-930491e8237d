import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsUUID, IsInt, IsNumber, IsOptional, Min, IsNotEmpty, ValidateNested } from 'class-validator'
import { CreateItemPriceDiscountDto } from './create-item-price-discount.dto'

export class UpdateItemPriceDiscountDto extends CreateItemPriceDiscountDto {
  @ApiProperty({ description: 'Id giá sản phẩm' })
  @IsNotEmpty()
  id: string
}

export class ListUpdateItemPriceDiscountDto {
  @ApiProperty({ description: 'Danh sách Id giá sản phẩm' })
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => UpdateItemPriceDiscountDto)
  items: UpdateItemPriceDiscountDto[]
}
