import { Body, Controller, Get, Post, Query, Req } from '@nestjs/common'
import { ApiTags } from '@nestjs/swagger'
import {
  CreateDeliveryNoteDto,
  DetailTrackingDto,
  FilterDeliverNoteDto,
  PackingListDto,
  TrackingList3PLDto,
  TrackingListSupplierDto,
  UpdateDeliveryNoteDto,
  UpdateDeliveryStatusDto,
  UpdateFileTrackingDto,
  UpdateReceivingStatusDto,
} from './dto/deliveryNote.dto'
import { DeliveryNoteService } from './deliveryNote.service'
import { Request as IRequest } from 'express'

@ApiTags('DeliveryNote')
@Controller('delivery-note')
export class DeliveryNoteController {
  constructor(private readonly deliveryNoteService: DeliveryNoteService) {}

  @Get('pagination')
  async getBankPagination(@Query() params: FilterDeliverNoteDto) {
    return this.deliveryNoteService.paginationDeliveryNote(params)
  }

  @Get('pagination-supplier')
  async paginationSupplierDeliveryNote(@Query() params: FilterDeliverNoteDto) {
    return this.deliveryNoteService.paginationSupplierDeliveryNote(params)
  }

  @Get('detail')
  public async detail(@Query('id') id: string) {
    return await this.deliveryNoteService.detailDeliveryNote(id)
  }

  @Get('check-po-approved')
  public async checkPoApproved(@Query('id') id: string) {
    return await this.deliveryNoteService.checkPoApproved(id)
  }

  @Post('create')
  async createDeliveryNote(@Body() data: CreateDeliveryNoteDto) {
    return this.deliveryNoteService.createDeliveryNote(data)
  }

  @Post('update')
  public async update(@Body() data: UpdateDeliveryNoteDto) {
    return await this.deliveryNoteService.updateDeliveryNote(data)
  }

  @Post('update_tracking')
  async updateTracking(@Body() data: UpdateReceivingStatusDto, @Req() req: IRequest) {
    const { id } = data
    return this.deliveryNoteService.updateTracking(id, req)
  }

  /** Cập nhật chứng từ cho chặng */
  @Post('update_file')
  async updateFile(@Body() data: UpdateFileTrackingDto) {
    return this.deliveryNoteService.updateFileTracking(data)
  }

  // Chi tiết phiếu giao nhận
  @Get('detail-tracking')
  async detailTracking(@Query() body: DetailTrackingDto, @Req() req: IRequest) {
    return this.deliveryNoteService.detailTracking(body, req)
  }

  /** Portal */
  @Get('supplier-tracking')
  async getTrackingSupPagination(@Query() params: TrackingListSupplierDto) {
    return this.deliveryNoteService.listTrackingPoSupplier(params)
  }

  @Get('3pl-tracking')
  async getTracking3plPagination(@Query() params: TrackingListSupplierDto) {
    return this.deliveryNoteService.listTrackingPo3PL(params)
  }

  @Get('packing-note-tracking')
  async getPackingNoteTrackingPagination(@Query() params: PackingListDto) {
    return this.deliveryNoteService.listPackingTracking(params)
  }

  @Get('packing-note-detail')
  async getPackingNoteDetail(@Query('id') id: string, @Req() req: IRequest) {
    return this.deliveryNoteService.detailPackingNote(id, req)
  }

  @Post('packing-note-update')
  async updatePackingNote(@Body() data: UpdateDeliveryStatusDto, @Req() req: IRequest) {
    return this.deliveryNoteService.updatePackingStatus(data.id, req)
  }
}
