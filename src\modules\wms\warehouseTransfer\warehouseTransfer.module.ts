import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import { WarehouseTransferService } from './warehouseTransfer.service'
import { WarehouseTransferController } from './warehouseTransfer.controller'
import {
  ItemDetailRepository,
  ItemRepository,
  WarehouseProductDetailRepository,
  WarehouseProductRepository,
  WarehouseRepository,
  WarehouseTransferRepository,
} from '../../../repositories'
import { OutboundModule } from '../outbound/outbound.module'
import { InboundModule } from '../inbound/inbound.module'
import { WarehouseTransferPublicController } from './warehouseTransferPublic.controller'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      WarehouseTransferRepository,
      WarehouseRepository,
      ItemDetailRepository,
      ItemRepository,
      WarehouseProductRepository,
      WarehouseProductDetailRepository,
    ]),
    OutboundModule,
    InboundModule,
  ],
  controllers: [WarehouseTransferController, WarehouseTransferPublicController],
  providers: [WarehouseTransferService],
  exports: [WarehouseTransferService],
})
export class WarehouseTransferModule {}
