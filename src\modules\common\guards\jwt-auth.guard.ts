import { ExecutionContext, Inject, Injectable } from '@nestjs/common'
import { AuthGuard } from '@nestjs/passport'
import { JwtService } from '@nestjs/jwt'
import { Repository } from 'typeorm'
import { UserEntity } from '../../../entities'
import { InjectRepository } from '@nestjs/typeorm'

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  // constructor(
  //   @Inject(JwtService) private readonly jwtService: JwtService,
  //   @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>,
  // ) {
  //   super()
  // }
  // async canActivate(context: ExecutionContext): Promise<boolean> {
  //   const request = context.switchToHttp().getRequest()
  //   const token = request.headers['authorization']?.split(' ')[1]
  //   if (!token) return false
  //   try {
  //     const userInfo = this.jwtService.verify(token)
  //     request.user = await this.userRepo.findOne({ where: { id: userInfo.sub } })
  //     return !!request.user
  //   } catch (e) {
  //     return false
  //   }
  // }
}
