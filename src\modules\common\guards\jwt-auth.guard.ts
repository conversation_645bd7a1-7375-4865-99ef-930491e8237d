import { ExecutionContext, Inject, Injectable } from '@nestjs/common'
import { AuthGuard } from '@nestjs/passport'
import { JwtService } from '@nestjs/jwt'
import { DataSource } from 'typeorm'
import { UserEntity } from '../../../entities'
import { InjectDataSource } from '@nestjs/typeorm'

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  private userRepo

  constructor(@Inject(JwtService) private readonly jwtService: JwtService, @InjectDataSource() private readonly dataSource: DataSource) {
    super()
    this.userRepo = this.dataSource.getRepository(UserEntity) //
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest()
    const token = request.headers['authorization']?.split(' ')[1]
    if (!token) return false

    try {
      const userInfo = this.jwtService.verify(token)
      request.user = await this.userRepo.findOne({ where: { id: userInfo.sub } })
      return !!request.user
    } catch (e) {
      return false
    }
  }
}
