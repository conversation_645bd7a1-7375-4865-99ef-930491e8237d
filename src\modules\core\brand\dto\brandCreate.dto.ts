import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString, IsEmail, IsOptional, IsUUID } from 'class-validator'

export class BrandCreateDto {
  @IsNotEmpty()
  @IsString()
  code: string

  @IsNotEmpty()
  @IsString()
  name: string

  @IsOptional()
  email: string

  @IsOptional()
  description: string

  @IsOptional()
  address: string

  @IsOptional()
  wardId: string

  @IsOptional()
  districtId: string

  @IsOptional()
  cityId: string

  @IsOptional()
  phone: string

  @IsOptional()
  @IsUUID()
  parentId: string

  lstMediaBrandType?: MediaDto[]
}

export class MediaDto {
  @ApiProperty({ description: 'Id sản phẩm' })
  @IsString()
  productId?: string
  @ApiProperty({ description: 'Đường dẫn của file' })
  @IsNotEmpty({ message: 'Đường dẩn của file không được để trống' })
  @IsString()
  url: string

  content: string
  table: string

  @ApiProperty({ description: 'Tên file' })
  @IsNotEmpty({ message: 'Tên file không được để trống' })
  @IsString()
  name: string
}
