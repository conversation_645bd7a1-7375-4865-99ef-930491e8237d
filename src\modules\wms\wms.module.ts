import { Module } from '@nestjs/common'
import { WarehouseModule } from './warehouse/warehouse.module'
import { InboundModule } from './inbound/inbound.module'
import { OutboundModule } from './outbound/outbound.module'
import { WarehouseTransferModule } from './warehouseTransfer/warehouseTransfer.module'
import { CheckInventoryModule } from './checkInventory/checkInventory.module'
import { CheckInventoryDetailModule } from './checkInventoryDetail/checkInventoryDetail.module'
import { wmsPublicApiModule } from './apiWmsPublic/wmsPublicApi.module'

@Module({
  imports: [
    WarehouseModule,
    InboundModule,
    OutboundModule,
    WarehouseTransferModule,
    // wmsPublicApiModule,
    CheckInventoryModule,
    wmsPublicApiModule,
    CheckInventoryDetailModule,
  ],
})
export class wmsModule {}
