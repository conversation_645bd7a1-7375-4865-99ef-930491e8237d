import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'

/** Interface để gửi email password User. */
export class EmailSendPasswordDto {
  @ApiProperty({ description: 'Id công ty' })
  @IsNotEmpty()
  @IsString()
  companyId: string

  @ApiProperty({ description: 'Email nhận password' })
  @IsNotEmpty()
  @IsString()
  email: string

  @ApiProperty({ description: 'Mật khẩu' })
  @IsNotEmpty()
  @IsString()
  password: string
}
