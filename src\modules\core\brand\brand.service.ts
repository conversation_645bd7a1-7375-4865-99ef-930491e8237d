import { Injectable, ConflictException } from '@nestjs/common'
import { BrandRepository, MediaRepository } from '../../../repositories'
import { PaginationDto } from '../../../dto/pagination.dto'
import { BrandCreateDto } from './dto'
import { ERROR_CODE_TAKEN, ERROR_NOT_FOUND_DATA, IMPORT_SUCCESS, UPDATE_ACTIVE_SUCCESS, enumData } from '../../../constants'
import { FilterOneDto, UserDto } from '../../../dto'
import { BrandEntity, CityEntity, DistrictEntity, MediaEntity, WardEntity } from '../../../entities'
import { In, IsNull, Like } from 'typeorm'
import { BrandCreateExcelDto } from './dto'
import { BrandUpdateDto } from './dto/brandUpdate.dto'

@Injectable()
export class BrandService {
  constructor(private readonly repo: BrandRepository, private mediaRepo: MediaRepository) { }

  public async find(data: any) {
    const whereCon: any = { isDeleted: false }
    if (data.name) whereCon.name = Like(`%${data.name}%`)
    if (data.code) whereCon.code = Like(`%${data.code}%`)
    if (data.email) whereCon.email = Like(`%${data.email}%`)
    if (data.parentId) whereCon.parentId = data.parentId
    if (data.wardId) whereCon.wardId = data.wardId
    if (data.districtId) whereCon.districtId = data.districtId
    if (data.cityId) whereCon.cityId = data.cityId
    if (data.lstId?.length > 0) whereCon.id = In(data.lstId)
    if (data.lstCode?.length > 0) whereCon.code = In(data.lstCode)
    if (data.lstParentId?.length > 0) whereCon.parentId = In(data.lstParentId)
    let relations: any = []
    if (data.isRelation) {
      relations = { parent: { parent: { parent: { parent: true } } } }
    }
    let res: any = await this.repo.find({ where: whereCon, relations: relations })
    const dictMedia: any = {}
    {
      const lstProductId = res.mapAndDistinct((e) => e.id)
      const lstMedia: any = await this.mediaRepo.find({ where: { productId: In(lstProductId), table: enumData.MediaTable.Brand.code } })
      lstMedia.forEach((c) => {
        if (Array.isArray(dictMedia[c.productId])) {
          dictMedia[c.productId] = [...dictMedia[c.productId], c]
        } else {
          dictMedia[c.productId] = [c]
        }
      })
    }

    for (let item of res) {
      item['images'] = dictMedia[item.id] || []
    }

    return res
  }

  public async findByPermission(userlogin: UserDto) {
    // if (userlogin.isAdmin) {
    //   return await this.repo.find({ where: { isDeleted: false } })
    // }
    // const empBrands = await this.repo.manager.getRepository(EmpBrandEntity).find({ where: { isDeleted: false, employeeId: userlogin.employeeId } })
    // if (empBrands.length == 0) return []
    // let brandIds = empBrands.map((c) => c.brandId)
    // brandIds = Array.from(new Set(brandIds))
    // let lstChild: BrandEntity[]
    // let lstParentId = Array.from(new Set(brandIds))
    // do {
    //   lstChild = await this.repo.manager.getRepository(BrandEntity).find({ where: { isDeleted: false, parentId: In(lstParentId) } })
    //   lstParentId = await lstChild.map((s) => {
    //     return s.id
    //   })
    //   lstParentId = Array.from(new Set(lstParentId))
    //   brandIds.push(...lstParentId)
    //   brandIds = Array.from(new Set(brandIds))
    // } while (lstChild.length > 0)
    // return await this.repo.find({ where: { isDeleted: false, id: In(brandIds) }, order: { createdAt: 'ASC' } })
    return []
  }

  public async createData(user: UserDto, data: BrandCreateDto) {
    const checkCodeExist = await this.repo.findOne({ where: { code: data.code } })
    if (checkCodeExist) throw new ConflictException(ERROR_CODE_TAKEN)

    const newEntity = this.repo.create({ ...data })
    newEntity.createdBy = user.id
    await this.repo.insert(newEntity)

    if (data.lstMediaBrandType && data.lstMediaBrandType.length > 0) {
      let lstImages: any = []
      for (let item of data.lstMediaBrandType) {
        item.productId = newEntity.id
        item.url = item.url
        item.table = enumData.MediaTable.Brand.code
        lstImages.push(item)
      }
      await this.mediaRepo.insert(lstImages)
    }

    return { message: 'Tạo mới thành công' }
  }

  public async updateData(user: UserDto, data: BrandUpdateDto) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    if (data.code != entity.code) {
      const checkCodeExist = await this.repo.findOne({
        where: { code: data.code },
        select: { id: true },
      })
      if (checkCodeExist) throw new Error(ERROR_CODE_TAKEN)
    }

    entity.updatedBy = user.id
    entity.name = data.name
    entity.email = data.email
    entity.code = data.code
    entity.phone = data.phone
    entity.address = data.address
    entity.description = data.description
    entity.wardId = data.wardId
    entity.cityId = data.cityId
    entity.districtId = data.districtId
    entity.parentId = data.parentId
    await this.repo.update(entity.id, entity)
    if (data.lstMediaBrandType && data.lstMediaBrandType.length > 0) {
      await this.mediaRepo.delete({ productId: entity.id, table: enumData.MediaTable.Brand.code })
      let lstImages: any = []
      for (let item of data.lstMediaBrandType) {
        item.productId = entity.id
        item.url = item.url
        item.table = enumData.MediaTable.Brand.code
        lstImages.push(item)
      }
      await this.mediaRepo.insert(lstImages)
    }

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  public async pagination(data: PaginationDto) {
    let whereCon: any = {}
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.cityId) whereCon.cityId = data.where.cityId
    if (data.where.districtId) whereCon.districtId = data.where.districtId
    if (data.where.wardId) whereCon.wardId = data.where.wardId
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    let result: any = await this.repo.findAndCount({
      relations: { district: true, ward: true, city: true, parent: { parent: { parent: { parent: true } } } },
      where: whereCon,
      order: { createdAt: 'DESC' },
      skip: data.skip,
      take: data.take,
    })
    if (result[0].length === 0) return []

    const dicMedia: any = {}
    {
      const lstMedia = await this.mediaRepo.find({ where: { table: enumData.MediaTable.Brand.code } })
      lstMedia.forEach((x) => (dicMedia[x.productId] = x))
    }
    for (let item of result[0]) {
      item['images'] = dicMedia[item.id] || []
      let cityObj = await item.__city__
      let districtObj = await item.__district__
      let wardObj = await item.__ward__
      item.cityName = cityObj?.name
      item.districtName = districtObj?.name
      item.wardName = wardObj?.name
      delete item.__city__
      delete item.__district__
      delete item.__ward__
      item.media = dicMedia[item.id]
    }

    return result
  }

  public async updateIsDelete(id: string, user: UserDto) {
    const entity = await this.repo.findOne({ where: { id } })
    if (!entity) {
      throw new Error(ERROR_NOT_FOUND_DATA)
    }

    await this.repo.update({ id: entity.id }, { isDeleted: !entity.isDeleted, updatedBy: user.id })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  public async getListParent(data: { id: string }) {
    const brand = await this.repo.findOne({ where: { id: data.id } })
    if (brand && brand.parentId) {
      //Nếu k phải level 1
      // Tìm cha
      const brandParent = await this.repo.findOne({
        where: {
          isDeleted: false,
          id: brand.parentId,
        },
      })
      if (brandParent && brandParent.parentId) {
        const brandParent2 = await this.repo.findOne({
          where: {
            isDeleted: false,
            id: brandParent.parentId,
          },
        })
        // Nếu không có parentId tức là brand level = 2
        //Danh sách level 1
        const list1 = await this.repo.find({
          where: {
            isDeleted: false,
            parentId: IsNull(),
          },
        })

        // Danh sách level 2
        const list2 = await this.repo.find({
          where: {
            isDeleted: false,
            parentId: brandParent ? brandParent.parentId : '',
          },
        })

        // Danh sách level 3
        const list3 = await this.repo.find({
          where: {
            isDeleted: false,
            parentId: brandParent2 ? brandParent2.parentId : '',
          },
        })
        return {
          list1,
          list2,
          list3,
          idLv3: brandParent2?.id,
          idLv2: brandParent.id,
          idLv1: brandParent2?.parentId,
        }
      } else {
        // Nếu không có parentId tức là brand level = 2
        //Danh sách level 1
        const list1 = await this.repo.find({
          where: {
            isDeleted: false,
            parentId: IsNull(),
          },
        })

        // Danh sách level 2
        const list2 = await this.repo.find({
          where: {
            isDeleted: false,
            parentId: brandParent ? brandParent.parentId : '',
          },
        })
        return { list1, list2, idLv1: brandParent?.id, idLv2: brandParent?.parentId }
      }
    } else {
      // Nếu không có parentId tức là brand level = 1
      //Danh sách level 1
      const list1 = await this.repo.find({
        where: {
          isDeleted: false,
          parentId: IsNull(),
        },
      })
      return { list1, idLv1: data.id }
    }
  }

  public async findLevel() {
    return await this.repo.find({ where: { parentId: IsNull(), isDeleted: false } })
  }

  async findOne(data: FilterOneDto) {
    const whereCon: any = { id: data.id, isDeleted: false }
    return await this.repo.findOneBy(whereCon)
  }

  async createDataExcel(user: UserDto, data: BrandCreateExcelDto[]) {
    await this.repo.manager.transaction(async (trans) => {
      const brandRepo = trans.getRepository(BrandEntity)
      const cityRepo = trans.getRepository(CityEntity)
      const districtRepo = trans.getRepository(DistrictEntity)
      const wardRepo = trans.getRepository(WardEntity)
      data = data.map((c) => ({
        ...c,
        cityCode: c.cityCode == '' ? null : c?.cityCode?.toString().padStart(2, '0'),
        districtCode: c.districtCode == '' ? null : c?.districtCode?.toString().padStart(3, '0'),
        wardCode: c.wardCode == '' ? null : c?.wardCode?.toString().padStart(5, '0'),
      }))
      const lstCityCode = data.mapAndDistinct((c) => c.cityCode)
      const lstDistrictCode = data.mapAndDistinct((item) => item.districtCode)
      const lstWardCode = data.mapAndDistinct((item) => item.wardCode)

      const dicCity: any = {}
      {
        const lstCity: any = await cityRepo.find({ where: { code: In(lstCityCode), isDeleted: false } })
        lstCity.forEach((c) => (dicCity[c.code] = c))
      }

      const dicDistrict: any = {}
      {
        const lstDistrict: any = await districtRepo.find({ where: { code: In(lstDistrictCode), isDeleted: false } })
        lstDistrict.forEach((c) => (dicDistrict[c.code] = c))
      }

      const dicWard: any = {}
      {
        const lstWard: any = await wardRepo.find({ where: { code: In(lstWardCode), isDeleted: false } })
        lstWard.forEach((c) => (dicWard[c.code] = c))
      }

      const dicCode: any = {}
      {
        const listBrand: any[] = await brandRepo.find({
          where: { isDeleted: false },
          select: { id: true, code: true },
        })
        listBrand.forEach((c) => (dicCode[c.code] = c))
      }

      const lsBrandNew: BrandEntity[] = []
      const dicCodeFile: any = {}
      for (const [idx, item] of data.entries()) {
        if (dicCodeFile[item.code]) throw new Error(`[ Dòng ${idx + 1} - Mã Thương Hiệu [${item.code}] trùng với dòng ${dicCodeFile[item.code]} ]`)

        let parentId = null
        if (item.parentCode) {
          if (!dicCode[item.parentCode]) throw new Error(`[ Dòng ${idx + 1} - Mã Thương Hiệu Cha [${item.parentCode}] không tồn tại ]`)
          parentId = dicCode[item.parentCode].id
        }
        let cityId = null
        let districtId = null
        let wardId = null
        cityId = dicCity[item.cityCode]
        districtId = dicDistrict[item.districtCode]
        wardId = dicWard[item.wardCode]
        if (item.cityCode) {
          const city = dicCity[item.cityCode]
          if (!city) throw new Error(`[ Dòng ${idx + 3} - Mã Tỉnh/Thành phố [${item.cityCode}] không tồn tại ]`)
          cityId = city.id
          if (item.districtCode) {
            const district = dicDistrict[item.districtCode]
            if (!district) throw new Error(`[ Dòng ${idx + 3} - Mã Quận/Huyện [${item.districtCode}] không tồn tại ]`)
            if (district.cityId != cityId) {
              throw new Error(`[ Dòng ${idx + 3} - Mã Quận/Huyện [${item.districtCode}] không tồn tại trong Tỉnh/Thành phố [${item.cityCode}] ]`)
            }
            districtId = district.id
            if (item.wardCode) {
              const ward = dicWard[item.wardCode]
              if (!ward) throw new Error(`[ Dòng ${idx + 3} - Mã Phường/Xã [${item.wardCode}] không tồn tại ]`)
              if (ward.districtId != districtId) {
                throw new Error(`[ Dòng ${idx + 3} - Mã Phường/Xã [${item.wardCode}] không tồn tại trong Quận/Huyện [${item.districtCode}] ]`)
              }
              wardId = ward.id
            }
          }
        }
        if (!dicCode[item.code]) {
          const newLocation = brandRepo.create({
            ...item,
            cityId: cityId,
            districtId: districtId,
            wardId: wardId,
            parentId: parentId,
            createdAt: new Date(),
            createdBy: user.id,
          })
          await brandRepo.insert(newLocation)

          dicCodeFile[item.code] = idx + 1

          if (item.url) {
            const newMedia = new MediaEntity()
            newMedia.url = item.url
            newMedia.productId = newLocation.id
            newMedia.table = enumData.MediaTable.Brand.code
            newMedia.createdAt = new Date()
            newMedia.createdBy = user?.id
            await this.mediaRepo.insert(newMedia)
          }
        } else {
          const newLocation = brandRepo.create({
            ...item,
            cityId: cityId,
            districtId: districtId,
            wardId: wardId,
            parentId: parentId,
            createdAt: new Date(),
            createdBy: user.id,
          })
          await brandRepo.update(dicCode[item.code].id, newLocation)

          dicCodeFile[item.code] = idx + 1
          await this.mediaRepo.delete({ productId: dicCode[item.code].id, table: enumData.MediaTable.Brand.code })

          if (item.url) {

            const newMedia = new MediaEntity()
            newMedia.url = item.url
            newMedia.productId = dicCode[item.code].id
            newMedia.table = enumData.MediaTable.Brand.code
            newMedia.createdAt = new Date()
            newMedia.createdBy = user?.id

            await this.mediaRepo.insert(newMedia)
          }
        }
      }
    })

    return { message: IMPORT_SUCCESS }
  }
}
