import { MigrationInterface, QueryRunner } from "typeorm";

export class updateEntitySupplier1747033158056 implements MigrationInterface {
    name = 'updateEntitySupplier1747033158056'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "supplier" ADD "parentId" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "parentId"`);
    }

}
