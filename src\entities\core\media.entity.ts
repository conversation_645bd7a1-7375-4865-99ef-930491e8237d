import { BaseEntity } from './base.entity'
import { Entity, Column, OneToMany } from 'typeorm'

/** Hình ảnh Item*/
@Entity('media')
export class MediaEntity extends BaseEntity {
  /** URL Media */
  @Column({ type: 'varchar', length: 250, nullable: false })
  url: string

  @Column({ type: 'uuid', nullable: true })
  productId?: string

  @Column({ type: 'varchar', length: 250, nullable: true })
  table: string
}
