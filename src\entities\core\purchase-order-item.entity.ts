import { Column, Entity } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from './base.entity'

@Entity('purchase_order_item')
export class PurchaseOrderItemEntity extends BaseEntity {
  @ApiProperty({ description: 'PO ID' })
  @Column("uuid", { nullable: false })
  purchaseOrderId: string;

  @ApiProperty({ description: 'ID Combo' })
  @Column("uuid", { nullable: true })
  comboId: string;

  @ApiProperty({ description: 'ID Vùng' })
  @Column("uuid", { nullable: true })
  regionId: string;

  @ApiProperty({ description: 'ID Nhóm hàng hóa' })
  @Column("uuid", { nullable: true })
  itemGroupId: string;

  @ApiProperty({ description: 'ID Hàng hóa' })
  @Column("uuid", { nullable: true })
  itemId: string;

  @ApiProperty({ description: 'Tên Hàng hóa' })
  @Column({ nullable: false })
  itemName: string;

  @ApiProperty({ description: 'Mã Hàng hóa' })
  @Column({ nullable: true })
  itemCode: string;

  @ApiProperty({ description: 'Số lượng của combo' })
  @Column()
  quantityCombo: number

  @ApiProperty({ description: 'Số lượng đơn vị cơ sở' })
  @Column()
  quantityBasicUnit: number

  @ApiProperty({ description: 'Quy cách đóng gói' })
  @Column()
  packingSpecification: string

  @ApiProperty({ description: 'Đơn vị tính đặt hàng' })
  @Column({ nullable: true })
  purchaseUnit?: string

  @ApiProperty({ description: 'Đơn giá' })
  @Column({
    type: 'decimal',
    precision: 20,
    scale: 2,
    default: 0,
  })
  sellPrice: number

  @ApiProperty({ description: 'VAT của combo' })
  @Column({ nullable: true })
  vat: number

  @ApiProperty({ description: "VAT của item" })
  @Column("numeric", { default: 0, nullable: true })
  itemVat: number;

  @ApiProperty({ description: 'Thành tiền' })
  @Column({
    type: 'decimal',
    precision: 20,
    scale: 2,
    default: 0,
  })
  totalAmount: number

  @ApiProperty({ description: 'Thành tiền VAT' })
  @Column({
    type: 'decimal',
    precision: 20,
    scale: 2,
    default: 0,
  })
  totalAmountVat: number

  @ApiProperty({ description: 'ID Tỉnh/TP' })
  @Column("uuid", { nullable: true })
  provinceId: string;

  @ApiProperty({ description: 'Kho trung tâm nhận hàng' })
  @Column("uuid", { nullable: true })
  warehouseId: string

  @ApiProperty({ description: 'Bưu cục id' })
  @Column("uuid", { nullable: true, default: null })
  partnerId?: string | null;

  @ApiProperty({ description: 'Nhà cung cấp' })
  @Column("uuid", { nullable: true, default: null })
  supplierId?: string | null;

  @ApiProperty({ description: 'Đơn vị vận chuyển 3PL' })
  @Column("uuid", { nullable: true, default: null })
  deliveryId?: string | null;

  @ApiProperty({ description: 'ID Nhà phân phối' })
  @Column("uuid", { nullable: true })
  distributorId: string
}

