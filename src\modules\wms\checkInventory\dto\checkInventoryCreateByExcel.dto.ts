import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator'

export class CheckInventoryCreateByExcelDto {
  @ApiProperty({ description: '<PERSON>ã phiếu' })
  @IsNotEmpty()
  @IsString()
  code: string

  @ApiProperty({ description: 'Mã kho kiểm' })
  @IsNotEmpty()
  @IsString()
  warehouseCode: string

  @ApiProperty({ description: 'Ngày tạo phiếu' })
  @IsNotEmpty()
  @IsString()
  createdAt: Date

  @ApiProperty({ description: 'G<PERSON> chú' })
  @IsOptional()
  @IsString()
  description?: string

  @ApiProperty({ description: 'Sản phẩm' })
  @IsNotEmpty()
  @IsString()
  productCode: string

  @ApiProperty({ description: 'Hạn sử dụng' })
  @IsOptional()
  expiryDate: Date

  @ApiProperty({ description: 'Số lượng kiểm' })
  @IsOptional()
  quantity: number
}
