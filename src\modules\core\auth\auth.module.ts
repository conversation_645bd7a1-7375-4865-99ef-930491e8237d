import { Module } from '@nestjs/common'
import { ConfigModule, ConfigService } from '@nestjs/config'
import { PassportModule } from '@nestjs/passport'
import { JwtModule, JwtModuleOptions } from '@nestjs/jwt'
import { TypeOrmExModule } from '../../../typeorm'
import { AuthService } from './auth.service'
import { AuthController } from './auth.controller'
import { CompanyRepository, SupplierRepository, SurveyMemberRepository, UserRepository, UserSurveyRepository } from '../../../repositories'
import { JwtStrategy } from './jwt.strategy'
import { EmailModule } from '../../survey/email/email.module'

@Module({
  imports: [
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService): Promise<JwtModuleOptions> => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: { expiresIn: configService.get<string>('JWT_EXPIRY') },
      }),
    }),
    TypeOrmExModule.forCustomRepository([CompanyRepository, SurveyMemberRepository, UserRepository, UserSurveyRepository, SupplierRepository]),
    EmailModule,
  ],
  controllers: [AuthController],
  providers: [AuthService, JwtStrategy],
  exports: [AuthService],
})
export class AuthModule {}
