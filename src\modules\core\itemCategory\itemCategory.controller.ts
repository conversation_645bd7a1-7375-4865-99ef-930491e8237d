import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { JwtAuthGuard } from '../../common/guards'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { CurrentUser } from '../../common/decorators'
import { CategoryWithTypeLstDto, ItemCategoryCreateDto, ItemCategoryUpdateDto } from './dto'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { ItemCategoryService } from './itemCategory.service'
import { ItemCategoryCreateExcelDto } from './dto/itemCategoryCreateExcel.dto'

@ApiBearerAuth()
@ApiTags('ItemCategory')
@UseGuards(JwtAuthGuard)
@Controller('item_category')
export class ItemCategoryController {
  constructor(private readonly service: ItemCategoryService) { }

  @Post('find')
  public async find(@Body() data: any) {
    return await this.service.find(data)
  }

  /** <PERSON><PERSON>y ra danh sách Category Item (cấp 2) và Group Item (cấp 3) bằng item type  */
  @Post('find-category-by-type')
  public async findCategory(@Body() data: CategoryWithTypeLstDto) {
    const { typeId } = data
    return await this.service.findCategoryByType(typeId)
  }

  @Post('pagination')
  public async pagination(@Body() data: PaginationDto) {
    return await this.service.pagination(data)
  }

  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: ItemCategoryCreateDto) {
    return await this.service.createData(user, data)
  }

  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: ItemCategoryUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @Post('update_active')
  public async updateActive(@Body() data: any, @CurrentUser() user: UserDto) {
    return await this.service.updateIsDelete(data.id, user)
  }

  @Post('find_one')
  public async findOne(@Body() data: FilterOneDto) {
    return await this.service.findOne(data)
  }

  @Post('create_data_excel')
  public async createDataExcel(@Body() data: ItemCategoryCreateExcelDto[], @CurrentUser() user: UserDto) {
    return await this.service.createDataExcel(data, user)
  }
}
