import { BaseEntity } from '../core/base.entity'
import { Entity, Column, ManyToOne, Join<PERSON>olumn, OneToMany } from 'typeorm'
import { InboundEntity } from './inbound.entity'
import { InboundDetailCostPriceEntity } from './inboundDetailCostPrice.entity'
import { InboundDetailPriceEntity } from './inboundDetailPrice.entity'
import { InboundCostAllocationEntity } from './inboundCostAllocation.entity'

/** Item trong PNK */
@Entity('inbound_detail')
export class InboundDetailEntity extends BaseEntity {
  /** Id PNK */
  @Column({ type: 'varchar', nullable: true })
  inboundId: string
  @ManyToOne(() => InboundEntity, (p) => p.details)
  @JoinColumn({ name: 'inboundId', referencedColumnName: 'id' })
  inbound: Promise<InboundEntity>

  /** Id Sản phẩm */
  @Column({ type: 'varchar', length: 36, nullable: true })
  productId: string

  /** Mã sản phẩm */
  @Column({ type: 'varchar', length: 50, nullable: true })
  productCode: string

  /** Tên sản phẩm */
  @Column({ type: 'varchar', length: 250, nullable: true })
  productName: string

  /** Id Item trong PO */
  @Column({ type: 'varchar', length: 36, nullable: true })
  poDetailId: string

  /** Đơn vị tính (Có số lượng đơn vị cơ sở) */
  @Column({ type: 'varchar', length: 36, nullable: true })
  unitId: string

  /** Mã dvt */
  @Column({ type: 'varchar', length: 50, nullable: true })
  unitCode: string

  /** Tên dvt */
  @Column({ type: 'varchar', length: 250, nullable: true })
  unitName: string

  /** Số lượng cơ sở (lấy theo đơn vị tính) */
  @Column({ nullable: true })
  baseUnit: number

  /** Mã PNK */
  @Column({ type: 'varchar', length: 250, nullable: true })
  code: string

  /** Số lượng nhập */
  @Column({ nullable: true })
  quantity: number

  /** Số lượng trước đó */
  @Column({ nullable: true })
  quantityOld: number

  /** Chênh lệch */
  @Column({ nullable: true, default: 0 })
  quantityDiff: number

  /** Số lượng nhập theo đvcs = Số lượng nhập * Số lượng cơ sở */
  @Column({ nullable: true })
  totalQuantity: number

  /** Giá nhập theo đvtt của PO */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 2, default: 0 })
  price: number

  /** Giá nhập theo vnđ */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 2, default: 0 })
  priceVND: number

  /** Giá nhập sau khi phân bổ chi phí */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 2, default: 0 })
  priceAfterCost: number

  /** Ngày sản xuất */
  @Column({ type: 'timestamptz', nullable: true })
  manufactureDate: Date

  /** Hạn sử dụng */
  @Column({ type: 'timestamptz', nullable: true })
  expiryDate: Date

  /** Ghi chú */
  @Column({ type: 'text', nullable: true })
  description: string

  /** Số lượng tồn kho gần nhất */
  @Column({ nullable: true })
  quantityInventory: number

  /** Giá mua (lấy theo PO) */
  @Column({ nullable: true, default: 0, type: 'float' })
  buyPrice: number

  /** Giá mua VND (lấy theo PO) */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 2, default: 0 })
  buyPriceVND: number

  /** Giá vốn */
  @Column({ nullable: true, default: 0, type: 'float' })
  costPrice: number

  /** Giá bán */
  @Column({ nullable: true, default: 0, type: 'float' })
  sellPrice: number

  /** Thành tiền nhập (theo đvtt của PO) = Giá nhập theo đvtt của PO * Số lượng nhập */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 0, default: 0 })
  totalPrice: number

  /** Thành tiền nhập (theo vnd) = Giá nhập theo vnd * Số lượng nhập */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 0, default: 0 })
  totalPriceVND: number

  /** Chênh lệch tổng tiền = Tổng tiền - tổng tiền lưu */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 0, default: 0 })
  totalPriceOld: number

  /** Chênh lệch tổng tiền = Tổng tiền - tổng tiền lưu */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 0, default: 0 })
  totalPriceVNDOld: number

  /** Chênh lệch tổng tiền = Tổng tiền - tổng tiền lưu */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 0, default: 0 })
  totalPriceDiff: number

  /** Chênh lệch tổng tiền = Tổng tiền - tổng tiền lưu */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 0, default: 0 })
  totalPriceVNDDiff: number

  /** Số lô */
  @Column({ type: 'text', nullable: true })
  lotNumber: string

  /** Chi phí phân bổ */
  @Column({ nullable: true, default: 0, type: 'float' })
  costAllocation: number

  /** Trạng thái (enumData.TransportType) */
  @Column({ type: 'varchar', length: 50, nullable: true })
  transportType: string

  @OneToMany(() => InboundCostAllocationEntity, (p) => p.inboundDetail)
  costAllocations: Promise<InboundCostAllocationEntity[]>

  /** Danh sách lịch sử giá vốn */
  @OneToMany(() => InboundDetailCostPriceEntity, (p) => p.inboundDetail)
  costPrices: Promise<InboundDetailCostPriceEntity[]>

  @OneToMany(() => InboundDetailPriceEntity, (p) => p.inboundDetail)
  prices: Promise<InboundDetailPriceEntity[]>
}
