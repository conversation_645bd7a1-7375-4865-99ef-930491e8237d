import { Controller, UseGuards, Post, Body, Req } from '@nestjs/common'
import { JwtAuthGuard } from '../../common/guards'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { CurrentUser } from '../../common/decorators'
import { StoreService } from './store.service'
import { StoreUpdateDto } from './dto'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { CreateStoreDto } from '../../../helpers/dto/apiCaller.dto'
import { Request as IRequest } from 'express'

@ApiBearerAuth()
@ApiTags('Store')
@UseGuards(JwtAuthGuard)
@Controller('store')
export class StoreController {
  constructor(private readonly service: StoreService) {}

  // @Post('find')
  // public async find(@Body() data: any) {
  //   return await this.service.find(data)
  // }

  // @Post('pagination')
  // public async pagination(@Body() data: PaginationDto, @Req() req: IRequest) {
  //   return await this.service.pagination(data, req)
  // }

  // @Post('create_data')
  // public async createData(@CurrentUser() user: UserDto, @Body() data: CreateStoreDto, @Req() req: IRequest) {
  //   return await this.service.createData(user, data, req)
  // }

  // @Post('update_data')
  // public async updateData(@CurrentUser() user: UserDto, @Body() data: StoreUpdateDto) {
  //   return await this.service.updateData(user, data)
  // }

  // @Post('update_active')
  // public async updateActive(@Body() data: any, @CurrentUser() user: UserDto) {
  //   return await this.service.updateIsDelete(data.id, user)
  // }

  // @Post('find_one')
  // public async findOne(@Body() data: FilterOneDto) {
  //   return await this.service.findOne(data)
  // }

  // @Post('create_data_excel')
  // public async createDataExcel(@Body() data: CreateStoreDto[], @CurrentUser() user: UserDto) {
  //   return await this.service.createDataExcel(data, user)
  // }
}
