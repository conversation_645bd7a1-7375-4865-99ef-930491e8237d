import { IsNotEmpty, IsString, IsUUID } from 'class-validator'
import { OutboundCreateDto } from './outboundCreate.dto'
import { ApiPropertyOptional } from '@nestjs/swagger'

export class OutboundUpdateDto extends OutboundCreateDto {
  @ApiPropertyOptional({ description: "ID Phiếu xuất kho" })
  @IsNotEmpty()
  @IsString()
  id: string

  @ApiPropertyOptional({ description: "ID người cập nhật" })
  @IsNotEmpty()
  @IsUUID()
  updateBy: string

  @ApiPropertyOptional({ description: 'Ng<PERSON>y cập nhật phiếu' })
  @IsNotEmpty()
  @IsString()
  updatedAt: Date
}

export class MinusEmployeeProductQuantityDto {
  /** Nhân viên */
  @IsNotEmpty()
  @IsString()
  employeeId: string

  lstProductInfo: ProductInfo[]
}

class ProductInfo {
  /** Sản phẩm */
  @IsNotEmpty()
  @IsString()
  productId: string

  /** Sản phẩm thực trong kho nhân viên */
  @IsNotEmpty()
  @IsString()
  productDetailId: string

  /** Khối lượng trừ */
  @IsNotEmpty()
  quantity: number
}
