import { BaseEntity } from '../core/base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { OutboundEntity } from './outbound.entity'

@Entity('outbound_detail')
export class OutboundDetailEntity extends BaseEntity {
  /** Phiếu xuất kho */
  @Column({ type: 'varchar', nullable: true })
  outboundId: string
  @ManyToOne(() => OutboundEntity, (p) => p.outboundDetails)
  @JoinColumn({ name: 'outboundId', referencedColumnName: 'id' })
  outbound: Promise<OutboundEntity>

  /** Sản phẩm */
  @Column({ type: 'varchar', length: 36, nullable: true })
  productId: string

  /** Mã sp */
  @Column({ type: 'varchar', length: 50, nullable: true })
  productCode: string

  /** Tên sp */
  @Column({ type: 'varchar', length: 250, nullable: true })
  productName: string

  /** <PERSON>ản phẩm combo */
  @Column({ type: 'boolean', nullable: false, default: false })
  isCombo: boolean

  /** Id của sản phẩm combo */
  @Column({ type: 'varchar', length: 36, nullable: true })
  outboundDetailComboId: string

  /** Sản phẩm thực xuất kho */
  @Column({ type: 'varchar', length: 36, nullable: true })
  productDetailId: string

  /** Đơn vị tính (Có số lượng đơn vị cơ sở) */
  @Column({ type: 'varchar', length: 36, nullable: true })
  unitId: string

  /** Mã dvt */
  @Column({ type: 'varchar', length: 50, nullable: true })
  unitCode: string

  /** Tên dvt */
  @Column({ type: 'varchar', length: 250, nullable: true })
  unitName: string

  /** Hạn sử dụng */
  @Column({ type: 'timestamptz', nullable: true })
  expiryDate: Date

  /** Ngày sản xuất */
  @Column({ type: 'timestamptz', nullable: true })
  manufactureDate: Date

  /**
   * Autofill theo hạn sử dụng
   * Nếu cùng 1 HSD nhưng có nhiều số lô → cho phép chọn số lô thuộc hạn sử dụng đó
   */
  @Column({ type: 'varchar', length: 36, nullable: true })
  lotNumber: string

  /** Tồn kho */
  @Column({ type: 'int', nullable: true, default: 0 })
  inventory: number

  /** Số lượng xuất */
  @Column({ type: 'int', nullable: true, default: 0 })
  quantity: number

  /** Số lượng thực xuất */
  @Column({ type: 'int', nullable: true, default: 0 })
  realQuantity: number
}
