import { Module } from '@nestjs/common'
import { LuckyBillApplyRegionModule } from './lucky-bill-apply-region/lucky-bill-apply-region.module'
import { LuckyBillConfigModule } from './lucky-bill-config/lucky-bill-config.module'
import { TypeOrmExModule } from '../../typeorm'
import { LuckyBillConfigRepository } from '../../repositories/lucky-bill/lucky-bill-config.repository'
import { LuckyBillApplyRegionRepository } from '../../repositories/lucky-bill/lucky-bill-apply-region.repository'

@Module({
  imports: [LuckyBillConfigModule, LuckyBillApplyRegionModule],
})
export class LuckyBillModule {}
