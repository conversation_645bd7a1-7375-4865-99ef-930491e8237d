import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import { DeliveryNoteController } from './deliveryNote.controller'
import { DeliveryNoteService } from './deliveryNote.service'
import {
  DeliveryNotePackingRefRepository,
  DeliveryNotePackingRepository,
  DeliveryNoteRepository,
  DeliveryNoteTrackingRepository,
} from '../../../repositories/core/deliveryNote.repository'
import {
  CityRepository,
  DistrictRepository,
  EmployeeRepository,
  InboundDetailRepository,
  InboundRepository,
  ItemComboRepository,
  ItemDetailRepository,
  ItemPriceRepository,
  ItemRepository,
  PurchaseOrderItemRepository,
  PurchaseOrderRepository,
  PurchaseOrderSaleOrderRepository,
  RegionCityRepository,
  SupplierRepository,
  UserRepository,
  WardRepository,
  WarehouseProductDetailRepository,
  WarehouseProductRepository,
  WarehouseRepository,
} from '../../../repositories'
import {
  DeliveryNoteChildDetailRepository,
  DeliveryNoteChildRepository,
  DeliveryNoteLastMileProductRepository,
} from '../../../repositories/core/deliveryNoteChild.repository'
import { InboundModule } from '../../wms/inbound/inbound.module'
import { OutboundModule } from '../../wms/outbound/outbound.module'
import { WarehouseModule } from '../../wms/warehouse/warehouse.module'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      RegionCityRepository,
      DeliveryNoteRepository,
      PurchaseOrderRepository,
      DeliveryNoteChildRepository,
      DeliveryNoteChildDetailRepository,
      PurchaseOrderItemRepository,
      DeliveryNoteTrackingRepository,
      SupplierRepository,
      PurchaseOrderSaleOrderRepository,
      DeliveryNoteLastMileProductRepository,
      WarehouseRepository,
      InboundRepository,
      InboundDetailRepository,
      ItemRepository,
      ItemPriceRepository,
      ItemDetailRepository,
      WarehouseProductRepository,
      WarehouseProductDetailRepository,
      DeliveryNotePackingRepository,
      DeliveryNotePackingRefRepository,
      UserRepository,
      EmployeeRepository,
      CityRepository,
      WardRepository,
      DistrictRepository,
      ItemComboRepository,
    ]),
    InboundModule,
    OutboundModule,
    WarehouseModule,
  ],
  controllers: [DeliveryNoteController],
  providers: [DeliveryNoteService],
  exports: [DeliveryNoteService],
})
export class DeliveryNoteModule {}
