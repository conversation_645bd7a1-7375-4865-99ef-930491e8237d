import { MigrationInterface, QueryRunner } from "typeorm";

export class updateContact1742373949181 implements MigrationInterface {
    name = 'updateContact1742373949181'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "contact" ADD "isRead" boolean NOT NULL DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "contact" DROP COLUMN "isRead"`);
    }

}
