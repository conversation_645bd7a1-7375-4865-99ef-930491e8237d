import { Injectable } from '@nestjs/common';
import { CreateBusinessOrderDto } from './dto/create-business-order.dto';
import { UpdateBusinessOrderDto } from './dto/update-business-order.dto';

@Injectable()
export class BusinessOrderService {
  create(createBusinessOrderDto: CreateBusinessOrderDto) {
    return 'This action adds a new businessOrder';
  }

  findAll() {
    return `This action returns all businessOrder`;
  }

  findOne(id: number) {
    return `This action returns a #${id} businessOrder`;
  }

  update(id: number, updateBusinessOrderDto: UpdateBusinessOrderDto) {
    return `This action updates a #${id} businessOrder`;
  }

  remove(id: number) {
    return `This action removes a #${id} businessOrder`;
  }
}
