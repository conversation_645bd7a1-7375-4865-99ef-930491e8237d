import { Column, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { ItemEntity } from './item.entity'

/** <PERSON>h sách sản phẩm của sản phẩm combo */
@Entity('item_combo')
export class ItemComboEntity extends BaseEntity {
  @Column({ type: 'varchar', nullable: false })
  itemId: string
  @ManyToOne(() => ItemEntity, (p) => p.itemCombo)
  @JoinColumn({ name: 'itemId', referencedColumnName: 'id' })
  item: Promise<ItemEntity>

  @Column({ type: 'varchar', nullable: false })
  itemInComboId: string
  @ManyToOne(() => ItemEntity, (p) => p.itemCombination)
  @JoinColumn({ name: 'itemInComboId', referencedColumnName: 'id' })
  itemInCombo: Promise<ItemEntity>

  /** <PERSON><PERSON> lượng sản phẩm trong combo */
  @Column({ nullable: false })
  quantity: number
}
