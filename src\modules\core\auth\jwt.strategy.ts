import { ExtractJwt, Strategy } from 'passport-jwt'
import { PassportStrategy } from '@nestjs/passport'
import { Injectable, NotFoundException, UnauthorizedException } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { UserDto } from '../../../dto'
import { SupplierRepository, UserRepository } from '../../../repositories'
import { enumData } from 'src/constants'
import { UpdateEmailAdminto } from './dto'

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(private readonly repo: UserRepository, public readonly configService: ConfigService, public readonly supplierRepo: SupplierRepository) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('JWT_SECRET'),
    })
  }

  async validate(payload: { uid: string }): Promise<UserDto> {
    const user = await this.repo.findOne({
      where: { id: payload.uid },
      select: { username: true, id: true, type: true, createdAt: true, updatedAt: true, supplierId: true },
    })

    const supplier = await this.supplierRepo.findOne({
      where: { id: payload.uid },
      select: { username: true, id: true, createdAt: true, updatedAt: true, is3PL: true, isSupplier: true, isDistributor: true },
    })

    if (!user && !supplier) throw new UnauthorizedException('Không tìm thấy user! Không có quyền truy cập!')

    if (user) {
      return {
        id: user.id,
        username: user.username,
        isAdmin: user.type == enumData.UserType.Admin.code,
        type: user.type,
        companyId: user.companyId,
        userId: user.id,
        employeeId: user.id,
        supplierId: user.supplierId,
      }
    } else {
      return {
        id: supplier.id,
        username: supplier.username,
        isAdmin: false,
        is3PL: supplier.is3PL,
        isSupplier: supplier.isSupplier,
        isDistributor: supplier.isDistributor,
      }
    }
  }

  /** Admin đổi email user Survey */
  public async updateEmailSurvey_Admin(user: UserDto, data: UpdateEmailAdminto) {
    const userCurrent = await this.repo.findOne({ where: { id: user.id }, select: { id: true, companyId: true } })
    if (!userCurrent?.companyId) throw new NotFoundException('Tài khoản đang thao tác không xác định được Công ty.')

    const userEntity = await this.repo.findOne({
      where: { id: data.userId, companyId: userCurrent.companyId },
      select: { id: true },
    })
    if (!userEntity) throw new NotFoundException('Tài khoản không tồn tại.')

    await this.repo.update(userEntity.id, { email: data.newEmail, updatedAt: new Date(), updatedBy: user.id })

    return { message: 'Đổi email thành công.' }
  }
}
