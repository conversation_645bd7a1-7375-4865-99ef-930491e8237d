import { ApiProperty } from '@nestjs/swagger'
import { ArrayNotEmpty, IsArray, IsNotEmpty, IsNumber, IsOptional, IsUUID } from 'class-validator'
import { Transform } from 'class-transformer'

export class CreateLuckyBillApplyRegionDto {
  @ApiProperty({ description: 'Id cấu hình áp dụng' })
  @IsOptional()
  @IsUUID()
  id?: string

  @ApiProperty({ description: 'Id cấu hình Lucky Bill' })
  @IsNotEmpty()
  luckyBillConfigId: string

  @ApiProperty({ description: 'Id thành phố' })
  @IsNotEmpty({ each: true })
  @IsUUID('4', { each: true })
  @IsArray()
  @ArrayNotEmpty()
  cityIds: string[]

  @ApiProperty({ description: 'Giới hạn' })
  @IsNotEmpty()
  @IsNumber()
  @Transform(({ value }) => Number(value))
  limit: number
}
