import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString } from 'class-validator'
import { NSWarehouse } from '../../../../constants'

export class WarehouseCreateDto {
  @IsNotEmpty()
  @IsString()
  name: string

  @IsNotEmpty()
  @IsString()
  code: string

  @IsOptional()
  description?: string

  @IsOptional()
  cityId?: string

  @IsOptional()
  districtId?: string

  @IsOptional()
  wardId?: string

  @IsOptional()
  address?: string

  @IsOptional()
  status?: string

  @IsOptional()
  isDefault?: boolean

  @IsOptional()
  cbm?: number

  @IsOptional()
  type?: NSWarehouse.EWarehouseType = NSWarehouse.EWarehouseType.MBC // Default là MBC

  @ApiProperty({ description: 'dài' })
  @IsOptional()
  length?: number

  @ApiProperty({ description: 'rộng' })
  @IsOptional()
  width?: number

  @ApiProperty({ description: 'cao' })
  @IsOptional()
  height?: number

  @ApiProperty({ description: 'Id store' })
  @IsOptional()
  storeId?: string

  @IsOptional()
  configProvince?: string[]

  @IsOptional()
  configDistrict?: string[]

  /**
   * Param form OMS
   */
  @IsOptional()
  cityCode?: string

  @IsOptional()
  districtCode?: string

  @IsOptional()
  wardCode?: string
}
