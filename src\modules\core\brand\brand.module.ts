import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import { BrandRepository, MediaRepository } from '../../../repositories'
import { BrandController } from './brand.controller'
import { BrandService } from './brand.service'
import { BrandPublicController } from './brandPublic.controller'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([BrandRepository, MediaRepository])],
  controllers: [BrandController, BrandPublicController],
  providers: [BrandService],
  exports: [BrandService],
})
export class BrandModule { }
