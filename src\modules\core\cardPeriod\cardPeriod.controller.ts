import { Controller, UseGuards, Post, Body, Req, Query, Get } from '@nestjs/common'
import { JwtAuthGuard } from '../../common/guards'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { Request as IRequest } from 'express'
import { omsApiHelper } from '../../../helpers/omsApiHelper'
import { DeclareCardDuration, DeclareCardPeriod } from '../../../helpers/dto/apiCaller.dto'

@ApiBearerAuth()
@ApiTags('CardPeriod')
@UseGuards(JwtAuthGuard)
@Controller('card_period')
export class CardPeriodController {
  constructor() {}

  @Get('list')
  public async pagination(@Query() data: { pageSize: any; pageIndex: any }, @Req() req: IRequest) {
    return await omsApiHelper.getCardPeriod(req, data)
  }

  @Post('import_card_period')
  public async importCardPeriodExcel(@Body() data: DeclareCardPeriod[], @Req() req: IRequest) {
    return await omsApiHelper.importCardPeriodExcel(req, data)
  }
}
