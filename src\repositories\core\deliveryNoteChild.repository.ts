import { DeliveryNoteChildEntity, DeliveryNoteChildDetailEntity, DeliveryNoteLastMileProductEntity } from '../../entities'
import { CustomRepository } from '../../typeorm'
import { BaseRepository } from '../base.repo'

@CustomRepository(DeliveryNoteChildEntity)
export class DeliveryNoteChildRepository extends BaseRepository<DeliveryNoteChildEntity> { }

@CustomRepository(DeliveryNoteChildDetailEntity)
export class DeliveryNoteChildDetailRepository extends BaseRepository<DeliveryNoteChildDetailEntity> { }

@CustomRepository(DeliveryNoteLastMileProductEntity)
export class DeliveryNoteLastMileProductRepository extends BaseRepository<DeliveryNoteLastMileProductEntity> { }