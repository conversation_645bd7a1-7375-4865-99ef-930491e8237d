import { IsNotEmpty, IsString } from 'class-validator'

export class MediaCreateDto {
  @IsNotEmpty()
  @IsString()
  id: string

  @IsNotEmpty()
  @IsString()
  type: string

  @IsNotEmpty()
  @IsString()
  link: string

  @IsNotEmpty()
  @IsString()
  url: string

  @IsNotEmpty()
  banner: any[]

  @IsNotEmpty()
  @IsString()
  content: string

  @IsNotEmpty()
  @IsString()
  term: string

  @IsNotEmpty()
  @IsString()
  typeName: string

  @IsNotEmpty()
  @IsString()
  img: string[]

  @IsNotEmpty()
  personalDeposit: number

  @IsNotEmpty()
  corporateDeposit: number
}
