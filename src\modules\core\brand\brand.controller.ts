import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { BrandService } from './brand.service'
import { JwtAuthGuard } from '../../common/guards'
import { BrandCreateDto } from './dto/brandCreate.dto'
import { BrandUpdateDto } from './dto/brandUpdate.dto'
import { BrandCreateExcelDto, BrandUpdateIsActiveDto } from './dto'
import { CurrentUser } from '../../common/decorators'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'

@ApiBearerAuth()
@ApiTags('Brand')
@UseGuards(JwtAuthGuard)
@Controller('brand')
export class BrandController {
  constructor(private readonly service: BrandService) {}

  @Post('find_by_permission')
  public async findByPermission(@CurrentUser() user: UserDto, @Body() data: any) {
    return await this.service.findByPermission(user)
  }

  @Post('find')
  public async find(@Body() data: any) {
    return await this.service.find(data)
  }

  @Post('pagination')
  public async pagination(@Body() data: PaginationDto) {
    return await this.service.pagination(data)
  }

  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: BrandCreateDto) {
    return await this.service.createData(user, data)
  }

  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: BrandUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @Post('update_active')
  public async updateActive(@Body() data: BrandUpdateIsActiveDto, @CurrentUser() user: UserDto) {
    return await this.service.updateIsDelete(data.id, user)
  }

  @Post('details')
  public async getListParent(@Body() data: any) {
    return await this.service.getListParent(data)
  }

  @Post('find_brand_level_1')
  public async findLevel() {
    return await this.service.findLevel()
  }

  @Post('find_one')
  public async findOne(@Body() data: FilterOneDto) {
    return await this.service.findOne(data)
  }

  @Post('create_data_excel')
  public async createDataExcel(@CurrentUser() user: UserDto, @Body() data: BrandCreateExcelDto[]) {
    return await this.service.createDataExcel(user, data)
  }
}
