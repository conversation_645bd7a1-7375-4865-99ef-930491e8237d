import { Injectable } from "@nestjs/common";
import { CREATE_SUCCESS, DELETE_SUCCESS, NSBank, UPDATE_SUCCESS } from "../../../constants";
import { BankRepository, BankBranchRepository } from "../../../repositories";
import { CreateBankDto, UpdateBankDto, BankBranchLstDto } from "./dto/bank.dto";
import { ILike, In } from 'typeorm';
import * as XLSX from 'xlsx';
import { BankBrandEntity, BankEntity } from "../../../entities";
import { PageRequest } from "../../../dto";

@Injectable()
export class BankService {
  constructor(
    private readonly bankRepo: BankRepository,
    private readonly bankBranchRepo: BankBranchRepository
  ) { }

  // Danh sách dành cho select trên app
  async getBankList() {
    const result = await this.bankRepo.find({ where: { isDeleted: false } })
    const mappingResult = result.map((val) => ({ ...val, searchString: `${val.name} - ${val.shortName} (${val.bankCode})` }));
    return mappingResult;
  }

  // Dành cho Admin
  async getBankPagination(params: any) {
    const whereCon: any = {}
    if (params.name) whereCon.name = ILike(`%${params.name}%`)
    if (params.code) whereCon.code = ILike(`%${params.code}%`)
    const { data, total } = await this.bankRepo.findPagination({ where: whereCon }, params)

    const mappingResult = data.map((val) => ({ ...val, searchString: `${val.name} - ${val.shortName} (${val.bankCode})` }));
    return { data: mappingResult, total };
  }

  async getBranchByBankCode(BankBranchLstDto: BankBranchLstDto) {
    const { code, isDeleted } = BankBranchLstDto
    if (!code) {
      throw new Error(`Param code must be not null`);
    }

    const whereCon: any = {}
    if (code) whereCon.bankCode = code
    if (isDeleted) whereCon.isDeleted = isDeleted

    const result = await this.bankBranchRepo.find({
      where: whereCon
    });
    if (!result) {
      throw new Error(`Bank with code ${code} not found`);
    }
    return result;
  }

  async createBank(data: CreateBankDto) {
    try {
      const { branches, ...bankData } = data;
      const whereCon: any = {}
      if (bankData?.name) whereCon.name = bankData.name;
      if (bankData?.code) whereCon.code = bankData.code;
      if (bankData?.bankCode) whereCon.bankCode = ILike(`%${bankData.bankCode}%`);

      const existingBank = await this.bankRepo.findOne({
        where: whereCon,
      });

      if (existingBank) {
        throw new Error(
          `Bank name (${bankData.name}) or code (${bankData.code}) already exists.`
        );
      }
      const bank = await this.bankRepo.save(bankData);

      if (branches && branches.length > 0) {
        const branchEntities = branches.map(branch => {
          const branchEntity = this.bankBranchRepo.create(branch);
          branchEntity.bankCode = bank.code;
          return branchEntity;
        });
        await this.bankBranchRepo.save(branchEntities);
      }

      return { messaging: CREATE_SUCCESS }
    } catch (error) {
      throw new Error(error)
    }
  }

  async update(updateBankDto: UpdateBankDto) {
    try {
      const { bankId, branches, ...data } = updateBankDto;
      const check = await this.bankRepo.findOneBy({ id: bankId });
      if (!check) {
        throw new Error(`Bank ID ${bankId} is not found`);
      }
      await this.bankRepo.update({ id: bankId }, data);
      if (branches) {
        for (let index = 0; index < branches.length; index++) {
          const branch = branches[index];
          if (branch.id) {
            await this.bankBranchRepo.update(branch.id, branch);
          } else {
            const newBranch = this.bankBranchRepo.create(branch);
            newBranch.bankCode = check.code;
            await this.bankBranchRepo.save(newBranch);
          }
        }

      }

      return { message: UPDATE_SUCCESS };
    } catch (error) {
      throw new Error(error)
    }
  }

  // Soft Delete Bank
  async delete(id: string) {
    const check = await this.bankRepo.findOne({ where: { id } });
    if (!check) {
      throw new Error(`Bank ID ${id} is not found`);
    }
    await this.bankRepo.update(id, { isDeleted: true });
    return { messaging: DELETE_SUCCESS }
  }

  // Soft Delete chi nhánh của Bank
  async deleteBranch(id: string) {
    const check = await this.bankBranchRepo.findOneBy({ id });
    if (!check) {
      throw new Error(`Bank Branch ID ${id} is not found`);
    }
    await this.bankBranchRepo.update(id, { isDeleted: true });
    return { messaging: DELETE_SUCCESS }
  }

  async importData(file: Express.Multer.File): Promise<any> {
    try {
      return await this.bankRepo.manager.transaction(async (manager) => {
        const workbook = XLSX.read(file.buffer, { type: 'buffer' });
        const sheet = workbook.Sheets[workbook.SheetNames[0]];
        const data = XLSX.utils.sheet_to_json(sheet);

        if (data.length == 0) {
          throw new Error(`Không tìm thấy data import`);
        }

        for (let i = 0; i < data.length; i += 500) {
          const batch = data.slice(i, i + 500);
          const bankCodes = [...new Set(batch.map((row) => String(row['Bank_Code'])))];
          const branchCodes = batch.map((row) => row['Branch_Code']);

          // Lấy các Bank đã tồn tại trong database
          const existingBanks = await manager
            .getRepository(BankEntity)
            .find({ where: { code: In(bankCodes) } });
          const existingBankCodes = new Set(existingBanks.map((b) => b.code));

          // Lấy các Branch đã tồn tại trong database
          const existingBranches = await manager
            .getRepository(BankBrandEntity)
            .find({ where: { code: In(branchCodes) } });

          const existingBranchCodes = new Set(existingBranches.map((b) => b.code));

          const newBanks = bankCodes
            .filter((code) => !existingBankCodes.has(code))
            .map((code) => {
              const row = batch.find((r) => r['Bank_Code'] === code);
              if (row) {
                const name = row['Bank'].split('-')[0].trim();
                const _bankCode = row['Bank'].match(/\(([^)]+)\)/);

                return manager.getRepository(BankEntity).create({
                  name: name,
                  logo: row['Logo'],
                  code: row['Bank_Code'],
                  bankCode: _bankCode ? _bankCode[1].replace(/\s+/g, '') : "code",
                  shortName: _bankCode ? _bankCode[1] : "",
                });
              }
            });
          if (newBanks.length > 0) {
            await manager.getRepository(BankEntity).insert(newBanks);
          }

          // Chuẩn bị dữ liệu để insert Branch
          const newBranches = batch
            .filter((row) => !existingBranchCodes.has(row['Branch_Code']))
            .map((row) => {
              const name = row['Branch'].split('-')[0].trim();
              return manager.getRepository(BankBrandEntity).create({
                name: name,
                code: row['Branch_Code'],
                bankCode: row['Bank_Code'],
              });
            });

          // Insert Branch mới
          if (newBranches.length > 0) {
            await manager.getRepository(BankBrandEntity).insert(newBranches);
          }
        }

        return { message: 'Import completed successfully' };
      });
    } catch (error) {
      throw new Error(error);
    }

  }
}