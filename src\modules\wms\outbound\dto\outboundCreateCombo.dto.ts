import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString } from 'class-validator'
import { OutboundDetailCreateDto } from './outboundCreate.dto'

export class OutboundCreateComboDto {
  @ApiProperty({ description: 'Loại phiếu' })
  @IsNotEmpty()
  @IsString()
  type: string

  @ApiProperty({ description: 'Kho vật lý' })
  @IsNotEmpty()
  @IsString()
  warehouseId: string

  @ApiProperty({ description: 'ID người tạo' })
  @IsNotEmpty()
  @IsString()
  createBy: string

  @ApiProperty({ description: 'Id nhân viên (<PERSON><PERSON> trừ tồn kho của nhân viên)' })
  @IsString()
  employeeId?: string

  @ApiProperty({ description: 'Id phiếu chuyển kho' })
  @IsOptional()
  @IsString()
  warehouseTransferId?: string

  @ApiProperty({ description: 'Id đơn hàng' })
  @IsOptional()
  @IsString()
  orderId?: string

  @ApiProperty({ description: 'Mã đơn hàng' })
  @IsOptional()
  @IsString()
  orderCode?: string

  @ApiProperty({ description: 'Tên khách hàng của đơn hàng' })
  @IsOptional()
  @IsString()
  customerName?: string

  @ApiProperty({ description: 'Ngày tạo phiếu' })
  @IsNotEmpty()
  @IsString()
  createdAt: Date

  @ApiProperty({ description: 'Ngày soạn hàng' })
  @IsOptional()
  @IsString()
  preparationDate?: Date

  @ApiProperty({ description: 'Người soạn hàng' })
  @IsOptional()
  @IsString()
  preparedBy?: string

  @ApiProperty({ description: 'Ghi chú' })
  @IsOptional()
  @IsString()
  description?: string

  @ApiProperty({ description: 'Số lượng kiện hàng' })
  @IsOptional()
  packageQuantity?: number

  @ApiProperty({ description: 'Danh sách sản phẩm combo trong kho vật lý' })
  lstOutboundComboDetail: OutboundDetailComboCreateDto[]
}

export class OutboundDetailComboCreateDto {
  @ApiProperty({ description: 'Sản phẩm' })
  @IsNotEmpty()
  @IsString()
  productId: string

  @ApiProperty({ description: 'Số lượng xuất' })
  @IsOptional()
  quantity: number

  @ApiProperty({ description: 'Giá vốn' })
  @IsOptional()
  costPrice?: number

  @ApiProperty({ description: 'Hạn sử dụng' })
  @IsOptional()
  expiryDate: Date

  @ApiProperty({ description: 'Ngày sản xuất' })
  @IsOptional()
  manufactureDate: Date

  @ApiProperty({ description: 'Danh sách sản phẩm trong kho vật lý' })
  lstOutboundDetail: OutboundDetailCreateDto[]
}
