import { ApiProperty } from '@nestjs/swagger'
import { IsBoolean, IsNotEmpty, IsString } from 'class-validator'

/** Interface Active/Inactive user Survey */
export class UpdateStatusUserSurveyDto {
  @ApiProperty({ description: 'ID đối tác' })
  @IsString()
  @IsNotEmpty()
  companyId: string

  @ApiProperty({ description: 'Id Tài khoản' })
  @IsString()
  @IsNotEmpty()
  userId: string

  @ApiProperty({ description: 'Trạng thái hoạt động' })
  @IsBoolean()
  @IsNotEmpty()
  isDeleted: boolean
}
