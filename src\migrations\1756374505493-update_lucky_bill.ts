import { MigrationInterface, QueryRunner } from 'typeorm'

export class updateLuckyBill1756374505493 implements MigrationInterface {
  name = 'updateLuckyBill1756374505493'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "lucky_bill_apply_region" DROP COLUMN "regionIds"`)
    await queryRunner.query(`ALTER TABLE "lucky_bill_apply_region" DROP COLUMN "districtIds"`)
    await queryRunner.query(`ALTER TABLE "lucky_bill_apply_region" ADD "regionId" uuid NOT NULL`)
    await queryRunner.query(`ALTER TABLE "lucky_bill_apply_region" ADD "districtId" uuid NOT NULL`)
    await queryRunner.query(
      `ALTER TABLE "lucky_bill_apply_region" ADD CONSTRAINT "FK_27abcdb50f010b37c7e7303afe0" FOREIGN KEY ("regionId") REFERENCES "region"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "lucky_bill_apply_region" ADD CONSTRAINT "FK_1437a94b4963f3c87909271713e" FOREIGN KEY ("districtId") REFERENCES "district"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "lucky_bill_apply_region" DROP CONSTRAINT "FK_1437a94b4963f3c87909271713e"`)
    await queryRunner.query(`ALTER TABLE "lucky_bill_apply_region" DROP CONSTRAINT "FK_27abcdb50f010b37c7e7303afe0"`)
    await queryRunner.query(`ALTER TABLE "lucky_bill_apply_region" DROP COLUMN "districtId"`)
    await queryRunner.query(`ALTER TABLE "lucky_bill_apply_region" DROP COLUMN "regionId"`)
    await queryRunner.query(`ALTER TABLE "lucky_bill_apply_region" ADD "districtIds" text array NOT NULL`)
    await queryRunner.query(`ALTER TABLE "lucky_bill_apply_region" ADD "regionIds" text array NOT NULL`)
  }
}
