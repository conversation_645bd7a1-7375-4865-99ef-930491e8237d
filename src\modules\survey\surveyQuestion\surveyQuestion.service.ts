import { Injectable } from '@nestjs/common'
import { customAlphabet } from 'nanoid'
import { SurveyCreateDto } from './dto/surveyQuestionCreate.dto'
import { Between, In, IsNull, Not } from 'typeorm'
import {
  QuestionRepository,
  SurveyMemberRepository,
  SurveyQuestionListDetailRepository,
  SurveyQuestionRepository,
  SurveyRepository,
  TopicRepository,
  UserSurveyRepository,
} from '../../../repositories/survey'
import { PaginationDto, UserDto } from '../../../dto'
import { CREATE_SUCCESS, DELETE_SUCCESS, enumData, ERROR_NOT_FOUND_DATA } from '../../../constants'
import { QuestionEntity, SurveyEntity, SurveyQuestionEntity } from '../../../entities/survey'
const nanoid = customAlphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890', 10)
import { Request as IRequest } from 'express'

@Injectable()
export class SurveyQuestionService {
  constructor(
    private repo: SurveyQuestionRepository,
    private surveyRepo: SurveyRepository,
    private surveyMemberRepo: SurveyMemberRepository,
    private surveyQuestionRepo: SurveyQuestionRepository,
    private questionRepo: QuestionRepository,
    private surveyQuestionListDetailRepo: SurveyQuestionListDetailRepository,
    private topicRepo: TopicRepository,
    private userSurveyRepo: UserSurveyRepository,
  ) {}

  public async getSurveyStatsByMonth(from: any, to: any) {
    const whereCon: any = {}
    whereCon.surveyMemberId = Not(IsNull())
    whereCon.createdAt = Between(from, to)
    const queryBuilder = this.repo
      .createQueryBuilder('surveyQuestion')
      .select(['DATE(surveyQuestion.createdAt) as date', 'COUNT(DISTINCT surveyQuestion.surveyMemberId) as count'])
      .where(whereCon)
      .groupBy('DATE(surveyQuestion.createdAt)')
    const results = await queryBuilder.getRawMany()
    return results.map((row) => ({
      date: row.date,
      count: parseInt(row.count),
    }))
  }

  // Hàm lấy danh sách phiếu trả lời khảo sát
  public async pagination(user: UserDto, data: PaginationDto, req: IRequest) {
    const whereCon: any = {}

    if (data.where.isDeleted !== undefined) whereCon.isDeleted = data.where.isDeleted
    if (data.where.surveyId) whereCon.surveyId = data.where.surveyId
    if (data.where.surveyMemberId) whereCon.surveyMemberId = data.where.surveyMemberId
    else whereCon.surveyMemberId = Not(IsNull())

    if (data.where.createdAt) {
      if (typeof data.where.createdAt === 'string') {
        whereCon.createdAt = new Date(data.where.createdAt)
      } else {
        whereCon.createdAt = data.where.createdAt
      }
    }

    const queryBuilder = this.repo
      .createQueryBuilder('surveyQuestion')
      .select(['surveyQuestion.surveyId as surveyId', 'surveyQuestion.surveyMemberId as surveyMemberId', 'surveyQuestion.createdAt as createdAt'])
      .where(whereCon)
      .groupBy('surveyQuestion.surveyId')
      .addGroupBy('surveyQuestion.surveyMemberId')
      .addGroupBy('surveyQuestion.createdAt')

    const rs = await queryBuilder.getRawMany()
    const results = rs.slice(data.skip, data.skip + data.take)
    // Đếm số lượng phiếu khảo sát
    let total = rs.length
    let lstSurveyId = results.map((x) => x.surveyid)
    let lstSurvey = await this.surveyRepo.find({
      where: { id: In(lstSurveyId), questions: { value: IsNull() } },
      relations: { questions: true },
      order: { createdAt: 'DESC' },
    })
    const mapSurvey = lstSurvey.convertToMap((x) => x.id)
    let lstSurveyMemberId = results.mapAndDistinct((x) => x.surveymemberid)
    let lstSurveyMember = await this.surveyMemberRepo.find({ where: { id: In(lstSurveyMemberId) } })
    const lstUser = await this.userSurveyRepo.find({ where: { id: In(lstSurveyMember.map((x) => x.userId)) } })
    lstSurveyMember.forEach((x: any) => {
      const user = lstUser.find((u) => u.id === x.userId)
      x.username = user?.username || ''
      x.type = user.type
    })
    let mapSurveyMember = lstSurveyMember.convertToMap((x) => x.id)
    let lstSurveyQuestion = await Promise.all(lstSurvey.map(async (x) => await x.questions))
    let lstQuestionIdTemp = lstSurveyQuestion.map((x) => x.map((y) => y.questionId))
    let lstQuestionId = lstQuestionIdTemp.flat()
    let lstQuestion: any = await this.surveyQuestionListDetailRepo.find({
      where: { id: In(lstQuestionId) },
      relations: { childs: true },
    })
    for (let question of lstQuestion) {
      if (question.questionListDetail) {
        question.__questionlistDetails__ = JSON.parse(question.questionListDetail)
        question.__questionlistDetails__ = question.__questionlistDetails__.sort((a, b) => a.sort - b.sort)
      }
      const lstChilds = await question.childs
      for (let child of lstChilds) {
        if (child.questionListDetail) {
          child.__questionlistDetails__ = JSON.parse(child.questionListDetail)
          child.__questionlistDetails__ = child.__questionlistDetails__.sort((a, b) => a.sort - b.sort)
        }
      }
    }
    let mapQuestion = lstQuestion.convertToMap((x) => x.id)
    // Lặp qua các phiếu khảo sát
    for (let answer of results as any) {
      answer.createdAt = answer.createdat
      delete answer.createdat
      answer.surveyMemberId = answer.surveymemberid
      delete answer.surveymemberid
      answer.surveyId = answer.surveyid
      delete answer.surveyid

      // Lấy thông tin survey
      let survey: any = mapSurvey.get(answer.surveyId)
      answer.survey = { ...survey }
      delete answer.survey.__questions__
      let question = await survey.questions
      answer.survey.listQuestion = JSON.parse(JSON.stringify(question))
      answer.survey.listQuestion.forEach((x) => (x.value = null))
      answer.surveyMember = mapSurveyMember.get(answer.surveyMemberId)
      let firstElement = true
      let lstChildResult = []
      //Lặp qua các câu hỏi có trong phiếu khảo sát
      for (let q of answer.survey.listQuestion) {
        // Lấy thông tin câu hỏi cấp 1
        if (mapQuestion.get(q.questionId) == null) continue
        q.question = JSON.parse(JSON.stringify(mapQuestion.get(q.questionId)))
        if (q.question == null) continue
        let question = q.question
        // Lấy thông tin câu hỏi con
        let childsData = q.question.__childs__
        let childs = JSON.parse(JSON.stringify(childsData))
        q.question.lstChild = childs.length > 0 ? childs.sort((a, b) => a.sort - b.sort) : []

        // Nếu có câu hỏi con
        if (childs.length > 0) {
          let lstQuestionChildId = childs.map((c) => c.id)
          lstChildResult = [...lstChildResult, ...lstQuestionChildId]
          let lstResult = await this.surveyQuestionRepo.find({
            where: {
              surveyMemberId: answer.surveyMemberId,
              questionId: In(lstQuestionChildId),
              surveyId: answer.surveyId,
              createdAt: answer.createdAt,
            },
            select: { value: true, questionId: true },
          })

          let mapResult = lstResult.convertToMap((x) => x.questionId)
          // Lặp qua từng câu hỏi con
          for (let c of q.question?.lstChild as any) {
            // Lấy ra value của câu hỏi con
            let result = lstResult.filter((x) => x.questionId == c.id)
            if (result.length == 0) continue
            let list: any = []
            if (['Checkbox', 'List'].includes(c.type)) {
              // Lấy thông tin câu hỏi con
              let questionC = await this.surveyQuestionListDetailRepo.findOne({
                where: { id: c.id },
              })

              // Lấy thông tin các questionListDetail của câu hỏi con
              list = JSON.parse(questionC.questionListDetail)
            }
            // Trường hợp câu hỏi con là checkbox
            if (c.type == 'Checkbox') {
              for (let rs of result) {
                for (let value of rs.value) {
                  const questionDetail = list.find((detail) => detail.value.toString() === value.toString())
                  if (c.value === undefined) c.value = []
                  if (questionDetail) {
                    c.__questionlistDetails__.find((x) => x.id === questionDetail.id).checked = true
                    c.value.push(questionDetail.name)
                  } else {
                    c.value.push('Khác: ' + value)
                    c.other = value
                  }
                }
              }
            } else {
              if (['String', 'Number'].includes(c.type)) {
                c.value = mapResult.get(c.id).value[0]
              }
              if (c.type == 'List') {
                const filteredDetails = list.find((detail) => detail.value.toString() === mapResult.get(c.id).value)
                if (filteredDetails) list.find((detail) => detail.value.toString() === mapResult.get(c.id).value).checked = true
                c.value = filteredDetails?.name || ''
              }
            }
          }
        } else if (q.question?.lstChild.length == 0) {
          let list = question.__questionlistDetails__
          q.type = question.type
          if (question.type == 'Checkbox') {
            let result = await this.surveyQuestionRepo.findOne({
              where: { surveyMemberId: answer.surveyMemberId, questionId: q.question.id, createdAt: answer.createdAt },
              select: { value: true, questionId: true },
            })
            q.value = []
            if (result && result !== null) {
              for (let rs of result.value) {
                const questionDetail = list.find((detail) => detail.value.toString() === rs.toString())
                if (q.value === undefined) q.value = []
                if (questionDetail) {
                  list.find((x) => x.id === questionDetail.id).checked = true
                  q.value.push(questionDetail.name)
                } else {
                  q.value.push('Khác: ' + rs)
                  q.other = rs
                }
              }
            }
          } else {
            let result = await this.surveyQuestionRepo.findOne({
              where: { surveyMemberId: answer.surveyMemberId, questionId: q.question.id, createdAt: answer.createdAt },
              select: { value: true, questionId: true },
            })
            const filteredDetails = list.find((detail) => detail.value.toString() === result?.value[0])
            if (result && ['String', 'Number'].includes(question.type)) {
              q.value = result.value[0]
            }
            if (result && question.type == 'List') {
              if (filteredDetails) list.find((detail) => detail.value.toString() === result?.value[0]).checked = true
              q.value = filteredDetails?.name
            }
          }
        }
        if (firstElement) {
          answer.topic = await this.topicRepo.findOne({ where: { id: q.topicId } })
        }
        firstElement = false
        q.surveyMemberId = answer.surveyMemberId.id
      }
      if (answer.survey.listQuestion) answer.survey.listQuestion = answer.survey.listQuestion.filter((x) => !lstChildResult.includes(x.questionId))
      answer.survey.listQuestion = answer.survey.listQuestion.sort((a, b) => {
        if (!a?.question?.sort) return 1 // Nếu `a.question.sort` không tồn tại, đẩy xuống cuối
        if (!b?.question?.sort) return -1
        return a.question.sort - b.question.sort
      })
    }
    return [results, total]
  }

  public async answerOverviewPagination(data: PaginationDto) {
    const whereCon: any = {}
    if (data.where.surveyId) whereCon.surveyId = data.where.surveyId
    if (data.where.isDeleted !== undefined) whereCon.isDeleted = data.where.isDeleted
    whereCon.surveyMemberId = Not(IsNull())
    const res = await this.repo.find({ where: whereCon })
    // Dùng Map để loại bỏ trùng lặp dựa trên surveyMemberId + createdAt + surveyId
    const uniqueAnswers = new Map<
      string,
      { surveyMemberId: number; createdAt: string; surveyId: number; surveyMemberName: string; surveyMemberPhone: string }
    >()
    const dictSurveyMember: any = {}
    {
      const lstSurveyMemberId = Array.from(new Set(res.map((x) => x.surveyMemberId)))
      const lstSurveyMember = await this.surveyMemberRepo.find({ where: { id: In(lstSurveyMemberId) } })
      const lstUser = await this.userSurveyRepo.find({ where: { id: In(lstSurveyMember.map((x) => x.userId)) } })
      lstSurveyMember.forEach((x: any) => {
        x.username = lstUser.find((u) => u.id === x.userId)?.username || ''
        x.type = lstUser.find((u) => u.id === x.userId)?.type || ''
        dictSurveyMember[x.id] = x || null
      })
    }
    res.forEach((cur: any) => {
      const key = `${cur.surveyMemberId}-${cur.createdAt}-${cur.surveyId}`
      if (!uniqueAnswers.has(key)) {
        uniqueAnswers.set(key, {
          surveyMemberId: cur.surveyMemberId,
          surveyMemberName:
            dictSurveyMember[cur.surveyMemberId]?.type === 'Customer'
              ? dictSurveyMember[cur.surveyMemberId]?.company
              : dictSurveyMember[cur.surveyMemberId]?.username || '',
          surveyMemberPhone: dictSurveyMember[cur.surveyMemberId]?.phone || '',
          createdAt: cur.createdAt,
          surveyId: cur.surveyId,
        })
      }
    })
    const rs = Array.from(uniqueAnswers.values())
    const paginatedData = rs.slice(data.skip, data.skip + data.take)
    return { total: rs.length, data: paginatedData }
  }

  public async findAll(data: any) {
    return await this.repo.find({ where: { ...data } })
  }

  public async findQuestion(data: any) {
    let lstSurveyQuestion = await this.repo.find({ where: { surveyMemberId: IsNull(), value: IsNull(), surveyId: data.surveyId } })
    for (let surveyQ of lstSurveyQuestion as any) {
      let question = await this.questionRepo.findOne({
        where: { id: surveyQ.questionId },
        relations: { childs: { questionlistDetails: true }, questionlistDetails: true },
      })
      surveyQ.questionId = question
    }
    return lstSurveyQuestion
  }

  public async findByCompany(user: UserDto) {
    return await this.repo.find({
      where: {
        // companyId: user.companyId,
        isDeleted: false,
      },
    })
  }

  public async findTopicBySurvey(user: UserDto, surveyId: string) {
    const foundTopicId = await this.repo.findOne({
      where: {
        // companyId: user.companyId,
        isDeleted: false,
        surveyId,
      },

      select: {
        topicId: true,
      },
    })
    if (!foundTopicId) {
      return null
    }
    const foundTopic = await this.topicRepo.findOne({
      where: {
        id: foundTopicId.topicId,
        isDeleted: false,
      },
      select: {
        id: true,
        code: true,
        name: true,
      },
    })
    if (!foundTopic) {
      return null
    }
    return foundTopic
  }

  /** Phiếu trả lời theo từng chủ đề  */
  public async findSurveyAnswerWithTopic(user: UserDto, filter?: { year?: number; month?: number }): Promise<any[]> {
    const topics = await this.topicRepo.find()

    let year = filter?.year ?? new Date().getFullYear()
    let month = filter?.month ?? new Date().getMonth() + 1

    const firstDayOfMonth = new Date(year, month - 1, 1, 0, 0, 0, 0)
    const lastDayOfMonth = new Date(year, month, 0, 23, 59, 59, 999)

    const stats = await Promise.all(
      topics.map(async (topic) => {
        const questions = await this.surveyQuestionRepo.find({
          where: {
            topicId: topic.id,
            surveyMemberId: Not(IsNull()),
            createdAt: Between(firstDayOfMonth, lastDayOfMonth),
          },
          select: ['createdAt'],
        })

        const uniqueAnswers = new Set(questions.map((q) => JSON.stringify(q.createdAt)))

        return { topicName: topic.name, totalResponses: uniqueAnswers.size }
      }),
    )

    return stats
  }

  /** Thêm các câu hỏi trong chủ đề vào phiếu khảo sát */
  public async createQuestionByTopic(user: UserDto, data: SurveyCreateDto) {
    const [foundTopic, foundSurvey, foundTopicSurvey, lstSurveyMember]: any[] = await Promise.all([
      /** Kiểm tra topic tồn tại hay chưa */
      this.topicRepo.findOne({
        where: {
          id: data.topicId,
          isDeleted: false,
        },
        relations: { questions: true },
        select: {
          id: true,
          questions: true,
        },
      }),

      /** Kiểm tra phiếu khảo sát đã có tồn tại */
      this.surveyRepo.findOne({
        where: {
          id: data.surveyId,
          isDeleted: false,
        },
      }),

      /** Kiểm tra phiếu khảo sát đã có chủ đề hay chưa */
      this.repo.findOne({
        where: {
          surveyId: data.surveyId,
          isDeleted: false,
        },
      }),

      /** Lấy danh sách nhân viên trong phiếu khảo sát */
      this.surveyMemberRepo.find({
        where: {
          isDeleted: false,
          surveyId: data.surveyId,
        },
      }),
    ])
    if (!foundTopic || !foundSurvey) {
      throw new Error(ERROR_NOT_FOUND_DATA)
    }
    if (foundTopic.__questions__.length == 0) {
      throw new Error('Chủ đề chưa có câu hỏi')
    }
    if (foundTopicSurvey) {
      throw new Error('Phiếu khảo sát chỉ được chứa một chủ đề')
    }

    const lstSurveyQuestion = []
    for (const item of foundTopic.__questions__) {
      for (const surveyMember of lstSurveyMember) {
        const surveyQuestion = new SurveyQuestionEntity()
        surveyQuestion.categoryId = item.categoriesId
        surveyQuestion.topicId = item.topicId
        surveyQuestion.questionId = item.id
        // surveyQuestion.companyId = user.companyId
        surveyQuestion.surveyId = data.surveyId
        surveyQuestion.surveyMemberId = surveyMember.id
        surveyQuestion.createdAt = new Date()
        surveyQuestion.createdBy = user.id

        lstSurveyQuestion.push(surveyQuestion)
      }
    }
    await this.repo.save(lstSurveyQuestion)
    return { message: CREATE_SUCCESS }
  }

  /** Xóa chủ đề trong phiếu khảo sát */
  public async deleteQuestionByTopic(user: UserDto, data: SurveyCreateDto) {
    const [foundTopic, foundSurvey, foundSurveyQuestion] = await Promise.all([
      this.topicRepo.findOne({
        where: {
          // companyId: user.companyId,
          id: data.topicId,
          isDeleted: false,
        },
        select: {
          id: true,
        },
      }),
      /** Kiểm tra phiếu khảo sát đã có tồn tại */
      this.surveyRepo.findOne({
        where: {
          // companyId: user.companyId,
          id: data.surveyId,
          isDeleted: false,
        },
      }),
      /** Kiểm tra xem đã có ai làm bài khảo sát chưa*/
      this.repo.findOne({
        where: {
          // companyId: user.companyId,
          surveyId: data.surveyId,
          topicId: data.topicId,
          value: Not(IsNull()),
        },
        select: {
          id: true,
        },
      }),
    ])

    if (!foundTopic || !foundSurvey) {
      throw new Error(ERROR_NOT_FOUND_DATA)
    }

    if (foundSurveyQuestion) {
      throw new Error('Đã có nhân viên làm phiếu khảo sát, không thể thay đổi')
    }

    await this.repo.update(
      {
        surveyId: data.surveyId,
      },
      {
        isDeleted: true,
        updatedAt: new Date(),
        updatedBy: user.id,
      },
    )
    return { message: DELETE_SUCCESS }
  }

  /** Lấy chi tiết câu hỏi trong phiếu khảo sát của nhân viên */
  public async findSurveyQuestionUser(user: UserDto, data: PaginationDto) {
    const survey: any = await this.surveyMemberRepo.findOne({
      where: {
        userId: data.where.userId,
        surveyId: data.where.surveyId,
        // companyId: user.companyId,
        isDeleted: false,
      },
    })
    if (!survey) throw new Error('Phiếu khảo sát không còn tồn tại hoặc đã ngưng hoạt động')
    let lstSurveyQuestion: any = await this.surveyQuestionRepo.find({
      where: { surveyId: data.where.surveyId, isDeleted: false, surveyMemberId: survey.id },
    })
    if (lstSurveyQuestion.length === 0) {
      return []
    }
    const dictSurveyQuestion = new Map()
    let lstSurveyQuestionId = lstSurveyQuestion.map((x: any) => {
      dictSurveyQuestion.set(x.questionId, { score: x.score, value: x.value })
      return x.questionId
    })
    const [lstQuestion, count]: any = await this.questionRepo.findAndCount({
      where: {
        id: In(lstSurveyQuestionId),
        isDeleted: false,
        // companyId: user.companyId,
        parentId: IsNull(),
      },
      relations: { questionlistDetails: true, childs: { questionlistDetails: true } },
      order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } },
      skip: data.skip,
      take: data.take,
    })

    for (let question of lstQuestion) {
      if (dictSurveyQuestion.get(question.id)) {
        let findValueListDetail = dictSurveyQuestion.get(question.id).value
        /** Nếu câu hỏi dạng danh sách thì tìm để lấy giá trị */
        if (question.type === enumData.QuestionType.List.code) {
          findValueListDetail = question.__questionlistDetails__.find((q: any) => q.id === findValueListDetail)
          findValueListDetail = findValueListDetail ? findValueListDetail?.name + '. ' + findValueListDetail?.value : ''
        }
        question.score = dictSurveyQuestion.get(question.id).score
        question.value = findValueListDetail
      }
      if (question.__childs__.length > 0) {
        for (let child of question.__childs__) {
          if (dictSurveyQuestion.get(child.id)) {
            child.score = dictSurveyQuestion.get(child.id).score
            child.value = dictSurveyQuestion.get(child.id).value
          }
        }
      }
    }

    return [lstQuestion, count]
  }

  /**
   *
   * tạo mới ds câu hỏi thuộc về bài khảo sát
   * @param user
   * @param data
   * @returns
   */
  public async createListSurveyQuestion(user: UserDto, data: { lstQuestion: QuestionEntity[]; surveyId: string; surveyMemberId: string }) {
    return this.repo.manager.transaction(async (trans) => {
      const lstSurveyQuestion: SurveyQuestionEntity[] = []
      for (const item of data.lstQuestion) {
        const surveyQuestion = new SurveyQuestionEntity()
        surveyQuestion.topicId = item.topicId
        let survey = await trans.getRepository(SurveyEntity).findOne({ where: { id: data.surveyId } })
        surveyQuestion.surveyId = data.surveyId
        surveyQuestion.survey = Promise.resolve(survey)
        surveyQuestion.surveyMemberId = data.surveyMemberId
        surveyQuestion.questionId = item.id
        surveyQuestion.createdAt = new Date()
        lstSurveyQuestion.push(surveyQuestion)
      }
      await trans.getRepository(SurveyQuestionEntity).save(lstSurveyQuestion)
    })
  }

  /**
   *
   * cập nhật ds câu hỏi thuộc về bài khảo sát
   * @param user
   * @param data
   * @returns
   */
  public async editListSurveyQuestion(
    user: UserDto,
    data: { lstQuestionRemove?: SurveyQuestionEntity[]; lstQuestionAdd?: QuestionEntity[]; surveyId: string; surveyMemberId: string },
  ) {
    return this.repo.manager.transaction(async (trans) => {
      if (data.lstQuestionRemove && data.lstQuestionRemove.length > 0) {
        const lstSurveyQuestionsRemove: SurveyQuestionEntity[] = []
        for (const item of data.lstQuestionRemove) {
          const surveyQuestionExist = item
          surveyQuestionExist.isDeleted = true

          lstSurveyQuestionsRemove.push(surveyQuestionExist)
        }

        // cập nhật ds câu hỏi muốn xoá (isDeleted = true)
        await trans.getRepository(SurveyQuestionEntity).save(lstSurveyQuestionsRemove)
      }

      if (data.lstQuestionAdd && data.lstQuestionAdd.length > 0) {
        const lstSurveyQuestionAdd: SurveyQuestionEntity[] = []
        for (const item of data.lstQuestionAdd) {
          const surveyQuestionNew = new SurveyQuestionEntity()
          surveyQuestionNew.topicId = item.topicId
          surveyQuestionNew.surveyId = data.surveyId
          surveyQuestionNew.surveyMemberId = data.surveyMemberId

          lstSurveyQuestionAdd.push(surveyQuestionNew)
        }

        // lưu ds câu hỏi khảo sát mới
        await trans.getRepository(SurveyQuestionEntity).save(lstSurveyQuestionAdd)
      }
    })
  }
}
