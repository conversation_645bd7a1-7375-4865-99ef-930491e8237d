import { Controller, Post, Body, Req } from '@nestjs/common'
import { ProductUpdateIsActiveDto } from './dto/productUpdateIsActive.dto'
import { ProductCreateDto } from './dto/productCreate.dto'
import { ProductUpdateDto } from './dto/productUpdate.dto'
import { FilterIdDto, FilterOneDto, FindItemsByIdsDto, ListProduct, ListProductByChannel, PaginationDto, SearchDto, UserDto } from '../../../dto'
import { CurrentUser } from '../../common/decorators'
import { ApiOperation, ApiTags } from '@nestjs/swagger'

import { Request as IRequest } from 'express'
import { ProductCreateExcelDto, ProductUpdateIsActiveListDto, ProductExpiryDateDto } from './dto'
import { ItemService } from './item.service'
import { ProductDto } from './dto/product.dto'

@ApiTags('Item')
@Controller('item_public')
export class ItemPublicController {
  constructor(private readonly service: ItemService) {}

  @Post('find_by_permission_brand')
  public async findByPermissionBrand(@Req() req: IRequest, @Body() data: any) {
    return await this.service.findByPermissionBrand(req, data)
  }

  @Post('find')
  public async find(@Body() data: FilterIdDto, @Req() req: IRequest) {
    return await this.service.find(data, req)
  }

  /** Mobile: Lấy danh sách sản phẩm */
  @Post('search-product')
  public async search(@Body() data: SearchDto, @Req() req: IRequest) {
    return await this.service.searchItems(data, req)
  }

  @Post('search-product-mobile')
  public async searchMobile(@Body() data: SearchDto, @Req() req: IRequest) {
    return await this.service.searchItemsMobile(data, req)
  }

  /** Mobile: Lấy danh sách sản phẩm liên quan */
  @Post('related-product')
  public async getRelatedProducts(@Body() data: ProductDto) {
    return await this.service.getRelatedProducts(data)
  }

  /** Mobile: Kiểm tra sản phẩm có combo liên quan không */
  @Post('check-related-combo-in-product')
  public async checkRelatedComboInProduct(@Body() data: ProductDto) {
    return await this.service.checkRelatedComboInProduct(data)
  }

  @Post('find-all')
  public async findAll(@Body() data: ListProduct, @Req() req: IRequest) {
    return await this.service.findAll(data, req)
  }

  @Post('find-all-by-partner-channel')
  public async findAllItemByPartnerChannel(@Body() data: ListProductByChannel) {
    return await this.service.findAllItemByPartnerChannel(data)
  }

  @Post('find-all-mobile')
  public async findAllMobile(@Body() data: ListProduct, @Req() req: IRequest) {
    return await this.service.findAllMobile(data, req)
  }

  /** Mobile: Chi tiết sản phẩm, bao gồm cả combo */
  @Post('detail')
  public async detail(@Body() data: FilterIdDto) {
    return await this.service.details(data)
  }

  @Post('get_item')
  public async getItem(@Body() data: FilterIdDto, @Req() req: IRequest) {
    return await this.service.getItem(data)
  }
  /** Hàm tìm danh sách combo theo Ids, Lấy full thông tin combo */
  @Post('find-combo-by-ids')
  public async findByIds(@Body() data: FindItemsByIdsDto) {
    return await this.service.findByIds(data)
  }
  /** Hàm tìm danh sách combo theo Ids, Chỉ lấy thông tin combo */
  @Post('find-only-combo-by-ids')
  public async findComboByIds(@Body() data: { ids: string[] }) {
    return await this.service.findComboByIds(data.ids)
  }

  @Post('find_lot_number_in_product_detail')
  public async findLotNumberInProductDetail(@Body() data: FilterOneDto) {
    return await this.service.findLotNumberInProductDetail(data)
  }

  @Post('load_data')
  public async loadData(@Body() data: { isCombo?: boolean }) {
    return await this.service.loadData(data)
  }

  @Post('find_product_info')
  public async findProductInfo(@Body() data: FilterOneDto) {
    return await this.service.findProductInfo(data)
  }

  @Post('find_product_inventory')
  public async findProductInventory(@Body() data: ProductExpiryDateDto) {
    return await this.service.findProductInventory(data)
  }

  @Post('find_product_manufacture_date')
  public async findProductManufactureDate(@Body() data: ProductExpiryDateDto) {
    return await this.service.findProductManufactureDate(data)
  }

  // phân trang cho trang sản phẩm
  @Post('pagination')
  public async pagination(@Body() data: PaginationDto, @Req() req: IRequest) {
    return await this.service.pagination(data, req)
  }

  @Post('create_data')
  public async createData(@Body() data: ProductCreateDto, @Req() req: IRequest) {
    return await this.service.createData(data, req)
  }

  @Post('update_data')
  public async updateData(@Body() data: ProductUpdateDto, @Req() req: IRequest) {
    return await this.service.updateData(data, req)
  }

  @Post('update_active')
  public async updateActive(@Body() data: ProductUpdateIsActiveDto, @Req() req: IRequest) {
    return await this.service.updateIsDelete(data.id, req)
  }

  @Post('create_data_excel')
  public async createDataExcel(@Body() data: ProductCreateExcelDto[], @Req() req: IRequest) {
    return await this.service.createDataExcel(data, req)
  }

  @Post('update_lock')
  public async updateLock(@Body() data: any, @Req() req: IRequest) {
    return await this.service.updateLock(data, req)
  }

  @ApiOperation({ summary: 'phân trang cho phân kho' })
  @Post('pagination_warehouse')
  public async paginationWarehouse(@Body() data: PaginationDto, @Req() req: IRequest) {
    return await this.service.paginationWarehouse(data, req)
  }

  @Post('find_product_detail')
  public async findProductDetail(@Body() data: FilterIdDto) {
    return await this.service.findProductDetail(data)
  }

  @Post('find_detail_expirydate')
  public async findProductDetailEx(@Body() data: { id: string }, @Req() req: IRequest) {
    return await this.service.findDetailExpiryDate(data.id, req)
  }

  // phân trang cho menu tồn kho
  @Post('pagination_inventory')
  public async paginationInventory(@Body() data: PaginationDto, @Req() req: IRequest) {
    return await this.service.paginationInventory(data, req)
  }

  // phân trang cho menu thiết lập giá
  @Post('pagination_item_price')
  public async paginationProductPrice(@Body() data: PaginationDto, @Req() req: IRequest) {
    return await this.service.paginationProductPrice(data, req)
  }

  @Post('find_product_detail_expirydate')
  public async findProductDetailExpirydate(@Body() data: FilterIdDto) {
    return await this.service.findProductDetailExpirydate(data)
  }

  @Post('find_list_product_combo')
  public async findListProductCombo(@Body() data: FilterIdDto) {
    return await this.service.findListProductCombo(data)
  }

  @Post('update_active_list')
  public async updateActiveList(@Body() data: ProductUpdateIsActiveListDto, @Req() req: IRequest) {
    return await this.service.updateActiveList(data, req)
  }

  @Post('create_data_combo_excel')
  public async createDataComboExcel(@Body() data: { lstDataTable1: any[]; lstDataTable2: any[] }, @CurrentUser() user?: UserDto) {
    return await this.service.createDataComboExcel(data, user)
  }
}
