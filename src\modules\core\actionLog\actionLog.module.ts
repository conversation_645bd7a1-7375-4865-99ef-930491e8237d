import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import { ActionLogRepository} from '../../../repositories'
import { ActionLogController } from './actionLog.controller'
import { ActionLogService } from './actionLog.service'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([ActionLogRepository])],
  controllers: [ActionLogController],
  providers: [ActionLogService],
  exports: [ActionLogService],
})
export class ActionLogModule {}
