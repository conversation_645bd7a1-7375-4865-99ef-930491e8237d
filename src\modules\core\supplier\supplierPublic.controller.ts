import { Controller, UseGuards, Post, Body, Req } from '@nestjs/common'
import { SupplierService } from './supplier.service'
import { SupplierCreateDto } from './dto/supplierCreate.dto'
import { SupplierUpdateDto } from './dto/supplierUpdate.dto'
import { SupplierCreateExcelDto, SupplierRegisterDto, SupplierUpdateIsActiveDto } from './dto'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { CurrentUserPortal, JwtAuthGuard } from '../../common'
import { PaginationDto, UserDto } from '../../../dto'
import { ApeAuthGuard } from '../../survey/common/guards'

@ApiBearerAuth()
@ApiTags('Supplier')
@UseGuards(ApeAuthGuard)
@Controller('supplier_Public')
export class SupplierPublicController {
  constructor(private readonly service: SupplierService) {}

  @Post('find')
  public async find(@Body() data: any) {
    return await this.service.find(data)
  }

  @Post('pagination')
  public async pagination(@Body() data: PaginationDto) {
    return await this.service.pagination(data)
  }

  @Post('create_data')
  public async createData(@CurrentUserPortal() user: UserDto, @Body() data: SupplierCreateDto) {
    return await this.service.createData(user, data)
  }

  @Post('update_data')
  public async updateData(@CurrentUserPortal() user: UserDto, @Body() data: SupplierUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @Post('update_active')
  public async updateActive(@Body() data: SupplierUpdateIsActiveDto, @CurrentUserPortal() user: UserDto) {
    return await this.service.updateIsDelete(data.id, user)
  }

  @Post('find_detail')
  public async findDetail(@Body() data: SupplierUpdateIsActiveDto) {
    return await this.service.findDetail(data)
  }

  @Post('create_data_excel')
  public async createDataExcel(@CurrentUserPortal() user: UserDto, @Body() data: SupplierCreateExcelDto[]) {
    return await this.service.createDataExcel(user, data)
  }

  @ApiOperation({ summary: 'Hàm đăng ký Doanh nghiệp mới' })
  @Post('supplier_registration')
  public async supplierRegistration(@Req() req: Request, @Body() data: SupplierRegisterDto) {
    return await this.service.supplierRegistration(req, data)
  }
}
