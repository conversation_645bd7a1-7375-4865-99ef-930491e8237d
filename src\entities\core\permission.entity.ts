import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON>o<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm'
import { BaseEntity } from './base.entity'
import { UserEntity } from './user.entity'

@Entity({ name: 'permission' })
export class PermissionEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  roleCode: string

  @Column({
    nullable: false,
    default: false,
  })
  isActive: boolean

  @Column({
    type: 'varchar',
    nullable: false,
  })
  userId: string

  @ManyToOne(() => UserEntity, (user) => user.permission)
  @JoinColumn({ name: 'userId', referencedColumnName: 'employeeId' })
  user: Promise<UserEntity>
}
