import { MigrationInterface, QueryRunner } from "typeorm";

export class updateEntityWarehouseProduct1742958528762 implements MigrationInterface {
    name = 'updateEntityWarehouseProduct1742958528762'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "warehouse_product" ADD "quantityOrder" integer DEFAULT '0'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "warehouse_product" DROP COLUMN "quantityOrder"`);
    }

}
