import { MigrationInterface, QueryRunner } from "typeorm";

export class updateEntityOperationalArea1746847797942 implements MigrationInterface {
    name = 'updateEntityOperationalArea1746847797942'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "operational_areas" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "createdBy" character varying(36), "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT now(), "updatedBy" character varying(36), "isDeleted" boolean NOT NULL DEFAULT false, "areaId" character varying NOT NULL, "areaCode" character varying NOT NULL, "areaName" character varying NOT NULL, "type" character varying NOT NULL, "mappingRefType" character varying NOT NULL, "mappingRefId" character varying NOT NULL, CONSTRAINT "PK_2ea12201aa1d04be1e0ecc35eed" PRIMARY KEY ("id")); COMMENT ON COLUMN "operational_areas"."type" IS 'Loại khu vực: province, district hoặc ward'; COMMENT ON COLUMN "operational_areas"."mappingRefType" IS 'Loại đối tượng áp dụng: warehouse hoặc supplier'; COMMENT ON COLUMN "operational_areas"."mappingRefId" IS 'ID của đối tượng áp dụng (kho hoặc nhà cung cấp)'`);
        await queryRunner.query(`CREATE INDEX "IDX_13c78047f1f8e062de20c502c8" ON "operational_areas" ("mappingRefType") `);
        await queryRunner.query(`CREATE INDEX "IDX_b35657ed18996278cd347c4ad7" ON "operational_areas" ("mappingRefId") `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_b35657ed18996278cd347c4ad7"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_13c78047f1f8e062de20c502c8"`);
        await queryRunner.query(`DROP TABLE "operational_areas"`);
    }

}
