import { Repository } from 'typeorm'
import { OutboundDetailEntity, OutboundEntity, OutboundHistoryEntity } from '../../entities'
import { CustomRepository } from '../../typeorm'

@CustomRepository(OutboundEntity)
export class OutboundRepository extends Repository<OutboundEntity> {}

@CustomRepository(OutboundDetailEntity)
export class OutboundDetailRepository extends Repository<OutboundDetailEntity> {}

@CustomRepository(OutboundHistoryEntity)
export class OutboundHistoryRepository extends Repository<OutboundHistoryEntity> {}
