import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString } from 'class-validator'

export class InfoADto {
  @ApiPropertyOptional({ description: 'Tên bên A' })
  @IsOptional()
  name: string

  @ApiPropertyOptional({ description: 'CCCD bên A' })
  @IsOptional()
  personalId: string

  @ApiPropertyOptional({ description: 'Mã số thuế (Mã số doanh nghiệp) bên A' })
  @IsOptional()
  taxCode: string

  @ApiPropertyOptional({ description: 'Số điện thoại bên A' })
  @IsOptional()
  phone: string

  @ApiPropertyOptional({ description: 'Địa chỉ bên A' })
  @IsOptional()
  address: string

  @ApiPropertyOptional({ description: 'Người đại diện bên A' })
  @IsOptional()
  representative: string

  @ApiPropertyOptional({ description: '<PERSON>ứ<PERSON> vụ bên A' })
  @IsOptional()
  position: string

  @ApiPropertyOptional({ description: '<PERSON>ân hàng bên A' })
  @IsOptional()
  bank: string

  @ApiPropertyOptional({ description: 'Tên chủ tài khoản bên A' })
  @IsOptional()
  bankNumber: string

  @ApiPropertyOptional({ description: 'Số tài khoản đối tác bên A' })
  @IsOptional()
  bankAccount: string

  @ApiPropertyOptional({ description: 'Fax bên A' })
  @IsOptional()
  fax: string

  @ApiPropertyOptional({ description: 'Email bên A' })
  @IsOptional()
  email: string

  @ApiPropertyOptional({ description: 'Bộ phận bên A' })
  @IsOptional()
  part: string
}

export class ContractCreateDto {
  @IsOptional()
  contractName?: string

  @IsOptional()
  effectiveDate?: Date

  @IsOptional()
  expirationDate?: Date

  @IsOptional()
  templateApplied?: string

  @IsOptional()
  serviceType?: string

  @IsOptional()
  term: string

  @IsOptional()
  partnerId: string

  @IsOptional()
  partnerName: string

  @IsOptional()
  mediaCode: string

  @IsOptional()
  eContractType: string

  @IsOptional()
  isCTV: boolean

  // Thông tin bên A
  @IsOptional()
  infoA: InfoADto

  @ApiPropertyOptional({ description: 'Link file' })
  @IsOptional()
  fileUrl: string
}

export class ContractMemberCardFilterDto {
  @ApiProperty({ description: 'MemberId' })
  @IsNotEmpty()
  memberId: string

  @ApiProperty({ description: 'CardId' })
  @IsNotEmpty()
  cardId: string
}

export class ContractMediaTypeFilterDto {
  @ApiProperty({ description: 'MemberId' })
  @IsNotEmpty()
  memberId: string
}

export class ContractFilterDto {
  @ApiPropertyOptional({ description: 'List MemberId' })
  memberId?: string[]

  @ApiPropertyOptional({ description: 'List isCTV' })
  isCTV?: boolean[]

  @ApiPropertyOptional({ description: 'List eContractType' })
  eContractType?: string[]

  @ApiPropertyOptional({ description: 'List serviceType' })
  serviceType?: string[]

  @ApiPropertyOptional({ description: 'List partnerId' })
  partnerId?: string[]

  @ApiPropertyOptional({ description: 'List mediaCode' })
  mediaCode?: string[]
}
