// import { CheckInventoryEntity } from '.'
import { CheckInventoryEntity } from '.'
import { BaseEntity } from '../core/base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'

@Entity('check_inventory_history')
export class CheckInventoryHistoryEntity extends BaseEntity {
  /** Phiếu kiểm kho */
  @Column({ type: 'varchar', nullable: true })
  checkInventoryId: string
  @ManyToOne(() => CheckInventoryEntity, (p) => p.histories)
  @JoinColumn({ name: 'checkInventoryId', referencedColumnName: 'id' })
  checkInventory: Promise<CheckInventoryEntity>

  /** <PERSON><PERSON> chú */
  @Column({ type: 'text', nullable: true })
  description: string
}
