import { Body, Controller, Post, Req, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { Request as IRequest } from 'express'
// import { surveyAuthApiHelper } from '../../helpers'
import { DepartmentService } from './department.service'
import { DepartmentCreateDto } from './dto/departmentCreate.dto'
import { DepartmentUpdateDto } from './dto/departmentUpdate.dto'

import { FilterOneDto, UserDto } from '../../../dto'
import { CurrentUser } from '../../common/decorators'
import { JwtAuthGuard } from '../../common'
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@ApiTags('Department')
@Controller('department')
export class DepartmentController {
  constructor(private readonly service: DepartmentService) {}

  @ApiOperation({ summary: 'Lấy ds phòng ban' })
  @Post('find')
  public async find(@CurrentUser() user: UserDto, @Body() data: any) {
    // const user = await surveyAuthApiHelper.validateRequestCompany(req)
    return await this.service.find(user, data)
  }

  @ApiOperation({ summary: 'Lấy danh sách theo trang để hiển thị trên giao diện.' })
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: any) {
    // const user = await surveyAuthApiHelper.validateRequestCompany(req)
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo data phòng ban' })
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: DepartmentCreateDto) {
    // const user = await surveyAuthApiHelper.validateRequestCompany(req)
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật data phòng ban' })
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: DepartmentUpdateDto) {
    // const user = await surveyAuthApiHelper.validateRequestCompany(req)
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái phòng ban isDeleted' })
  @Post('update_active')
  public async updateActive(@CurrentUser() user: UserDto, @Body() data: FilterOneDto, @Req() req: IRequest) {
    // const user = await surveyAuthApiHelper.validateRequestCompany(req)
    return await this.service.updateIsDelete(user, data, req)
  }

  @ApiOperation({ summary: 'Lấy thông tin chi tiết phòng ban' })
  @Post('load_detail')
  public async loadDetail(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    // const user = await surveyAuthApiHelper.validateRequestCompany(req)
    return await this.service.loadDetail(user, data)
  }

  @ApiOperation({ summary: 'Hàm tạo mới bằng excel' })
  @Post('create_data_by_excel')
  public async createDataByExcel(@CurrentUser() user: UserDto, @Body() data: DepartmentCreateDto[], @Req() req: IRequest) {
    // const user = await surveyAuthApiHelper.validateRequestCompany(req)
    return await this.service.createDataByExcel(user, data, req)
  }
}
