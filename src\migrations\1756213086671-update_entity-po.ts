import { MigrationInterface, QueryRunner } from "typeorm";

export class updateEntityPo1756213086671 implements MigrationInterface {
    name = 'updateEntityPo1756213086671'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "purchase_order" ADD "warehouseId" uuid`);

    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "purchase_order" DROP COLUMN "warehouseId"`);
    }

}
