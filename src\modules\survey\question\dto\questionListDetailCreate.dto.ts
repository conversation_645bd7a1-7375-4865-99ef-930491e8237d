import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString, IsNumber } from 'class-validator'

export class QuestionListDetailCreateDto {
  @ApiProperty({ description: 'Tên', example: 'A' })
  @IsNotEmpty({ message: 'Tên không được trống' })
  @IsString()
  name: string

  @ApiProperty({ description: 'Id câu hỏi' })
  @IsNotEmpty({ message: 'Câu hỏi không được trống' })
  @IsString()
  questionId: string

  @ApiProperty({ description: 'Thứ tự sắp xếp' })
  @IsNotEmpty({ message: 'Thứ tự sắp xếp câu hỏi' })
  sort: number
}
