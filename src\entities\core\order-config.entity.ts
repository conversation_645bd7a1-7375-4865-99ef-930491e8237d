import { Column, Entity, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from './base.entity'

@Entity('order_config')
export class OrderConfigEntity extends BaseEntity {
  @ApiProperty({ description: "Của NCC nào" })
  @Column({ type: 'uuid', nullable: true })
  @Index()
  supplierId: string;

  @ApiProperty({ description: "Thời gian duyệt đơn" })
  @Column({ nullable: true })
  approvalTime: string;

  @ApiProperty({ description: "Số ngày NCC phải giao tới 3PL " })
  @Column({ nullable: true })
  supplierDeliveryTime: string;

  @ApiProperty({ description: "Số ngày 3PL phải giao hàng tới MBC" })
  @Column({ nullable: true })
  thirdPartyDeliveryTime: string;

  @ApiProperty({ description: "Thời gian nhận hàng mong muốn tối thiểu" })
  @Column({ default: '7', nullable: true })
  minReceivingTime: string;

  @ApiProperty({ description: "Thời gian nhận hàng mong muốn tối đa" })
  @Column({ default: '90', nullable: true })
  maxReceivingTime: string;

}

