import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards } from '@nestjs/common'
import { PricingService } from './pricing.service'
import { CreatePricingDto } from './dto/create-pricing.dto'
import { UpdatePricingDto } from './dto/update-pricing.dto'
import { ListPricingDto } from './dto/list-pricing.dto'
import { UserDto } from '../../../dto'
import { CurrentUser, JwtAuthGuard } from '../../common'
import { ImportExcelPricingDto } from './dto/import-excel-pricing.dto'

@Controller('pricing')
@UseGuards(JwtAuthGuard)
export class PricingController {
  constructor(private readonly pricingService: PricingService) {}

  @Post('list')
  list(@Body() params: ListPricingDto) {
    return this.pricingService.list(params)
  }

  @Post('create')
  create(@CurrentUser() user: UserDto, @Body() createPricingDto: CreatePricingDto) {
    return this.pricingService.create(createPricingDto, user)
  }

  //update
  @Post('update')
  update(@Body() updatePricingDto: UpdatePricingDto, @CurrentUser() user: UserDto) {
    return this.pricingService.update(updatePricingDto, user)
  }

  //delete
  @Post('setActive/:id')
  setActive(@Param('id') id: string, @CurrentUser() user: UserDto) {
    return this.pricingService.setActive(id, user)
  }

  //import excel
  @Post('import-excel')
  importExcel(@Body() data: ImportExcelPricingDto[], @CurrentUser() user: UserDto) {
    return this.pricingService.importExcel(data, user)
  }
}
