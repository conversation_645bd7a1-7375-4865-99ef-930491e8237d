import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsString, IsBoolean, IsNumber } from 'class-validator'
import { QuestionListDetailCreateDto } from './questionListDetailCreate.dto'

export class QuestionCreateMasterDataDto {
  @ApiProperty({ description: 'Tên câu hỏi' })
  @IsNotEmpty({ message: 'Tên không được trống' })
  @IsString()
  name: string

  @ApiProperty({ description: 'Mã câu hỏi' })
  code: string

  @ApiProperty({ description: 'Bắt buộc nhập' })
  @IsNotEmpty({ message: 'isRequired không được trống' })
  @IsBoolean()
  isRequired: boolean

  @ApiProperty({ description: 'Kiểu dữ liệu' })
  @IsNotEmpty({ message: 'Kiểu dữ liệu không được trống' })
  @IsString()
  type: string

  @ApiPropertyOptional()
  isHighlight: boolean
  @ApiPropertyOptional()
  hightlightValue: number
  @ApiPropertyOptional()
  sort: number

  @ApiPropertyOptional()
  percent: number
  @ApiPropertyOptional()
  percentRule: number

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  level: number

  @ApiPropertyOptional()
  description: string

  @ApiPropertyOptional()
  parentId: string

  @ApiPropertyOptional()
  scoreDLC: number
  @ApiPropertyOptional()
  requiredMin: number

  topicCode: string

  @ApiProperty({ description: 'Id danh mục' })
  @IsNotEmpty({ message: 'Danh mục không được trống' })
  @IsString()
  categoriesId: string

  @ApiProperty({ description: 'Cách xếp loại điểm' })
  @IsNotEmpty({ message: 'isCalUp không được trống' })
  @IsBoolean()
  isCalUp: boolean

  @ApiPropertyOptional()
  percentDownRule: number

  @ApiProperty({ description: 'id công ty' })
  @IsString()
  companyId: string

  @ApiProperty({ description: 'id user admin' })
  @IsString()
  createdBy: string

  lstDetail: QuestionListDetailCreateDto[]

  childs: any[]
}
