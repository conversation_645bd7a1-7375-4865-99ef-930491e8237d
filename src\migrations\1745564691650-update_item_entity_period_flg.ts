import { MigrationInterface, QueryRunner } from "typeorm";

export class updateItemEntityPeriodFlg1745564691650 implements MigrationInterface {
    name = 'updateItemEntityPeriodFlg1745564691650'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "item" ADD "isPeriodSale" boolean DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "item" DROP COLUMN "isPeriodSale"`);
    }

}
