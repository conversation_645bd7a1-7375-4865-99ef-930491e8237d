import { Module } from '@nestjs/common'
import { BusinessOrderService } from './business-order.service'
import { BusinessOrderController } from './business-order.controller'
import { TypeOrmExModule } from '../../../typeorm'
import { BusinessOrderItemRepository, BusinessOrderRepository } from '../../../repositories/core/businessOrder.repository'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([BusinessOrderRepository, BusinessOrderItemRepository])],
  controllers: [BusinessOrderController],
  providers: [BusinessOrderService],
})
export class BusinessOrderModule {}
