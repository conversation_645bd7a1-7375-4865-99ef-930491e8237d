import { Injectable, ConflictException, NotFoundException } from '@nestjs/common'
import { PackingRepository, PartnerMapRepository } from '../../../repositories'
import {
  ERROR_NOT_FOUND_DATA,
  ERROR_CODE_TAKEN,
  UPDATE_ACTIVE_SUCCESS,
  CREATE_SUCCESS,
  UPDATE_SUCCESS,
  IMPORT_SUCCESS,
  enumData,
  ERROR_USERNAME_DUPLICATE,
} from '../../../constants'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { In, Like } from 'typeorm'
import { PackingEntity, PartnerMapEntity } from '../../../entities'
import { PartnerMapCreateDto } from './dto/partnerMapCreate.dto'

@Injectable()
export class PartnerMapService {
  constructor(private readonly repo: PartnerMapRepository) {}

  public async find(data: any) {
    const whereCon: any = {}
    if (data.parentId) whereCon.parentId = data.parentId
    if (data.childId) whereCon.childId = data.childId
    if (data.role) whereCon.role = data.role
    return await this.repo.find({ where: whereCon })
  }

  public async createListData(data: PartnerMapCreateDto[]) {
    const listDeleteParentId = data.map((x) => x.parentId)
    await this.repo.delete({ parentId: In(listDeleteParentId) })
    const today = new Date()
    for (let item of data) {
      const entity = new PartnerMapEntity()
      if (item.childId == '') continue
      entity.parentId = item.parentId
      entity.childId = item.childId
      entity.role = item.role
      entity.createdAt = today
      await this.repo.save(entity)
    }
    return { message: CREATE_SUCCESS }
  }
}
