import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'

export class CancelComboDto {
  /** Id của sản phẩm combo */
  @IsNotEmpty()
  @IsString()
  id: string

  /** Id của kho nhập */
  @IsNotEmpty()
  @IsString()
  warehouseId: string

  @IsNotEmpty()
  lstProductInbound: ProductInboundDto[]
}

export class ProductInboundDto {
  @ApiProperty({ description: 'Id sản phẩm' })
  @IsNotEmpty({ message: 'Id sản phẩm không được để trống' })
  @IsString()
  productId: string

  @ApiProperty({ description: 'Ngày sản xuất' })
  @IsNotEmpty({ message: '<PERSON><PERSON>y sản xuất không được để trống' })
  manufactureDate: Date

  @ApiProperty({ description: 'Hạn sử dụng' })
  @IsNotEmpty({ message: 'Hạn sử dụng không được để trống' })
  expiryDate: Date

  @ApiProperty({ description: 'Số lượng' })
  @IsNotEmpty({ message: 'Số lượng không được để trống' })
  quantity: number

  @ApiProperty({ description: 'Mã lô' })
  @IsNotEmpty({ message: 'Mã lô không được để trống' })
  lotNumber: string
}
