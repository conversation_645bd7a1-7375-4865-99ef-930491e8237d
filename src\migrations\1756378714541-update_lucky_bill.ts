import { MigrationInterface, QueryRunner } from 'typeorm'

export class updateLuckyBill1756378714541 implements MigrationInterface {
  name = 'updateLuckyBill1756378714541'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "lucky_bill_apply_region" DROP CONSTRAINT "FK_1437a94b4963f3c87909271713e"`)
    await queryRunner.query(`ALTER TABLE "lucky_bill_apply_region" ADD "limit" integer DEFAULT '0'`)
    await queryRunner.query(`ALTER TABLE "lucky_bill_apply_region" DROP COLUMN "districtId"`)
    await queryRunner.query(`ALTER TABLE "lucky_bill_apply_region" ADD "districtId" uuid array NOT NULL`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "lucky_bill_apply_region" DROP COLUMN "districtId"`)
    await queryRunner.query(`ALTER TABLE "lucky_bill_apply_region" ADD "districtId" uuid NOT NULL`)
    await queryRunner.query(`ALTER TABLE "lucky_bill_apply_region" DROP COLUMN "limit"`)
    await queryRunner.query(
      `ALTER TABLE "lucky_bill_apply_region" ADD CONSTRAINT "FK_1437a94b4963f3c87909271713e" FOREIGN KEY ("districtId") REFERENCES "district"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }
}
