import { MigrationInterface, QueryRunner } from "typeorm";

export class updateEntityPoItems1747020709968 implements MigrationInterface {
    name = 'updateEntityPoItems1747020709968'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "purchase_order_item" ADD "provinceId" uuid`);
        await queryRunner.query(`ALTER TABLE "purchase_order_item" ADD "warehouseId" uuid`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "purchase_order_item" DROP COLUMN "warehouseId"`);
        await queryRunner.query(`ALTER TABLE "purchase_order_item" DROP COLUMN "provinceId"`);
    }

}
