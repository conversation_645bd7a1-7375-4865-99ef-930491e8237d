import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'

export class MediaCreateDto {
  @ApiProperty({ description: 'Id tài xế' })
  @IsString()
  productId: string

  @ApiProperty({ description: 'Đường dẩn của tập tin' })
  @IsNotEmpty()
  @IsString()
  url: string

  @ApiProperty({ description: 'Tên tập tin' })
  @IsNotEmpty()
  @IsString()
  name: string

  @ApiProperty({ description: 'Loại tập tin' })
  @IsNotEmpty()
  @IsString()
  type: string
}
