import { Controller, UseGuards, Post, Body, Get, Query } from '@nestjs/common'
import { JwtAuthGuard } from '../../common/guards'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { CurrentUser } from '../../common/decorators'
import { ItemGroupCreateDto, ItemGroupUpdateDto } from './dto'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { ItemGroupService } from './itemGroup.service'
import { ListItemGroupReq } from '@/dto/item-group.dto'
import { ItemGroupCreateExcelDto } from './dto/itemGroupCreateExcel.dto'
import { AuthGuard } from '../../common/guards/auth.guard'

@ApiTags('ItemGroup')
@Controller('item_group_public')
export class ItemGroupPublicController {
  constructor(private readonly service: ItemGroupService) { }

  @Post('find')
  public async find(@Body() data: any) {
    return await this.service.find(data)
  }

  @Post('pagination')
  public async pagination(@Body() data: PaginationDto) {
    return await this.service.pagination(data)
  }

  @Post('find_one')
  public async findOne(@Body() data: FilterOneDto) {
    return await this.service.findOne(data)
  }
}
