import { Controller, UseGuards, Post, Body, Req } from '@nestjs/common'
import { InboundService } from './inbound.service'
import { InboundCreateDto, InboundtApproveDto, InboundtUpdateDescriptionDto, InboundUpdateDto } from './dto'
import { CurrentUser } from '../../common/decorators'
import { FilterIdDto, PaginationDto, UserDto } from '../../../dto'
import { Request as IRequest } from 'express'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { JwtAuthGuard } from '../../common/guards'

@ApiBearerAuth()
@ApiTags('Inbound')
@UseGuards(JwtAuthGuard)
@Controller('inbound')
export class InboundController {
  constructor(private readonly service: InboundService) {}

  @ApiOperation({ summary: 'DS PNK phân trang' })
  @Post('pagination')
  async pagination(@Req() req: IRequest, @Body() data: PaginationDto) {
    return await this.service.pagination(req, data)
  }

  @ApiOperation({ summary: 'Tạo PNK' })
  @Post('create_data')
  async createData(@Req() req: IRequest, @Body() data: InboundCreateDto) {
    return await this.service.createData(data, req)
  }

  @ApiOperation({ summary: 'Tìm số lô của sản phẩm theo hạn sử dụng' })
  @Post('find_product_lot_number_by_expiry_date')
  async findProductLotNumberByExpiryDate(@Body() data: { productId: string; expiryDate: Date }) {
    return await this.service.findProductLotNumberByExpiryDate(data)
  }

  @ApiOperation({ summary: 'Sửa PNK' })
  @Post('update_data')
  async updateData(@Req() req: IRequest, @Body() data: InboundUpdateDto) {
    return await this.service.updateData(data, req)
  }

  @ApiOperation({ summary: 'Duyệt PNK' })
  @Post('approve_data')
  async approveData(@Req() req: IRequest, @Body() data: InboundtApproveDto) {
    return await this.service.approveData(data, req)
  }

  @ApiOperation({ summary: 'Hủy PNK' })
  @Post('cancel_data')
  async cancelData(@Req() req: IRequest, @Body() data: InboundtApproveDto) {
    return await this.service.cancelData(data, req)
  }

  @ApiOperation({ summary: 'Chi tiết PNK' })
  @Post('detail')
  async findDetail(@Req() req: IRequest, @Body() data: { id: string }) {
    return await this.service.findDetail(req, data.id)
  }

  @ApiOperation({ summary: 'Cập nhật mô tả PNK' })
  @Post('update_inbound_description')
  async updateInboundDescription(@Body() data: InboundtUpdateDescriptionDto, @CurrentUser() user: UserDto, @Req() req: IRequest) {
    return await this.service.updateInboundDescription(data, req, user)
  }

  @ApiOperation({ summary: 'Lấy giá mua, giá bán của sản phẩm trong PNK gần nhất' })
  @Post('find_product')
  async findProduct(@Body() data: { productId: string }) {
    return await this.service.findProduct(data.productId)
  }

  @Post('export_excel')
  async exportExcel(@Req() req: IRequest, @Body() data: PaginationDto, @CurrentUser() user: UserDto) {
    return await this.service.exportExcel(req, data, user)
  }

  @ApiOperation({ summary: 'Tìm kiếm' })
  @Post('find')
  async find(@Body() data: FilterIdDto, @Req() req: IRequest) {
    return await this.service.find(data, req)
  }

  @ApiOperation({ summary: 'Load dữ liệu select box ' })
  @Post('load_data_selectbox')
  async findDataSelectBox(@Body() data: FilterIdDto) {
    return await this.service.findDataSelectBox(data)
  }

  @ApiOperation({ summary: 'Cập nhật phân bổ chi phí cho phiếu nhập kho' })
  @Post('update_cost_allocation')
  async updateCostAllocation(@Body() data: InboundtApproveDto, @CurrentUser() user: UserDto, @Req() req: IRequest) {
    return await this.service.updateCostAllocation(data, req, user)
  }

  @ApiOperation({ summary: 'CHi tiết phiếu nhập kho' })
  @Post('detail_inbound')
  async findInbondDetail(@Body() data: { id: string }) {
    return await this.service.findInbondDetail(data.id)
  }

  @ApiOperation({ summary: 'DS PNK phân trang' })
  @Post('pagination_history')
  async paginationHistory(@Req() req: IRequest, @Body() data: PaginationDto) {
    return await this.service.paginationHistory(req, data)
  }

  @ApiOperation({ summary: 'DS PNK NCC phân trang' })
  @Post('pagination_history_mbc')
  async paginationHistoryMBC(@Req() req: IRequest, @Body() data: PaginationDto) {
    return await this.service.paginationHistoryMBC(req, data)
  }

  @ApiOperation({ summary: 'Sửa PNK' })
  @Post('update_data_approved')
  async updateDataApproved(@Req() req: IRequest, @Body() data: InboundUpdateDto, @CurrentUser() user: UserDto) {
    return await this.service.updateDataApproved(data, req, user)
  }

  @Post('print')
  public async print(@Body() data: string[], @Req() req: IRequest) {
    return await this.service.print(data, req)
  }

  @ApiOperation({ summary: 'Tìm số lô của sản phẩm theo hạn sử dụng' })
  @Post('find_product_transport_type')
  async findProductByTransportType(@Body() data: { productId: string; transportType: string }) {
    return await this.service.findProductByTransportType(data)
  }
}
