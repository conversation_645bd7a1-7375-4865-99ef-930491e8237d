import { Body, Controller, Post, Req, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { Request as IRequest } from 'express'
// import { surveyAuthApiHelper } from '../../helpers'
import { ApeAuthGuard } from '../common/guards'
import { SurveyCreateDto } from './dto/surveyCreate.dto'
import { SurveyUpdateDto } from './dto/surveyUpdate.dto'
import { SurveyService } from './survey.service'
import { SendAllNotifyDto, SendNotifyDto } from './dto/sendNotify.dto'
import { CurrentUser } from '../../common/decorators'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { JwtAuthGuard } from '../../common/guards'

@UseGuards(ApeAuthGuard)
@ApiBearerAuth()
@ApiTags('Survey')
@Controller('survey')
export class SurveyController {
  constructor(private service: SurveyService) {}

  @ApiOperation({ summary: 'Danh sách phân trang' })
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Load all phiếu khảo sát' })
  @Post('find_all')
  public async findAll(@Body() data: any) {
    return await this.service.findAll(data)
  }

  @ApiOperation({ summary: 'Tạo mới mẫu khảo sát' })
  @UseGuards(JwtAuthGuard)
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: SurveyCreateDto, @Req() req: IRequest) {
    return await this.service.createData(user, data, req)
  }

  @ApiOperation({ summary: 'Chỉnh sửa mới mẫu khảo sát' })
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: SurveyUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Chỉnh sửa trạng thái hoạt động phiếu khảo sát' })
  @Post('update_active')
  public async updateActive(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateActive(user, data)
  }

  @ApiOperation({ summary: 'Phân trang theo tháng năm' })
  @Post('find_by_month_year')
  public async findByMonthYear(@CurrentUser() user: UserDto, @Body() data: { month: number; year: number }) {
    return await this.service.findByMonthYear(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái phiếu khảo sát Huỷ' })
  @UseGuards(JwtAuthGuard)
  @Post('update_status_cancel')
  public async updateStatusCancel(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateStatusCancel(user, data)
  }
  @ApiOperation({ summary: 'Cập nhật trạng thái phiếu khảo sát Ngưng hoạt động' })
  @UseGuards(JwtAuthGuard)
  @Post('update_status_pending')
  public async updateStatusPendind(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateStatusPending(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái phiếu khảo sát Mới tạo(mở lại)' })
  @UseGuards(JwtAuthGuard)
  @Post('update_status_reopen')
  public async updateStatusReOpen(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateStatusReOpen(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái phiếu khảo sát' })
  @UseGuards(JwtAuthGuard)
  @Post('update_status_complete')
  public async updateStatusComplete(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateStatusComplete(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái phiếu khảo sát' })
  @UseGuards(JwtAuthGuard)
  @Post('update_status_doing')
  public async updateStatusDoing(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateStatusDoing(user, data)
  }

  @ApiOperation({ summary: 'chi tiết' })
  @Post('find_detail')
  public async findDetail(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.findDetail(user, data)
  }

  @ApiOperation({ summary: 'tạo phiếu khảo sát bằng file import excel' })
  @UseGuards(JwtAuthGuard)
  @Post('create_data_by_excel')
  public async createDataByExcel(@CurrentUser() user: UserDto, @Body() data: SurveyCreateDto[]) {
    return await this.service.createDataByExcel(user, data)
  }
}
