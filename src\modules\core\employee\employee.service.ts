import { ConflictException, Injectable } from '@nestjs/common'
import { Like, Not, In, IsNull } from 'typeorm'
import { Request as IRequest } from 'express'
import { EmployeeImportDto, EmployeeUpdateDto, EmployeeCreateDto } from './dto'
import { AuthService } from '../auth/auth.service'
import { UserService } from '../user/user.service'
import { DepartmentRepository, EmployeeRepository, UserRepository } from '../../../repositories'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { BrandEntity, DepartmentEntity, EmployeeEntity, UserEntity } from '../../../entities'
import { CREATE_SUCCESS, enumData, ERROR_NOT_FOUND_DATA, IMPORT_SUCCESS, UPDATE_SUCCESS } from '../../../constants'
import { UpdatePasswordDto } from '../auth/dto'
import { coreHelper } from '../../../helpers'
import { ActionLogService } from '../actionLog/actionLog.service'

@Injectable()
export class EmployeeService {
  constructor(
    private readonly repo: EmployeeRepository,
    private departmentRepository: DepartmentRepository,
    private readonly authService: AuthService,
    private readonly userService: UserService,
    private readonly actionService: ActionLogService,
    private readonly userRepo: UserRepository,
  ) { }

  /** lấy ds nhân viên */
  public async find(user?: UserDto) {
    return await this.repo.find({
      where: { isDeleted: false },
    })
  }

  /** lấy một dữ liệu chi tiết */
  public async findDetail(data: FilterOneDto, user?: UserDto) {
    const res: any = await this.repo.findOne({
      where: { id: data.id },
    })

    return res
  }
  public async loadData(userLogin?: UserDto) {
    return await this.repo.find({
      where: { isDeleted: false },
      order: { name: 'ASC' },
    })
  }

  public async loadDataByDepartment(data: FilterOneDto, userLogin?: UserDto) {
    return await this.repo.find({
      where: { isDeleted: false, departmentId: data.id },
      order: { name: 'ASC' },
    })
  }

  /** Tạo mới nhân viên */
  // , req: IRequest
  async createData(data: EmployeeCreateDto, userLogin?: UserDto) {
    return this.repo.manager.transaction(async (transaction) => {
      const employeeRepo = transaction.getRepository(EmployeeEntity)
      let code = ''
      const objData = await employeeRepo.findOne({
        where: { code: Like(`${code}___`) },
        order: { createdAt: 'DESC' },
      })

      const exist = await employeeRepo.findOneBy({
        code: code,
      })
      if (exist) throw new ConflictException(`Mã NV [${data.code}] đã được sử dụng!`)
      if (data.phone && data.email) {
        if (data.phone.slice(0, 2) === '84') {
          data.phone = data.phone.slice(2)
        } else if (data.phone.slice(0, 1) === '0') {
          data.phone = data.phone.slice(1)
        }
        data.phone = '0' + data.phone
        const exist = await this.repo.findOne({
          where: [{ phone: data.phone }, { email: data.email }],
          select: { phone: true, email: true },
        })
      }


      const employeeCreate = new EmployeeEntity()
      employeeCreate.code = data.code
      employeeCreate.name = data.name
      employeeCreate.phone = data.phone
      employeeCreate.email = data.email
      employeeCreate.departmentId = data.departmentId
      employeeCreate.address = data.address
      employeeCreate.cityId = data.cityId
      employeeCreate.districtId = data.districtId
      employeeCreate.wardId = data.wardId
      employeeCreate.description = data.description
      employeeCreate.createdBy = userLogin?.id
      employeeCreate.createdAt = new Date()
      const newEmployee = await this.repo.save(employeeCreate);
      const user = await this.authService.createUserSurvey(
        userLogin,
        { username: data.username, password: data.password, email: data.email, employeeId: newEmployee.id },
        data?.isAdmin ? enumData.UserType.Admin.code : enumData.UserType.Employee.code);
      newEmployee.userId = user.id
      await this.repo.save(newEmployee);
      return { message: CREATE_SUCCESS }
    })
  }

  /** Cập nhật nhân viên */
  // , req: IRequest
  public async updateData(data: EmployeeUpdateDto, user?: UserDto) {
    return await this.repo.manager.transaction(async (trans) => {
      const employee: any = await trans.getRepository(EmployeeEntity).findOne({ where: { id: data.id }, relations: { user: true } })
      if (!employee) throw new Error(ERROR_NOT_FOUND_DATA)
      if (data.phone.slice(0, 2) === '84') {
        data.phone = data.phone.slice(2)
      } else if (data.phone.slice(0, 1) === '0') {
        data.phone = data.phone.slice(1)
      }
      data.phone = '0' + data.phone
      const lstTask = []
      if (employee.phone != data.phone) {
        lstTask.push(
          trans.getRepository(EmployeeEntity).findOne({
            where: [{ phone: data.phone }],
            select: { phone: true },
          }),
        )
      }
      if (employee.email != data.email) {
        lstTask.push(
          trans.getRepository(EmployeeEntity).findOne({
            where: [{ email: data.email }],
            select: { email: true },
          }),
        )
      }
      const [existPhone, existEmail] = await Promise.all(lstTask)
      // if (existPhone?.phone && employee.phone != existPhone.phone && existPhone?.phone === data.phone) throw new ConflictException(ERROR_PHONE_TAKEN)
      // if (existEmail?.email && employee.email != existEmail.email && existEmail?.email === data.email) throw new ConflictException(ERROR_EMAIL_TAKEN)

      const isChangeEmail = data.email != employee.email
      const roleChange = employee.__user__.type != (data?.isAdmin ? enumData.UserType.Admin.code : enumData.UserType.Employee.code)
      employee.name = data.name
      employee.phone = data.phone
      employee.email = data.email
      employee.description = data.description
      employee.departmentId = data.departmentId
      employee.address = data.address
      employee.cityId = data.cityId
      employee.districtId = data.districtId
      employee.wardId = data.wardId
      employee.updatedBy = data.updatedBy
      employee.updatedAt = new Date()
      await trans.getRepository(EmployeeEntity).save(employee)
      if (isChangeEmail) {
        // await surveyAuthApiHelper.updateEmail_Admin(req, employee.userId, data.email)
        // let userEntity = await trans.getRepository(UserEntity).findOne({ where: { id: employee.userId } })
        await this.authService.updateEmailSurvey_Admin(user, { userId: employee.userId, newEmail: data.email })
      }

      if (roleChange) {
        await this.userRepo.update({ employeeId: data.id }, { type: (data?.isAdmin ? enumData.UserType.Admin.code : enumData.UserType.Employee.code) })
      }

      return { message: UPDATE_SUCCESS }
    })
  }

  /**
   * Hàm phân trang
   * @param relations mối quan hệ tới những bảng khác trong db
   * @param data.where các điều kiện tìm kiếm
   * @param data.order thứ tự sắp xếp kết quả trả về theo 1 cột nào đó
   * @param data.skip dùng để phẩn trang, chỉ định vị trí bắt đầu của kết quả trả về
   * @param data.take dùng để phẩn trang, giới hạn kết quả trả về
   * @returns
   */
  public async pagination(data: PaginationDto, user?: UserDto, lstUserId?: Array<string>) {
    let whereCon: any = {}
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
      relations: { department: true, user: true },
    })

    if (res[0].length == 0) return res
    for (const item of res[0]) {
      item.departmentName = item.__department__?.name
      item.username = item.__user__?.username
      item.isAdmin = item.__user__?.type === enumData.UserType.Admin.code
      delete item.__department__
      delete item.__user__
    }
    return res
  }

  /** Cập nhật trạng thái kích hoạt */
  public async updateActive(data: FilterOneDto, req: IRequest, user?: UserDto) {
    // return await this.repo.manager.transaction(async (trans) => {
    //   const repo = trans.getRepository(EmployeeEntity)
    //   const employee: any = await repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    //   if (!employee) throw new Error(ERROR_NOT_FOUND_DATA)
    //   if (!employee.userId) throw new Error('Nhân viên chưa có tài khoản!')
    //   const foundDepart: any = await this.departmentRepository.findOne({
    //     where: {
    //       id: employee.departmentId,
    //       isDeleted: false,
    //     },
    //   })
    //   if (!foundDepart) {
    //     throw new Error(`Phòng ban không tồn tại hoặc đã bị ngưng hoạt động`)
    //   }
    //   const newIsDeleted = !employee.isDeleted
    //   await this.authService.updateStatusUserSurvey(user, {
    //     companyId: user.companyId,
    //     userId: employee.userId,
    //     isDeleted: newIsDeleted,
    //   })
    //   await repo.update(employee.id, { isDeleted: newIsDeleted, updatedBy: user.id, updatedAt: new Date() })
    //   return { message: UPDATE_ACTIVE_SUCCESS }
    // })
  }

  /** Cập nhật mật khẩu */
  public async updatePassword(data: UpdatePasswordDto, req: IRequest, user?: UserDto) {
    // await this.authService.updatePasswordSurvey_Admin(user, { })
    // return { message: UPDATE_SUCCESS }
  }

  /** Thêm nhân viên bằng file excel */
  async createDataByExcel(data: EmployeeImportDto[], user: UserDto) {
    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(EmployeeEntity)
      const userRepo = trans.getRepository(UserEntity)
      const departmentRepo = trans.getRepository(DepartmentEntity)
      const brandRepo = trans.getRepository(BrandEntity)

      const dictUser: any = {}
      {
        const lstUsername = coreHelper.selectDistinct(data, 'username')
        const lstUser = await userRepo.find({ where: { username: In(lstUsername), isDeleted: false } })
        lstUser.forEach((c) => (dictUser[c.username] = c))
      }

      const dictDept: any = {}
      {
        const lstCode = coreHelper.selectDistinct(data, 'departmentCode')
        const lstDept = await departmentRepo.find({ where: { code: In(lstCode), isDeleted: false } })
        lstDept.forEach((c) => (dictDept[c.code] = c))
      }

      const dictEmp: any = {}
      {
        const lstCode = coreHelper.selectDistinct(data, 'managerCode')
        const lstManager = await repo.find({ where: { code: In(lstCode), isDeleted: false } })
        lstManager.forEach((c) => (dictEmp[c.code] = c))
      }

      const dictBrand: any = {}
      {
        const lstBarnd = await brandRepo.find({ where: { isDeleted: false, parentId: IsNull() } })
        lstBarnd.forEach((c) => (dictBrand[c.code] = c))
      }

      for (const [idx, item] of data.entries()) {
        if (item.departmentCode) {
          if (!dictDept[item.departmentCode]) throw new Error(`[ Dòng ${idx + 1} - Mã Phòng ban [${item.departmentCode}] không tồn tại ]`)
        }

        if (item.managerCode) {
          if (!dictEmp[item.managerCode]) throw new Error(`[ Dòng ${idx + 1} - Mã Nhân Viên Quản lý [${item.managerCode}] không tồn tại ]`)
          if (dictEmp[item.managerCode].departmentId !== dictDept[item.departmentCode].id)
            throw new Error(`[ Dòng ${idx + 1} - Mã Nhân Viên Quản lý [${item.managerCode}] không nằm trong phòng ban [${item.departmentCode}] ]`)
        }
        if (item.username && dictUser[item.username]) throw new Error(`[ Dòng ${idx + 1} - Tên tài khoản [${item.username}] đã được sử dụng ]`)
        if (item.lstBrandCode) {
          const lstBrandNew = item.lstBrandCode.split(',')
          for (let item of lstBrandNew) {
            if (!dictBrand[item]) throw new Error(`[ Dòng ${idx + 1} - Mã Thương Hiệu [${item}] không tồn tại ]`)
          }
        }
      }

      const dicCode: any = {}
      {
        const listEmp: any[] = await repo.find({
          where: { isDeleted: false },
          select: { id: true, code: true },
        })
        listEmp.forEach((c) => (dicCode[c.code] = c))
      }
      const dicCodeFile: any = {}
      for (const [idx, item] of data.entries()) {
        if (dicCodeFile[item.code]) throw new Error(`[ Dòng ${idx + 1} - Mã Nhân Viên [${item.code}] trùng với dòng ${dicCodeFile[item.code]} ]`)
        if (dicCode[item.code]) throw new Error(`[ Dòng ${idx + 1} - Mã Nhân Viên [${item.code}] đã được sử dụng ]`)

        const empEntity = new EmployeeEntity()
        empEntity.code = item.code
        empEntity.name = item.name
        empEntity.departmentId = dictDept[item.departmentCode]?.id
        empEntity.managerId = dictEmp[item.managerCode]?.id
        empEntity.isMobile = item.isMobile
        empEntity.email = item.email
        empEntity.phone = item.phone
        empEntity.description = item.description
        empEntity.createdAt = new Date()
        // empEntity.createdBy = user.id
        const createdEntity = await repo.save(empEntity)

        if (item.username) {
          const userNew = new UserEntity()
          // userNew.createdBy = user.id
          userNew.username = item.username
          userNew.password = item.password || '123456'
          userNew.type = enumData.UserType.Employee.code
          userNew.employeeId = createdEntity.id
          const newUserEntity: any = userRepo.create({ ...userNew })
          const userCreated = await userRepo.save(newUserEntity)
          await repo.update(createdEntity.id, { userId: userCreated.id })
        }
      }
    })
    return { message: IMPORT_SUCCESS }
  }
}
