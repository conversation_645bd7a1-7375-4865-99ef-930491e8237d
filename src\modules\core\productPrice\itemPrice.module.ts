import { Module } from '@nestjs/common'
import { ItemPriceService } from './itemPrice.service'
import { ItemPriceController } from './itemPrice.controller'
import { InboundDetailRepository, ItemPriceRepository, ItemRepository } from '../../../repositories'
import { TypeOrmExModule } from '../../../typeorm'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([ItemPriceRepository, ItemRepository, InboundDetailRepository])],
  controllers: [ItemPriceController],
  providers: [ItemPriceService],
  exports: [ItemPriceService],
})
export class ItemPriceModule {}
