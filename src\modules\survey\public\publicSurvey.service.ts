import { ConflictException, Injectable, NotFoundException } from '@nestjs/common'
import {
  QuestionListDetailRepository,
  QuestionRepository,
  SurveyMemberRepository,
  SurveyQuestionRepository,
  SurveyRepository,
  TopicRepository,
} from '../../../repositories'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { Between, In, IsNull, LessThanOrEqual, Like, MoreThanOrEqual, Not } from 'typeorm'
import {
  CREATE_SUCCESS,
  DELETE_SUCCESS,
  enumData,
  ERROR_CODE_TAKEN,
  ERROR_NOT_FOUND_DATA,
  UPDATE_ACTIVE_SUCCESS,
  UPDATE_SUCCESS,
} from '../../../constants'
import { coreHelper } from '../../../helpers'
import { QuestionEntity, QuestionListDetailEntity, SurveyEntity, SurveyHistoryEntity, SurveyQuestionEntity, TopicEntity } from '../../../entities'
import { SurveyUpdateDto } from '../survey/dto/surveyUpdate.dto'
import { NotifyService } from '../notify/notify.service'
import { nanoid } from 'nanoid'
import { SurveyCreateDto } from '../survey/dto/surveyCreate.dto'
import { Request as IRequest } from 'express'
import { TopicCreateDto, TopicCreateExcelDto, TopicUpdateDto } from '../topic/dto'
import {
  QuestionCreateDto,
  QuestionCreateMasterDataDto,
  QuestionListDetailCreateDto,
  QuestionListDetailUpdateDto,
  QuestionUpdateDto,
} from '../question/dto'
import { SurveyAnswerQuestionDto, SurveyFilterOneDto } from '../mobileApp/dto'
import { omsApiHelper } from '../../../helpers/omsApiHelper'

@Injectable()
export class PublicSurveyService {
  constructor(
    private readonly surveyRepo: SurveyRepository,
    private readonly topicRepo: TopicRepository,
    private readonly surveyQuestionRepo: SurveyQuestionRepository,
    private readonly notifyService: NotifyService,
    private readonly surveyMemberRepo: SurveyMemberRepository,
    private readonly questionRepo: QuestionRepository,
    private readonly questionListDetailRepo: QuestionListDetailRepository,
  ) {}

  /** Region survey */

  public async surveyFindDetail(user: UserDto, data: FilterOneDto) {
    const entity: any = await this.surveyRepo.findOne({
      where: { id: data.id },
      relations: { members: true, histories: true, questions: true },
    })
    if (!entity) throw new Error('Phiếu khảo sát không tồn tại')
    let lstStatus = coreHelper.convertObjToArray(enumData.SurveyStatus)
    const statusItem = lstStatus.find((x) => x.code === entity.status)
    if (statusItem) {
      entity.statusName = statusItem.name
      entity.statusColor = statusItem.color
    }
    return entity
  }

  public async surveyPagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = {}
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.createdAt) whereCon.createdAt = data.where.createdAt
    if (
      data.where.status === enumData.SurveyStatus.New.code ||
      data.where.status === enumData.SurveyStatus.Doing.code ||
      data.where.status === enumData.SurveyStatus.Complete.code ||
      data.where.status === enumData.SurveyStatus.Cancel.code
    ) {
      whereCon.status = data.where.status
    }

    if (data.where.timeStart && data.where.timeEnd) {
      whereCon.timeStart = MoreThanOrEqual(new Date(data.where.timeStart))
      whereCon.timeEnd = LessThanOrEqual(new Date(data.where.timeEnd))
    } else if (data.where.timeStart) {
      whereCon.timeStart = MoreThanOrEqual(new Date(data.where.timeStart))
    } else if (data.where.timeEnd) {
      whereCon.timeEnd = LessThanOrEqual(new Date(data.where.timeEnd))
    }

    const resSurvey: any = await this.surveyRepo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      relations: { questions: true, members: true },
      order: { createdAt: 'DESC' },
    })
    let lstStatus = coreHelper.convertObjToArray(enumData.SurveyStatus)
    for (const item of resSurvey[0]) {
      const findTopic = item.__questions__.find((question: any) => question.isDeleted === false)
      item.topicCount = findTopic ? 1 : 0
      item.memberCount = item.__members__.filter((member: any) => member.isDeleted === false).length
      item.memberDoneCount = item.__members__.filter(
        (member: any) => member.isDeleted === false && member.status === enumData.SurveyMemberStatus.Done.code,
      ).length
      item.lstMember = item.__members__.map((x) => x.userId)
      item.topicId = findTopic?.topicId
      item.showBtnCancel = item.status === enumData.SurveyStatus.New.code
      item.showBtnDoing = item.status === enumData.SurveyStatus.New.code
      item.showBtnComplete = item.status === enumData.SurveyStatus.Doing.code
      item.showBtnEdit = item.status === enumData.SurveyStatus.New.code
      item.showBtnEmployee = true
      item.showBtnDone = item.status === enumData.SurveyStatus.Complete.code
      delete item.__questions__
      delete item.__members__
      const statusItem = lstStatus.find((x) => x.code === item.status)
      if (statusItem) {
        item.statusName = statusItem.name
        item.statusColor = statusItem.color
      }
    }
    return resSurvey
  }

  public async surveyFindAll(data: any) {
    let whereCon: any = { isDeleted: false }
    if (data.where.topicId) {
      whereCon.questions = {}
      whereCon.questions.topicId = data.where.topicId
    }
    if (data.where.status) {
      whereCon.status = data.where.status
    }
    return await this.surveyRepo.find({
      where: whereCon,
      relations: {
        questions: true,
      },
    })
  }

  /** Tạo mới phiếu khảo sát */
  public async surveyCreateData(user: UserDto, data: SurveyCreateDto, req: IRequest) {
    const date = new Date()
    const now = new Date([date.getFullYear(), date.getMonth() + 1, date.getDate()].join('-')).getTime()
    if (now > new Date(data.timeStart).getTime() || now > new Date(data.timeEnd).getTime()) {
      throw new Error('Không thể chọn thời gian nhỏ hơn hiện tại')
    }
    if (new Date(data.timeStart).getTime() > new Date(data.timeEnd).getTime()) {
      throw new Error('Thời gian bắt đầu không thể nhỏ hơn thời gian kết thúc')
    }
    let lstNotify = []
    await this.surveyRepo.manager.transaction(async (trans) => {
      const survey = new SurveyEntity()
      survey.code = 'SURVEY_' + `${nanoid()}`
      survey.name = data.name
      survey.timeStart = data.timeStart
      survey.timeEnd = data.timeEnd
      survey.description = data.description
      survey.createdAt = new Date()
      survey.status = enumData.SurveyStatus.New.code
      let newEntity = await trans.getRepository(SurveyEntity).save(survey)

      if (data.topicId) {
        const topicItem = await trans
          .getRepository(TopicEntity)
          .findOne({ where: { id: data.topicId, isDeleted: false }, relations: { questions: { questionlistDetails: true } } })
        if (!topicItem) throw new Error('Chủ đề không còn tồn tại!. Vui lòng kiểm tra lại.')
        let lstTopicQuestion = await topicItem.questions
        if (lstTopicQuestion.length === 0) throw new Error('Chủ đề chưa được thiết lập câu hỏi !. Vui lòng kiểm tra lại.')
        for (const qs of lstTopicQuestion) {
          const surveyQuestion = new SurveyQuestionEntity()
          surveyQuestion.topicId = topicItem.id
          surveyQuestion.questionId = qs.id
          surveyQuestion.surveyId = newEntity.id
          surveyQuestion.survey = Promise.resolve(newEntity)
          surveyQuestion.createdAt = new Date()
          await trans.getRepository(SurveyQuestionEntity).save(surveyQuestion)
        }
      }
    })
    await this.notifyService.createData(user, lstNotify, req)
    return { message: CREATE_SUCCESS }
  }

  /** Cập nhật phiếu khảo sát */
  public async surveyUpdateData(user: UserDto, data: SurveyUpdateDto) {
    await this.surveyRepo.manager.transaction(async (trans) => {
      const entity = await trans.getRepository(SurveyEntity).findOne({ where: { isDeleted: false, id: data.id } })
      if (!entity) throw new Error('Phiếu khảo sát không còn tồn tại!')
      if (entity.status === enumData.SurveyStatus.Cancel.code || entity.status === enumData.SurveyStatus.Complete.code)
        throw new Error('Phiếu khảo sát đã bị hủy hoặc hoàn thành. Không thể cập nhật được. Vui lòng kiểm tra lại !')

      if (entity.status === enumData.SurveyStatus.Doing.code)
        throw new Error('Phiếu khảo sát thực hiện. Không thể cập nhật được. Vui lòng kiểm tra lại !')

      entity.name = data.name
      entity.description = data.description
      entity.timeStart = data.timeStart
      entity.timeEnd = data.timeEnd
      entity.updatedAt = new Date()
      entity.updatedBy = user.id
      await trans.getRepository(SurveyEntity).save(entity)
    })

    return { message: UPDATE_SUCCESS }
  }

  /** Cập nhật trạng thái hoạt động - ngưng hoạt động */
  public async surveyUpdateActive(user: UserDto, data: FilterOneDto) {
    const lstTask = [
      this.surveyRepo.findOne({ where: { id: data.id } }),
      this.surveyQuestionRepo.findOne({ where: { surveyId: data.id, isDeleted: false } }),
    ]
    const [foundSurvey, foundQuestion, foundMember] = await Promise.all(lstTask)
    if (!foundSurvey) {
      throw new Error('Phiếu khảo sát không tồn tại')
    }
    if (foundQuestion) {
      throw new Error('Không thể ngưng hoạt động phiếu khảo sát đã có câu hỏi')
    }
    const newIsDeleted = !foundSurvey.isDeleted
    await this.surveyRepo.update(data.id, { isDeleted: newIsDeleted, updatedAt: new Date() })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  /** End region */

  /** Region survey question */
  public async getSurveyStatsByMonth(from: any, to: any) {
    const whereCon: any = {}
    whereCon.surveyMemberId = Not(IsNull())
    whereCon.createdAt = Between(from, to)
    const queryBuilder = this.surveyQuestionRepo
      .createQueryBuilder('surveyQuestion')
      .select(['DATE(surveyQuestion.createdAt) as date', 'COUNT(DISTINCT surveyQuestion.surveyMemberId) as count'])
      .where(whereCon)
      .groupBy('DATE(surveyQuestion.createdAt)')
    const results = await queryBuilder.getRawMany()
    return results.map((row) => ({
      date: row.date,
      count: parseInt(row.count),
    }))
  }

  public async surveyQuestionPagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = {}
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    whereCon.surveyMemberId = Not(IsNull())

    // Lấy ra các phiếu khảo sát
    const queryBuilder = this.surveyQuestionRepo
      .createQueryBuilder('surveyQuestion')
      .select(['surveyQuestion.surveyId as surveyId', 'surveyQuestion.surveyMemberId as surveyMemberId', 'surveyQuestion.createdAt as createdAt'])
      .where(whereCon)
      .skip(data.skip)
      .take(data.take)
      .groupBy('surveyQuestion.surveyId')
      .addGroupBy('surveyQuestion.surveyMemberId')
      .addGroupBy('surveyQuestion.createdAt')

    const results = await queryBuilder.getRawMany()
    // Đếm số lượng phiếu khảo sát
    let total = 0
    let lstSurveyId = results.map((x) => x.surveyid)
    let lstSurvey = await this.surveyRepo.find({
      where: { id: In(lstSurveyId), questions: { value: IsNull() } },
      relations: { questions: true },
      order: { createdAt: 'DESC' },
    })
    let mapSurvey = lstSurvey.convertToMap((x) => x.id)
    let lstSurveyMemberId = results.map((x) => x.surveymemberid)
    let lstSurveyMember = await this.surveyMemberRepo.find({ where: { id: In(lstSurveyMemberId) } })
    let mapSurveyMember = lstSurveyMember.convertToMap((x) => x.id)
    let lstSurveyQuestion = await Promise.all(lstSurvey.map(async (x) => await x.questions))
    let lstQuestionIdTemp = lstSurveyQuestion.map((x) => x.map((y) => y.questionId))
    let lstQuestionId = lstQuestionIdTemp.flat()
    let lstQuestion = await this.questionRepo.find({ where: { id: In(lstQuestionId) }, relations: { childs: true } })
    let mapQuestion = lstQuestion.convertToMap((x) => x.id)
    // Lặp qua các phiếu khảo sát
    for (let answer of results as any) {
      answer.createdAt = answer.createdat
      delete answer.createdat
      answer.surveyMemberId = answer.surveymemberid
      delete answer.surveymemberid
      answer.surveyId = answer.surveyid
      delete answer.surveyid
      total += 1
      // Lấy thông tin survey
      let survey = mapSurvey.get(answer.surveyId)
      let question = await survey.questions
      answer.survey = survey
      answer.survey.questions = question
      answer.survey.listQuestion = question
      delete answer.survey.__questions__
      answer.surveyMember = mapSurveyMember.get(answer.surveyMemberId)
      let firstElement = true
      let lstChildResult = []
      //Lặp qua các câu hỏi có trong phiếu khảo sát
      for (let q of answer.survey.listQuestion) {
        // Lấy thông tin câu hỏi cấp 1
        q.question = mapQuestion.get(q.questionId)
        if (q.question == null) continue
        let question = q.question
        // Lấy thông tin câu hỏi con
        let childs = await q.question.childs

        // Nếu có câu hỏi con
        if (childs.length > 0) {
          let lstQuestionChildId = childs.map((c) => c.id)
          lstChildResult = [...lstChildResult, ...lstQuestionChildId]
          let lstResult = await this.surveyQuestionRepo.find({
            where: {
              surveyMemberId: answer.surveyMemberId,
              questionId: In(lstQuestionChildId),
              surveyId: answer.surveyId,
              createdAt: answer.createdAt,
            },
            select: { value: true, questionId: true },
          })

          let mapResult = lstResult.convertToMap((x) => x.questionId)
          // Lặp qua từng câu hỏi con
          for (let c of childs as any) {
            // Lấy ra value của câu hỏi con
            let result = lstResult.filter((x) => x.questionId == c.id)
            if (result.length == 0) continue
            let list: any = []
            if (['Checkbox', 'List'].includes(c.type)) {
              // Lấy thông tin câu hỏi con
              let questionC = await this.questionRepo.findOne({
                where: { id: c.id },
                relations: { questionlistDetails: true },
              })

              // Lấy thông tin các questionListDetail của câu hỏi con
              list = await questionC.questionlistDetails
            }
            // Trường hợp câu hỏi con là checkbox
            if (c.type == 'Checkbox') {
              for (let rs of result) {
                const filteredDetails = list.filter((detail) => detail.value.toString() === rs.value)
                c.value.push(filteredDetails.map((x) => x.name))
              }
            } else {
              if (question.type == 'String') {
                c.value = mapResult.get(c.id).value[0]
              }
              if (question.type == 'List') {
                const filteredDetails = list.filter((detail) => detail.value.toString() === mapResult.get(c.id).value)
                c.value = filteredDetails[0].name
              }
            }
          }
        } else if (childs.length == 0) {
          let list = await question.questionlistDetails
          q.type = question.type
          if (question.type == 'Checkbox') {
            let result = await this.surveyQuestionRepo.findOne({
              where: { surveyMemberId: answer.surveyMemberId, questionId: q.question.id, createdAt: answer.createdAt },
              select: { value: true, questionId: true },
            })
            q.value = []
            for (let rs of result.value) {
              const filteredDetails = list.filter((detail) => detail.value.toString() === rs)
              q.value.push(filteredDetails[0].name)
            }
          } else {
            let result = await this.surveyQuestionRepo.findOne({
              where: { surveyMemberId: answer.surveyMemberId, questionId: q.question.id },
              select: { value: true, questionId: true },
            })
            const filteredDetails = list.filter((detail) => detail.value.toString() === result.value[0])
            if (result && question.type == 'String') {
              q.value = result.value[0]
            }
            if (result && question.type == 'List') {
              q.value = filteredDetails[0].name
            }
          }
        }
        if (firstElement) {
          answer.topic = await this.topicRepo.findOne({ where: { id: q.topicId } })
        }
        firstElement = false
        q.surveyMemberId = answer.surveyMemberId.id
      }
      if (answer.survey.listQuestion) answer.survey.listQuestion = answer.survey.listQuestion.filter((x) => !lstChildResult.includes(x.questionId))
    }
    return [results, total]
  }

  public async surveyQuestionFindAll(data: any) {
    return await this.surveyQuestionRepo.find({ where: { ...data } })
  }

  public async surveyQuestionFindQuestion(data: any) {
    let lstSurveyQuestion = await this.surveyQuestionRepo.find({ where: { surveyMemberId: IsNull(), value: IsNull(), surveyId: data.surveyId } })
    for (let surveyQ of lstSurveyQuestion as any) {
      let question = await this.questionRepo.findOne({
        where: { id: surveyQ.questionId },
        relations: { childs: { questionlistDetails: true }, questionlistDetails: true },
      })
      surveyQ.questionId = question
    }
    return lstSurveyQuestion
  }
  /** End region */

  /** Region topic */
  /** Hàm tìm kiếm */
  public async topicFind(data: { isDeleted?: boolean }) {
    let whereCon: any = {}
    if (data.isDeleted != undefined) whereCon.isDeleted = data.isDeleted
    const res = await this.topicRepo.find({ where: whereCon, order: { name: 'ASC' } })
    return res
  }

  /**
   * Hàm thêm mới chủ đề
   * @param user User Token
   * @param id Id của chủ đề
   * @param code Mã chủ đề
   * @param name Tên chủ đề
   * @param description Ghi chú
   * @returns message: Tạo mới thành công
   */
  public async topicCreateData(user: UserDto, data: TopicCreateDto) {
    const newEntity = this.topicRepo.create(data)
    newEntity.createdAt = new Date()
    //newEntity.createdBy = user.username
    await newEntity.save()

    return { message: CREATE_SUCCESS }
  }

  /**
   * Hàm cập nhật chủ đề
   * @param user User Token
   * @param id Id của chủ đề
   * @param code Mã chủ đề
   * @param name Tên chủ đề
   * @param description Ghi chú
   * @returns message: Cập nhật thành công
   */
  public async topicUpdateData(user: UserDto, data: TopicUpdateDto) {
    const entity = await this.topicRepo.findOne({ where: { id: data.id } })
    if (!entity) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    //entity.updatedBy = user.id
    entity.updatedAt = new Date()
    entity.name = data.name
    entity.description = data.description
    // entity.companyId = user.companyId
    await entity.save()

    return { message: UPDATE_SUCCESS }
  }

  public async topicFindWithSurvey(data: { isDeleted?: boolean; type?: string }) {
    let whereCon: any = {}
    if (data.isDeleted != undefined) whereCon.isDeleted = data.isDeleted
    if (data.type != undefined) whereCon.type = data.type
    let res: any = await this.topicRepo.find({ select: { name: true, id: true }, order: { createdAt: 'DESC' } })
    let list = []
    for (let r of res) {
      let survey = await this.surveyRepo.findOne({
        where: { questions: { topicId: r.id } },
        relations: { questions: true },
        order: { createdAt: 'DESC' },
      })
      if (survey) r.surveyId = survey.id
    }
    return res
  }

  /**
   * Hàm phân trang
   * @param relations mối quan hệ tới những bảng khác trong db
   * @param where các điều kiện tìm kiếm
   * @param order thứ tự sắp xếp kết quả trả về theo 1 cột nào đó
   * @param skip dùng để phẩn trang, chỉ định vị trí bắt đầu của kết quả trả về
   * @param take dùng để phẩn trang, giới hạn kết quả trả về
   * @returns
   */
  public async topicPagination(data: PaginationDto) {
    const whereCon: any = {}
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.categoriesId) whereCon.categoriesId = data.where.categoriesId

    const res: any = await this.topicRepo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
      relations: { questions: true },
    })
    for (const item of res[0]) {
      item.numQuestion = item.__questions__.length || 0
      delete item.__questions__
    }
    return res
  }

  /**
   * Hàm cập nhật trạng thái hoạt động của chủ đề
   * @param user User Token
   * @param idStr id của chủ đề
   * @returns message: Cập nhật trạng thái thành công
   */
  public async topicUpdateActive(user: UserDto, idStr: string) {
    const entity = await this.topicRepo.findOne({ where: { id: idStr } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    await this.topicRepo.update(idStr, { isDeleted: !entity.isDeleted, updatedBy: user.id, updatedAt: new Date() })
    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  /**
   * Hàm cập nhật chủ đề bẳng excel
   */
  public async topicCreateDataByExcel(user: UserDto, data: TopicCreateExcelDto[]) {
    await this.topicRepo.manager.transaction('READ UNCOMMITTED', async (trans) => {
      const lstTask = []
      const repo = trans.getRepository(TopicEntity)
      // danh sách chủ đề
      const dictTopic: any = {}
      {
        const lstTopic = await repo.find({ where: { isDeleted: false } })
        lstTopic.forEach((c) => (dictTopic[c.code] = c.id))
      }

      for (const item of data) {
        if (dictTopic[item.code]) throw new Error(`Mã chủ đề [${item.code}] đã có trong hệ thống`)
      }

      const setExcelTopic = new Set()
      data.forEach((topic) => {
        if (setExcelTopic.has(topic.code) === true) {
          throw new Error(`Mã chủ đề [${topic.code}] lặp lại trong file excel`)
        }
        setExcelTopic.add(topic.code)
      })

      for (let item of data) {
        const topic = new TopicEntity()
        topic.createdBy = user.id
        topic.createdAt = new Date()
        topic.code = item.code
        topic.name = item.name
        topic.description = item.description
        const task = repo.save(topic)
        lstTask.push(task)
      }
      await Promise.all(lstTask)
    })
    return { message: CREATE_SUCCESS }
  }
  /** End region */

  /** Region question */
  /**
   * Tìm câu hỏi theo id
   * @param user
   * @param questionId
   * @returns
   */
  public async questionFindOne(user: UserDto, data: { questionId: string }) {
    return await this.questionRepo.findOne({
      where: {
        // isDeleted: false,
        // companyId: user.companyId,
        id: data.questionId,
      },
      relations: {
        questionlistDetails: true,
      },
    })
  }

  public async questionCreateData(user: UserDto, data: QuestionCreateDto) {
    const topic = await this.topicRepo.findOne({ where: { id: data.topicId, isDeleted: false } })
    if (!topic) throw new ConflictException('Chủ đề này đã bị xóa hoặc ngừng hoạt động. vui lòng kiểm tra lại')

    const code = topic.code + '_' + `${nanoid()}`
    data.code = code
    data.topicId = topic.id

    const newEntity = this.questionRepo.create(data)
    //newEntity.createdBy = user.id
    newEntity.createdAt = new Date()
    if (newEntity.sort == null) throw new Error('Thứ tự sắp xếp không thể để trống')
    await this.questionRepo.save(newEntity)

    return { message: CREATE_SUCCESS }
  }

  public async questionUpdateData(user: UserDto, data: QuestionUpdateDto) {
    const entity = await this.questionRepo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    const topic = await this.topicRepo.findOne({ where: { id: data.topicId, isDeleted: false } })
    if (!topic) throw new ConflictException('Chủ đề này đã bị xóa hoặc ngừng hoạt động. vui lòng kiểm tra lại')
    entity.name = data.name
    if (data.sort !== null) {
      entity.sort = data.sort
    }
    entity.isHighlight = data.isHighlight
    entity.hightlightValue = data.hightlightValue
    entity.isRequired = data.isRequired
    entity.type = data.type
    entity.level = data.level
    entity.description = data.description
    entity.updatedBy = user.id
    entity.updatedAt = new Date()
    entity.img = data.img
    await entity.save()

    return { message: UPDATE_SUCCESS }
  }
  public async questionPagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = { parentId: IsNull() }
    if (data.where.name) data.where.name = Like(`%${data.where.name}%`)
    if (data.where.topicId) whereCon.topicId = data.where.topicId
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    return await this.questionRepo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      relations: { questionlistDetails: true, childs: { questionlistDetails: true } },
      order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } },
    })
  }

  public async questionUpdateActive(user: UserDto, id: string) {
    const entity = await this.questionRepo.findOne({ where: { id } })
    if (!entity) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    entity.isDeleted = !entity.isDeleted
    await this.questionRepo.update(entity.id, { isDeleted: entity.isDeleted, updatedBy: user.id })
    if (entity.level === 1) {
      let lstChild = await this.questionRepo.find({ where: { parentId: entity.id }, select: { id: true } })
      for (let child of lstChild) {
        await this.questionRepo.update(child.id, { isDeleted: entity.isDeleted, updatedBy: user.id })
      }
    }
    if (entity.level === 2) {
      if (entity.isDeleted === false) {
        let parent = await this.questionRepo.findOne({ where: { id: entity.parentId }, select: { id: true } })
        if (parent) {
          await this.questionRepo.update(parent.id, { isDeleted: false, updatedBy: user.id })
        }
      }
    }
    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  public async question_find(data: { topicId: string; isGetRelation: boolean }) {
    const whereCon: any = { topicId: data.topicId, level: 1, isDeleted: false }
    let objRelation = {}
    if (data.isGetRelation) {
      objRelation = { questionlistDetails: true, childs: { questionlistDetails: true } }
    }
    const res = await this.questionRepo.find({
      where: whereCon,
      order: { sort: 'ASC', createdAt: 'ASC' },
      relations: { questionlistDetails: true, childs: { questionlistDetails: true } },
    })

    return res
  }
  public async questionlistdetail_pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = {}
    if (data.where.questionId) whereCon.questionId = data.where.questionId
    if (data.where.name) data.where.name = Like(`%${data.where.name}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    return await this.questionListDetailRepo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { value: 'DESC' },
    })
  }

  //#region questionListDetail
  public async questionlistdetail_create_data(user: UserDto, data: QuestionListDetailCreateDto) {
    let entity = await this.questionListDetailRepo.create(data)
    // entity.companyId = user.companyId
    //entity.createdBy = user.id
    const questionEntity = await this.questionListDetailRepo.findOne({ where: { questionId: data.questionId }, order: { value: 'DESC' } })
    if (questionEntity) entity.value = questionEntity.value + 1
    else entity.value = 1
    await this.questionListDetailRepo.save(entity)
    return { id: entity.id, message: CREATE_SUCCESS }
  }

  public async questionlistdetail_update_data(user: UserDto, data: QuestionListDetailUpdateDto) {
    const entity = await this.questionListDetailRepo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    entity.name = data.name
    entity.sort = data.sort
    await entity.save()

    return { id: entity.id, message: UPDATE_SUCCESS }
  }

  public async questionlistdetail_update_active(user: UserDto, id: string) {
    const entity = await this.questionListDetailRepo.findOne({ where: { id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    await this.questionListDetailRepo.update(id, { isDeleted: !entity.isDeleted })
    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  public async questionDeleteAll(user: UserDto, topicId: string) {
    return this.questionRepo.manager.transaction(async (manager) => {
      const questionListDetailRepo = manager.getRepository(QuestionListDetailEntity)
      const questionTechRepo = manager.getRepository(QuestionEntity)

      const surveyQuestionRepo = manager.getRepository(SurveyQuestionEntity)
      const check = await surveyQuestionRepo.find({ where: { topicId: topicId, isDeleted: false } })
      if (check.length > 0) throw new Error('Không thể xóa do câu hỏi đã tồn tại trong phiếu khảo sát. Vui lòng kiểm tra lại')

      const level = 1
      const lstQuestionLv1 = await questionTechRepo.find({ where: { topicId, level } })
      if (lstQuestionLv1.length > 0) {
        for (const questionLv1 of lstQuestionLv1) {
          const lstQuestionLv2 = await questionLv1.childs
          if (lstQuestionLv2.length > 0) {
            for (const questionLv2 of lstQuestionLv2) {
              // xóa cấu hình danh sách lv2
              await questionListDetailRepo.delete({ questionId: questionLv2.id })
            }
          }
          // xóa lv2
          await questionTechRepo.delete({ parentId: questionLv1.id })

          // xóa cấu hình danh sách lv1
          await questionListDetailRepo.delete({ questionId: questionLv1.id })
        }
        // xóa lv1
        await questionTechRepo.delete({ topicId })
      }

      return { message: DELETE_SUCCESS }
    })
  }

  public async questionCreateMasterData(data: QuestionCreateMasterDataDto[]) {
    for (let item of data) {
      const topic = await this.topicRepo.findOne({ where: { code: item.topicCode, isDeleted: false } })
      if (!topic) throw new ConflictException('Chủ đề này đã bị xóa hoặc ngừng hoạt động. vui lòng kiểm tra lại')

      const question = new QuestionEntity()
      question.topicId = topic.id
      question.createdBy = item.createdBy
      question.createdAt = new Date()
      question.name = item.name
      question.code = item.code
      question.isRequired = item.isRequired
      question.type = item.type
      question.isHighlight = item.isHighlight
      question.hightlightValue = item.hightlightValue
      question.sort = item.sort
      question.level = item.level
      question.description = item.description
      question.parentId = item.parentId
      let qs = await this.questionRepo.save(question)
      if (item.lstDetail && item.lstDetail.length > 0) {
        for (let x of item.lstDetail) {
          let questionDetail = new QuestionListDetailEntity()
          questionDetail.createdAt = new Date()
          questionDetail.createdBy = item.createdBy
          questionDetail.questionId = qs.id
          questionDetail.name = x.name
          await this.questionListDetailRepo.save(questionDetail)
        }
      }
      if (item.childs && item.childs.length > 0) {
        for (let y of item.childs) {
          const questionChild = new QuestionEntity()
          questionChild.topicId = topic.id
          questionChild.createdBy = item.createdBy
          questionChild.createdAt = new Date()
          questionChild.parentId = qs.id
          questionChild.name = y.name
          questionChild.code = y.code
          questionChild.isRequired = y.isRequired
          questionChild.type = y.type
          questionChild.isHighlight = y.isHighlight
          questionChild.hightlightValue = y.hightlightValue
          questionChild.sort = y.sort
          questionChild.level = y.level
          questionChild.description = y.description
          let questionChildRs = await this.questionRepo.save(questionChild)

          if (y.__questionListDetails__ && y.__questionListDetails__.length > 0) {
            for (let z of y.__questionListDetails__) {
              let questionDetailChild = new QuestionListDetailEntity()
              questionDetailChild.createdAt = new Date()
              questionDetailChild.createdBy = item.createdBy
              questionDetailChild.questionId = questionChildRs.id
              questionDetailChild.name = z.name
              questionDetailChild.value = z.value
              await this.questionListDetailRepo.save(questionDetailChild)
            }
          }
        }
      }
    }

    return { message: CREATE_SUCCESS }
  }

  public async questionImport(topicId: string, user: UserDto, data: { lstDataTable1: any[]; lstDataTable2: any[] }) {
    const check = await this.surveyQuestionRepo.find({ where: { topicId: topicId, isDeleted: false } })

    return this.questionRepo.manager.transaction(async (manager) => {
      const questionRepo = manager.getRepository(QuestionEntity)
      const questionListDetailRepo = manager.getRepository(QuestionListDetailEntity)
      const topicRepo = manager.getRepository(TopicEntity)
      const topic = await topicRepo.findOne({ where: { id: topicId, isDeleted: false } })
      if (!topic) throw new Error('Chủ đề này không tồn tại. Vui lòng kiểm tra lại !')
      // add lv1
      var lstDataLv1 = data.lstDataTable1.filter((c: any) => c.level == 1)
      for (const item of lstDataLv1) {
        if (!item.id) {
          const objQuestionNew = new QuestionEntity()
          objQuestionNew.createdBy = user.id
          objQuestionNew.topicId = topicId
          objQuestionNew.level = 1
          objQuestionNew.sort = item.sort || 0
          objQuestionNew.name = item.name
          objQuestionNew.type = item.type
          objQuestionNew.isRequired = item.isRequired
          objQuestionNew.isHighlight = item.isHighlight
          objQuestionNew.hightlightValue = item.hightlightValue
          objQuestionNew.createdAt = new Date()
          const objServiceTech = await questionRepo.save(objQuestionNew)
          item.id = objServiceTech.id

          const lstDetail = data.lstDataTable2.filter((c: any) => c.zenListId == item.zenId)
          if (lstDetail.length > 0) {
            for (const detail of lstDetail) {
              const detailNew = new QuestionListDetailEntity()
              detailNew.createdAt = new Date()
              detailNew.createdBy = user.id
              detailNew.questionId = item.id
              detailNew.name = detail.nameList
              detailNew.value = detail.valueList
              await questionListDetailRepo.save(detailNew)
            }
          }
        }
      }

      // add lv2
      var lstDataLv2 = data.lstDataTable1.filter((c: any) => c.level == 2)
      for (const item of lstDataLv2) {
        if (!item.id) {
          const objQuestionNew = new QuestionEntity()
          objQuestionNew.createdBy = user.id
          objQuestionNew.createdAt = new Date()
          objQuestionNew.topicId = topicId
          objQuestionNew.level = 2
          objQuestionNew.sort = item.sort || 0
          objQuestionNew.name = item.name
          objQuestionNew.type = item.type
          objQuestionNew.isRequired = item.isRequired
          objQuestionNew.isHighlight = item.isHighlight
          objQuestionNew.hightlightValue = item.hightlightValue
          const parent = lstDataLv1.find((c: any) => c.zenId == item.parentZenId)
          if (parent) objQuestionNew.parentId = parent.id
          const objServiceTech = await questionRepo.save(objQuestionNew)
          item.id = objServiceTech.id

          const lstDetail = data.lstDataTable2.filter((c: any) => c.zenListId == item.zenId)
          if (lstDetail.length > 0) {
            for (const detail of lstDetail) {
              const detailNew = new QuestionListDetailEntity()
              detailNew.createdAt = new Date()
              detailNew.createdBy = user.id
              detailNew.questionId = item.id
              detailNew.name = detail.nameList
              detailNew.value = detail.valueList
              await questionListDetailRepo.save(detailNew)
            }
          }
        }
      }
    })
  }
  /** End region */

  /** Trả lời và lưu câu trả lời */
  async surveyAnswerQuestion(data: SurveyAnswerQuestionDto) {
    const surveyItem = await this.surveyRepo.findOne({ where: { id: data.surveyId, isDeleted: false } })
    if (!surveyItem) throw new Error('Phiếu khảo sát không còn tồn tại!')

    const today = new Date()
    if (today > surveyItem.timeEnd) throw new Error('phiếu khảo sát không còn khả năng do hết hạn khảo sát.')
    await this.surveyRepo.manager.transaction(async (manager) => {
      const surveyQuestionValueRepo = manager.getRepository(SurveyQuestionEntity)
      for (let index = 0; index < data.questionInfo.length; index++) {
        const item = data.questionInfo[index]
        if (item.value) {
          let surveyQuestion = new SurveyQuestionEntity()
          let question = await this.questionRepo.findOne({ where: { id: item.questionId } })
          let topicId = await manager.getRepository(TopicEntity).findOne({ where: { id: question.topicId } })
          if (!topicId) throw new Error(ERROR_NOT_FOUND_DATA)
          surveyQuestion.value = item.value
          surveyQuestion.topicId = topicId.id
          surveyQuestion.questionId = item.questionId
          let survey = await manager.getRepository(SurveyEntity).findOne({ where: { id: data.surveyId } })
          if (!survey) throw new Error(ERROR_NOT_FOUND_DATA)
          surveyQuestion.surveyId = data.surveyId
          surveyQuestion.survey = Promise.resolve(survey)
          surveyQuestion.surveyMemberId = data.surveyMemberId
          surveyQuestion.createdAt = today
          await surveyQuestionValueRepo.save(surveyQuestion)
        }
      }
      const surveyHistory = manager.getRepository(SurveyHistoryEntity)
      const history = new SurveyHistoryEntity()
      history.surveyId = surveyItem.id
      history.createdAt = today
      history.status = enumData.SurveyStatus.Doing.code
      // await surveyHistory.save(history)
    })

    if (surveyItem.status === enumData.SurveyStatus.New.code) {
      surveyItem.status = enumData.SurveyStatus.Doing.code
      await this.surveyRepo.save(surveyItem)
    }
    return { message: UPDATE_SUCCESS }
  }

  async mobileAppSurveyQuestion(user: UserDto, data: SurveyFilterOneDto) {
    const survey = await this.surveyRepo.findOne({
      where: { id: data.surveyId },
    })
    if (!survey) throw new Error('Phiếu khảo sát không còn tồn tại')
    if (survey.isDeleted) throw new Error('Phiếu khảo sát đã ngưng hoạt động')
    if (survey.status === enumData.SurveyStatus.New.code) throw new Error('Phiếu khảo sát chưa bắt đầu vui lòng liên hệ Admin để bắt đầu khảo sát')
    if (survey.status === enumData.SurveyStatus.Cancel.code) throw new Error('Phiếu khảo sát đã hủy vui lòng kiểm tra lại')
    let lstSurveyQuestion: any = await this.surveyQuestionRepo.find({
      where: { surveyId: data.surveyId, isDeleted: false, surveyMemberId: IsNull() },
    })
    if (lstSurveyQuestion.length > 0) {
      let lstSurveyQuestionId = lstSurveyQuestion.map((x: { questionId: any }) => x.questionId)
      if (lstSurveyQuestionId.length > 0) {
        let lstQuestion: any = await this.questionRepo.find({
          where: {
            id: In(lstSurveyQuestionId),
            isDeleted: false,
          },
          relations: { questionlistDetails: true, childs: { questionlistDetails: true } },
          order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } },
        })
        let lstQuestionId: any[] = lstQuestion.map((x) => x.id)
        let lstQuestionParent: any[] = lstQuestion.filter((x) => x.parentId !== null)
        let mapQuestionDistinct = lstQuestionParent.mapAndDistinct((x) => x.parentId)
        for (let item of mapQuestionDistinct) {
          if (item == null) continue
          let question = lstQuestion.find((x) => x.id == item)
          let lstChilds = await question.childs
          lstChilds = lstChilds.filter((x) => lstQuestionId.includes(x.id))
          let lstChildId = lstChilds.map((x) => x.id)
          lstQuestion = lstQuestion.filter((x) => !lstChildId.includes(x.id))
          delete question.__childs__
          question.childs = lstChilds
        }
        return lstQuestion
      }
    }
    return []
  }
}
