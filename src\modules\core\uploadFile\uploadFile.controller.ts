import { Controller, Post, UploadedFile, UploadedFiles, UseGuards, UseInterceptors } from '@nestjs/common'
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express'
import { UploadFileService } from './uploadFile.service'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { JwtAuthGuard } from '../../common/guards'

@ApiBearerAuth()
@ApiTags('UploadFile')
@UseGuards(JwtAuthGuard)
@Controller('upload_files')
export class UploadFileController {
  constructor(private readonly service: UploadFileService) {}

  @ApiOperation({ summary: 'Upload single file S3' })
  @Post('upload_single_s3')
  @UseInterceptors(FileInterceptor('file'))
  async uploadSingle(@UploadedFile() file: Express.Multer.File) {
    return await this.service.uploadSingle(file)
  }

  @ApiOperation({ summary: 'Upload multi file S3' })
  @Post('upload_multi_s3')
  @UseInterceptors(FilesInterceptor('files'))
  async uploadFile(@UploadedFiles() files: Array<Express.Multer.File>) {
    return await this.service.uploadFile(files)
  }
}
