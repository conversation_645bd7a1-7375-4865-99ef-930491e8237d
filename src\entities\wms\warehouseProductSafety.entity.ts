import { BaseEntity } from '../core/base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { WarehouseEntity } from './warehouse.entity'
// import { ProductEntity } from './product.entity'

/** <PERSON><PERSON><PERSON> mức tồn kho an toàn */
@Entity('warehouse_product_safety')
export class WarehouseProductSafetyEntity extends BaseEntity {
  /** Id PNK */
  @Column({ type: 'varchar', nullable: true })
  warehouseId: string
  @ManyToOne(() => WarehouseEntity, (p) => p.products)
  @JoinColumn({ name: 'warehouseId', referencedColumnName: 'id' })
  warehouse: Promise<WarehouseEntity>

  /** Id Sản phẩm */
  @Column({ type: 'varchar', length: 36, nullable: true })
  productId: string

  /** Mã sp */
  @Column({ type: 'varchar', length: 50, nullable: true })
  productCode: string

  /** Tên sp */
  @Column({ type: 'varchar', length: 250, nullable: true })
  productName: string

  /** Số lượng */
  @Column({ nullable: true, default: 0 })
  quantity: number

  /** Số lượng tối thiểu */
  @Column({ nullable: true, default: 0 })
  quantityMin: number

  /** Số lượng tối đa */
  @Column({ nullable: true, default: 0 })
  quantityMax: number
}
