// DTOs for Bank and BankBranch entities

import { IsNotEmpty, IsOptional, IsString, IsUUID, ValidateNested, ArrayMinSize, isNotEmpty } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class DeleteDto {
  @ApiProperty({ description: "ID của Bank" })
  @IsNotEmpty()
  @IsUUID()
  id: string;
}

// BankBranch DTO for nested usage
export class CreateBankBranchDto {
  @IsNotEmpty()
  @IsString()
  code: string;

  @IsNotEmpty()
  @IsString()
  name: string;
}

export class UpdateBankBranchDto {
  @IsOptional()
  @IsUUID()
  id?: string;

  @IsOptional()
  @IsString()
  code?: string;

  @IsOptional()
  @IsString()
  name?: string;
}

// Bank DTOs
export class CreateBankDto {
  @IsNotEmpty()
  @IsString()
  code: string;

  @IsNotEmpty()
  @IsString()
  bankCode: string;

  @IsNotEmpty()
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  shortName?: string;

  @IsOptional()
  @IsString()
  bin?: string;

  @IsOptional()
  @IsString()
  key?: string;

  @IsOptional()
  @IsString()
  logo?: string;

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateBankBranchDto)
  @ArrayMinSize(1)
  branches?: CreateBankBranchDto[];
}

export class UpdateBankDto {
  @ApiProperty({ description: "ID" })
  @IsNotEmpty()
  @IsUUID()
  bankId: string

  @ApiPropertyOptional({ description: "Bank code theo template excel" })
  @IsOptional()
  @IsString()
  code?: string;

  @ApiPropertyOptional({ description: "Bank code" })
  @IsOptional()
  @IsString()
  bankCode?: string;

  @ApiPropertyOptional({ description: "Tên ngân hàng" })
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  shortName?: string;

  @IsOptional()
  @IsString()
  bin?: string;

  @IsOptional()
  @IsString()
  key?: string;

  @IsOptional()
  @IsString()
  logo?: string;

  @ApiPropertyOptional({ description: "Danh sách chi nhánh" })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => UpdateBankBranchDto)
  @ArrayMinSize(1)
  branches?: UpdateBankBranchDto[];
}

export class BankBranchLstDto {
  @ApiProperty({ description: "Trạng thái", default: "********" })
  @IsNotEmpty()
  code: string

  @ApiPropertyOptional({ description: "Trạng thái", default: true })
  @IsOptional()
  isDeleted: string
}