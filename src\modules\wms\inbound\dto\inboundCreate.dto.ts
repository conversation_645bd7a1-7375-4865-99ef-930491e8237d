import { transformer } from './../../../../constants/enumData';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsString, IsNumber, IsOptional, IsUUID } from 'class-validator'
import { enumData } from '../../../../constants'

export class InboundCreateDto {

  @ApiProperty({ description: "Id Phiếu giao nhận LM" })
  @IsOptional()
  deliveryNoteLmCode?: string

  @ApiProperty({ description: "Id của kho" })
  @IsNotEmpty()
  @IsString()
  warehouseId: string

  @ApiPropertyOptional({ description: "Id phiếu chuyển kho" })
  @IsOptional()
  @IsString()
  warehouseTransferId?: string

  @ApiPropertyOptional({ description: "Id PO" })
  @IsOptional()
  poId?: string

  /** Đơn vị tiền tệ enum Currency */
  // @IsNotEmpty()
  // @IsString()
  @ApiProperty({ description: "Đơn vị tiền tệ" })
  currencyCode?: string

  // Tỷ lệ nhập kho
  @ApiProperty({ description: "Tỉ giá nhập kho" })
  @IsNumber()
  @IsNotEmpty()
  exchangeRate: number

  // Tỷ lệ nhập kho
  @ApiPropertyOptional({ description: "Ghi chú nhập kho" })
  @IsOptional()
  description?: string

  // Tổng trị giá nhập
  @ApiProperty({ description: "Tổng trị giá nhập theo các item" })
  @IsOptional()
  totalPriceVND?: number

  // Loại hình vận chuyện
  @ApiPropertyOptional({
    description: `Loại hình vạn chuyển ${Object.values(enumData.TransportType).map((type: any) => `${type.name} (${type.code})`).join(', ')}`
  })
  @IsOptional()
  transportType?: string

  // Danh sách item trong phiếu
  @ApiProperty({ description: "Danh sách phiếu nhập kho" })
  lstDetail: InboundDetailCreateDto[]

  @ApiProperty({ description: "ID người tạo PNX" })
  @IsNotEmpty()
  @IsUUID()
  createBy: string

  @ApiProperty({ description: "Cờ tạo từ phiếu giao nhận" })
  @IsOptional()
  isFromDeliveryNote?: boolean = false

  @ApiPropertyOptional({ description: "Chứng từ phiếu nhập kho" })
  @IsOptional()
  files?: string[];

  @ApiPropertyOptional({ description: "Mã CODE PO" })
  @IsOptional()
  poCode?: string
}

class InboundDetailCreateDto {
  @IsOptional()
  id?: string

  @ApiProperty({ description: "ID PO Detail" })
  @IsOptional()
  @IsString()
  poDetailId?: string

  @ApiProperty({ description: "Đơn vị tính" })
  @IsOptional()
  @IsString()
  unitId?: string

  @ApiProperty({ description: "ID hàng hóa" })
  @IsNotEmpty()
  @IsString()
  productId: string

  @ApiProperty({ description: "Mã Code hàng hóa" })
  @IsNotEmpty()
  @IsString()
  productCode: string

  @ApiProperty({ description: "Tên hàng hóa" })
  @IsNotEmpty()
  @IsString()
  productName: string

  @ApiProperty({ description: "Số lượng" })
  @IsNotEmpty()
  @IsNumber()
  quantity: number

  @IsNotEmpty()
  @IsNumber()
  price: number

  @IsOptional()
  @IsNumber()
  buyPrice?: number

  @IsOptional()
  @IsNumber()
  buyPriceVND?: number

  @ApiProperty({ description: "Ngày hết hạn" })
  @IsOptional()
  @IsNotEmpty()
  expiryDate?: Date

  @IsOptional()
  @IsNotEmpty()
  manufactureDate?: Date

  @IsOptional()
  description?: string

  @IsOptional()
  lotNumber?: string

  @IsOptional()
  isCombo?: string

  totalPriceVND: number

  priceVND: number
}
