import { MigrationInterface, QueryRunner } from 'typeorm'

export class update1742985641184 implements MigrationInterface {
  name = 'update1742985641184'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "permission" ALTER COLUMN "createdAt" TYPE TIMESTAMP WITH TIME ZONE`)
    await queryRunner.query(`ALTER TABLE "permission" ALTER COLUMN "updatedAt" TYPE TIMESTAMP WITH TIME ZONE`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "permission" ALTER COLUMN "createdAt" TYPE TIMESTAMP DEFAULT now()`)
    await queryRunner.query(`ALTER TABLE "permission" ALTER COLUMN "updatedAt" TYPE TIMESTAMP DEFAULT now()`)
  }
}
