import { Controller, UseGuards, Post, Body, UseInterceptors, UploadedFile, Query, Req, Get } from "@nestjs/common";
import { JwtAuthGuard } from '../../common/guards'
import { ApiBearerAuth, ApiTags } from "@nestjs/swagger";
import { OrderConfigService } from "./orderConfig.service";
import { CreateOrderConfigDto, ListOrderConfigDto, UpdateOrderConfigDto } from "./dto/orderConfig.dto";

@ApiBearerAuth()
@ApiTags('Order Config Time Line')
@UseGuards(JwtAuthGuard)
@Controller('order-config')
export class OrderConfigController {
  constructor(private readonly service: OrderConfigService) { }

  @Post("list")
  public async list(@Body() body: ListOrderConfigDto) {
    return this.service.listConfig(body)
  }

  @Post("create")
  public async create(@Body() body: CreateOrderConfigDto) {
    return this.service.createConfig(body)
  }

  @Post("update")
  public async update(@Body() body: UpdateOrderConfigDto) {
    return this.service.updateConfig(body)
  }
}