import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator'
import { CheckInventoryCreateDto } from './checkInventoryCreate.dto'
import { ApiProperty } from '@nestjs/swagger'

export class CheckInventoryUpdateDto extends CheckInventoryCreateDto {
  @ApiProperty({ description: 'Id phiếu kiểm kho' })
  @IsNotEmpty()
  @IsString()
  id: string

  @ApiProperty({ description: 'Id người cập nhật phiếu' })
  @IsOptional()
  @IsUUID()
  updateBy?: string
}
