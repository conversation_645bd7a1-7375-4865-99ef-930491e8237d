import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsString, IsNumber, IsOptional } from 'class-validator'
export class InforBankDto {
  @ApiPropertyOptional({ description: 'Quốc gia' })
  @IsOptional()
  nation: string

  @ApiPropertyOptional({ description: 'Thành phố' })
  @IsOptional()
  city: string

  @ApiPropertyOptional({ description: 'Ngân hàng' })
  @IsOptional()
  bank: string

  @ApiPropertyOptional({ description: 'Chi nhánh' })
  @IsOptional()
  branch: string

  @ApiPropertyOptional({ description: 'Số tài khoản ngân hàng' })
  @IsOptional()
  bankNumber: string

  @ApiPropertyOptional({ description: 'Chủ thẻ' })
  @IsOptional()
  accountName: string

  @ApiPropertyOptional({ description: 'Swift Code' })
  @IsOptional()
  swiftCode: string

  @ApiPropertyOptional({ description: 'IBAN' })
  @IsOptional()
  IBAN: string

  @ApiPropertyOptional({ description: 'File thông báo mở tài khoản' })
  @IsOptional()
  file: string
}
export class SupplierRegisterDto {
  /** tài khoản */
  @IsOptional()
  username: string

  /** mật khẩu */
  @IsOptional()
  password: string

  /** xác nhận mật khẩu */
  @IsOptional()
  confirmPassword: string

  /** Mô tả về nhà cung cấp */
  @IsOptional()
  description: string

  @IsOptional()
  tradingHabits: string

  /** Mã số doanh nghiệp */
  @IsOptional()
  code: string

  /** Tên doanh nghiệp */
  @IsOptional()
  name: string

  /** Tên giao dịch */
  @IsOptional()
  dealName: string

  /** Tên viết tắt */
  @IsOptional()
  abbreviateName: string

  /** Loại hình doanh nghiệp */
  @IsOptional()
  companyType: string

  /** Quốc gia */
  @IsOptional()
  nation: string

  /** Người đại diện pháp luật */
  @IsOptional()
  represen: string

  /** Tên giám đốc */
  @IsOptional()
  positionRepresen: string

  /** Số fax */
  @IsOptional()
  faxRepresen: string

  /** lưu ý người đại diện pháp luật */
  @IsOptional()
  noteRepresen: string

  /** Email */
  @IsOptional()
  email: string

  /** Điện thoại */
  @IsOptional()
  phone: string

  /** Năm thành lập công ty */
  @IsOptional()
  createYear: string

  /** Trạng thái */
  @ApiPropertyOptional()
  status: string

  @ApiPropertyOptional()
  isDistributor: boolean

  @ApiPropertyOptional()
  isSupplier: boolean

  @ApiPropertyOptional()
  is3PL: boolean

  /** Thông tin tài khoản ngân hàng */
  @IsOptional()
  inforBank: InforBankDto
}
