import { MigrationInterface, QueryRunner } from 'typeorm'

export class updateLuckyBill1756374920476 implements MigrationInterface {
  name = 'updateLuckyBill1756374920476'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "lucky_bill_apply_region" DROP CONSTRAINT "FK_27abcdb50f010b37c7e7303afe0"`)
    await queryRunner.query(`ALTER TABLE "lucky_bill_apply_region" DROP COLUMN "regionId"`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "lucky_bill_apply_region" ADD "regionId" uuid NOT NULL`)
    await queryRunner.query(
      `ALTER TABLE "lucky_bill_apply_region" ADD CONSTRAINT "FK_27abcdb50f010b37c7e7303afe0" FOREIGN KEY ("regionId") REFERENCES "region"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }
}
