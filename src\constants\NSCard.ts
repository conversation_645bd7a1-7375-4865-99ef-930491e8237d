export namespace NSCard {
  export enum ECardType {
    PREPAID = 'PREPAID', // Trả trước
    POSTPAID = 'POSTPAID', // Trả sau
  }

  export enum EMemberCardStatus {
    INACTIVE = 'INACTIVE', // Thẻ chưa được kích hoạt
    ACTIVE = 'ACTIVE', // Thẻ đang hoạt động
    EXPIRED = 'EXPIRED', // Thẻ đã hết hạn
    SUSPENDED = 'SUSPENDED', // Thẻ bị tạm ngưng
    CANCELED = 'CANCELED', // Thẻ đã bị hủy
  }

  export enum ECardDurationUnit {
    MONTH = 'MONTH', // Tháng
    DAY = 'DAY', // Ngày
    YEAR = 'YEAR', // Năm
  }

  export enum ECardPeriodUnit {
    WEEK = 'WEEK', // Tuần
    MONTH = 'MONTH', // Tháng
  }

  export enum ECardTypeStatus {
    ISSUED = 'ISSUED', // <PERSON>ang phát hành
    NOT_ISSUED = 'NOT_ISSUED', // Chưa phát hành
  }
}
