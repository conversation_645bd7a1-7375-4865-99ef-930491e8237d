import { Controller, UseGuards, Post, Body, Req } from '@nestjs/common';
import { CurrentUser } from '../../common/decorators';
import { FilterIdDto, PaginationDto, UserDto } from '../../../dto';
import { Request as IRequest } from 'express';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../common/guards';
import { DeliveryScheduleService } from './poDeliverySchedule.service';
import { CreatePoDeliveryScheduleDto } from './dto';


@ApiBearerAuth()
@ApiTags('Po Delivery Schedule')
@UseGuards(JwtAuthGuard)
@Controller('po-delivery-schedule')
export class DeliveryScheduleController {
  constructor(private readonly service: DeliveryScheduleService) { }

  @ApiOperation({ summary: 'Danh sách lịch giao hàng có phân trang' })
  @Post('pagination')
  async pagination(@Req() req: IRequest, @Body() data: PaginationDto) {
    return await this.service.pagination(req, data);
  }

  @ApiOperation({ summary: 'Tạo lịch giao hàng' })
  @Post('create_data')
  async createData(@Req() req: IRequest, @Body() data: CreatePoDeliveryScheduleDto, @CurrentUser() user: UserDto) {
    return await this.service.createData(data, req, user);
  }
}
