import { MigrationInterface, QueryRunner } from 'typeorm'

export class updateUserRelation1753332693619 implements MigrationInterface {
  name = 'updateUserRelation1753332693619'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" ADD "vendor" character varying(50)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "vendor"`)
  }
}
