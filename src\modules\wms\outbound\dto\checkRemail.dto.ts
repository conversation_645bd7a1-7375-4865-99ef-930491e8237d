import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString } from 'class-validator'

export class CheckingRemainDto {
  @ApiProperty({ description: '<PERSON>ho vật lý' })
  @IsNotEmpty()
  @IsString()
  warehouseId: string

  lstItemDetail: itemDetail[]
}

export class itemDetail {
  @ApiProperty({ description: 'Số lượng' })
  @IsNotEmpty()
  @IsString()
  quantity: string

  @ApiProperty({ description: 'Id sản phẩm' })
  @IsNotEmpty()
  @IsString()
  productId: string
}
