import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { JwtAuthGuard } from '../../common/guards'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { CurrentUser } from '../../common/decorators'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { PartnerMapService } from './partnerMap.service'
import { PartnerMapCreateDto } from './dto/partnerMapCreate.dto'

@ApiBearerAuth()
@ApiTags('Partner map')
@UseGuards(JwtAuthGuard)
@Controller('partner_map')
export class PartnerMapController {
  constructor(private readonly service: PartnerMapService) {}

  @Post('find')
  public async find(@Body() data: any) {
    return await this.service.find(data)
  }

  @Post('create_list_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: PartnerMapCreateDto[]) {
    return await this.service.createListData(data)
  }
}
