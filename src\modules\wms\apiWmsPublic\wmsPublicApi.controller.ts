import { Controller, UseGuards, Body, Req, Get, Query } from '@nestjs/common'
import { CurrentUser } from '../../common/decorators'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { Request as IRequest } from 'express'
import { JwtAuthGuard } from '../../common/guards'
import { AuthGuard } from '../../common/guards/auth.guard'
import { wmsPublicApiService } from './wmsPulicApi.service'
import { ListComboReq, ListItemGroupReq } from '../../../dto/item-group.dto'
import { IdDto } from '../../../dto/id.dto'

@ApiBearerAuth()
@ApiTags('Public API')
@UseGuards(AuthGuard)
@Controller('public')
export class wmsPublicApiController {
  constructor(private readonly service: wmsPublicApiService) { }

  @Get('find_category')
  async findCategory() {
    return await this.service.findCategory()
  }

  @Get('find_product_type')
  async findProductType() {
    return await this.service.findProductType()
  }

  @Get('find_item_price')
  async findItemPrice() {
    return await this.service.findItemPrice()
  }

  @Get('find_warehouse')
  async findWarehouse() {
    return await this.service.findWarehouse()
  }

  @Get('find_warehouse_item')
  async findWarehouseItem(@Req() req: IRequest) {
    const header = req.headers
    let lstWarehouse = []
    if (header['warehouseid']) lstWarehouse = header['warehouseid'].toString().split(',')
    return await this.service.findWarehouseItem(lstWarehouse)
  }

  @Get('find_combo')
  async findComboItem(@Req() req: IRequest) {
    const header = req.headers
    let lstWarehouse = []
    if (header['warehouseid']) lstWarehouse = header['warehouseid'].toString().split(',')
    return await this.service.findWarehouseComboItem(lstWarehouse)
  }

  @Get('list-item-group')
  public listItemGroup(@Query() params: ListItemGroupReq) {
    return this.service.listItemGroup(params)
  }

  @Get('list-combo-v2')
  public listCombo2(@Query() params: ListComboReq, @Req() req: IRequest) {
    const header = req.headers
    let lstWarehouse = []
    if (header['warehouseid']) lstWarehouse = header['warehouseid'].toString().split(',')
    return this.service.listWarehouseComboItem(params, lstWarehouse)
  }

  @Get('list-combo')
  public listCombo(@Query() params: ListComboReq) {
    return this.service.listCombo(params)
  }

  @Get('combo-detail')
  public comboDetail(@Query() params: IdDto) {
    return this.service.comboDetail(params)
  }
}
