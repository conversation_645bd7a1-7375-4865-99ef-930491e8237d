import { Controller, UseGuards, Request, Post, Body } from '@nestjs/common'
import { JwtAuthGuard } from '../../common/guards'
import { Request as IRequest } from 'express'
import { RegionCreateDto, RegionUpdateDto } from './dto'
import { CurrentUser } from '../../common/decorators'
import { PaginationDto, UserDto } from '../../../dto'
import { RegionService } from './region.service'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'

@ApiBearerAuth()
@ApiTags('Region')
@UseGuards(JwtAuthGuard)
@Controller('region')
export class RegionController {
  constructor(private readonly service: RegionService) {}

  @Post('find')
  public async find(@Request() req: IRequest, @Body() data: any) {
    return await this.service.find(data)
  }

  @Post('find_region_city')
  public async findRegionCity(@Request() req: IRequest) {
    return await this.service.findRegionCity()
  }

  @Post('pagination')
  public async pagination(@Request() req: IRequest, @Body() data: PaginationDto) {
    return await this.service.pagination(data)
  }

  @Post('create_data')
  public async createData(@Request() req: IRequest, @CurrentUser() user: UserDto, @Body() data: RegionCreateDto) {
    return await this.service.createData(user, data)
  }

  @Post('update_data')
  public async updateData(@Request() req: IRequest, @CurrentUser() user: UserDto, @Body() data: RegionUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @Post('update_active')
  public async updateActive(@Request() req: IRequest, @Body() data: any, @CurrentUser() user: UserDto) {
    return await this.service.updateActive(data.id, user)
  }
}
