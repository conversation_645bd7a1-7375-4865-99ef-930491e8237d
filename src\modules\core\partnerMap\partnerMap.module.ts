import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import { PartnerMapRepository } from '../../../repositories'
import { PartnerMapController } from './partnerMap.controller'
import { PartnerMapService } from './partnerMap.service'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([PartnerMapRepository])],
  controllers: [PartnerMapController],
  providers: [PartnerMapService],
  exports: [PartnerMapService],
})
export class PartnerMapModule {}
