import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'

/** Interface user tự đổi mật khẩu Auth/Survey */
export class UpdatePasswordDto {
  @ApiProperty({ description: '<PERSON>ật khẩu hiện tại' })
  @IsNotEmpty()
  @IsString()
  currentPassword: string

  @ApiProperty({ description: 'Mật khẩu mới' })
  @IsNotEmpty()
  @IsString()
  newPassword: string
}
