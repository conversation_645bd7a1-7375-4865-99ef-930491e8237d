import { CREATE_SUCCESS, ERROR_NOT_FOUND_DATA, enumData, UPDATE_ACTIVE_SUCCESS } from '@/constants'
import { UserDto, PaginationDto } from '@/dto'
import { SettingMediaEntity } from '@/entities'
import { coreHelper } from '@/helpers'
import { SettingMediaCreateDto } from '@/modules/core/settingMedia/dto'
import { CustomRepository } from '@/typeorm'
import { Repository, Like } from 'typeorm'

@CustomRepository(SettingMediaEntity)
export class SettingMediaRepository extends Repository<SettingMediaEntity> {
  async createData(data: SettingMediaCreateDto, userLogin: UserDto) {
    let media = new SettingMediaEntity()
    media.type = data.type
    media.typeName = data.typeName
    media.term = data.term
    media.content = data.content
    media.url = data.url
    media.link = data.link
    media.commission = data.commission
    media.personalDeposit = data.personalDeposit
    media.corporateDeposit = data.corporateDeposit
    media.bankCode = data.bankCode
    if (data.img && data.img.length > 0) {
      media.img = data.img
    }
    media.createdBy = userLogin.username
    const mediaEntity = this.manager.getRepository(SettingMediaEntity).create({ ...media })
    await this.manager.getRepository(SettingMediaEntity).save(mediaEntity)
    return { message: CREATE_SUCCESS }
  }
  async updateData(data: SettingMediaCreateDto, userLogin: UserDto) {
    const entity = await this.findOne({ where: { id: data.id } })
    if (!entity) {
      throw new Error(ERROR_NOT_FOUND_DATA)
    }
    entity.type = data.type
    entity.typeName = data.typeName
    entity.term = data.term
    entity.content = data.content
    entity.url = data.url
    entity.link = data.link
    entity.personalDeposit = data.personalDeposit
    entity.corporateDeposit = data.corporateDeposit
    entity.commission = data.commission
    entity.bankCode = data.bankCode
    if (data.img && data.img.length > 0) {
      entity.img = data.img
    }
    entity.updatedBy = userLogin.id
    entity.updatedAt = new Date()
    const updatedEntity = await this.save(entity)
    return updatedEntity
  }

  async pagination(data: PaginationDto) {
    const whereCondition: any = {}
    if (data.where) {
      if (data.where.isDeleted != undefined) whereCondition.isDeleted = data.where.isDeleted
      if (data.where.code) whereCondition.code = Like(`%${data.where.code}%`)
      if (data.where.name) whereCondition.name = Like(`%${data.where.name}%`)
    }

    const defaultSettingStrings = coreHelper.convertObjToArray(enumData.SettingMedia)

    const resSettingString = await this.find()
    const map = new Map(resSettingString.map((item) => [item.type, item]))

    for (let item of defaultSettingStrings) {
      const curSt = map.get(item.type)
      if (curSt) {
        Object.assign(item, curSt)
      }
    }

    return defaultSettingStrings
  }

  async updateIsDelete(id: string) {
    const entity = await this.findOne({ where: { id: id } })
    if (!entity) {
      throw new Error(ERROR_NOT_FOUND_DATA)
    }
    entity.isDeleted = !entity.isDeleted
    const updatedEntity = await this.save(entity)
    return { message: UPDATE_ACTIVE_SUCCESS }
  }
}
