import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsNumber, IsOptional } from 'class-validator';

export class UpdatePoDeliveryScheduleDto {
  @ApiProperty({ description: 'ID lịch giao hàng cần cập nhật' })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({ description: 'Mã giao hàng' })
  @IsString()
  @IsOptional()
  code?: string;

  @ApiProperty({ description: 'ID nhà cung cấp' })
  @IsString()
  @IsOptional()
  supplierId?: string;

  @ApiProperty({ description: 'ID sản phẩm' })
  @IsString()
  @IsOptional()
  productInfo?: string;

  @ApiProperty({ description: 'ID bên giao hàng (3PL)' })
  @IsString()
  @IsOptional()
  thirdPartyLogistics?: string;

  @ApiProperty({ description: 'Số lượng ước tính' })
  @IsNumber()
  @IsOptional()
  estimatedQuantity?: number;

  @ApiProperty({ description: 'Số lượng đã phân phối' })
  @IsNumber()
  @IsOptional()
  distributedQuantity?: number;

  @ApiProperty({ description: 'Ngày phân phối dự kiến' })
  @IsString()
  @IsOptional()
  distributionTime?: string;

  @ApiProperty({ description: 'Trạng thái' })
  @IsString()
  @IsOptional()
  status?: string;
}
