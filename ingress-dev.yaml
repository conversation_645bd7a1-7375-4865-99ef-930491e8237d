apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: nginx-ingress-host
  namespace: ape-bl-dev
  annotations:
    kubernetes.io/ingress.class: 'nginx'
    nginx.ingress.kubernetes.io/proxy-body-size: '100m'
spec:
  rules:
    - host: ape-bl-api-dev.apetechs.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ape-bl-api-dev
                port:
                  number: 80
    - host: ape-bl-admin-dev.apetechs.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ape-bl-admin-dev
                port:
                  number: 80
    - host: ape-bl-customer-dev.apetechs.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ape-bl-customer-dev
                port:
                  number: 80
    - host: ape-bl-partner-dev.apetechs.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ape-bl-partner-dev
                port:
                  number: 80
    - host: ape-bl-survey-dev.apetechs.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ape-bl-survey-dev
                port:
                  number: 80
    - host: ape-bl-survey-api-dev.apetechs.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ape-bl-survey-dev
                port:
                  number: 80
    - host: ape-bl-oms-api-dev.apetechs.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ape-bl-oms-api-dev
                port:
                  number: 80
    - host: ape-bl-portal-dev.apetechs.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ape-bl-portal-dev
                port:
                  number: 80
    - host: ape-bl-foundation-dev.apetechs.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ape-bl-foundation-dev
                port:
                  number: 80
    - host: ape-bl-procument-dev.apetechs.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ape-bl-procurement-dev
                port:
                  number: 80