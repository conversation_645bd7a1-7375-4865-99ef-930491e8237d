import { MigrationInterface, QueryRunner } from "typeorm";

export class updateEntityPoSo1747385486707 implements MigrationInterface {
    name = 'updateEntityPoSo1747385486707'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "purchase_order_so" ADD "orderAddressType" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "purchase_order_so" DROP COLUMN "orderAddressType"`);
    }

}
