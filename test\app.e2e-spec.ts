import { Test, TestingModule } from '@nestjs/testing'
import { INestApplication } from '@nestjs/common'
import * as request from 'supertest'
import * as sharp from 'sharp'
import * as fs from 'fs'
// import { AppModule } from '@/modules/app.module'

// describe('AppController (e2e)', () => {
//   let app: INestApplication

//   beforeEach(async () => {
//     const moduleFixture: TestingModule = await Test.createTestingModule({
//       imports: [AppModule],
//     }).compile()

//     app = moduleFixture.createNestApplication()
//     await app.init()
//   })

//   it('/ (GET)', () => {
//     return request(app.getHttpServer()).get('/').expect(200).expect('Hello World!')
//   })
// })


const compressImage = async (buffer: Buffer, targetMaxByte = 1024 * 5) => {
  const image = sharp(buffer)

  let metadata = await image.metadata()
  const extension = metadata.format
  const quality = 80
  let newBuffer = buffer
  // resize image before compress
  const MAX_WIDTH = 1000
  
  if (metadata.width > MAX_WIDTH) {
    const newHeight = Math.round((MAX_WIDTH / metadata.width) * metadata.height)
    image.resize(MAX_WIDTH, newHeight, { fit: 'inside'})
  }

  metadata = await image.metadata()
  
  console.log(metadata.width, metadata.height)
  if (metadata.size > targetMaxByte) {
    const newQuality = Math.round((targetMaxByte / metadata.size) * quality) || 80
    if (newQuality > 100) {
      return buffer
    }
    if (extension === 'jpeg' || extension === 'jpg') {
      newBuffer = await image.jpeg({ quality: newQuality, mozjpeg: true }).rotate().toBuffer()
    }
    if (extension === 'png') {
      newBuffer = await image.png({ quality: newQuality, }).rotate().toBuffer()
    }
  }

  // save buffer to file
  fs.writeFileSync('/Users/<USER>/Downloads/test2.png', newBuffer)
  
  return newBuffer
}

describe('Test compress image', () => {
  it('compress image', async () => {
    const buffer = fs.readFileSync('/Users/<USER>/Downloads/test.png')
    console.log(buffer)
    const result = await compressImage(buffer)
    expect(result).toBeInstanceOf(Buffer)
  })
})