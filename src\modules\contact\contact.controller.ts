import { Body, Controller, Get, Post, Query } from '@nestjs/common'
import { PaginationDto } from '../../dto'
import { ContactService } from './contact.service'
import { ContactDto } from './dto/contact.dto'
import { ApiTags } from '@nestjs/swagger'

@ApiTags('Contact')
@Controller('contact')
export class ContactController {
  constructor(private readonly contactService: ContactService) {}
  @Get('pagination')
  async getContactPagination(@Query() data: PaginationDto) {
    return await this.contactService.getContactPagination(data)
  }

  @Post('add-contact')
  async addContact(@Body() data: ContactDto) {
    return await this.contactService.addContact(data)
  }

  @Post('update-read')
  async updateReadStatus(@Body() id: any) {
    return await this.contactService.updateReadStatus(id)
  }
}
