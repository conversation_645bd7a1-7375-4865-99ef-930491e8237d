import { MigrationInterface, QueryRunner } from "typeorm";

export class updateEntityPoSo1743154621209 implements MigrationInterface {
    name = 'updateEntityPoSo1743154621209'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "purchase_order_so" ADD "status" character varying DEFAULT 'NEWLYCREATED'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "purchase_order_so" DROP COLUMN "status"`);
    }

}
