import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import { WarehouseService } from './warehouse.service'
import { WarehouseController } from './warehouse.controller'
import {
  WarehouseProductDetailRepository,
  WarehouseRepository,
  ItemRepository,
  UnitRepository,
  ItemDetailRepository,
  InboundDetailRepository,
  WarehouseProductRepository,
  WarehouseProductSafetyEntityRepository,
  ItemComboRepository,
  CityRepository,
  OperationalAreaRepository,
  SupplierRepository,
  DistrictRepository,
  WardRepository,
} from '../../../repositories'
import { WarehousePublicController } from './warehousePublic.controller'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      WarehouseRepository,
      ItemRepository,
      UnitRepository,
      WarehouseProductDetailRepository,
      WarehouseProductRepository,
      ItemDetailRepository,
      ItemComboRepository,
      WarehouseProductSafetyEntityRepository,
      InboundDetailRepository,
      CityRepository,
      OperationalAreaRepository,
      SupplierRepository,
      CityRepository,
      DistrictRepository,
      WardRepository
    ]),
  ],
  controllers: [WarehouseController, WarehousePublicController],
  providers: [WarehouseService],
  exports: [WarehouseService],
})
export class WarehouseModule { }
