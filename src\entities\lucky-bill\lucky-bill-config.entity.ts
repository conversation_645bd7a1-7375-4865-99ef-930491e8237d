import { Column, <PERSON><PERSON><PERSON>, Join<PERSON><PERSON>um<PERSON>, OneToMany } from 'typeorm'
import { BaseEntity } from '../core'
import { LuckyBillApplyRegionEntity } from './lucky-bill-apply-region.entity'
import { NSLuckyBill } from '../../constants/NSLuckyBill'

@Entity('lucky_bill_config')
export class LuckyBillConfigEntity extends BaseEntity {
  @Column({ type: 'varchar', length: 255, nullable: false })
  name: string

  @Column({ type: 'varchar', length: 50, nullable: false, unique: true })
  code: string

  @Column({ type: 'int', nullable: false })
  value: number

  @Column({ type: 'timestamptz', nullable: false })
  applyDateFrom: Date

  @Column({ type: 'timestamptz', nullable: false })
  applyDateTo: Date

  //Ngày quay gần nhất
  @Column({ type: 'timestamptz', nullable: true })
  lastDrawDate: Date

  //c<PERSON> chế quay
  @Column({ type: 'varchar', length: 50, nullable: true, default: NSLuckyBill.ELuckyDrawMechanism.MANUAL })
  luckyDrawMechanism: NSLuckyBill.ELuckyDrawMechanism

  @Column({ type: 'timestamptz', nullable: true })
  frequency: Date

  @Column({ type: 'text', nullable: true })
  description: string

  //onetomany cascade delete
  @OneToMany(() => LuckyBillApplyRegionEntity, (p) => p.luckyBillConfigId, { cascade: true })
  @JoinColumn({ name: 'luckyBillConfigId', referencedColumnName: 'id' })
  applyRegions: Promise<LuckyBillApplyRegionEntity[]>
}
