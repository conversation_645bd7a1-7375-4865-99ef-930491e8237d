import { Controller, Post, Body, Req } from '@nestjs/common'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { ContractService } from './contract.service'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { ContractCreateDto, ContractFilterDto, ContractGenerateDto, ContractMediaTypeFilterDto, ContractMemberCardFilterDto } from './dto'
import { CurrentUser } from '../../common/decorators'
import { CreateContractDto } from './dto/createContract.dto'
import { ContractUpdateIsActiveDto } from './dto/contractUpdateIsActive.dto'
import { Request as IRequest } from 'express'

@ApiBearerAuth()
@ApiTags('Public Contract')
@Controller('public_contract')
export class PublicContractController {
  constructor(private readonly service: ContractService) {}

  @Post('find_detail')
  public async findDetail(@Body() data: FilterOneDto) {
    return await this.service.findDetail(data)
  }

  @Post('find_by_member')
  public async findByMember(@Body() data: FilterOneDto) {
    return await this.service.findByMember(data)
  }

  @Post('find_by_member_card')
  public async findByMemberCard(@Body() data: ContractMemberCardFilterDto) {
    return await this.service.findByMemberCard(data)
  }

  @Post('find-contract')
  public async findContract(@Body() data: ContractFilterDto) {
    return await this.service.findContract(data)
  }

  @Post('pagination')
  public async pagination(@Body() data: PaginationDto) {
    return await this.service.pagination(data)
  }

  @Post('create_data')
  public async createData(@Body() data: ContractCreateDto, @CurrentUser() user: UserDto, @Req() req: IRequest) {
    return await this.service.createData(user, data, req)
  }

  @Post('create_contract')
  async createContract(@CurrentUser() user: UserDto, @Body() data: CreateContractDto[]) {
    return await this.service.createContract(data)
  }

  @Post('update_active')
  public async updateActive(@Body() data: ContractUpdateIsActiveDto, @CurrentUser() user: UserDto) {
    return await this.service.updateIsDelete(data.id, user)
  }

  @Post('generate_template')
  public async generateTemplate(@Req() req: IRequest, @Body() data: ContractGenerateDto) {
    return await this.service.generateTemplate(data, req)
  }

  @Post('find_by_media_type')
  public async findByMediaType(@Body() data: ContractMediaTypeFilterDto) {
    return await this.service.findByMediaType(data)
  }
}
