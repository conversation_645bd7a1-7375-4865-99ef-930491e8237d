import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'

export class CompanyCreateTopicMasterDataDto {
  @ApiProperty({ description: 'Tên chủ đề' })
  @IsNotEmpty()
  @IsString()
  name: string

  @ApiProperty({ description: 'Mã chủ đề' })
  @IsNotEmpty()
  @IsString()
  code: string

  @ApiProperty({ description: '<PERSON>h mục' })
  @IsNotEmpty()
  @IsString()
  categoriesId: string

  @ApiProperty({ description: '<PERSON>hi chú' })
  description: string

  createdBy: string

  companyId: string
}
