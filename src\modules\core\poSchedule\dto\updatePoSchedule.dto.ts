import { IsNotEmpty, IsString, <PERSON>UUI<PERSON>, IsOptional } from 'class-validator';

export class UpdatePoScheduleDto {
  @IsUUID()
  @IsNotEmpty()
  id: string;

  @IsString()
  @IsOptional()
  supplier?: string;

  @IsString()
  @IsOptional()
  productName?: string;

  @IsOptional()
  expectedQuantity?: number;

  @IsString()
  @IsOptional()
  unit?: string;

  @IsOptional()
  unitPrice?: number;

  @IsOptional()
  totalPrice?: number;

  @IsString()
  @IsOptional()
  supplyTimeType?: string;

  @IsOptional()
  supplyTime?: Date;

  @IsString()
  @IsOptional()
  note?: string;

  @IsString()
  @IsOptional()
  status?: string;
}
