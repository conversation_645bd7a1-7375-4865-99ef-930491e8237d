import { Controller, UseGuards, Request, Post, Body, Req } from '@nestjs/common'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { Request as IRequest } from 'express'
import { ItemPriceCreateDto, ItemPriceImportDto, ItemPriceUpdateDto, ItemPriceUpdatePriceDto } from './dto'
import { CurrentUser, JwtAuthGuard } from '../../common'
import { ItemPriceService } from './itemPrice.service'
import { FilterIdDto, UserDto } from '../../../dto'
import { AuthGuard } from '../../common/guards/auth.guard'

@ApiBearerAuth()
@ApiTags('ItemPrice')
@UseGuards(JwtAuthGuard)
@Controller('item_price')
export class ItemPriceController {
  constructor(private readonly service: ItemPriceService) {}

  @Post('find')
  public async find(@Request() req: IRequest, @Body() data: any) {
    return await this.service.find(data)
  }

  @Post('pagination')
  public async pagination(@Request() req: IRequest, @Body() data: any) {
    return await this.service.pagination(data, req)
  }

  @Post('find_for_edit')
  public async findForEdit(@Request() req: IRequest, @Body() data: { id: string }) {
    return await this.service.findForEdit(data.id, req)
  }

  @Post('find_for_add')
  public async findForAdd(@Request() req: IRequest) {
    return await this.service.findForAdd(req)
  }

  @Post('find_item')
  public async findItem(@Request() req: IRequest, @Body() data: { id: string }) {
    return await this.service.findItem(data.id)
  }

  @Post('create_data')
  public async createData(@Request() req: IRequest, @CurrentUser() user: UserDto, @Body() data: ItemPriceCreateDto) {
    return await this.service.createData(user, data, req)
  }

  @Post('update_data')
  public async updateData(@Request() req: IRequest, @CurrentUser() user: UserDto, @Body() data: ItemPriceUpdateDto) {
    return await this.service.updateData(user, data, req)
  }

  @Post('update_active')
  public async updateActive(@Request() req: IRequest, @Body() data: any, @CurrentUser() user: UserDto) {
    return await this.service.updateActive(data.id, user, req)
  }

  @Post('update_list_price')
  public async updateListPrice(@Request() req: IRequest, @Body() data: ItemPriceUpdatePriceDto[], @CurrentUser() user: UserDto) {
    return await this.service.updateListPrice(user, data, req)
  }

  @Post('find_item_price')
  public async findItemPrice(@Body() data: FilterIdDto) {
    return await this.service.findItemPrice(data)
  }

  @Post('create_data_excel')
  public async createDataExcel(@CurrentUser() user: UserDto, @Body() data: ItemPriceImportDto[], @Req() req: IRequest) {
    return await this.service.createDataExcel(user, data, req)
  }
}
