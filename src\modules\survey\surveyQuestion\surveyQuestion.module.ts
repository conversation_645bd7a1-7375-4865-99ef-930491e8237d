import { Module } from '@nestjs/common'
import { SurveyQuestionController } from './surveyQuestion.controller'
import { SurveyQuestionService } from './surveyQuestion.service'
import { TypeOrmExModule } from '../../../typeorm'
import {
  CategoriesRepository,
  QuestionRepository,
  SurveyMemberRepository,
  SurveyQuestionListDetailRepository,
  SurveyQuestionRepository,
  SurveyRepository,
  TopicRepository,
  UserSurveyRepository,
} from '../../../repositories/survey'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      SurveyRepository,
      SurveyMemberRepository,
      SurveyQuestionRepository,
      TopicRepository,
      QuestionRepository,
      UserSurveyRepository,
      SurveyQuestionListDetailRepository,
    ]),
  ],
  controllers: [SurveyQuestionController],
  providers: [SurveyQuestionService],
  exports: [SurveyQuestionService],
})
export class SurveyQuestionModule {}
