import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import { NewsCategoryRepository, NewsRepository } from '../../../repositories'
import { NewsController } from './news.controller'
import { NewsService } from './news.service'
import { NewsPublicController } from './newsPublic.controller'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([NewsRepository, NewsCategoryRepository])],
  controllers: [NewsController, NewsPublicController],
  providers: [NewsService],
  exports: [NewsService],
})
export class NewsModule {}
