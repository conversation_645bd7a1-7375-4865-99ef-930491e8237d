import { Body, Controller, Post, Req, UseGuards } from '@nestjs/common'
import { FilterOneDto, UserDto } from '../../../dto'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { MediaCreateDto, MediaFilterDto } from './dto'
import { MediaService } from './media.service'
import { JwtAuthGuard } from '../../common/guards'
import { CurrentUser } from '../../common/decorators'

@ApiBearerAuth()
@ApiTags('Media')
@UseGuards(JwtAuthGuard)
@Controller('media')
export class MediaController {
  constructor(private readonly service: MediaService) {}

  @ApiOperation({ summary: 'Danh sách hình ảnh của sản phẩm' })
  @Post('find')
  public async find(@Body() data: MediaFilterDto, @CurrentUser() user: UserDto) {
    return await this.service.find(data, user)
  }

  @ApiOperation({ summary: 'Tạo mới hình ảnh' })
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: MediaCreateDto[]) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật xóa toàn bộ hình ảnh của sản phẩm' })
  @Post('update_delete')
  public async updateDelete(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateDelete(user, data)
  }
}
