import { Module } from '@nestjs/common'
import { LuckyBillApplyRegionService } from './lucky-bill-apply-region.service'
import { LuckyBillApplyRegionController } from './lucky-bill-apply-region.controller'
import { TypeOrmExModule } from '../../../typeorm'
import { LuckyBillConfigRepository } from '../../../repositories/lucky-bill/lucky-bill-config.repository'
import { LuckyBillApplyRegionRepository } from '../../../repositories/lucky-bill/lucky-bill-apply-region.repository'
import { RegionCityRepository, RegionRepository } from '../../../repositories'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([LuckyBillConfigRepository, LuckyBillApplyRegionRepository, RegionRepository, RegionCityRepository])],
  controllers: [LuckyBillApplyRegionController],
  providers: [LuckyBillApplyRegionService],
  exports: [LuckyBillApplyRegionService],
})
export class LuckyBillApplyRegionModule {}
