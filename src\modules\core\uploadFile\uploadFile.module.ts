import { Module } from '@nestjs/common'
import { UploadFileController } from './uploadFile.controller'
import { UploadFileService } from './uploadFile.service'
import { UploadFileOmsController } from './uploadFileOms.controller'

@Module({
  imports: [],
  controllers: [UploadFileController, UploadFileOmsController],
  providers: [UploadFileService],
  exports: [UploadFileService],
})
export class UploadFileModule {}
