import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsOptional,
  IsUUID,
  IsEnum,
} from 'class-validator';
import { NSPo } from '../../../../constants';

export class UpdateStatusPurchaseOrderDto {
  @ApiProperty({ description: 'PO ID' })
  @IsNotEmpty()
  @IsUUID()
  id: string

  @ApiProperty({ description: 'ID người cập nhật' })
  @IsOptional()
  updateBy?: string

  @ApiPropertyOptional({
    description: `Trạng thái ${Object.values(NSPo.EPoStatus).join(' | ')}`,
    enum: NSPo.EPoStatus,
    default: NSPo.EPoStatus.NEWLYCREATED,
  })
  @IsOptional()
  @IsEnum(NSPo.EPoStatus)
  status?: NSPo.EPoStatus;
}

export class UpdateStatusDistributorDto {
  @ApiProperty({ description: 'Distribute ID' })
  @IsNotEmpty()
  @IsUUID()
  distributorId: string

  @ApiPropertyOptional({
    description: `Trạng thái giao hàng ${Object.values(NSPo.EPoStatusDistribute).join(' | ')}`,
    enum: NSPo.EPoStatusDistribute,
  })
  @IsEnum(NSPo.EPoStatusDistribute)
  status: NSPo.EPoStatusDistribute;
}