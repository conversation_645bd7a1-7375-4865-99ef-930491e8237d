import { MigrationInterface, QueryRunner } from "typeorm";

export class updateEntityDeliveryTrackingPacking1747453018361 implements MigrationInterface {
    name = 'updateEntityDeliveryTrackingPacking1747453018361'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "delivery_note_tracking" ADD "supplierId" uuid`);
        await queryRunner.query(`ALTER TABLE "delivery_note_tracking" ADD "warehouseId" uuid`);
        await queryRunner.query(`ALTER TABLE "delivery_note_packing" ADD "warehouseId" uuid`);
        await queryRunner.query(`CREATE INDEX "IDX_0dff12059637baa96ca8e240cb" ON "delivery_note_packing" ("warehouseId") `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_0dff12059637baa96ca8e240cb"`);
        await queryRunner.query(`ALTER TABLE "delivery_note_packing" DROP COLUMN "warehouseId"`);
        await queryRunner.query(`ALTER TABLE "delivery_note_tracking" DROP COLUMN "warehouseId"`);
        await queryRunner.query(`ALTER TABLE "delivery_note_tracking" DROP COLUMN "supplierId"`);
    }

}
