import { Injectable } from '@nestjs/common'
import {
  ItemRepository,
  WarehouseProductRepository,
  WarehouseRepository,
  ItemCategoryRepository,
  ItemPriceRepository,
  PackingRepository,
  ItemTypeRepository,
  MediaRepository,
  ItemGroupRepository,
} from '../../../repositories'
import { FindOptionsWhere, In } from 'typeorm'
import { ListComboReq, ListItemGroupReq } from '../../../dto/item-group.dto'
import { ItemEntity, ItemGroupEntity } from '../../../entities'
import { IdDto } from '../../../dto/id.dto'
@Injectable()
export class wmsPublicApiService {
  constructor(
    private readonly warehouseRepo: WarehouseRepository,
    private readonly productRepo: ItemRepository,
    private readonly itemPriceRepository: ItemPriceRepository,
    private readonly packingRepository: PackingRepository,
    private readonly warehouseProductRepo: WarehouseProductRepository,
    private readonly itemCategoryRepository: ItemCategoryRepository,
    private readonly itemTypeRepository: ItemTypeRepository,
    private readonly mediaRepository: MediaRepository,
    private readonly itemGroupRepo: ItemGroupRepository,
  ) { }

  public async findCategory() {
    return this.itemCategoryRepository.find({ where: { isDeleted: false } })
  }
  public async findProductType() {
    return this.itemTypeRepository.find({ where: { isDeleted: false } })
  }

  public async findItemPrice() {
    const lstPrice: any = await this.itemPriceRepository.find({
      where: { isDeleted: false, isFinal: true },
      select: { itemId: true, item: { name: true }, priceSell: true },
      relations: { item: true },
    })
    for (const item of lstPrice) {
      item.itemName = item?.__item__?.name
      delete item.__item__
    }
    return lstPrice
  }

  public async findWarehouse() {
    return this.warehouseRepo.find({ select: { id: true, code: true, name: true, address: true }, where: { isDeleted: false } })
  }

  public async findWarehouseItem(lstWarehouse: string[]) {
    let rs = []
    /* tìm ra danh sách warehouse*/
    const lstWarehouseRs = await this.warehouseProductRepo.find({ where: { warehouseId: In(lstWarehouse), isDeleted: false } })
    /* Lấy ra chi tiết của item nằm trong danh sách trả về */
    if (lstWarehouseRs.length > 0) {
      const itemDic: any = {}
      const lstItemId = lstWarehouseRs.map((e) => e.productId)
      const lstItemDetail = await this.productRepo.find({ where: { id: In(lstItemId) } })
      for (const item of lstItemDetail) {
        const itemPrice = await this.itemPriceRepository.findOne({ where: { itemId: item.id, isFinal: true, isDeleted: false } })
        const packing = await this.packingRepository.findOne({ where: { id: item.packingId } })
        const lstImg = await this.mediaRepository.find({ where: { productId: item.id, isDeleted: false } })
        itemDic[item.id] = {
          id: item.id,
          code: item.code,
          name: item.name,
          price: itemPrice?.priceSell || 0,
          stock: 0,
          packing: packing.name,
          listImg: lstImg,
        }
      }
      const dicItemWarehouse: any = {}

      for (const itemWarehouse of lstWarehouseRs) {
        if (dicItemWarehouse[itemWarehouse.productId]) {
          dicItemWarehouse[itemWarehouse.productId].stock += itemWarehouse.quantity || 0
        } else {
          dicItemWarehouse[itemWarehouse.productId] = itemDic[itemWarehouse.productId]
          dicItemWarehouse[itemWarehouse.productId].stock = itemWarehouse.quantity || 0
        }
      }
      rs = Object.values(dicItemWarehouse)
    } else {
      const itemDic: any = {}
      const lstWarehouseRs = await this.warehouseProductRepo.find({ where: { isDeleted: false } })
      let lstItemId = []
      if (lstWarehouseRs.length > 0) lstItemId = lstWarehouseRs.map((e) => e.productId)
      const lstItemDetail = await this.productRepo.find({ where: { id: In(lstItemId) } })
      for (const item of lstItemDetail) {
        const itemPrice = await this.itemPriceRepository.findOne({ where: { itemId: item.id, isFinal: true, isDeleted: false } })
        const packing = await this.packingRepository.findOne({ where: { id: item.packingId } })
        const lstImg = await this.mediaRepository.find({ where: { productId: item.id, isDeleted: false } })
        itemDic[item.id] = {
          id: item.id,
          code: item.code,
          name: item.name,
          price: itemPrice?.priceSell || 0,
          stock: 0,
          packing: packing.name,
          listImg: lstImg,
        }
      }
      const dicItemWarehouse: any = {}

      for (const itemWarehouse of lstWarehouseRs) {
        if (dicItemWarehouse[itemWarehouse.productId]) {
          dicItemWarehouse[itemWarehouse.productId].stock += itemWarehouse.quantity || 0
        } else {
          dicItemWarehouse[itemWarehouse.productId] = itemDic[itemWarehouse.productId]
          dicItemWarehouse[itemWarehouse.productId].stock = itemWarehouse.quantity || 0
        }
      }
      rs = Object.values(dicItemWarehouse)
    }

    return rs
  }
  public async findWarehouseComboItem(lstWarehouse: string[]) {
    const lstWarehouseRs = await this.warehouseProductRepo.find({ where: { warehouseId: In(lstWarehouse), isDeleted: false } })
    const comboWhere: any = { isCombo: true, isDeleted: false }
    if (lstWarehouseRs.length > 0) {
      const lstItemId = lstWarehouseRs.map((e) => e.productId)
      comboWhere.id = In(lstItemId)
    }
    const lstCombo: any = await this.productRepo.find({ where: comboWhere, relations: { itemCombo: { itemInCombo: true }, prices: true } })
    const rs = []
    for (const combo of lstCombo) {
      const comboDetail: any = {}
      let price = await combo.prices
      comboDetail.price = price.find((x) => x.isFinal) || null
      comboDetail.name = combo.name
      comboDetail.code = combo.code
      comboDetail.id = combo.id
      comboDetail.stock = combo.quantity
      comboDetail.dateFrom = combo.dateFrom
      const packing = await this.packingRepository.findOne({ where: { id: comboDetail.packingId } })
      comboDetail.packing = packing.name
      comboDetail.dateTo = combo.dateTo
      // comboDetail.price = combo.sellPrice
      comboDetail.listImg = await this.mediaRepository.find({ where: { productId: combo.id, isDeleted: false } })
      comboDetail.lstItem = []
      for (const itemIncombo of combo.__itemCombo__) {
        const itemDetail: any = {}
        itemDetail.name = itemIncombo.__itemInCombo__.name
        itemDetail.code = itemIncombo.__itemInCombo__.code
        itemDetail.id = itemIncombo.__itemInCombo__.id
        itemDetail.quantity = itemIncombo.quantity
        itemDetail.packing = await this.packingRepository.findOne({ where: { id: itemIncombo.__itemInCombo__.packingId } })
        itemDetail.listImg = await this.mediaRepository.find({ where: { productId: itemIncombo.__itemInCombo__.id, isDeleted: false } })
        comboDetail.lstItem.push(itemDetail)
      }
      rs.push(comboDetail)
    }
    return rs
  }

  public async listWarehouseComboItem(data: ListComboReq, lstWarehouse: string[]) {
    const lstWarehouseRs = await this.warehouseProductRepo.find({ where: { warehouseId: In(lstWarehouse), isDeleted: false } })
    const comboWhere: any = { isCombo: true, isDeleted: false }
    if (lstWarehouseRs.length > 0) {
      const lstItemId = lstWarehouseRs.map((e) => e.productId)
      comboWhere.id = In(lstItemId)
    }
    // let itemGroup = null
    // if (data.itemGroupId) {
    //   comboWhere.itemGroupId = '2133ae03-d9c0-4ca8-88bf-4ef80b348f17'
    //   itemGroup = await this.itemGroupRepo.findOne({ where: { id: '2133ae03-d9c0-4ca8-88bf-4ef80b348f17', isDeleted: false } })
    // }
    const lstCombo: any = await this.productRepo.findAndCount({ where: comboWhere, relations: { itemCombo: { itemInCombo: true }, prices: true } })
    const rs = {
      data: [],
      total: lstCombo[1],
    }
    let listItemId = []
    listItemId = lstCombo[0].map((combo) => combo.__itemCombo__.map((x) => x.__itemInCombo__.id))
    listItemId = listItemId.flat()
    let prices = await this.itemPriceRepository.find({ where: { itemId: In(listItemId) } })
    let listImage = await this.mediaRepository.find({ where: { productId: In(lstCombo[0].map((x) => x.id)), isDeleted: false } })
    let listProductId = lstCombo[0].map((x) => x.__itemCombo__.map((x) => x.__itemInCombo__.id)).flat()
    let listImageProduct = await this.mediaRepository.find({
      where: { productId: In(listProductId), isDeleted: false },
    })
    let mapListImageProduct = listImageProduct.convertToMap((x) => x.productId)
    let mapImage = listImage.convertToMap((x) => x.productId)
    let mapPrices = prices.convertToMap((x) => x.itemId)
    for (const combo of lstCombo[0]) {
      const comboDetail: any = {}
      let price = await combo.prices
      price = price?.find((x) => x.isFinal) || null
      if (price !== null) price = price.priceSell
      comboDetail.price = price
      comboDetail.originalPrice = 0
      comboDetail.name = combo.name
      comboDetail.code = combo.code
      comboDetail.id = combo.id
      // comboDetail.price = combo.sellPrice
      comboDetail.images = mapImage.get(combo.id)
      comboDetail.items = []

      for (const itemIncombo of combo.__itemCombo__) {
        const itemDetail: any = {}
        itemDetail.name = itemIncombo.__itemInCombo__.name
        itemDetail.code = itemIncombo.__itemInCombo__.code
        itemDetail.id = itemIncombo.__itemInCombo__.id
        itemDetail.quantity = itemIncombo.quantity
        comboDetail.originalPrice += itemIncombo.quantity * mapPrices.get(itemIncombo.__itemInCombo__.id)?.priceSell || 0
        itemDetail.unitId = itemIncombo.__itemInCombo__.unitId
        itemDetail.itemTypeId = itemIncombo.__itemInCombo__.itemTypeId
        itemDetail.itemGroupId = itemIncombo.__itemInCombo__.itemGroupId
        itemDetail.itemCategoryId = itemIncombo.__itemInCombo__.itemCategoryId
        itemDetail.quantityUnit = itemIncombo.__itemInCombo__.quantityUnit
        itemDetail.kg = itemIncombo.__itemInCombo__.kg
        itemDetail.dateFrom = itemIncombo.__itemInCombo__.dateFrom
        itemDetail.dateTo = itemIncombo.__itemInCombo__.quantityUnit
        itemDetail.packingId = itemIncombo.__itemInCombo__.packingId
        itemDetail.images = mapListImageProduct.get(itemDetail.id)
        comboDetail.items.push(itemDetail)
      }
      comboDetail.originalPrice = comboDetail.originalPrice.toFixed(2)
      rs.data.push(comboDetail)
    }
    return rs
  }

  async listItemGroup(params: ListItemGroupReq) {
    const wheres = [`ig."isDeleted" = FALSE`]
    if (params?.groupType) {
      wheres.push(`ig."groupType" = '${params?.groupType}'`)
    }
    const sql = `
      SELECT 
        ig.id, ig.code, ig."name", ig."groupType", ig."description", ig."itemCategoryId",
        COALESCE ( json_agg ( mda.url ) FILTER ( WHERE mda.url IS NOT NULL ), '[]' ) AS "images"
      FROM 
        item_group ig 
      LEFT JOIN media mda ON ig."id" = mda."productId" AND mda."table" = 'ItemGroup'
      WHERE  ${wheres.join(' AND ')}
      GROUP BY 
        ig."id",
        ig."code",
        ig."name", 
        ig."groupType", 
        ig."itemCategoryId",
        ig."description"
    `
    let item = await this.itemGroupRepo.queryPagination(sql, params)
    if (params?.groupType === 'COMBO') {
      const customOrder = ['Chăm Sóc Nhà Cửa - An Gia', 'Chăm Sóc Cá Nhân - Ngọc Thể', 'Thực Phẩm Tươi - Sum Vầy', 'Thực Phẩm Khô - Đoàn Tụ']
      item.data = item.data.sort((a: any, b: any) => {
        const indexA = customOrder.indexOf(a.name) !== -1 ? customOrder.indexOf(a.name) : customOrder.length
        const indexB = customOrder.indexOf(b.name) !== -1 ? customOrder.indexOf(b.name) : customOrder.length
        return indexA - indexB
      })
    }
    return item
  }
  listCombo(params: ListComboReq) {
    const wheres = [`combo."isDeleted" = FALSE`]
    if (params?.itemGroupId) {
      wheres.push(`itg."id" = '${params?.itemGroupId}'`)
    }
    const sql = `
    SELECT
      combo."id",
      combo."name",
      combo."description",
      CAST( COALESCE(combo_price."priceSell",0 ) AS FLOAT )AS "price",
      SUM ( CAST ( COALESCE ( product_price."priceSell" * item_combo."quantity", 0 ) AS FLOAT ) ) AS "originalPrice",
      COALESCE ( json_agg ( mda.url ) FILTER ( WHERE mda.url IS NOT NULL ), '[]' ) AS "images",
      to_json ( itg.* ) AS "itemGroup",
      COALESCE (
        json_agg (
          jsonb_set (
            jsonb_set ( to_jsonb ( product ), '{price}', to_jsonb ( COALESCE ( product_price."priceSell", 0 ) ) ),
            '{quantity}',
            to_jsonb ( COALESCE ( item_combo."quantity", 0 ) ) 
          ) 
        ) FILTER ( WHERE product."id" IS NOT NULL ),
        '[]' 
      ) AS "items" 
    FROM
      item_combo item_combo
      LEFT JOIN item "combo" ON combo."id" = item_combo."itemId" AND combo."isCombo" = true 
      LEFT JOIN item "product" ON product."id" = item_combo."itemInComboId" AND product."isDeleted" = FALSE
      LEFT JOIN item_group "itg" ON itg."id" = combo."itemGroupId" AND itg."isDeleted" = FALSE
      LEFT JOIN item_price "product_price" ON product_price."itemId" = product."id" AND product_price."isFinal" =true 
      LEFT JOIN item_price "combo_price" ON combo_price."itemId" = combo."id" AND combo_price."isFinal" = true 
      LEFT JOIN media mda ON mda."productId" = combo."id"
    WHERE  ${wheres.join(' AND ')}
    GROUP BY
      combo."id",
      combo."name",
      combo."description",
      combo."isFavoriteCombo",
      itg.*,
      combo_price."priceSell"
     ORDER BY
      combo."isFavoriteCombo" DESC
     `
    return this.itemGroupRepo.queryPagination(sql, params)
  }

  async comboDetail({ id }: IdDto) {
    const wheres = [`combo."id" = '${id}'`, `combo."isDeleted" = FALSE`]
    const sql = `
      SELECT
        combo."id",
        combo."name",
        combo."description",
        CAST( COALESCE(combo_price."priceSell",0 ) AS FLOAT )AS "price",
        SUM ( CAST ( COALESCE ( product_price."priceSell" * item_combo."quantity", 0 ) AS FLOAT ) ) AS "originalPrice",
        COALESCE ( json_agg ( mda.url ) FILTER ( WHERE mda.url IS NOT NULL ), '[]' ) AS "images",
        to_json ( itg.* ) AS "itemGroup",
        COALESCE (
          json_agg (
            jsonb_set (
              jsonb_set ( to_jsonb ( product ), '{price}', to_jsonb ( COALESCE ( product_price."priceSell", 0 ) ) ),
              '{quantity}',
              to_jsonb ( COALESCE ( item_combo."quantity", 0 ) ) 
            ) 
          ) FILTER ( WHERE product."id" IS NOT NULL ),
          '[]' 
        ) AS "items" 
      FROM
        item_combo item_combo
        LEFT JOIN item "combo" ON combo."id" = item_combo."itemId" AND combo."isCombo" = true 
        LEFT JOIN item "product" ON product."id" = item_combo."itemInComboId" AND product."isDeleted" = FALSE
        LEFT JOIN item_group "itg" ON itg."id" = combo."itemGroupId" AND itg."isDeleted" = FALSE
        LEFT JOIN item_price "product_price" ON product_price."itemId" = product."id" AND product_price."isFinal" =true 
        LEFT JOIN item_price "combo_price" ON combo_price."itemId" = combo."id" AND combo_price."isFinal" = true 
        LEFT JOIN media mda ON mda."productId" = combo."id"
      WHERE  ${wheres.join(' AND ')}
      GROUP BY
        combo."id",
        combo."name",
        combo."description",
        itg."id",
        itg.*,
        combo_price."priceSell"
     `
    const combo = await this.itemGroupRepo.queryOne<{ items: ItemEntity[];[key: string]: any }>(sql)
    const lstIemFilter: any = {}
    for (const item of combo.items) {
      lstIemFilter[item.id] = item
    }
    combo.items = Object.values(lstIemFilter)
    const productIds = combo.items.map((v) => v.id)
    const medias = await this.mediaRepository.find({
      where: {
        productId: In(productIds),
      },
    })
    const mapMedia = medias.reduce((result, item) => {
      const oldImages = result[item.productId] || []
      return {
        ...result,
        [item.productId]: [...oldImages, item.url],
      }
    }, {})
    return {
      ...combo,
      items: combo.items.map((it) => {
        return {
          ...it,
          images: mapMedia[it.id] || [],
        }
      }),
    }
  }
}
