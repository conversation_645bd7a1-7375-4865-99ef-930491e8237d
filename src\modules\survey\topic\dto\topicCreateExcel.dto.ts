import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'

export class TopicCreateExcelDto {
  @ApiProperty({ description: 'Tên chủ đề' })
  @IsNotEmpty({ message: 'Tên không được trống' })
  @IsString()
  name: string

  @ApiProperty({ description: 'Mã chủ đề' })
  @IsNotEmpty({ message: 'Mã không được trống' })
  @IsString()
  code: string

  @ApiProperty({ description: 'Mã danh mục' })
  cateCode: string

  @ApiProperty({ description: '<PERSON>hi chú' })
  description: string
}
