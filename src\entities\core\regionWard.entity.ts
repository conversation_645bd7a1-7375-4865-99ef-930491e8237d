import { <PERSON><PERSON><PERSON>, Column, <PERSON>To<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm'
import { BaseEntity } from './base.entity'
import { RegionEntity } from './region.entity'
import { WardEntity } from './ward.entity'

@Entity('region_ward')
export class RegionWardEntity extends BaseEntity {
  /** Vùng */
  @Column({ type: 'varchar', nullable: false })
  regionId: string
  @ManyToOne(() => RegionEntity, (p) => p.cities)
  @JoinColumn({ name: 'regionId', referencedColumnName: 'id' })
  region: Promise<RegionEntity>

  /** phường / xã */
  @Column({ type: 'varchar', nullable: false })
  wardId: string
  @ManyToOne(() => WardEntity, (p) => p.id)
  @JoinColumn({ name: 'ward', referencedColumnName: 'id' })
  ward: Promise<WardEntity>
}
