import { CanActivate, ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common'

@Injectable()
export class AuthGuard implements CanActivate {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest()
    const key_secret_micro = request.headers['x-apikey']

    if (!key_secret_micro) throw new UnauthorizedException('Không có quyền truy cập! (code: KEY_SECRET_MICRO_ERROR_1)')

    if (!process.env.KEY_SECRET_MICRO) throw new UnauthorizedException('Không có quyền truy cập! (code: KEY_SECRET_MICRO_ERROR_2)')
    if (key_secret_micro !== process.env.KEY_SECRET_MICRO)
      throw new UnauthorizedException('Không có quyền truy cập! (code: KEY_SECRET_MICRO_ERROR_3)')

    return true
  }
}
