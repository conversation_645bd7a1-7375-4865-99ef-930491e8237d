import { MigrationInterface, QueryRunner } from 'typeorm'

export class initPricing1757562304688 implements MigrationInterface {
  name = 'initPricing1757562304688'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "pricing" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "createdBy" character varying(36), "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT now(), "updatedBy" character varying(36), "isDeleted" boolean NOT NULL DEFAULT false, "code" character varying(255) NOT NULL, "price" numeric NOT NULL, "itemId" uuid NOT NULL, "supplierId" uuid NOT NULL, "description" text, CONSTRAINT "PK_4f6e9c88033106a989aa7ce9dee" PRIMARY KEY ("id"))`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "pricing"`)
  }
}
