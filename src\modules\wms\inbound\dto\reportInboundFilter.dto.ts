import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString, IsNumber, IsOptional } from 'class-validator'

export class ReportInboundDto {
  @ApiProperty({ description: 'Mã PO' })
  @IsNotEmpty()
  @IsString()
  poCode: string

  @ApiProperty({ description: 'Mã phiếu nhập' })
  @IsOptional()
  @IsString()
  code?: string

  @ApiProperty({ description: 'NCC' })
  @IsOptional()
  @IsString()
  supplierId?: string

  @ApiProperty({ description: 'Thời gian' })
  @IsOptional()
  date?: Date[]

  @ApiProperty({ description: '' })
  @IsOptional()
  productName?: string
}
