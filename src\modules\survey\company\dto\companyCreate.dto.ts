import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'

/** Interface tạo đối tác */
export class CompanyCreateDto {
  @ApiProperty({ description: 'Tên đối tác' })
  @IsNotEmpty()
  @IsString()
  name: string

  @ApiProperty({ description: 'Mã đối tác' })
  @IsNotEmpty()
  @IsString()
  code: string

  @ApiProperty({ description: 'Mã số thuế' })
  taxCode: string

  @ApiProperty({ description: 'Số điện thoại' })
  phone: string

  @ApiProperty({ description: 'Email' })
  email: string

  @ApiProperty({ description: 'Địa chỉ liên hệ' })
  address: string

  @ApiProperty({ description: 'Tên ngân hàng' })
  bankName: string

  @ApiProperty({ description: 'Chi nhánh ngân hàng' })
  bankBranch: string

  @ApiProperty({ description: 'Số tài khoản ngân hàng' })
  bankAccount: string

  @ApiProperty({ description: 'Tên liên hệ' })
  contactName: string

  @ApiProperty({ description: 'SĐT liên hệ' })
  contactNumberPhone: string

  @ApiProperty({ description: 'Tên miền trang admin Survey của đối tác' })
  domain: string

  @ApiProperty({ description: 'Tài khoản' })
  username: string

  @ApiProperty({ description: 'Mật khẩu' })
  password: string

  @ApiProperty({ description: 'danh sách danh mục' })
  lstCateId: any = []
}
