import { Module } from '@nestjs/common'
import { SurveyController } from './survey.controller'
import { SurveyService } from './survey.service'
import { NotifyModule } from '../notify/notify.module'
import { TypeOrmExModule } from '../../../typeorm'
import {
  EmployeeRepository,
  SurveyHistoryRepository,
  SurveyMemberRepository,
  SurveyQuestionListDetailRepository,
  SurveyQuestionRepository,
  SurveyRepository,
  TopicRepository,
  UserRepository,
  UserSurveyRepository,
} from '../../../repositories'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      SurveyRepository,
      SurveyMemberRepository,
      SurveyQuestionRepository,
      SurveyHistoryRepository,
      EmployeeRepository,
      TopicRepository,
      UserRepository,
      UserSurveyRepository,
      SurveyQuestionListDetailRepository,
    ]),
    NotifyModule,
  ],
  controllers: [SurveyController],
  providers: [SurveyService],
  exports: [SurveyService],
})
export class SurveyModule {}
